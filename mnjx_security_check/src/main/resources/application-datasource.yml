#======================数据源配置===============================#
spring:
  #======================数据库集群配置===============================#
  datasource:
    # 数据源配置，此处配置一主一从的环境，当前我只有一台，所以此处配置一样的
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为master
      primary: master
      # 严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      strict: false
      # 数据库的配置列表
      datasource:
        # 主库
        master:
          driver-class-name:  ${DATABASE_DRIVER_CLASS_NAME}
          # 数据库的链接 mnjx.db.psc.sw  50014
          url: jdbc:mysql://${DATABASE_IP}:${DATABASE_PORT}/${DATABASE_NAME_MNJX}?useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai&autoReconnect=true&rewriteBatchedStatements=true
          # 数据库用户名
          username: ${DATABASE_USER}
          # 用户名的密码
          password: ${MYSQL_ROOT_PASSWORD}
        # 从库
        slave_1:
          driver-class-name:  ${DATABASE_DRIVER_CLASS_NAME}
          # 数据库的链接 mnjx.db.psc.sw  50014
          url: jdbc:mysql://${DATABASE_IP}:${DATABASE_PORT}/${DATABASE_NAME_MNJX}?useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai&autoReconnect=true&rewriteBatchedStatements=true
          # 数据库用户名
          username: ${DATABASE_USER}
          # 用户名的密码
          password: ${MYSQL_ROOT_PASSWORD}
    hikari:
      max-lifetime: 1800000
      connection-timeout: 5000
      idle-timeout: 3600000
      connection-test-query: /**ping*/
      maximum-pool-size: 12
      minimum-idle: 4