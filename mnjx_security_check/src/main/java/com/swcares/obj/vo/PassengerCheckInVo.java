package com.swcares.obj.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 旅客值机信息
 *
 * <AUTHOR>
 */
@Data
public class PassengerCheckInVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * PnrId
     */
    private String pnrId;

    /**
     * segId
     */
    private String pnrSegId;

    /**
     * Cki Id
     */
    private String psgCkiId;

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班日期
     */
    private String flightDate;

    /**
     * 航节号
     */
    private String pnrSegNo;

    /**
     * 出发城市
     */
    private String org;

    /**
     * 到达城市
     */
    private String dst;

    /**
     * 起飞时间
     */
    private String estimateOff;

    /**
     * pnrNmId
     */
    private String pnrNmId;

    /**
     * 旅客姓名
     */
    private String name;

    /**
     * 旅客姓名拼音
     */
    private String queryName;

    /**
     * 性别
     */
    private String sex;

    /**
     * ssr信息中的证件号信息
     */
    private String idNo;

    /**
     * 登机口
     */
    private String gate;

    /**
     * 旅客状态
     */
    private String ckiStatus;

    /**
     * Bn号
     */
    private String aboardNo;

    /**
     * 座位
     */
    private String psgSeat;
}
