import{R as u}from"./index-a2fbd71b.js";const M=(t,n)=>{const r=e=>(e<10?"0":"")+e;return n.replace(/yyyy|MM|dd/g,e=>{switch(e){case"yyyy":return r(t.getFullYear());case"MM":return r(t.getMonth()+1);case"mm":return r(t.getMinutes());case"dd":return r(t.getDate());default:return""}})},A=t=>`0${["JAN","FEB","MAR","APR","MAY","JUN","JUL","AUG","SEP","OCT","NOV","DEC"].indexOf(t)+1}`.slice(-2),N=(t,n)=>{const r=t.slice(0,2),e=A(t.slice(2,5));return`${n||u().year()}-${e}-${r}`},f=()=>{const t=new Date;return t.setHours(0),t.setMinutes(0),t.setSeconds(0),t.setMilliseconds(0),t},l=(t,n)=>{const e=/^(\d{2})([A-Za-z]{3})(\d{2})$/.exec(t);if(e&&e.length===4){const s=e[1],a=e[2].toUpperCase(),o=e[3],g={JAN:"01",FEB:"02",MAR:"03",APR:"04",MAY:"05",JUN:"06",JUL:"07",AUG:"08",SEP:"09",OCT:"10",NOV:"11",DEC:"12"};let c;const i=new Date().getFullYear()%100,d=`19${o}`,m=`20${o}`;return n?Number.parseInt(o)<=i?c=m:Number.parseInt(o)>i&&(c=d):c=m,`${c}-${g[a]}-${s}`}},D=t=>{const n=new Date(t);if(isNaN(n.getTime()))return"";const r=n.getMonth()+1,e=n.getDate(),s=["JAN","FEB","MAR","APR","MAY","JUN","JUL","AUG","SEP","OCT","NOV","DEC"][r-1];return`${e.toString().padStart(2,"0")}${s}`},S=t=>{const n=new Date(t);if(isNaN(n.getTime()))return"";const r=n.getFullYear().toString().slice(-2),e=n.getMonth()+1,s=n.getDate(),o=["JAN","FEB","MAR","APR","MAY","JUN","JUL","AUG","SEP","OCT","NOV","DEC"][e-1];return`${s.toString().padStart(2,"0")}${o}${r}`},p=(t,n)=>{const r=parseInt(t,10),e=Math.abs(r).toString().padStart(4,"0"),s=parseInt(e.substring(0,2),10),a=parseInt(e.substring(2,4),10);return r>=0?u(n).subtract(s,"hour").subtract(a,"minute").format():u(n).add(s,"hour").add(a,"minute").format()};export{N as a,l as b,S as c,M as d,D as f,f as g,p};
