import{B as ue,dA as se,dw as c,dN as Q,e4 as F,dF as I,dM as h,f as X,D as oe,g as ie,r as de,aE as ce,dE as me,h as V,dB as E,dI as pe,e7 as be,a2 as fe,o as ve,da as Ne,j as b,m as z,W as R,l as t,p as K,aF as _,x as M,w as Y,k as S,fA as Ve,fB as he,E as j,z as q,dG as Ie,fC as ye,q as A,a7 as ge,_ as we,d as Ee,d9 as _e,dK as H,A as Se}from"./index-5ab303b6.js";import{v as J}from"./index-7a4f5690.js";const Ae=ue({id:{type:String,default:void 0},step:{type:Number,default:1},stepStrictly:Boolean,max:{type:Number,default:Number.POSITIVE_INFINITY},min:{type:Number,default:Number.NEGATIVE_INFINITY},modelValue:Number,readonly:Boolean,disabled:Boolean,size:se,controls:{type:Boolean,default:!0},controlsPosition:{type:String,default:"",values:["","right"]},valueOnClear:{type:[String,Number,null],validator:l=>l===null||c(l)||["min","max"].includes(l),default:null},name:String,label:String,placeholder:String,precision:{type:Number,validator:l=>l>=0&&l===Number.parseInt(`${l}`,10)},validateEvent:{type:Boolean,default:!0}}),Fe={[Q]:(l,P)=>P!==l,blur:l=>l instanceof FocusEvent,focus:l=>l instanceof FocusEvent,[F]:l=>c(l)||I(l),[h]:l=>c(l)||I(l)},Pe=["aria-label","onKeydown"],ke=["aria-label","onKeydown"],Be=X({name:"ElInputNumber"}),xe=X({...Be,props:Ae,emits:Fe,setup(l,{expose:P,emit:d}){const a=l,{t:O}=oe(),m=ie("input-number"),v=de(),u=ce({currentValue:a.modelValue,userInput:null}),{formItem:f}=me(),U=V(()=>c(a.modelValue)&&a.modelValue<=a.min),G=V(()=>c(a.modelValue)&&a.modelValue>=a.max),Z=V(()=>{const e=$(a.step);return E(a.precision)?Math.max($(a.modelValue),e):(e>a.precision,a.precision)}),k=V(()=>a.controls&&a.controlsPosition==="right"),W=pe(),N=be(),B=V(()=>{if(u.userInput!==null)return u.userInput;let e=u.currentValue;if(I(e))return"";if(c(e)){if(Number.isNaN(e))return"";E(a.precision)||(e=e.toFixed(a.precision))}return e}),x=(e,n)=>{if(E(n)&&(n=Z.value),n===0)return Math.round(e);let r=String(e);const s=r.indexOf(".");if(s===-1||!r.replace(".","").split("")[s+n])return e;const g=r.length;return r.charAt(g-1)==="5"&&(r=`${r.slice(0,Math.max(0,g-1))}6`),Number.parseFloat(Number(r).toFixed(n))},$=e=>{if(I(e))return 0;const n=e.toString(),r=n.indexOf(".");let s=0;return r!==-1&&(s=n.length-r-1),s},L=(e,n=1)=>c(e)?x(e+a.step*n):u.currentValue,C=()=>{if(a.readonly||N.value||G.value)return;const e=Number(B.value)||0,n=L(e);y(n),d(F,u.currentValue)},D=()=>{if(a.readonly||N.value||U.value)return;const e=Number(B.value)||0,n=L(e,-1);y(n),d(F,u.currentValue)},T=(e,n)=>{const{max:r,min:s,step:o,precision:p,stepStrictly:g,valueOnClear:w}=a;r<s&&Ee("InputNumber","min should not be greater than max.");let i=Number(e);if(I(e)||Number.isNaN(i))return null;if(e===""){if(w===null)return null;i=_e(w)?{min:s,max:r}[w]:w}return g&&(i=x(Math.round(i/o)*o,p)),E(p)||(i=x(i,p)),(i>r||i<s)&&(i=i>r?r:s,n&&d(h,i)),i},y=(e,n=!0)=>{var r;const s=u.currentValue,o=T(e);if(!n){d(h,o);return}s!==o&&(u.userInput=null,d(h,o),d(Q,o,s),a.validateEvent&&((r=f==null?void 0:f.validate)==null||r.call(f,"change").catch(p=>H())),u.currentValue=o)},ee=e=>{u.userInput=e;const n=e===""?null:Number(e);d(F,n),y(n,!1)},ne=e=>{const n=e!==""?Number(e):"";(c(n)&&!Number.isNaN(n)||e==="")&&y(n),u.userInput=null},te=()=>{var e,n;(n=(e=v.value)==null?void 0:e.focus)==null||n.call(e)},re=()=>{var e,n;(n=(e=v.value)==null?void 0:e.blur)==null||n.call(e)},ae=e=>{d("focus",e)},le=e=>{var n;d("blur",e),a.validateEvent&&((n=f==null?void 0:f.validate)==null||n.call(f,"blur").catch(r=>H()))};return fe(()=>a.modelValue,e=>{const n=T(u.userInput),r=T(e,!0);!c(n)&&(!n||n!==r)&&(u.currentValue=r,u.userInput=null)},{immediate:!0}),ve(()=>{var e;const{min:n,max:r,modelValue:s}=a,o=(e=v.value)==null?void 0:e.input;if(o.setAttribute("role","spinbutton"),Number.isFinite(r)?o.setAttribute("aria-valuemax",String(r)):o.removeAttribute("aria-valuemax"),Number.isFinite(n)?o.setAttribute("aria-valuemin",String(n)):o.removeAttribute("aria-valuemin"),o.setAttribute("aria-valuenow",u.currentValue||u.currentValue===0?String(u.currentValue):""),o.setAttribute("aria-disabled",String(N.value)),!c(s)&&s!=null){let p=Number(s);Number.isNaN(p)&&(p=null),d(h,p)}}),Ne(()=>{var e,n;const r=(e=v.value)==null?void 0:e.input;r==null||r.setAttribute("aria-valuenow",`${(n=u.currentValue)!=null?n:""}`)}),P({focus:te,blur:re}),(e,n)=>(b(),z("div",{class:K([t(m).b(),t(m).m(t(W)),t(m).is("disabled",t(N)),t(m).is("without-controls",!e.controls),t(m).is("controls-right",t(k))]),onDragstart:n[1]||(n[1]=A(()=>{},["prevent"]))},[e.controls?R((b(),z("span",{key:0,role:"button","aria-label":t(O)("el.inputNumber.decrease"),class:K([t(m).e("decrease"),t(m).is("disabled",t(U))]),onKeydown:_(D,["enter"])},[M(t(j),null,{default:Y(()=>[t(k)?(b(),S(t(Ve),{key:0})):(b(),S(t(he),{key:1}))]),_:1})],42,Pe)),[[t(J),D]]):q("v-if",!0),e.controls?R((b(),z("span",{key:1,role:"button","aria-label":t(O)("el.inputNumber.increase"),class:K([t(m).e("increase"),t(m).is("disabled",t(G))]),onKeydown:_(C,["enter"])},[M(t(j),null,{default:Y(()=>[t(k)?(b(),S(t(Ie),{key:0})):(b(),S(t(ye),{key:1}))]),_:1})],42,ke)),[[t(J),C]]):q("v-if",!0),M(t(ge),{id:e.id,ref_key:"input",ref:v,type:"number",step:e.step,"model-value":t(B),placeholder:e.placeholder,readonly:e.readonly,disabled:t(N),size:t(W),max:e.max,min:e.min,name:e.name,label:e.label,"validate-event":!1,onWheel:n[0]||(n[0]=A(()=>{},["prevent"])),onKeydown:[_(A(C,["prevent"]),["up"]),_(A(D,["prevent"]),["down"])],onBlur:le,onFocus:ae,onInput:ee,onChange:ne},null,8,["id","step","model-value","placeholder","readonly","disabled","size","max","min","name","label","onKeydown"])],34))}});var Ce=we(xe,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/input-number/src/input-number.vue"]]);const ze=Se(Ce);export{ze as E};
