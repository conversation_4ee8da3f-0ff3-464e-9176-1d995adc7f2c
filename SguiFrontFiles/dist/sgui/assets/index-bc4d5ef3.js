import{gk as Je,fW as be,gl as Gn,a as ct,b as Xe,gm as Zn,i as sn,gn as Jn,go as ft,gp as qe,gq as Xn,gr as we,dt as ln,C as k,d6 as Yn,h as x,d8 as ze,a2 as F,o as Ye,dx as Ce,g2 as un,l as c,s as Qn,r as B,dv as Ie,gs as It,gt as er,fa as tr,gu as nr,gv as rr,B as te,dw as ot,L as je,f as L,v as le,_ as ue,g as Be,F as pe,j as Z,m as Ve,p as pn,n as or,e3 as at,W as cn,gw as ar,d2 as sr,a8 as ir,gx as lr,gy as ur,x as Re,gz as fn,ge as Ge,dF as dn,k as de,w as ne,M as dt,z as _e,gA as pr,dc as Bt,Q as cr,A as gn,K as $t,J as fr,fO as Ee,X as dr,T as gr,Y as vr,e8 as mr,fX as yr,g8 as hr,fs as br,H as wr}from"./index-5ab303b6.js";import{i as Tr}from"./isUndefined-aa0326a0.js";const Q=(e,t,{checkForDefaultPrevented:n=!0}={})=>o=>{const a=e==null?void 0:e(o);if(n===!1||!a)return t==null?void 0:t(o)},Qs=e=>t=>t.pointerType==="mouse"?e(t):void 0;var Or=Je(be,"WeakMap");const st=Or;var Ar=9007199254740991;function vn(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Ar}function Er(e){return e!=null&&vn(e.length)&&!Gn(e)}var Cr=Object.prototype;function xr(e){var t=e&&e.constructor,n=typeof t=="function"&&t.prototype||Cr;return e===n}function Pr(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}var Sr="[object Arguments]";function Ft(e){return ct(e)&&Xe(e)==Sr}var mn=Object.prototype,jr=mn.hasOwnProperty,Rr=mn.propertyIsEnumerable,_r=Ft(function(){return arguments}())?Ft:function(e){return ct(e)&&jr.call(e,"callee")&&!Rr.call(e,"callee")};const kr=_r;function Mr(){return!1}var yn=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Dt=yn&&typeof module=="object"&&module&&!module.nodeType&&module,Ir=Dt&&Dt.exports===yn,Lt=Ir?be.Buffer:void 0,Br=Lt?Lt.isBuffer:void 0,$r=Br||Mr;const Fr=$r;var Dr="[object Arguments]",Lr="[object Array]",Nr="[object Boolean]",Wr="[object Date]",Hr="[object Error]",zr="[object Function]",Ur="[object Map]",Kr="[object Number]",qr="[object Object]",Vr="[object RegExp]",Gr="[object Set]",Zr="[object String]",Jr="[object WeakMap]",Xr="[object ArrayBuffer]",Yr="[object DataView]",Qr="[object Float32Array]",eo="[object Float64Array]",to="[object Int8Array]",no="[object Int16Array]",ro="[object Int32Array]",oo="[object Uint8Array]",ao="[object Uint8ClampedArray]",so="[object Uint16Array]",io="[object Uint32Array]",S={};S[Qr]=S[eo]=S[to]=S[no]=S[ro]=S[oo]=S[ao]=S[so]=S[io]=!0;S[Dr]=S[Lr]=S[Xr]=S[Nr]=S[Yr]=S[Wr]=S[Hr]=S[zr]=S[Ur]=S[Kr]=S[qr]=S[Vr]=S[Gr]=S[Zr]=S[Jr]=!1;function lo(e){return ct(e)&&vn(e.length)&&!!S[Xe(e)]}function uo(e){return function(t){return e(t)}}var hn=typeof exports=="object"&&exports&&!exports.nodeType&&exports,xe=hn&&typeof module=="object"&&module&&!module.nodeType&&module,po=xe&&xe.exports===hn,rt=po&&Zn.process,co=function(){try{var e=xe&&xe.require&&xe.require("util").types;return e||rt&&rt.binding&&rt.binding("util")}catch{}}();const Nt=co;var Wt=Nt&&Nt.isTypedArray,fo=Wt?uo(Wt):lo;const go=fo;var vo=Object.prototype,mo=vo.hasOwnProperty;function yo(e,t){var n=sn(e),r=!n&&kr(e),o=!n&&!r&&Fr(e),a=!n&&!r&&!o&&go(e),i=n||r||o||a,u=i?Pr(e.length,String):[],l=u.length;for(var s in e)(t||mo.call(e,s))&&!(i&&(s=="length"||o&&(s=="offset"||s=="parent")||a&&(s=="buffer"||s=="byteLength"||s=="byteOffset")||Jn(s,l)))&&u.push(s);return u}function ho(e,t){return function(n){return e(t(n))}}var bo=ho(Object.keys,Object);const wo=bo;var To=Object.prototype,Oo=To.hasOwnProperty;function Ao(e){if(!xr(e))return wo(e);var t=[];for(var n in Object(e))Oo.call(e,n)&&n!="constructor"&&t.push(n);return t}function Eo(e){return Er(e)?yo(e):Ao(e)}function Co(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}function xo(){this.__data__=new ft,this.size=0}function Po(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}function So(e){return this.__data__.get(e)}function jo(e){return this.__data__.has(e)}var Ro=200;function _o(e,t){var n=this.__data__;if(n instanceof ft){var r=n.__data__;if(!qe||r.length<Ro-1)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new Xn(r)}return n.set(e,t),this.size=n.size,this}function $e(e){var t=this.__data__=new ft(e);this.size=t.size}$e.prototype.clear=xo;$e.prototype.delete=Po;$e.prototype.get=So;$e.prototype.has=jo;$e.prototype.set=_o;function ko(e,t){for(var n=-1,r=e==null?0:e.length,o=0,a=[];++n<r;){var i=e[n];t(i,n,e)&&(a[o++]=i)}return a}function Mo(){return[]}var Io=Object.prototype,Bo=Io.propertyIsEnumerable,Ht=Object.getOwnPropertySymbols,$o=Ht?function(e){return e==null?[]:(e=Object(e),ko(Ht(e),function(t){return Bo.call(e,t)}))}:Mo;const Fo=$o;function Do(e,t,n){var r=t(e);return sn(e)?r:Co(r,n(e))}function ei(e){return Do(e,Eo,Fo)}var Lo=Je(be,"DataView");const it=Lo;var No=Je(be,"Promise");const lt=No;var Wo=Je(be,"Set");const ut=Wo;var zt="[object Map]",Ho="[object Object]",Ut="[object Promise]",Kt="[object Set]",qt="[object WeakMap]",Vt="[object DataView]",zo=we(it),Uo=we(qe),Ko=we(lt),qo=we(ut),Vo=we(st),se=Xe;(it&&se(new it(new ArrayBuffer(1)))!=Vt||qe&&se(new qe)!=zt||lt&&se(lt.resolve())!=Ut||ut&&se(new ut)!=Kt||st&&se(new st)!=qt)&&(se=function(e){var t=Xe(e),n=t==Ho?e.constructor:void 0,r=n?we(n):"";if(r)switch(r){case zo:return Vt;case Uo:return zt;case Ko:return Ut;case qo:return Kt;case Vo:return qt}return t});const ti=se;var Go=be.Uint8Array;const ni=Go,Zo=ln({type:k(Boolean),default:null}),Jo=ln({type:k(Function)}),bn=e=>{const t=`update:${e}`,n=`onUpdate:${e}`,r=[t],o={[e]:Zo,[n]:Jo};return{useModelToggle:({indicator:i,toggleReason:u,shouldHideWhenRouteChanges:l,shouldProceed:s,onShow:f,onHide:d})=>{const h=Yn(),{emit:v}=h,m=h.props,g=x(()=>ze(m[n])),O=x(()=>m[e]===null),p=T=>{i.value!==!0&&(i.value=!0,u&&(u.value=T),ze(f)&&f(T))},w=T=>{i.value!==!1&&(i.value=!1,u&&(u.value=T),ze(d)&&d(T))},A=T=>{if(m.disabled===!0||ze(s)&&!s())return;const C=g.value&&Ce;C&&v(t,!0),(O.value||!C)&&p(T)},y=T=>{if(m.disabled===!0||!Ce)return;const C=g.value&&Ce;C&&v(t,!1),(O.value||!C)&&w(T)},E=T=>{un(T)&&(m.disabled&&T?g.value&&v(t,!1):i.value!==T&&(T?p():w()))},P=()=>{i.value?y():A()};return F(()=>m[e],E),l&&h.appContext.config.globalProperties.$route!==void 0&&F(()=>({...h.proxy.$route}),()=>{l.value&&i.value&&y()}),Ye(()=>{E(m[e])}),{hide:y,show:A,toggle:P,hasUpdateHandler:g}},useModelToggleProps:o,useModelToggleEmits:r}};bn("modelValue");var H="top",K="bottom",q="right",z="left",gt="auto",Fe=[H,K,q,z],ge="start",ke="end",Xo="clippingParents",wn="viewport",Ae="popper",Yo="reference",Gt=Fe.reduce(function(e,t){return e.concat([t+"-"+ge,t+"-"+ke])},[]),vt=[].concat(Fe,[gt]).reduce(function(e,t){return e.concat([t,t+"-"+ge,t+"-"+ke])},[]),Qo="beforeRead",ea="read",ta="afterRead",na="beforeMain",ra="main",oa="afterMain",aa="beforeWrite",sa="write",ia="afterWrite",la=[Qo,ea,ta,na,ra,oa,aa,sa,ia];function X(e){return e?(e.nodeName||"").toLowerCase():null}function V(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function ve(e){var t=V(e).Element;return e instanceof t||e instanceof Element}function U(e){var t=V(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function mt(e){if(typeof ShadowRoot>"u")return!1;var t=V(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function ua(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var r=t.styles[n]||{},o=t.attributes[n]||{},a=t.elements[n];!U(a)||!X(a)||(Object.assign(a.style,r),Object.keys(o).forEach(function(i){var u=o[i];u===!1?a.removeAttribute(i):a.setAttribute(i,u===!0?"":u)}))})}function pa(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(r){var o=t.elements[r],a=t.attributes[r]||{},i=Object.keys(t.styles.hasOwnProperty(r)?t.styles[r]:n[r]),u=i.reduce(function(l,s){return l[s]="",l},{});!U(o)||!X(o)||(Object.assign(o.style,u),Object.keys(a).forEach(function(l){o.removeAttribute(l)}))})}}var Tn={name:"applyStyles",enabled:!0,phase:"write",fn:ua,effect:pa,requires:["computeStyles"]};function J(e){return e.split("-")[0]}var ie=Math.max,Ze=Math.min,me=Math.round;function ye(e,t){t===void 0&&(t=!1);var n=e.getBoundingClientRect(),r=1,o=1;if(U(e)&&t){var a=e.offsetHeight,i=e.offsetWidth;i>0&&(r=me(n.width)/i||1),a>0&&(o=me(n.height)/a||1)}return{width:n.width/r,height:n.height/o,top:n.top/o,right:n.right/r,bottom:n.bottom/o,left:n.left/r,x:n.left/r,y:n.top/o}}function yt(e){var t=ye(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function On(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&mt(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function ee(e){return V(e).getComputedStyle(e)}function ca(e){return["table","td","th"].indexOf(X(e))>=0}function re(e){return((ve(e)?e.ownerDocument:e.document)||window.document).documentElement}function Qe(e){return X(e)==="html"?e:e.assignedSlot||e.parentNode||(mt(e)?e.host:null)||re(e)}function Zt(e){return!U(e)||ee(e).position==="fixed"?null:e.offsetParent}function fa(e){var t=navigator.userAgent.toLowerCase().indexOf("firefox")!==-1,n=navigator.userAgent.indexOf("Trident")!==-1;if(n&&U(e)){var r=ee(e);if(r.position==="fixed")return null}var o=Qe(e);for(mt(o)&&(o=o.host);U(o)&&["html","body"].indexOf(X(o))<0;){var a=ee(o);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||t&&a.willChange==="filter"||t&&a.filter&&a.filter!=="none")return o;o=o.parentNode}return null}function De(e){for(var t=V(e),n=Zt(e);n&&ca(n)&&ee(n).position==="static";)n=Zt(n);return n&&(X(n)==="html"||X(n)==="body"&&ee(n).position==="static")?t:n||fa(e)||t}function ht(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Pe(e,t,n){return ie(e,Ze(t,n))}function da(e,t,n){var r=Pe(e,t,n);return r>n?n:r}function An(){return{top:0,right:0,bottom:0,left:0}}function En(e){return Object.assign({},An(),e)}function Cn(e,t){return t.reduce(function(n,r){return n[r]=e,n},{})}var ga=function(e,t){return e=typeof e=="function"?e(Object.assign({},t.rects,{placement:t.placement})):e,En(typeof e!="number"?e:Cn(e,Fe))};function va(e){var t,n=e.state,r=e.name,o=e.options,a=n.elements.arrow,i=n.modifiersData.popperOffsets,u=J(n.placement),l=ht(u),s=[z,q].indexOf(u)>=0,f=s?"height":"width";if(!(!a||!i)){var d=ga(o.padding,n),h=yt(a),v=l==="y"?H:z,m=l==="y"?K:q,g=n.rects.reference[f]+n.rects.reference[l]-i[l]-n.rects.popper[f],O=i[l]-n.rects.reference[l],p=De(a),w=p?l==="y"?p.clientHeight||0:p.clientWidth||0:0,A=g/2-O/2,y=d[v],E=w-h[f]-d[m],P=w/2-h[f]/2+A,T=Pe(y,P,E),C=l;n.modifiersData[r]=(t={},t[C]=T,t.centerOffset=T-P,t)}}function ma(e){var t=e.state,n=e.options,r=n.element,o=r===void 0?"[data-popper-arrow]":r;o!=null&&(typeof o=="string"&&(o=t.elements.popper.querySelector(o),!o)||!On(t.elements.popper,o)||(t.elements.arrow=o))}var ya={name:"arrow",enabled:!0,phase:"main",fn:va,effect:ma,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function he(e){return e.split("-")[1]}var ha={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ba(e){var t=e.x,n=e.y,r=window,o=r.devicePixelRatio||1;return{x:me(t*o)/o||0,y:me(n*o)/o||0}}function Jt(e){var t,n=e.popper,r=e.popperRect,o=e.placement,a=e.variation,i=e.offsets,u=e.position,l=e.gpuAcceleration,s=e.adaptive,f=e.roundOffsets,d=e.isFixed,h=i.x,v=h===void 0?0:h,m=i.y,g=m===void 0?0:m,O=typeof f=="function"?f({x:v,y:g}):{x:v,y:g};v=O.x,g=O.y;var p=i.hasOwnProperty("x"),w=i.hasOwnProperty("y"),A=z,y=H,E=window;if(s){var P=De(n),T="clientHeight",C="clientWidth";if(P===V(n)&&(P=re(n),ee(P).position!=="static"&&u==="absolute"&&(T="scrollHeight",C="scrollWidth")),P=P,o===H||(o===z||o===q)&&a===ke){y=K;var I=d&&P===E&&E.visualViewport?E.visualViewport.height:P[T];g-=I-r.height,g*=l?1:-1}if(o===z||(o===H||o===K)&&a===ke){A=q;var j=d&&P===E&&E.visualViewport?E.visualViewport.width:P[C];v-=j-r.width,v*=l?1:-1}}var _=Object.assign({position:u},s&&ha),D=f===!0?ba({x:v,y:g}):{x:v,y:g};if(v=D.x,g=D.y,l){var R;return Object.assign({},_,(R={},R[y]=w?"0":"",R[A]=p?"0":"",R.transform=(E.devicePixelRatio||1)<=1?"translate("+v+"px, "+g+"px)":"translate3d("+v+"px, "+g+"px, 0)",R))}return Object.assign({},_,(t={},t[y]=w?g+"px":"",t[A]=p?v+"px":"",t.transform="",t))}function wa(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=r===void 0?!0:r,a=n.adaptive,i=a===void 0?!0:a,u=n.roundOffsets,l=u===void 0?!0:u,s={placement:J(t.placement),variation:he(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Jt(Object.assign({},s,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:l})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Jt(Object.assign({},s,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}var xn={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:wa,data:{}},Ue={passive:!0};function Ta(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,a=o===void 0?!0:o,i=r.resize,u=i===void 0?!0:i,l=V(t.elements.popper),s=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&s.forEach(function(f){f.addEventListener("scroll",n.update,Ue)}),u&&l.addEventListener("resize",n.update,Ue),function(){a&&s.forEach(function(f){f.removeEventListener("scroll",n.update,Ue)}),u&&l.removeEventListener("resize",n.update,Ue)}}var Pn={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Ta,data:{}},Oa={left:"right",right:"left",bottom:"top",top:"bottom"};function Ke(e){return e.replace(/left|right|bottom|top/g,function(t){return Oa[t]})}var Aa={start:"end",end:"start"};function Xt(e){return e.replace(/start|end/g,function(t){return Aa[t]})}function bt(e){var t=V(e),n=t.pageXOffset,r=t.pageYOffset;return{scrollLeft:n,scrollTop:r}}function wt(e){return ye(re(e)).left+bt(e).scrollLeft}function Ea(e){var t=V(e),n=re(e),r=t.visualViewport,o=n.clientWidth,a=n.clientHeight,i=0,u=0;return r&&(o=r.width,a=r.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(i=r.offsetLeft,u=r.offsetTop)),{width:o,height:a,x:i+wt(e),y:u}}function Ca(e){var t,n=re(e),r=bt(e),o=(t=e.ownerDocument)==null?void 0:t.body,a=ie(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),i=ie(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),u=-r.scrollLeft+wt(e),l=-r.scrollTop;return ee(o||n).direction==="rtl"&&(u+=ie(n.clientWidth,o?o.clientWidth:0)-a),{width:a,height:i,x:u,y:l}}function Tt(e){var t=ee(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function Sn(e){return["html","body","#document"].indexOf(X(e))>=0?e.ownerDocument.body:U(e)&&Tt(e)?e:Sn(Qe(e))}function Se(e,t){var n;t===void 0&&(t=[]);var r=Sn(e),o=r===((n=e.ownerDocument)==null?void 0:n.body),a=V(r),i=o?[a].concat(a.visualViewport||[],Tt(r)?r:[]):r,u=t.concat(i);return o?u:u.concat(Se(Qe(i)))}function pt(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function xa(e){var t=ye(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function Yt(e,t){return t===wn?pt(Ea(e)):ve(t)?xa(t):pt(Ca(re(e)))}function Pa(e){var t=Se(Qe(e)),n=["absolute","fixed"].indexOf(ee(e).position)>=0,r=n&&U(e)?De(e):e;return ve(r)?t.filter(function(o){return ve(o)&&On(o,r)&&X(o)!=="body"}):[]}function Sa(e,t,n){var r=t==="clippingParents"?Pa(e):[].concat(t),o=[].concat(r,[n]),a=o[0],i=o.reduce(function(u,l){var s=Yt(e,l);return u.top=ie(s.top,u.top),u.right=Ze(s.right,u.right),u.bottom=Ze(s.bottom,u.bottom),u.left=ie(s.left,u.left),u},Yt(e,a));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function jn(e){var t=e.reference,n=e.element,r=e.placement,o=r?J(r):null,a=r?he(r):null,i=t.x+t.width/2-n.width/2,u=t.y+t.height/2-n.height/2,l;switch(o){case H:l={x:i,y:t.y-n.height};break;case K:l={x:i,y:t.y+t.height};break;case q:l={x:t.x+t.width,y:u};break;case z:l={x:t.x-n.width,y:u};break;default:l={x:t.x,y:t.y}}var s=o?ht(o):null;if(s!=null){var f=s==="y"?"height":"width";switch(a){case ge:l[s]=l[s]-(t[f]/2-n[f]/2);break;case ke:l[s]=l[s]+(t[f]/2-n[f]/2);break}}return l}function Me(e,t){t===void 0&&(t={});var n=t,r=n.placement,o=r===void 0?e.placement:r,a=n.boundary,i=a===void 0?Xo:a,u=n.rootBoundary,l=u===void 0?wn:u,s=n.elementContext,f=s===void 0?Ae:s,d=n.altBoundary,h=d===void 0?!1:d,v=n.padding,m=v===void 0?0:v,g=En(typeof m!="number"?m:Cn(m,Fe)),O=f===Ae?Yo:Ae,p=e.rects.popper,w=e.elements[h?O:f],A=Sa(ve(w)?w:w.contextElement||re(e.elements.popper),i,l),y=ye(e.elements.reference),E=jn({reference:y,element:p,strategy:"absolute",placement:o}),P=pt(Object.assign({},p,E)),T=f===Ae?P:y,C={top:A.top-T.top+g.top,bottom:T.bottom-A.bottom+g.bottom,left:A.left-T.left+g.left,right:T.right-A.right+g.right},I=e.modifiersData.offset;if(f===Ae&&I){var j=I[o];Object.keys(C).forEach(function(_){var D=[q,K].indexOf(_)>=0?1:-1,R=[H,K].indexOf(_)>=0?"y":"x";C[_]+=j[R]*D})}return C}function ja(e,t){t===void 0&&(t={});var n=t,r=n.placement,o=n.boundary,a=n.rootBoundary,i=n.padding,u=n.flipVariations,l=n.allowedAutoPlacements,s=l===void 0?vt:l,f=he(r),d=f?u?Gt:Gt.filter(function(m){return he(m)===f}):Fe,h=d.filter(function(m){return s.indexOf(m)>=0});h.length===0&&(h=d);var v=h.reduce(function(m,g){return m[g]=Me(e,{placement:g,boundary:o,rootBoundary:a,padding:i})[J(g)],m},{});return Object.keys(v).sort(function(m,g){return v[m]-v[g]})}function Ra(e){if(J(e)===gt)return[];var t=Ke(e);return[Xt(e),t,Xt(t)]}function _a(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,a=o===void 0?!0:o,i=n.altAxis,u=i===void 0?!0:i,l=n.fallbackPlacements,s=n.padding,f=n.boundary,d=n.rootBoundary,h=n.altBoundary,v=n.flipVariations,m=v===void 0?!0:v,g=n.allowedAutoPlacements,O=t.options.placement,p=J(O),w=p===O,A=l||(w||!m?[Ke(O)]:Ra(O)),y=[O].concat(A).reduce(function(oe,Y){return oe.concat(J(Y)===gt?ja(t,{placement:Y,boundary:f,rootBoundary:d,padding:s,flipVariations:m,allowedAutoPlacements:g}):Y)},[]),E=t.rects.reference,P=t.rects.popper,T=new Map,C=!0,I=y[0],j=0;j<y.length;j++){var _=y[j],D=J(_),R=he(_)===ge,N=[H,K].indexOf(D)>=0,$=N?"width":"height",M=Me(t,{placement:_,boundary:f,rootBoundary:d,altBoundary:h,padding:s}),b=N?R?q:z:R?K:H;E[$]>P[$]&&(b=Ke(b));var W=Ke(b),G=[];if(a&&G.push(M[D]<=0),u&&G.push(M[b]<=0,M[W]<=0),G.every(function(oe){return oe})){I=_,C=!1;break}T.set(_,G)}if(C)for(var Le=m?3:1,et=function(oe){var Y=y.find(function(We){var Oe=T.get(We);if(Oe)return Oe.slice(0,oe).every(function(ce){return ce})});if(Y)return I=Y,"break"},Te=Le;Te>0;Te--){var Ne=et(Te);if(Ne==="break")break}t.placement!==I&&(t.modifiersData[r]._skip=!0,t.placement=I,t.reset=!0)}}var ka={name:"flip",enabled:!0,phase:"main",fn:_a,requiresIfExists:["offset"],data:{_skip:!1}};function Qt(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function en(e){return[H,q,K,z].some(function(t){return e[t]>=0})}function Ma(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,a=t.modifiersData.preventOverflow,i=Me(t,{elementContext:"reference"}),u=Me(t,{altBoundary:!0}),l=Qt(i,r),s=Qt(u,o,a),f=en(l),d=en(s);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:s,isReferenceHidden:f,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":d})}var Ia={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Ma};function Ba(e,t,n){var r=J(e),o=[z,H].indexOf(r)>=0?-1:1,a=typeof n=="function"?n(Object.assign({},t,{placement:e})):n,i=a[0],u=a[1];return i=i||0,u=(u||0)*o,[z,q].indexOf(r)>=0?{x:u,y:i}:{x:i,y:u}}function $a(e){var t=e.state,n=e.options,r=e.name,o=n.offset,a=o===void 0?[0,0]:o,i=vt.reduce(function(f,d){return f[d]=Ba(d,t.rects,a),f},{}),u=i[t.placement],l=u.x,s=u.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=s),t.modifiersData[r]=i}var Fa={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:$a};function Da(e){var t=e.state,n=e.name;t.modifiersData[n]=jn({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}var Rn={name:"popperOffsets",enabled:!0,phase:"read",fn:Da,data:{}};function La(e){return e==="x"?"y":"x"}function Na(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,a=o===void 0?!0:o,i=n.altAxis,u=i===void 0?!1:i,l=n.boundary,s=n.rootBoundary,f=n.altBoundary,d=n.padding,h=n.tether,v=h===void 0?!0:h,m=n.tetherOffset,g=m===void 0?0:m,O=Me(t,{boundary:l,rootBoundary:s,padding:d,altBoundary:f}),p=J(t.placement),w=he(t.placement),A=!w,y=ht(p),E=La(y),P=t.modifiersData.popperOffsets,T=t.rects.reference,C=t.rects.popper,I=typeof g=="function"?g(Object.assign({},t.rects,{placement:t.placement})):g,j=typeof I=="number"?{mainAxis:I,altAxis:I}:Object.assign({mainAxis:0,altAxis:0},I),_=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,D={x:0,y:0};if(P){if(a){var R,N=y==="y"?H:z,$=y==="y"?K:q,M=y==="y"?"height":"width",b=P[y],W=b+O[N],G=b-O[$],Le=v?-C[M]/2:0,et=w===ge?T[M]:C[M],Te=w===ge?-C[M]:-T[M],Ne=t.elements.arrow,oe=v&&Ne?yt(Ne):{width:0,height:0},Y=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:An(),We=Y[N],Oe=Y[$],ce=Pe(0,T[M],oe[M]),Wn=A?T[M]/2-Le-ce-We-j.mainAxis:et-ce-We-j.mainAxis,Hn=A?-T[M]/2+Le+ce+Oe+j.mainAxis:Te+ce+Oe+j.mainAxis,tt=t.elements.arrow&&De(t.elements.arrow),zn=tt?y==="y"?tt.clientTop||0:tt.clientLeft||0:0,Ct=(R=_==null?void 0:_[y])!=null?R:0,Un=b+Wn-Ct-zn,Kn=b+Hn-Ct,xt=Pe(v?Ze(W,Un):W,b,v?ie(G,Kn):G);P[y]=xt,D[y]=xt-b}if(u){var Pt,qn=y==="x"?H:z,Vn=y==="x"?K:q,ae=P[E],He=E==="y"?"height":"width",St=ae+O[qn],jt=ae-O[Vn],nt=[H,z].indexOf(p)!==-1,Rt=(Pt=_==null?void 0:_[E])!=null?Pt:0,_t=nt?St:ae-T[He]-C[He]-Rt+j.altAxis,kt=nt?ae+T[He]+C[He]-Rt-j.altAxis:jt,Mt=v&&nt?da(_t,ae,kt):Pe(v?_t:St,ae,v?kt:jt);P[E]=Mt,D[E]=Mt-ae}t.modifiersData[r]=D}}var Wa={name:"preventOverflow",enabled:!0,phase:"main",fn:Na,requiresIfExists:["offset"]};function Ha(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function za(e){return e===V(e)||!U(e)?bt(e):Ha(e)}function Ua(e){var t=e.getBoundingClientRect(),n=me(t.width)/e.offsetWidth||1,r=me(t.height)/e.offsetHeight||1;return n!==1||r!==1}function Ka(e,t,n){n===void 0&&(n=!1);var r=U(t),o=U(t)&&Ua(t),a=re(t),i=ye(e,o),u={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(r||!r&&!n)&&((X(t)!=="body"||Tt(a))&&(u=za(t)),U(t)?(l=ye(t,!0),l.x+=t.clientLeft,l.y+=t.clientTop):a&&(l.x=wt(a))),{x:i.left+u.scrollLeft-l.x,y:i.top+u.scrollTop-l.y,width:i.width,height:i.height}}function qa(e){var t=new Map,n=new Set,r=[];e.forEach(function(a){t.set(a.name,a)});function o(a){n.add(a.name);var i=[].concat(a.requires||[],a.requiresIfExists||[]);i.forEach(function(u){if(!n.has(u)){var l=t.get(u);l&&o(l)}}),r.push(a)}return e.forEach(function(a){n.has(a.name)||o(a)}),r}function Va(e){var t=qa(e);return la.reduce(function(n,r){return n.concat(t.filter(function(o){return o.phase===r}))},[])}function Ga(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function Za(e){var t=e.reduce(function(n,r){var o=n[r.name];return n[r.name]=o?Object.assign({},o,r,{options:Object.assign({},o.options,r.options),data:Object.assign({},o.data,r.data)}):r,n},{});return Object.keys(t).map(function(n){return t[n]})}var tn={placement:"bottom",modifiers:[],strategy:"absolute"};function nn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(r){return!(r&&typeof r.getBoundingClientRect=="function")})}function Ot(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,r=n===void 0?[]:n,o=t.defaultOptions,a=o===void 0?tn:o;return function(i,u,l){l===void 0&&(l=a);var s={placement:"bottom",orderedModifiers:[],options:Object.assign({},tn,a),modifiersData:{},elements:{reference:i,popper:u},attributes:{},styles:{}},f=[],d=!1,h={state:s,setOptions:function(g){var O=typeof g=="function"?g(s.options):g;m(),s.options=Object.assign({},a,s.options,O),s.scrollParents={reference:ve(i)?Se(i):i.contextElement?Se(i.contextElement):[],popper:Se(u)};var p=Va(Za([].concat(r,s.options.modifiers)));return s.orderedModifiers=p.filter(function(w){return w.enabled}),v(),h.update()},forceUpdate:function(){if(!d){var g=s.elements,O=g.reference,p=g.popper;if(nn(O,p)){s.rects={reference:Ka(O,De(p),s.options.strategy==="fixed"),popper:yt(p)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach(function(C){return s.modifiersData[C.name]=Object.assign({},C.data)});for(var w=0;w<s.orderedModifiers.length;w++){if(s.reset===!0){s.reset=!1,w=-1;continue}var A=s.orderedModifiers[w],y=A.fn,E=A.options,P=E===void 0?{}:E,T=A.name;typeof y=="function"&&(s=y({state:s,options:P,name:T,instance:h})||s)}}}},update:Ga(function(){return new Promise(function(g){h.forceUpdate(),g(s)})}),destroy:function(){m(),d=!0}};if(!nn(i,u))return h;h.setOptions(l).then(function(g){!d&&l.onFirstUpdate&&l.onFirstUpdate(g)});function v(){s.orderedModifiers.forEach(function(g){var O=g.name,p=g.options,w=p===void 0?{}:p,A=g.effect;if(typeof A=="function"){var y=A({state:s,name:O,instance:h,options:w}),E=function(){};f.push(y||E)}})}function m(){f.forEach(function(g){return g()}),f=[]}return h}}Ot();var Ja=[Pn,Rn,xn,Tn];Ot({defaultModifiers:Ja});var Xa=[Pn,Rn,xn,Tn,Fa,ka,Wa,ya,Ia],Ya=Ot({defaultModifiers:Xa});const Qa=(e,t,n={})=>{const r={name:"updateState",enabled:!0,phase:"write",fn:({state:l})=>{const s=es(l);Object.assign(i.value,s)},requires:["computeStyles"]},o=x(()=>{const{onFirstUpdate:l,placement:s,strategy:f,modifiers:d}=c(n);return{onFirstUpdate:l,placement:s||"bottom",strategy:f||"absolute",modifiers:[...d||[],r,{name:"applyStyles",enabled:!1}]}}),a=Qn(),i=B({styles:{popper:{position:c(o).strategy,left:"0",top:"0"},arrow:{position:"absolute"}},attributes:{}}),u=()=>{a.value&&(a.value.destroy(),a.value=void 0)};return F(o,l=>{const s=c(a);s&&s.setOptions(l)},{deep:!0}),F([e,t],([l,s])=>{u(),!(!l||!s)&&(a.value=Ya(l,s,c(o)))}),Ie(()=>{u()}),{state:x(()=>{var l;return{...((l=c(a))==null?void 0:l.state)||{}}}),styles:x(()=>c(i).styles),attributes:x(()=>c(i).attributes),update:()=>{var l;return(l=c(a))==null?void 0:l.update()},forceUpdate:()=>{var l;return(l=c(a))==null?void 0:l.forceUpdate()},instanceRef:x(()=>c(a))}};function es(e){const t=Object.keys(e.elements),n=It(t.map(o=>[o,e.styles[o]||{}])),r=It(t.map(o=>[o,e.attributes[o]]));return{styles:n,attributes:r}}function rn(){let e;const t=(r,o)=>{n(),e=window.setTimeout(r,o)},n=()=>window.clearTimeout(e);return er(()=>n()),{registerTimeout:t,cancelTimeout:n}}let on;const _n=()=>{const e=nr(),t=rr(),n=x(()=>`${e.value}-popper-container-${t.prefix}`),r=x(()=>`#${n.value}`);return{id:n,selector:r}},ts=e=>{const t=document.createElement("div");return t.id=e,document.body.appendChild(t),t},ns=()=>{const{id:e,selector:t}=_n();return tr(()=>{Ce&&!on&&!document.body.querySelector(t.value)&&(on=ts(e.value))}),{id:e,selector:t}},rs=te({showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0}}),os=({showAfter:e,hideAfter:t,autoClose:n,open:r,close:o})=>{const{registerTimeout:a}=rn(),{registerTimeout:i,cancelTimeout:u}=rn();return{onOpen:f=>{a(()=>{r(f);const d=c(n);ot(d)&&d>0&&i(()=>{o(f)},d)},c(e))},onClose:f=>{u(),a(()=>{o(f)},c(t))}}},kn=Symbol("elForwardRef"),as=e=>{je(kn,{setForwardRef:n=>{e.value=n}})},ss=e=>({mounted(t){e(t)},updated(t){e(t)},unmounted(){e(null)}}),At=Symbol("popper"),Mn=Symbol("popperContent"),is=["dialog","grid","group","listbox","menu","navigation","tooltip","tree"],In=te({role:{type:String,values:is,default:"tooltip"}}),ls=L({name:"ElPopper",inheritAttrs:!1}),us=L({...ls,props:In,setup(e,{expose:t}){const n=e,r=B(),o=B(),a=B(),i=B(),u=x(()=>n.role),l={triggerRef:r,popperInstanceRef:o,contentRef:a,referenceRef:i,role:u};return t(l),je(At,l),(s,f)=>le(s.$slots,"default")}});var ps=ue(us,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/popper/src/popper.vue"]]);const Bn=te({arrowOffset:{type:Number,default:5}}),cs=L({name:"ElPopperArrow",inheritAttrs:!1}),fs=L({...cs,props:Bn,setup(e,{expose:t}){const n=e,r=Be("popper"),{arrowOffset:o,arrowRef:a,arrowStyle:i}=pe(Mn,void 0);return F(()=>n.arrowOffset,u=>{o.value=u}),Ie(()=>{a.value=void 0}),t({arrowRef:a}),(u,l)=>(Z(),Ve("span",{ref_key:"arrowRef",ref:a,class:pn(c(r).e("arrow")),style:or(c(i)),"data-popper-arrow":""},null,6))}});var ds=ue(fs,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/popper/src/arrow.vue"]]);const gs="ElOnlyChild",vs=L({name:gs,setup(e,{slots:t,attrs:n}){var r;const o=pe(kn),a=ss((r=o==null?void 0:o.setForwardRef)!=null?r:at);return()=>{var i;const u=(i=t.default)==null?void 0:i.call(t,n);if(!u||u.length>1)return null;const l=$n(u);return l?cn(ar(l,n),[[a]]):null}}});function $n(e){if(!e)return null;const t=e;for(const n of t){if(sr(n))switch(n.type){case ur:continue;case lr:case"svg":return an(n);case ir:return $n(n.children);default:return n}return an(n)}return null}function an(e){const t=Be("only-child");return Re("span",{class:t.e("content")},[e])}const Fn=te({virtualRef:{type:k(Object)},virtualTriggering:Boolean,onMouseenter:{type:k(Function)},onMouseleave:{type:k(Function)},onClick:{type:k(Function)},onKeydown:{type:k(Function)},onFocus:{type:k(Function)},onBlur:{type:k(Function)},onContextmenu:{type:k(Function)},id:String,open:Boolean}),ms=L({name:"ElPopperTrigger",inheritAttrs:!1}),ys=L({...ms,props:Fn,setup(e,{expose:t}){const n=e,{role:r,triggerRef:o}=pe(At,void 0);as(o);const a=x(()=>u.value?n.id:void 0),i=x(()=>{if(r&&r.value==="tooltip")return n.open&&n.id?n.id:void 0}),u=x(()=>{if(r&&r.value!=="tooltip")return r.value}),l=x(()=>u.value?`${n.open}`:void 0);let s;return Ye(()=>{F(()=>n.virtualRef,f=>{f&&(o.value=fn(f))},{immediate:!0}),F(o,(f,d)=>{s==null||s(),s=void 0,Ge(f)&&(["onMouseenter","onMouseleave","onClick","onKeydown","onFocus","onBlur","onContextmenu"].forEach(h=>{var v;const m=n[h];m&&(f.addEventListener(h.slice(2).toLowerCase(),m),(v=d==null?void 0:d.removeEventListener)==null||v.call(d,h.slice(2).toLowerCase(),m))}),s=F([a,i,u,l],h=>{["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach((v,m)=>{dn(h[m])?f.removeAttribute(v):f.setAttribute(v,h[m])})},{immediate:!0})),Ge(d)&&["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach(h=>d.removeAttribute(h))},{immediate:!0})}),Ie(()=>{s==null||s(),s=void 0}),t({triggerRef:o}),(f,d)=>f.virtualTriggering?_e("v-if",!0):(Z(),de(c(vs),dt({key:0},f.$attrs,{"aria-controls":c(a),"aria-describedby":c(i),"aria-expanded":c(l),"aria-haspopup":c(u)}),{default:ne(()=>[le(f.$slots,"default")]),_:3},16,["aria-controls","aria-describedby","aria-expanded","aria-haspopup"]))}});var hs=ue(ys,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/popper/src/trigger.vue"]]);const bs=["fixed","absolute"],ws=te({boundariesPadding:{type:Number,default:0},fallbackPlacements:{type:k(Array),default:void 0},gpuAcceleration:{type:Boolean,default:!0},offset:{type:Number,default:12},placement:{type:String,values:vt,default:"bottom"},popperOptions:{type:k(Object),default:()=>({})},strategy:{type:String,values:bs,default:"absolute"}}),Dn=te({...ws,id:String,style:{type:k([String,Array,Object])},className:{type:k([String,Array,Object])},effect:{type:String,default:"dark"},visible:Boolean,enterable:{type:Boolean,default:!0},pure:Boolean,focusOnShow:{type:Boolean,default:!1},trapping:{type:Boolean,default:!1},popperClass:{type:k([String,Array,Object])},popperStyle:{type:k([String,Array,Object])},referenceEl:{type:k(Object)},triggerTargetEl:{type:k(Object)},stopPopperMouseEvent:{type:Boolean,default:!0},ariaLabel:{type:String,default:void 0},virtualTriggering:Boolean,zIndex:Number}),Ts={mouseenter:e=>e instanceof MouseEvent,mouseleave:e=>e instanceof MouseEvent,focus:()=>!0,blur:()=>!0,close:()=>!0},Os=(e,t=[])=>{const{placement:n,strategy:r,popperOptions:o}=e,a={placement:n,strategy:r,...o,modifiers:[...Es(e),...t]};return Cs(a,o==null?void 0:o.modifiers),a},As=e=>{if(Ce)return fn(e)};function Es(e){const{offset:t,gpuAcceleration:n,fallbackPlacements:r}=e;return[{name:"offset",options:{offset:[0,t??12]}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5,fallbackPlacements:r}},{name:"computeStyles",options:{gpuAcceleration:n}}]}function Cs(e,t){t&&(e.modifiers=[...e.modifiers,...t??[]])}const xs=0,Ps=e=>{const{popperInstanceRef:t,contentRef:n,triggerRef:r,role:o}=pe(At,void 0),a=B(),i=B(),u=x(()=>({name:"eventListeners",enabled:!!e.visible})),l=x(()=>{var p;const w=c(a),A=(p=c(i))!=null?p:xs;return{name:"arrow",enabled:!Tr(w),options:{element:w,padding:A}}}),s=x(()=>({onFirstUpdate:()=>{m()},...Os(e,[c(l),c(u)])})),f=x(()=>As(e.referenceEl)||c(r)),{attributes:d,state:h,styles:v,update:m,forceUpdate:g,instanceRef:O}=Qa(f,n,s);return F(O,p=>t.value=p),Ye(()=>{F(()=>{var p;return(p=c(f))==null?void 0:p.getBoundingClientRect()},()=>{m()})}),{attributes:d,arrowRef:a,contentRef:n,instanceRef:O,state:h,styles:v,role:o,forceUpdate:g,update:m}},Ss=(e,{attributes:t,styles:n,role:r})=>{const{nextZIndex:o}=pr(),a=Be("popper"),i=x(()=>c(t).popper),u=B(ot(e.zIndex)?e.zIndex:o()),l=x(()=>[a.b(),a.is("pure",e.pure),a.is(e.effect),e.popperClass]),s=x(()=>[{zIndex:c(u)},c(n).popper,e.popperStyle||{}]),f=x(()=>r.value==="dialog"?"false":void 0),d=x(()=>c(n).arrow||{});return{ariaModal:f,arrowStyle:d,contentAttrs:i,contentClass:l,contentStyle:s,contentZIndex:u,updateZIndex:()=>{u.value=ot(e.zIndex)?e.zIndex:o()}}},js=(e,t)=>{const n=B(!1),r=B();return{focusStartRef:r,trapped:n,onFocusAfterReleased:s=>{var f;((f=s.detail)==null?void 0:f.focusReason)!=="pointer"&&(r.value="first",t("blur"))},onFocusAfterTrapped:()=>{t("focus")},onFocusInTrap:s=>{e.visible&&!n.value&&(s.target&&(r.value=s.target),n.value=!0)},onFocusoutPrevented:s=>{e.trapping||(s.detail.focusReason==="pointer"&&s.preventDefault(),n.value=!1)},onReleaseRequested:()=>{n.value=!1,t("close")}}},Rs=L({name:"ElPopperContent"}),_s=L({...Rs,props:Dn,emits:Ts,setup(e,{expose:t,emit:n}){const r=e,{focusStartRef:o,trapped:a,onFocusAfterReleased:i,onFocusAfterTrapped:u,onFocusInTrap:l,onFocusoutPrevented:s,onReleaseRequested:f}=js(r,n),{attributes:d,arrowRef:h,contentRef:v,styles:m,instanceRef:g,role:O,update:p}=Ps(r),{ariaModal:w,arrowStyle:A,contentAttrs:y,contentClass:E,contentStyle:P,updateZIndex:T}=Ss(r,{styles:m,attributes:d,role:O}),C=pe(Bt,void 0),I=B();je(Mn,{arrowStyle:A,arrowRef:h,arrowOffset:I}),C&&(C.addInputId||C.removeInputId)&&je(Bt,{...C,addInputId:at,removeInputId:at});let j;const _=(R=!0)=>{p(),R&&T()},D=()=>{_(!1),r.visible&&r.focusOnShow?a.value=!0:r.visible===!1&&(a.value=!1)};return Ye(()=>{F(()=>r.triggerTargetEl,(R,N)=>{j==null||j(),j=void 0;const $=c(R||v.value),M=c(N||v.value);Ge($)&&(j=F([O,()=>r.ariaLabel,w,()=>r.id],b=>{["role","aria-label","aria-modal","id"].forEach((W,G)=>{dn(b[G])?$.removeAttribute(W):$.setAttribute(W,b[G])})},{immediate:!0})),M!==$&&Ge(M)&&["role","aria-label","aria-modal","id"].forEach(b=>{M.removeAttribute(b)})},{immediate:!0}),F(()=>r.visible,D,{immediate:!0})}),Ie(()=>{j==null||j(),j=void 0}),t({popperContentRef:v,popperInstanceRef:g,updatePopper:_,contentStyle:P}),(R,N)=>(Z(),Ve("div",dt({ref_key:"contentRef",ref:v},c(y),{style:c(P),class:c(E),tabindex:"-1",onMouseenter:N[0]||(N[0]=$=>R.$emit("mouseenter",$)),onMouseleave:N[1]||(N[1]=$=>R.$emit("mouseleave",$))}),[Re(c(cr),{trapped:c(a),"trap-on-focus-in":!0,"focus-trap-el":c(v),"focus-start-el":c(o),onFocusAfterTrapped:c(u),onFocusAfterReleased:c(i),onFocusin:c(l),onFocusoutPrevented:c(s),onReleaseRequested:c(f)},{default:ne(()=>[le(R.$slots,"default")]),_:3},8,["trapped","focus-trap-el","focus-start-el","onFocusAfterTrapped","onFocusAfterReleased","onFocusin","onFocusoutPrevented","onReleaseRequested"])],16))}});var ks=ue(_s,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/popper/src/content.vue"]]);const Ms=gn(ps),Et=Symbol("elTooltip"),Ln=te({...rs,...Dn,appendTo:{type:k([String,Object])},content:{type:String,default:""},rawContent:{type:Boolean,default:!1},persistent:Boolean,ariaLabel:String,visible:{type:k(Boolean),default:null},transition:String,teleported:{type:Boolean,default:!0},disabled:Boolean}),Nn=te({...Fn,disabled:Boolean,trigger:{type:k([String,Array]),default:"hover"},triggerKeys:{type:k(Array),default:()=>[$t.enter,$t.space]}}),{useModelToggleProps:Is,useModelToggleEmits:Bs,useModelToggle:$s}=bn("visible"),Fs=te({...In,...Is,...Ln,...Nn,...Bn,showArrow:{type:Boolean,default:!0}}),Ds=[...Bs,"before-show","before-hide","show","hide","open","close"],Ls=(e,t)=>fr(e)?e.includes(t):e===t,fe=(e,t,n)=>r=>{Ls(c(e),t)&&n(r)},Ns=L({name:"ElTooltipTrigger"}),Ws=L({...Ns,props:Nn,setup(e,{expose:t}){const n=e,r=Be("tooltip"),{controlled:o,id:a,open:i,onOpen:u,onClose:l,onToggle:s}=pe(Et,void 0),f=B(null),d=()=>{if(c(o)||n.disabled)return!0},h=Ee(n,"trigger"),v=Q(d,fe(h,"hover",u)),m=Q(d,fe(h,"hover",l)),g=Q(d,fe(h,"click",y=>{y.button===0&&s(y)})),O=Q(d,fe(h,"focus",u)),p=Q(d,fe(h,"focus",l)),w=Q(d,fe(h,"contextmenu",y=>{y.preventDefault(),s(y)})),A=Q(d,y=>{const{code:E}=y;n.triggerKeys.includes(E)&&(y.preventDefault(),s(y))});return t({triggerRef:f}),(y,E)=>(Z(),de(c(hs),{id:c(a),"virtual-ref":y.virtualRef,open:c(i),"virtual-triggering":y.virtualTriggering,class:pn(c(r).e("trigger")),onBlur:c(p),onClick:c(g),onContextmenu:c(w),onFocus:c(O),onMouseenter:c(v),onMouseleave:c(m),onKeydown:c(A)},{default:ne(()=>[le(y.$slots,"default")]),_:3},8,["id","virtual-ref","open","virtual-triggering","class","onBlur","onClick","onContextmenu","onFocus","onMouseenter","onMouseleave","onKeydown"]))}});var Hs=ue(Ws,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tooltip/src/trigger.vue"]]);const zs=L({name:"ElTooltipContent",inheritAttrs:!1}),Us=L({...zs,props:Ln,setup(e,{expose:t}){const n=e,{selector:r}=_n(),o=Be("tooltip"),a=B(null),i=B(!1),{controlled:u,id:l,open:s,trigger:f,onClose:d,onOpen:h,onShow:v,onHide:m,onBeforeShow:g,onBeforeHide:O}=pe(Et,void 0),p=x(()=>n.transition||`${o.namespace.value}-fade-in-linear`),w=x(()=>n.persistent);Ie(()=>{i.value=!0});const A=x(()=>c(w)?!0:c(s)),y=x(()=>n.disabled?!1:c(s)),E=x(()=>n.appendTo||r.value),P=x(()=>{var b;return(b=n.style)!=null?b:{}}),T=x(()=>!c(s)),C=()=>{m()},I=()=>{if(c(u))return!0},j=Q(I,()=>{n.enterable&&c(f)==="hover"&&h()}),_=Q(I,()=>{c(f)==="hover"&&d()}),D=()=>{var b,W;(W=(b=a.value)==null?void 0:b.updatePopper)==null||W.call(b),g==null||g()},R=()=>{O==null||O()},N=()=>{v(),M=mr(x(()=>{var b;return(b=a.value)==null?void 0:b.popperContentRef}),()=>{if(c(u))return;c(f)!=="hover"&&d()})},$=()=>{n.virtualTriggering||d()};let M;return F(()=>c(s),b=>{b||M==null||M()},{flush:"post"}),F(()=>n.content,()=>{var b,W;(W=(b=a.value)==null?void 0:b.updatePopper)==null||W.call(b)}),t({contentRef:a}),(b,W)=>(Z(),de(vr,{disabled:!b.teleported,to:c(E)},[Re(gr,{name:c(p),onAfterLeave:C,onBeforeEnter:D,onAfterEnter:N,onBeforeLeave:R},{default:ne(()=>[c(A)?cn((Z(),de(c(ks),dt({key:0,id:c(l),ref_key:"contentRef",ref:a},b.$attrs,{"aria-label":b.ariaLabel,"aria-hidden":c(T),"boundaries-padding":b.boundariesPadding,"fallback-placements":b.fallbackPlacements,"gpu-acceleration":b.gpuAcceleration,offset:b.offset,placement:b.placement,"popper-options":b.popperOptions,strategy:b.strategy,effect:b.effect,enterable:b.enterable,pure:b.pure,"popper-class":b.popperClass,"popper-style":[b.popperStyle,c(P)],"reference-el":b.referenceEl,"trigger-target-el":b.triggerTargetEl,visible:c(y),"z-index":b.zIndex,onMouseenter:c(j),onMouseleave:c(_),onBlur:$,onClose:c(d)}),{default:ne(()=>[i.value?_e("v-if",!0):le(b.$slots,"default",{key:0})]),_:3},16,["id","aria-label","aria-hidden","boundaries-padding","fallback-placements","gpu-acceleration","offset","placement","popper-options","strategy","effect","enterable","pure","popper-class","popper-style","reference-el","trigger-target-el","visible","z-index","onMouseenter","onMouseleave","onClose"])),[[dr,c(y)]]):_e("v-if",!0)]),_:3},8,["name"])],8,["disabled","to"]))}});var Ks=ue(Us,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tooltip/src/content.vue"]]);const qs=["innerHTML"],Vs={key:1},Gs=L({name:"ElTooltip"}),Zs=L({...Gs,props:Fs,emits:Ds,setup(e,{expose:t,emit:n}){const r=e;ns();const o=yr(),a=B(),i=B(),u=()=>{var p;const w=c(a);w&&((p=w.popperInstanceRef)==null||p.update())},l=B(!1),s=B(),{show:f,hide:d,hasUpdateHandler:h}=$s({indicator:l,toggleReason:s}),{onOpen:v,onClose:m}=os({showAfter:Ee(r,"showAfter"),hideAfter:Ee(r,"hideAfter"),autoClose:Ee(r,"autoClose"),open:f,close:d}),g=x(()=>un(r.visible)&&!h.value);je(Et,{controlled:g,id:o,open:hr(l),trigger:Ee(r,"trigger"),onOpen:p=>{v(p)},onClose:p=>{m(p)},onToggle:p=>{c(l)?m(p):v(p)},onShow:()=>{n("show",s.value)},onHide:()=>{n("hide",s.value)},onBeforeShow:()=>{n("before-show",s.value)},onBeforeHide:()=>{n("before-hide",s.value)},updatePopper:u}),F(()=>r.disabled,p=>{p&&l.value&&(l.value=!1)});const O=p=>{var w,A;const y=(A=(w=i.value)==null?void 0:w.contentRef)==null?void 0:A.popperContentRef,E=(p==null?void 0:p.relatedTarget)||document.activeElement;return y&&y.contains(E)};return br(()=>l.value&&d()),t({popperRef:a,contentRef:i,isFocusInsideContent:O,updatePopper:u,onOpen:v,onClose:m,hide:d}),(p,w)=>(Z(),de(c(Ms),{ref_key:"popperRef",ref:a,role:p.role},{default:ne(()=>[Re(Hs,{disabled:p.disabled,trigger:p.trigger,"trigger-keys":p.triggerKeys,"virtual-ref":p.virtualRef,"virtual-triggering":p.virtualTriggering},{default:ne(()=>[p.$slots.default?le(p.$slots,"default",{key:0}):_e("v-if",!0)]),_:3},8,["disabled","trigger","trigger-keys","virtual-ref","virtual-triggering"]),Re(Ks,{ref_key:"contentRef",ref:i,"aria-label":p.ariaLabel,"boundaries-padding":p.boundariesPadding,content:p.content,disabled:p.disabled,effect:p.effect,enterable:p.enterable,"fallback-placements":p.fallbackPlacements,"hide-after":p.hideAfter,"gpu-acceleration":p.gpuAcceleration,offset:p.offset,persistent:p.persistent,"popper-class":p.popperClass,"popper-style":p.popperStyle,placement:p.placement,"popper-options":p.popperOptions,pure:p.pure,"raw-content":p.rawContent,"reference-el":p.referenceEl,"trigger-target-el":p.triggerTargetEl,"show-after":p.showAfter,strategy:p.strategy,teleported:p.teleported,transition:p.transition,"virtual-triggering":p.virtualTriggering,"z-index":p.zIndex,"append-to":p.appendTo},{default:ne(()=>[le(p.$slots,"content",{},()=>[p.rawContent?(Z(),Ve("span",{key:0,innerHTML:p.content},null,8,qs)):(Z(),Ve("span",Vs,wr(p.content),1))]),p.showArrow?(Z(),de(c(ds),{key:0,"arrow-offset":p.arrowOffset},null,8,["arrow-offset"])):_e("v-if",!0)]),_:3},8,["aria-label","boundaries-padding","content","disabled","effect","enterable","fallback-placements","hide-after","gpu-acceleration","offset","persistent","popper-class","popper-style","placement","popper-options","pure","raw-content","reference-el","trigger-target-el","show-after","strategy","teleported","transition","virtual-triggering","z-index","append-to"])]),_:3},8,["role"]))}});var Js=ue(Zs,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tooltip/src/tooltip.vue"]]);const ri=gn(Js);export{ri as E,vs as O,ut as S,Et as T,ni as U,vt as a,uo as b,Co as c,Fr as d,go as e,kr as f,ti as g,xr as h,Er as i,Ao as j,Eo as k,Nn as l,Q as m,Nt as n,$e as o,os as p,yo as q,ho as r,Fo as s,Mo as t,Ln as u,Do as v,Qs as w,ei as x,Ya as y,vn as z};
