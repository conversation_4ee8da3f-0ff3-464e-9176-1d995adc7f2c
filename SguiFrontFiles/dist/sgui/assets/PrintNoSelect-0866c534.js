import{er as Q,r as v,a1 as R,$,b1 as F,h as b,a2 as z,aI as j,f as A,j as _,k as B,w as D,m as C,a8 as H,a9 as L,l as c,z as U,aa as q,H as G,aH as J,p as K,E as W}from"./index-5ab303b6.js";import{F as X}from"./ticketOperationApi-63c815f5.js";import{c as N}from"./cloneDeep-315a2c4c.js";import{E as Y,a as Z}from"./index-00518de1.js";import{_ as ee}from"./_plugin-vue_export-helper-c27b6911.js";const te=Q("printNo",()=>{const n=v(new Map);return{printNosByOffice:n,setPrintNosByOffice:(r,o)=>{n.value.set(r,o)},deletePrintNosExceptOffice:r=>{const o=[];for(const[l]of n.value)l!==r&&o.push(l);for(const l of o)n.value.delete(l)}}}),ae=(n,k)=>{const{t:i}=R(),r=te(),o=$(),{printNosByOffice:l}=F(r),u=v(n.modelValue),p=v([]),t=v([]),O=v([]),y=v(n.ticketType??""),g=v(!1),S=b(()=>{var e;return((e=o.state.user)==null?void 0:e.crsSystem)??!1}),E=b(()=>`printNo-select ${n.selectClass??""}`),h=(e,a)=>{var P,d,m;const s=((P=o.state.user)==null?void 0:P[e])??"";return s||(((m=(((d=o.state.user)==null?void 0:d[a])??"").split(";"))==null?void 0:m[0])??"")},V=e=>{p.value=(l.value.get(e)??[]).map(a=>({value:a.devno,label:a.devno,type:`${a.devno} ${i("app.agentTicketQuery.ticketMachine.type_"+a.type)}`})),t.value=N(p.value),O.value=N(t.value),I(n.ticketType??""),n.needDistinguish&&(n.isInter?t.value=t.value.filter(a=>!a.type.includes(i("app.agentTicketQuery.ticketMachine.domestic"))):t.value=t.value.filter(a=>a.type.includes(i("app.agentTicketQuery.ticketMachine.domestic"))))},x=async()=>{var a;if(!S.value)return;const e=h("defaultOffice","office");if(!l.value.get(e)||((a=l.value.get(e))==null?void 0:a.length)===0)try{g.value=!0;const s=(await X({office:e},!0)).data.value;await r.setPrintNosByOffice((s==null?void 0:s.office.office)??e,(s==null?void 0:s.ticketMachines)??[])}finally{g.value=!1}await r.deletePrintNosExceptOffice(e),await V(e)},T=()=>{n.isInter?t.value=t.value.filter(e=>!e.type.includes(i("app.agentTicketQuery.ticketMachine.domestic"))):t.value=t.value.filter(e=>e.type.includes(i("app.agentTicketQuery.ticketMachine.domestic")))},f=e=>{var a,s,P;if(e){let d=[];j.test(e.trim())?d=(p.value??[]).filter(m=>m.value===e):d=(p.value??[]).filter(m=>m.type.includes(e)),t.value=N(d),u.value=((a=t.value)==null?void 0:a.length)>0?(P=(s=t.value)==null?void 0:s[0])==null?void 0:P.value:e.toUpperCase()}else t.value=N(p.value);n.needDistinguish&&T(),t.value=(t.value??[]).filter(d=>d.type.includes(y.value))},I=e=>{e&&(u.value=n.modelValue,y.value=N(e),e==="ARL"&&(y.value=i("app.pnrManagement.paymentMethod.currentTicketReal")),n.needDistinguish&&(t.value=N(p.value),T()),t.value=(t.value??[]).filter(a=>a.type.includes(y.value)))},M=()=>{var a;const e=t.value.find(s=>s.value===u.value);return e?(a=e.type)!=null&&a.includes(i("app.agentTicketQuery.ticketMachine.domestic"))?"D":"I":""},w=()=>{k("update:modelValue",u.value),k("deliverPrintType",M())};return z(()=>n.modelValue,()=>{u.value=n.modelValue}),{printNo:u,printNos:t,loading:g,selectClass:E,filterPrintNo:f,setPrintNo:w,filterPrintToTicketType:I,init:x}},ne=ae,le={key:0},se={key:1,class:"inline-block w-[12px]"},ie=A({__name:"PrintNoSelect",props:{modelValue:{},selectClass:{},isInter:{type:Boolean},needDistinguish:{type:Boolean},ticketType:{}},emits:["update:modelValue","deliverPrintType"],setup(n,{expose:k,emit:i}){const r=n,o=i,{printNo:l,printNos:u,loading:p,selectClass:t,filterPrintNo:O,setPrintNo:y,filterPrintToTicketType:g,init:S}=ne(r,o);return k({filterPrintToTicketType:g}),(E,h)=>{const V=W,x=Y,T=Z;return _(),B(T,{modelValue:c(l),"onUpdate:modelValue":h[0]||(h[0]=f=>J(l)?l.value=f:null),class:K(c(t)),"popper-class":"printNo-select-option",placeholder:" ",loading:c(p),filterable:"",clearable:"","filter-method":c(O),onFocus:c(S),onBlur:c(y)},{default:D(()=>[(_(!0),C(H,null,L(c(u),f=>(_(),B(x,{key:f.value,label:f.label,value:f.value},{default:D(()=>[c(u).some(I=>I.value===c(l))?(_(),C("span",le,[f.value===c(l)?(_(),B(V,{key:0,size:12,class:"iconfont icon-right-line"})):(_(),C("span",se))])):U("",!0),q(" "+G(f.type),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","class","loading","filter-method","onFocus","onBlur"])}}});const pe=ee(ie,[["__scopeId","data-v-db081c8d"]]);export{pe as P};
