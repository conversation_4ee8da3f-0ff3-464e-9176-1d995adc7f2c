import{em as ie,bH as zr}from"./index-5ab303b6.js";import{c as fe}from"./_commonjs-dynamic-modules-302442b1.js";import{E as re}from"./index-52e5474f.js";var ze={exports:{}};/*!
    localForage -- Offline Storage, Improved
    Version 1.10.0
    https://localforage.github.io/localForage
    (c) 2013-2017 Mozilla, Apache License 2.0
*/(function(K,te){(function(L){K.exports=L()})(function(){return function L(P,z,p){function E(B,Y){if(!z[B]){if(!P[B]){var v=typeof fe=="function"&&fe;if(!Y&&v)return v(B,!0);if(g)return g(B,!0);var h=new Error("Cannot find module '"+B+"'");throw h.code="MODULE_NOT_FOUND",h}var D=z[B]={exports:{}};P[B][0].call(D.exports,function(A){var $=P[B][1][A];return E($||A)},D,D.exports,L,P,z,p)}return z[B].exports}for(var g=typeof fe=="function"&&fe,T=0;T<p.length;T++)E(p[T]);return E}({1:[function(L,P,z){(function(p){var E=p.MutationObserver||p.WebKitMutationObserver,g;if(E){var T=0,B=new E(A),Y=p.document.createTextNode("");B.observe(Y,{characterData:!0}),g=function(){Y.data=T=++T%2}}else if(!p.setImmediate&&typeof p.MessageChannel<"u"){var v=new p.MessageChannel;v.port1.onmessage=A,g=function(){v.port2.postMessage(0)}}else"document"in p&&"onreadystatechange"in p.document.createElement("script")?g=function(){var N=p.document.createElement("script");N.onreadystatechange=function(){A(),N.onreadystatechange=null,N.parentNode.removeChild(N),N=null},p.document.documentElement.appendChild(N)}:g=function(){setTimeout(A,0)};var h,D=[];function A(){h=!0;for(var N,W,x=D.length;x;){for(W=D,D=[],N=-1;++N<x;)W[N]();x=D.length}h=!1}P.exports=$;function $(N){D.push(N)===1&&!h&&g()}}).call(this,typeof ie<"u"?ie:typeof self<"u"?self:typeof window<"u"?window:{})},{}],2:[function(L,P,z){var p=L(1);function E(){}var g={},T=["REJECTED"],B=["FULFILLED"],Y=["PENDING"];P.exports=v;function v(l){if(typeof l!="function")throw new TypeError("resolver must be a function");this.state=Y,this.queue=[],this.outcome=void 0,l!==E&&$(this,l)}v.prototype.catch=function(l){return this.then(null,l)},v.prototype.then=function(l,_){if(typeof l!="function"&&this.state===B||typeof _!="function"&&this.state===T)return this;var y=new this.constructor(E);if(this.state!==Y){var S=this.state===B?l:_;D(y,S,this.outcome)}else this.queue.push(new h(y,l,_));return y};function h(l,_,y){this.promise=l,typeof _=="function"&&(this.onFulfilled=_,this.callFulfilled=this.otherCallFulfilled),typeof y=="function"&&(this.onRejected=y,this.callRejected=this.otherCallRejected)}h.prototype.callFulfilled=function(l){g.resolve(this.promise,l)},h.prototype.otherCallFulfilled=function(l){D(this.promise,this.onFulfilled,l)},h.prototype.callRejected=function(l){g.reject(this.promise,l)},h.prototype.otherCallRejected=function(l){D(this.promise,this.onRejected,l)};function D(l,_,y){p(function(){var S;try{S=_(y)}catch(C){return g.reject(l,C)}S===l?g.reject(l,new TypeError("Cannot resolve promise with itself")):g.resolve(l,S)})}g.resolve=function(l,_){var y=N(A,_);if(y.status==="error")return g.reject(l,y.value);var S=y.value;if(S)$(l,S);else{l.state=B,l.outcome=_;for(var C=-1,O=l.queue.length;++C<O;)l.queue[C].callFulfilled(_)}return l},g.reject=function(l,_){l.state=T,l.outcome=_;for(var y=-1,S=l.queue.length;++y<S;)l.queue[y].callRejected(_);return l};function A(l){var _=l&&l.then;if(l&&(typeof l=="object"||typeof l=="function")&&typeof _=="function")return function(){_.apply(l,arguments)}}function $(l,_){var y=!1;function S(M){y||(y=!0,g.reject(l,M))}function C(M){y||(y=!0,g.resolve(l,M))}function O(){_(C,S)}var F=N(O);F.status==="error"&&S(F.value)}function N(l,_){var y={};try{y.value=l(_),y.status="success"}catch(S){y.status="error",y.value=S}return y}v.resolve=W;function W(l){return l instanceof this?l:g.resolve(new this(E),l)}v.reject=x;function x(l){var _=new this(E);return g.reject(_,l)}v.all=ue;function ue(l){var _=this;if(Object.prototype.toString.call(l)!=="[object Array]")return this.reject(new TypeError("must be an array"));var y=l.length,S=!1;if(!y)return this.resolve([]);for(var C=new Array(y),O=0,F=-1,M=new this(E);++F<y;)V(l[F],F);return M;function V(q,ne){_.resolve(q).then(se,function(J){S||(S=!0,g.reject(M,J))});function se(J){C[ne]=J,++O===y&&!S&&(S=!0,g.resolve(M,C))}}}v.race=X;function X(l){var _=this;if(Object.prototype.toString.call(l)!=="[object Array]")return this.reject(new TypeError("must be an array"));var y=l.length,S=!1;if(!y)return this.resolve([]);for(var C=-1,O=new this(E);++C<y;)F(l[C]);return O;function F(M){_.resolve(M).then(function(V){S||(S=!0,g.resolve(O,V))},function(V){S||(S=!0,g.reject(O,V))})}}},{1:1}],3:[function(L,P,z){(function(p){typeof p.Promise!="function"&&(p.Promise=L(2))}).call(this,typeof ie<"u"?ie:typeof self<"u"?self:typeof window<"u"?window:{})},{2:2}],4:[function(L,P,z){var p=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function E(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function g(){try{if(typeof indexedDB<"u")return indexedDB;if(typeof webkitIndexedDB<"u")return webkitIndexedDB;if(typeof mozIndexedDB<"u")return mozIndexedDB;if(typeof OIndexedDB<"u")return OIndexedDB;if(typeof msIndexedDB<"u")return msIndexedDB}catch{return}}var T=g();function B(){try{if(!T||!T.open)return!1;var e=typeof openDatabase<"u"&&/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),t=typeof fetch=="function"&&fetch.toString().indexOf("[native code")!==-1;return(!e||t)&&typeof indexedDB<"u"&&typeof IDBKeyRange<"u"}catch{return!1}}function Y(e,t){e=e||[],t=t||{};try{return new Blob(e,t)}catch(n){if(n.name!=="TypeError")throw n;for(var r=typeof BlobBuilder<"u"?BlobBuilder:typeof MSBlobBuilder<"u"?MSBlobBuilder:typeof MozBlobBuilder<"u"?MozBlobBuilder:WebKitBlobBuilder,o=new r,a=0;a<e.length;a+=1)o.append(e[a]);return o.getBlob(t.type)}}typeof Promise>"u"&&L(3);var v=Promise;function h(e,t){t&&e.then(function(r){t(null,r)},function(r){t(r)})}function D(e,t,r){typeof t=="function"&&e.then(t),typeof r=="function"&&e.catch(r)}function A(e){return typeof e!="string"&&(console.warn(e+" used as a key, but it is not a string."),e=String(e)),e}function $(){if(arguments.length&&typeof arguments[arguments.length-1]=="function")return arguments[arguments.length-1]}var N="local-forage-detect-blob-support",W=void 0,x={},ue=Object.prototype.toString,X="readonly",l="readwrite";function _(e){for(var t=e.length,r=new ArrayBuffer(t),o=new Uint8Array(r),a=0;a<t;a++)o[a]=e.charCodeAt(a);return r}function y(e){return new v(function(t){var r=e.transaction(N,l),o=Y([""]);r.objectStore(N).put(o,"key"),r.onabort=function(a){a.preventDefault(),a.stopPropagation(),t(!1)},r.oncomplete=function(){var a=navigator.userAgent.match(/Chrome\/(\d+)/),n=navigator.userAgent.match(/Edge\//);t(n||!a||parseInt(a[1],10)>=43)}}).catch(function(){return!1})}function S(e){return typeof W=="boolean"?v.resolve(W):y(e).then(function(t){return W=t,W})}function C(e){var t=x[e.name],r={};r.promise=new v(function(o,a){r.resolve=o,r.reject=a}),t.deferredOperations.push(r),t.dbReady?t.dbReady=t.dbReady.then(function(){return r.promise}):t.dbReady=r.promise}function O(e){var t=x[e.name],r=t.deferredOperations.pop();if(r)return r.resolve(),r.promise}function F(e,t){var r=x[e.name],o=r.deferredOperations.pop();if(o)return o.reject(t),o.promise}function M(e,t){return new v(function(r,o){if(x[e.name]=x[e.name]||we(),e.db)if(t)C(e),e.db.close();else return r(e.db);var a=[e.name];t&&a.push(e.version);var n=T.open.apply(T,a);t&&(n.onupgradeneeded=function(i){var f=n.result;try{f.createObjectStore(e.storeName),i.oldVersion<=1&&f.createObjectStore(N)}catch(u){if(u.name==="ConstraintError")console.warn('The database "'+e.name+'" has been upgraded from version '+i.oldVersion+" to version "+i.newVersion+', but the storage "'+e.storeName+'" already exists.');else throw u}}),n.onerror=function(i){i.preventDefault(),o(n.error)},n.onsuccess=function(){var i=n.result;i.onversionchange=function(f){f.target.close()},r(i),O(e)}})}function V(e){return M(e,!1)}function q(e){return M(e,!0)}function ne(e,t){if(!e.db)return!0;var r=!e.db.objectStoreNames.contains(e.storeName),o=e.version<e.db.version,a=e.version>e.db.version;if(o&&(e.version!==t&&console.warn('The database "'+e.name+`" can't be downgraded from version `+e.db.version+" to version "+e.version+"."),e.version=e.db.version),a||r){if(r){var n=e.db.version+1;n>e.version&&(e.version=n)}return!0}return!1}function se(e){return new v(function(t,r){var o=new FileReader;o.onerror=r,o.onloadend=function(a){var n=btoa(a.target.result||"");t({__local_forage_encoded_blob:!0,data:n,type:e.type})},o.readAsBinaryString(e)})}function J(e){var t=_(atob(e.data));return Y([t],{type:e.type})}function be(e){return e&&e.__local_forage_encoded_blob}function We(e){var t=this,r=t._initReady().then(function(){var o=x[t._dbInfo.name];if(o&&o.dbReady)return o.dbReady});return D(r,e,e),r}function Ve(e){C(e);for(var t=x[e.name],r=t.forages,o=0;o<r.length;o++){var a=r[o];a._dbInfo.db&&(a._dbInfo.db.close(),a._dbInfo.db=null)}return e.db=null,V(e).then(function(n){return e.db=n,ne(e)?q(e):n}).then(function(n){e.db=t.db=n;for(var i=0;i<r.length;i++)r[i]._dbInfo.db=n}).catch(function(n){throw F(e,n),n})}function H(e,t,r,o){o===void 0&&(o=1);try{var a=e.db.transaction(e.storeName,t);r(null,a)}catch(n){if(o>0&&(!e.db||n.name==="InvalidStateError"||n.name==="NotFoundError"))return v.resolve().then(function(){if(!e.db||n.name==="NotFoundError"&&!e.db.objectStoreNames.contains(e.storeName)&&e.version<=e.db.version)return e.db&&(e.version=e.db.version+1),q(e)}).then(function(){return Ve(e).then(function(){H(e,t,r,o-1)})}).catch(r);r(n)}}function we(){return{forages:[],db:null,dbReady:null,deferredOperations:[]}}function He(e){var t=this,r={db:null};if(e)for(var o in e)r[o]=e[o];var a=x[r.name];a||(a=we(),x[r.name]=a),a.forages.push(t),t._initReady||(t._initReady=t.ready,t.ready=We);var n=[];function i(){return v.resolve()}for(var f=0;f<a.forages.length;f++){var u=a.forages[f];u!==t&&n.push(u._initReady().catch(i))}var s=a.forages.slice(0);return v.all(n).then(function(){return r.db=a.db,V(r)}).then(function(c){return r.db=c,ne(r,t._defaultConfig.version)?q(r):c}).then(function(c){r.db=a.db=c,t._dbInfo=r;for(var d=0;d<s.length;d++){var m=s[d];m!==t&&(m._dbInfo.db=r.db,m._dbInfo.version=r.version)}})}function Ke(e,t){var r=this;e=A(e);var o=new v(function(a,n){r.ready().then(function(){H(r._dbInfo,X,function(i,f){if(i)return n(i);try{var u=f.objectStore(r._dbInfo.storeName),s=u.get(e);s.onsuccess=function(){var c=s.result;c===void 0&&(c=null),be(c)&&(c=J(c)),a(c)},s.onerror=function(){n(s.error)}}catch(c){n(c)}})}).catch(n)});return h(o,t),o}function Qe(e,t){var r=this,o=new v(function(a,n){r.ready().then(function(){H(r._dbInfo,X,function(i,f){if(i)return n(i);try{var u=f.objectStore(r._dbInfo.storeName),s=u.openCursor(),c=1;s.onsuccess=function(){var d=s.result;if(d){var m=d.value;be(m)&&(m=J(m));var b=e(m,d.key,c++);b!==void 0?a(b):d.continue()}else a()},s.onerror=function(){n(s.error)}}catch(d){n(d)}})}).catch(n)});return h(o,t),o}function Ge(e,t,r){var o=this;e=A(e);var a=new v(function(n,i){var f;o.ready().then(function(){return f=o._dbInfo,ue.call(t)==="[object Blob]"?S(f.db).then(function(u){return u?t:se(t)}):t}).then(function(u){H(o._dbInfo,l,function(s,c){if(s)return i(s);try{var d=c.objectStore(o._dbInfo.storeName);u===null&&(u=void 0);var m=d.put(u,e);c.oncomplete=function(){u===void 0&&(u=null),n(u)},c.onabort=c.onerror=function(){var b=m.error?m.error:m.transaction.error;i(b)}}catch(b){i(b)}})}).catch(i)});return h(a,r),a}function Xe(e,t){var r=this;e=A(e);var o=new v(function(a,n){r.ready().then(function(){H(r._dbInfo,l,function(i,f){if(i)return n(i);try{var u=f.objectStore(r._dbInfo.storeName),s=u.delete(e);f.oncomplete=function(){a()},f.onerror=function(){n(s.error)},f.onabort=function(){var c=s.error?s.error:s.transaction.error;n(c)}}catch(c){n(c)}})}).catch(n)});return h(o,t),o}function Je(e){var t=this,r=new v(function(o,a){t.ready().then(function(){H(t._dbInfo,l,function(n,i){if(n)return a(n);try{var f=i.objectStore(t._dbInfo.storeName),u=f.clear();i.oncomplete=function(){o()},i.onabort=i.onerror=function(){var s=u.error?u.error:u.transaction.error;a(s)}}catch(s){a(s)}})}).catch(a)});return h(r,e),r}function je(e){var t=this,r=new v(function(o,a){t.ready().then(function(){H(t._dbInfo,X,function(n,i){if(n)return a(n);try{var f=i.objectStore(t._dbInfo.storeName),u=f.count();u.onsuccess=function(){o(u.result)},u.onerror=function(){a(u.error)}}catch(s){a(s)}})}).catch(a)});return h(r,e),r}function Ze(e,t){var r=this,o=new v(function(a,n){if(e<0){a(null);return}r.ready().then(function(){H(r._dbInfo,X,function(i,f){if(i)return n(i);try{var u=f.objectStore(r._dbInfo.storeName),s=!1,c=u.openKeyCursor();c.onsuccess=function(){var d=c.result;if(!d){a(null);return}e===0||s?a(d.key):(s=!0,d.advance(e))},c.onerror=function(){n(c.error)}}catch(d){n(d)}})}).catch(n)});return h(o,t),o}function qe(e){var t=this,r=new v(function(o,a){t.ready().then(function(){H(t._dbInfo,X,function(n,i){if(n)return a(n);try{var f=i.objectStore(t._dbInfo.storeName),u=f.openKeyCursor(),s=[];u.onsuccess=function(){var c=u.result;if(!c){o(s);return}s.push(c.key),c.continue()},u.onerror=function(){a(u.error)}}catch(c){a(c)}})}).catch(a)});return h(r,e),r}function ke(e,t){t=$.apply(this,arguments);var r=this.config();e=typeof e!="function"&&e||{},e.name||(e.name=e.name||r.name,e.storeName=e.storeName||r.storeName);var o=this,a;if(!e.name)a=v.reject("Invalid arguments");else{var n=e.name===r.name&&o._dbInfo.db,i=n?v.resolve(o._dbInfo.db):V(e).then(function(f){var u=x[e.name],s=u.forages;u.db=f;for(var c=0;c<s.length;c++)s[c]._dbInfo.db=f;return f});e.storeName?a=i.then(function(f){if(f.objectStoreNames.contains(e.storeName)){var u=f.version+1;C(e);var s=x[e.name],c=s.forages;f.close();for(var d=0;d<c.length;d++){var m=c[d];m._dbInfo.db=null,m._dbInfo.version=u}var b=new v(function(w,R){var I=T.open(e.name,u);I.onerror=function(U){var ee=I.result;ee.close(),R(U)},I.onupgradeneeded=function(){var U=I.result;U.deleteObjectStore(e.storeName)},I.onsuccess=function(){var U=I.result;U.close(),w(U)}});return b.then(function(w){s.db=w;for(var R=0;R<c.length;R++){var I=c[R];I._dbInfo.db=w,O(I._dbInfo)}}).catch(function(w){throw(F(e,w)||v.resolve()).catch(function(){}),w})}}):a=i.then(function(f){C(e);var u=x[e.name],s=u.forages;f.close();for(var c=0;c<s.length;c++){var d=s[c];d._dbInfo.db=null}var m=new v(function(b,w){var R=T.deleteDatabase(e.name);R.onerror=function(){var I=R.result;I&&I.close(),w(R.error)},R.onblocked=function(){console.warn('dropInstance blocked for database "'+e.name+'" until all open connections are closed')},R.onsuccess=function(){var I=R.result;I&&I.close(),b(I)}});return m.then(function(b){u.db=b;for(var w=0;w<s.length;w++){var R=s[w];O(R._dbInfo)}}).catch(function(b){throw(F(e,b)||v.resolve()).catch(function(){}),b})})}return h(a,t),a}var er={_driver:"asyncStorage",_initStorage:He,_support:B(),iterate:Qe,getItem:Ke,setItem:Ge,removeItem:Xe,clear:Je,length:je,key:Ze,keys:qe,dropInstance:ke};function rr(){return typeof openDatabase=="function"}var Q="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",tr="~~local_forage_type~",Ee=/^~~local_forage_type~([^~]+)~/,oe="__lfsc__:",ce=oe.length,le="arbf",ve="blob",Se="si08",Ie="ui08",De="uic8",Re="si16",Ae="si32",Ne="ur16",Te="ui32",xe="fl32",Be="fl64",Ce=ce+le.length,Le=Object.prototype.toString;function Oe(e){var t=e.length*.75,r=e.length,o,a=0,n,i,f,u;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);var s=new ArrayBuffer(t),c=new Uint8Array(s);for(o=0;o<r;o+=4)n=Q.indexOf(e[o]),i=Q.indexOf(e[o+1]),f=Q.indexOf(e[o+2]),u=Q.indexOf(e[o+3]),c[a++]=n<<2|i>>4,c[a++]=(i&15)<<4|f>>2,c[a++]=(f&3)<<6|u&63;return s}function de(e){var t=new Uint8Array(e),r="",o;for(o=0;o<t.length;o+=3)r+=Q[t[o]>>2],r+=Q[(t[o]&3)<<4|t[o+1]>>4],r+=Q[(t[o+1]&15)<<2|t[o+2]>>6],r+=Q[t[o+2]&63];return t.length%3===2?r=r.substring(0,r.length-1)+"=":t.length%3===1&&(r=r.substring(0,r.length-2)+"=="),r}function nr(e,t){var r="";if(e&&(r=Le.call(e)),e&&(r==="[object ArrayBuffer]"||e.buffer&&Le.call(e.buffer)==="[object ArrayBuffer]")){var o,a=oe;e instanceof ArrayBuffer?(o=e,a+=le):(o=e.buffer,r==="[object Int8Array]"?a+=Se:r==="[object Uint8Array]"?a+=Ie:r==="[object Uint8ClampedArray]"?a+=De:r==="[object Int16Array]"?a+=Re:r==="[object Uint16Array]"?a+=Ne:r==="[object Int32Array]"?a+=Ae:r==="[object Uint32Array]"?a+=Te:r==="[object Float32Array]"?a+=xe:r==="[object Float64Array]"?a+=Be:t(new Error("Failed to get type for BinaryArray"))),t(a+de(o))}else if(r==="[object Blob]"){var n=new FileReader;n.onload=function(){var i=tr+e.type+"~"+de(this.result);t(oe+ve+i)},n.readAsArrayBuffer(e)}else try{t(JSON.stringify(e))}catch(i){console.error("Couldn't convert value into a JSON string: ",e),t(null,i)}}function or(e){if(e.substring(0,ce)!==oe)return JSON.parse(e);var t=e.substring(Ce),r=e.substring(ce,Ce),o;if(r===ve&&Ee.test(t)){var a=t.match(Ee);o=a[1],t=t.substring(a[0].length)}var n=Oe(t);switch(r){case le:return n;case ve:return Y([n],{type:o});case Se:return new Int8Array(n);case Ie:return new Uint8Array(n);case De:return new Uint8ClampedArray(n);case Re:return new Int16Array(n);case Ne:return new Uint16Array(n);case Ae:return new Int32Array(n);case Te:return new Uint32Array(n);case xe:return new Float32Array(n);case Be:return new Float64Array(n);default:throw new Error("Unkown type: "+r)}}var he={serialize:nr,deserialize:or,stringToBuffer:Oe,bufferToString:de};function Pe(e,t,r,o){e.executeSql("CREATE TABLE IF NOT EXISTS "+t.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],r,o)}function ar(e){var t=this,r={db:null};if(e)for(var o in e)r[o]=typeof e[o]!="string"?e[o].toString():e[o];var a=new v(function(n,i){try{r.db=openDatabase(r.name,String(r.version),r.description,r.size)}catch(f){return i(f)}r.db.transaction(function(f){Pe(f,r,function(){t._dbInfo=r,n()},function(u,s){i(s)})},i)});return r.serializer=he,a}function G(e,t,r,o,a,n){e.executeSql(r,o,a,function(i,f){f.code===f.SYNTAX_ERR?i.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name = ?",[t.storeName],function(u,s){s.rows.length?n(u,f):Pe(u,t,function(){u.executeSql(r,o,a,n)},n)},n):n(i,f)},n)}function ir(e,t){var r=this;e=A(e);var o=new v(function(a,n){r.ready().then(function(){var i=r._dbInfo;i.db.transaction(function(f){G(f,i,"SELECT * FROM "+i.storeName+" WHERE key = ? LIMIT 1",[e],function(u,s){var c=s.rows.length?s.rows.item(0).value:null;c&&(c=i.serializer.deserialize(c)),a(c)},function(u,s){n(s)})})}).catch(n)});return h(o,t),o}function fr(e,t){var r=this,o=new v(function(a,n){r.ready().then(function(){var i=r._dbInfo;i.db.transaction(function(f){G(f,i,"SELECT * FROM "+i.storeName,[],function(u,s){for(var c=s.rows,d=c.length,m=0;m<d;m++){var b=c.item(m),w=b.value;if(w&&(w=i.serializer.deserialize(w)),w=e(w,b.key,m+1),w!==void 0){a(w);return}}a()},function(u,s){n(s)})})}).catch(n)});return h(o,t),o}function Fe(e,t,r,o){var a=this;e=A(e);var n=new v(function(i,f){a.ready().then(function(){t===void 0&&(t=null);var u=t,s=a._dbInfo;s.serializer.serialize(t,function(c,d){d?f(d):s.db.transaction(function(m){G(m,s,"INSERT OR REPLACE INTO "+s.storeName+" (key, value) VALUES (?, ?)",[e,c],function(){i(u)},function(b,w){f(w)})},function(m){if(m.code===m.QUOTA_ERR){if(o>0){i(Fe.apply(a,[e,u,r,o-1]));return}f(m)}})})}).catch(f)});return h(n,r),n}function ur(e,t,r){return Fe.apply(this,[e,t,r,1])}function sr(e,t){var r=this;e=A(e);var o=new v(function(a,n){r.ready().then(function(){var i=r._dbInfo;i.db.transaction(function(f){G(f,i,"DELETE FROM "+i.storeName+" WHERE key = ?",[e],function(){a()},function(u,s){n(s)})})}).catch(n)});return h(o,t),o}function cr(e){var t=this,r=new v(function(o,a){t.ready().then(function(){var n=t._dbInfo;n.db.transaction(function(i){G(i,n,"DELETE FROM "+n.storeName,[],function(){o()},function(f,u){a(u)})})}).catch(a)});return h(r,e),r}function lr(e){var t=this,r=new v(function(o,a){t.ready().then(function(){var n=t._dbInfo;n.db.transaction(function(i){G(i,n,"SELECT COUNT(key) as c FROM "+n.storeName,[],function(f,u){var s=u.rows.item(0).c;o(s)},function(f,u){a(u)})})}).catch(a)});return h(r,e),r}function vr(e,t){var r=this,o=new v(function(a,n){r.ready().then(function(){var i=r._dbInfo;i.db.transaction(function(f){G(f,i,"SELECT key FROM "+i.storeName+" WHERE id = ? LIMIT 1",[e+1],function(u,s){var c=s.rows.length?s.rows.item(0).key:null;a(c)},function(u,s){n(s)})})}).catch(n)});return h(o,t),o}function dr(e){var t=this,r=new v(function(o,a){t.ready().then(function(){var n=t._dbInfo;n.db.transaction(function(i){G(i,n,"SELECT key FROM "+n.storeName,[],function(f,u){for(var s=[],c=0;c<u.rows.length;c++)s.push(u.rows.item(c).key);o(s)},function(f,u){a(u)})})}).catch(a)});return h(r,e),r}function hr(e){return new v(function(t,r){e.transaction(function(o){o.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'",[],function(a,n){for(var i=[],f=0;f<n.rows.length;f++)i.push(n.rows.item(f).name);t({db:e,storeNames:i})},function(a,n){r(n)})},function(o){r(o)})})}function mr(e,t){t=$.apply(this,arguments);var r=this.config();e=typeof e!="function"&&e||{},e.name||(e.name=e.name||r.name,e.storeName=e.storeName||r.storeName);var o=this,a;return e.name?a=new v(function(n){var i;e.name===r.name?i=o._dbInfo.db:i=openDatabase(e.name,"","",0),e.storeName?n({db:i,storeNames:[e.storeName]}):n(hr(i))}).then(function(n){return new v(function(i,f){n.db.transaction(function(u){function s(b){return new v(function(w,R){u.executeSql("DROP TABLE IF EXISTS "+b,[],function(){w()},function(I,U){R(U)})})}for(var c=[],d=0,m=n.storeNames.length;d<m;d++)c.push(s(n.storeNames[d]));v.all(c).then(function(){i()}).catch(function(b){f(b)})},function(u){f(u)})})}):a=v.reject("Invalid arguments"),h(a,t),a}var yr={_driver:"webSQLStorage",_initStorage:ar,_support:rr(),iterate:fr,getItem:ir,setItem:ur,removeItem:sr,clear:cr,length:lr,key:vr,keys:dr,dropInstance:mr};function gr(){try{return typeof localStorage<"u"&&"setItem"in localStorage&&!!localStorage.setItem}catch{return!1}}function Me(e,t){var r=e.name+"/";return e.storeName!==t.storeName&&(r+=e.storeName+"/"),r}function pr(){var e="_localforage_support_test";try{return localStorage.setItem(e,!0),localStorage.removeItem(e),!1}catch{return!0}}function _r(){return!pr()||localStorage.length>0}function br(e){var t=this,r={};if(e)for(var o in e)r[o]=e[o];return r.keyPrefix=Me(e,t._defaultConfig),_r()?(t._dbInfo=r,r.serializer=he,v.resolve()):v.reject()}function wr(e){var t=this,r=t.ready().then(function(){for(var o=t._dbInfo.keyPrefix,a=localStorage.length-1;a>=0;a--){var n=localStorage.key(a);n.indexOf(o)===0&&localStorage.removeItem(n)}});return h(r,e),r}function Er(e,t){var r=this;e=A(e);var o=r.ready().then(function(){var a=r._dbInfo,n=localStorage.getItem(a.keyPrefix+e);return n&&(n=a.serializer.deserialize(n)),n});return h(o,t),o}function Sr(e,t){var r=this,o=r.ready().then(function(){for(var a=r._dbInfo,n=a.keyPrefix,i=n.length,f=localStorage.length,u=1,s=0;s<f;s++){var c=localStorage.key(s);if(c.indexOf(n)===0){var d=localStorage.getItem(c);if(d&&(d=a.serializer.deserialize(d)),d=e(d,c.substring(i),u++),d!==void 0)return d}}});return h(o,t),o}function Ir(e,t){var r=this,o=r.ready().then(function(){var a=r._dbInfo,n;try{n=localStorage.key(e)}catch{n=null}return n&&(n=n.substring(a.keyPrefix.length)),n});return h(o,t),o}function Dr(e){var t=this,r=t.ready().then(function(){for(var o=t._dbInfo,a=localStorage.length,n=[],i=0;i<a;i++){var f=localStorage.key(i);f.indexOf(o.keyPrefix)===0&&n.push(f.substring(o.keyPrefix.length))}return n});return h(r,e),r}function Rr(e){var t=this,r=t.keys().then(function(o){return o.length});return h(r,e),r}function Ar(e,t){var r=this;e=A(e);var o=r.ready().then(function(){var a=r._dbInfo;localStorage.removeItem(a.keyPrefix+e)});return h(o,t),o}function Nr(e,t,r){var o=this;e=A(e);var a=o.ready().then(function(){t===void 0&&(t=null);var n=t;return new v(function(i,f){var u=o._dbInfo;u.serializer.serialize(t,function(s,c){if(c)f(c);else try{localStorage.setItem(u.keyPrefix+e,s),i(n)}catch(d){(d.name==="QuotaExceededError"||d.name==="NS_ERROR_DOM_QUOTA_REACHED")&&f(d),f(d)}})})});return h(a,r),a}function Tr(e,t){if(t=$.apply(this,arguments),e=typeof e!="function"&&e||{},!e.name){var r=this.config();e.name=e.name||r.name,e.storeName=e.storeName||r.storeName}var o=this,a;return e.name?a=new v(function(n){e.storeName?n(Me(e,o._defaultConfig)):n(e.name+"/")}).then(function(n){for(var i=localStorage.length-1;i>=0;i--){var f=localStorage.key(i);f.indexOf(n)===0&&localStorage.removeItem(f)}}):a=v.reject("Invalid arguments"),h(a,t),a}var xr={_driver:"localStorageWrapper",_initStorage:br,_support:gr(),iterate:Sr,getItem:Er,setItem:Nr,removeItem:Ar,clear:wr,length:Rr,key:Ir,keys:Dr,dropInstance:Tr},Br=function(t,r){return t===r||typeof t=="number"&&typeof r=="number"&&isNaN(t)&&isNaN(r)},Cr=function(t,r){for(var o=t.length,a=0;a<o;){if(Br(t[a],r))return!0;a++}return!1},Ue=Array.isArray||function(e){return Object.prototype.toString.call(e)==="[object Array]"},k={},Ye={},j={INDEXEDDB:er,WEBSQL:yr,LOCALSTORAGE:xr},Lr=[j.INDEXEDDB._driver,j.WEBSQL._driver,j.LOCALSTORAGE._driver],ae=["dropInstance"],me=["clear","getItem","iterate","key","keys","length","removeItem","setItem"].concat(ae),Or={description:"",driver:Lr.slice(),name:"localforage",size:4980736,storeName:"keyvaluepairs",version:1};function Pr(e,t){e[t]=function(){var r=arguments;return e.ready().then(function(){return e[t].apply(e,r)})}}function ye(){for(var e=1;e<arguments.length;e++){var t=arguments[e];if(t)for(var r in t)t.hasOwnProperty(r)&&(Ue(t[r])?arguments[0][r]=t[r].slice():arguments[0][r]=t[r])}return arguments[0]}var Fr=function(){function e(t){E(this,e);for(var r in j)if(j.hasOwnProperty(r)){var o=j[r],a=o._driver;this[r]=a,k[a]||this.defineDriver(o)}this._defaultConfig=ye({},Or),this._config=ye({},this._defaultConfig,t),this._driverSet=null,this._initDriver=null,this._ready=!1,this._dbInfo=null,this._wrapLibraryMethodsWithReady(),this.setDriver(this._config.driver).catch(function(){})}return e.prototype.config=function(r){if((typeof r>"u"?"undefined":p(r))==="object"){if(this._ready)return new Error("Can't call config() after localforage has been used.");for(var o in r){if(o==="storeName"&&(r[o]=r[o].replace(/\W/g,"_")),o==="version"&&typeof r[o]!="number")return new Error("Database version must be a number.");this._config[o]=r[o]}return"driver"in r&&r.driver?this.setDriver(this._config.driver):!0}else return typeof r=="string"?this._config[r]:this._config},e.prototype.defineDriver=function(r,o,a){var n=new v(function(i,f){try{var u=r._driver,s=new Error("Custom driver not compliant; see https://mozilla.github.io/localForage/#definedriver");if(!r._driver){f(s);return}for(var c=me.concat("_initStorage"),d=0,m=c.length;d<m;d++){var b=c[d],w=!Cr(ae,b);if((w||r[b])&&typeof r[b]!="function"){f(s);return}}var R=function(){for(var ee=function(Yr){return function(){var $r=new Error("Method "+Yr+" is not implemented by the current driver"),$e=v.reject($r);return h($e,arguments[arguments.length-1]),$e}},ge=0,Ur=ae.length;ge<Ur;ge++){var pe=ae[ge];r[pe]||(r[pe]=ee(pe))}};R();var I=function(ee){k[u]&&console.info("Redefining LocalForage driver: "+u),k[u]=r,Ye[u]=ee,i()};"_support"in r?r._support&&typeof r._support=="function"?r._support().then(I,f):I(!!r._support):I(!0)}catch(U){f(U)}});return D(n,o,a),n},e.prototype.driver=function(){return this._driver||null},e.prototype.getDriver=function(r,o,a){var n=k[r]?v.resolve(k[r]):v.reject(new Error("Driver not found."));return D(n,o,a),n},e.prototype.getSerializer=function(r){var o=v.resolve(he);return D(o,r),o},e.prototype.ready=function(r){var o=this,a=o._driverSet.then(function(){return o._ready===null&&(o._ready=o._initDriver()),o._ready});return D(a,r,r),a},e.prototype.setDriver=function(r,o,a){var n=this;Ue(r)||(r=[r]);var i=this._getSupportedDrivers(r);function f(){n._config.driver=n.driver()}function u(d){return n._extend(d),f(),n._ready=n._initStorage(n._config),n._ready}function s(d){return function(){var m=0;function b(){for(;m<d.length;){var w=d[m];return m++,n._dbInfo=null,n._ready=null,n.getDriver(w).then(u).catch(b)}f();var R=new Error("No available storage method found.");return n._driverSet=v.reject(R),n._driverSet}return b()}}var c=this._driverSet!==null?this._driverSet.catch(function(){return v.resolve()}):v.resolve();return this._driverSet=c.then(function(){var d=i[0];return n._dbInfo=null,n._ready=null,n.getDriver(d).then(function(m){n._driver=m._driver,f(),n._wrapLibraryMethodsWithReady(),n._initDriver=s(i)})}).catch(function(){f();var d=new Error("No available storage method found.");return n._driverSet=v.reject(d),n._driverSet}),D(this._driverSet,o,a),this._driverSet},e.prototype.supports=function(r){return!!Ye[r]},e.prototype._extend=function(r){ye(this,r)},e.prototype._getSupportedDrivers=function(r){for(var o=[],a=0,n=r.length;a<n;a++){var i=r[a];this.supports(i)&&o.push(i)}return o},e.prototype._wrapLibraryMethodsWithReady=function(){for(var r=0,o=me.length;r<o;r++)Pr(this,me[r])},e.prototype.createInstance=function(r){return new e(r)},e}(),Mr=new Fr;P.exports=Mr},{3:3}]},{},[4])(4)})})(ze);var Wr=ze.exports;const Z=zr(Wr),Vr=()=>({createData:async p=>{let E=0;for(;E<3;)try{Z.setItem(p.id,p);return}catch(g){const T=g.message??"";E+=1,E===3&&(console.error("error",g),re({type:"error",message:`新增数据缓存失败${T}`}))}},initLocalForage:()=>{Z.config({driver:Z.INDEXEDDB,name:"index_db_util",storeName:"airport"})},queryData:p=>Z.getItem(p).catch(E=>{const g=E.message??"";return re({type:"error",message:`查询数据缓存失败${g}`}),console.error("error",E),null}),deleteData:async p=>{Z.removeItem(p).catch(E=>{const g=E.message??"";console.error("error",E),re({type:"error",message:`新增数据缓存失败${g}`})})},deleteAllData:async()=>{Z.clear().then(()=>{re({type:"success",message:"缓存数据清理完成"})}).catch(p=>{const E=p.message??"";console.error("error",p),re({type:"error",message:`新增数据缓存失败${E}`})})}}),{createData:Hr,queryData:Kr,initLocalForage:_e,deleteData:Qr}=Vr(),Zr=async(K,te,L)=>{await _e(),Hr({id:K,localData:te,updateTime:L||0})},qr=async K=>(await _e(),await Kr(K)),kr=async K=>{await _e(),await Qr(K)};export{kr as d,qr as g,Z as l,Zr as s};
