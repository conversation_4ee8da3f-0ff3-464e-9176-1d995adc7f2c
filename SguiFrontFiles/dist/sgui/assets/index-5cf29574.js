import{f as c,g as _,fX as h,j as n,m as r,G as t,l as e,_ as u,B as g,D as k,h as f,U as v,v as p,x as $,p as i,n as B,H as C,z as w,A as N}from"./index-5ab303b6.js";const V={viewBox:"0 0 79 86",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"},x=["id"],E=["stop-color"],G=["stop-color"],R=["id"],S=["stop-color"],b=["stop-color"],z=["id"],I={id:"Illustrations",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},D={id:"B-type",transform:"translate(-1268.000000, -535.000000)"},M={id:"Group-2",transform:"translate(1268.000000, 535.000000)"},P=["fill"],U=["fill"],j={id:"Group-Copy",transform:"translate(34.500000, 31.500000) scale(-1, 1) rotate(-25.000000) translate(-34.500000, -31.500000) translate(7.000000, 10.000000)"},A=["fill"],H=["fill"],L=["fill"],O=["fill"],T=["fill"],X={id:"Rectangle-Copy-17",transform:"translate(53.000000, 45.000000)"},Z=["fill","xlink:href"],q=["fill","mask"],F=["fill"],J=c({name:"ImgEmpty"}),K=c({...J,setup(d){const s=_("empty"),l=h();return(a,m)=>(n(),r("svg",V,[t("defs",null,[t("linearGradient",{id:`linearGradient-1-${e(l)}`,x1:"38.8503086%",y1:"0%",x2:"61.1496914%",y2:"100%"},[t("stop",{"stop-color":`var(${e(s).cssVarBlockName("fill-color-1")})`,offset:"0%"},null,8,E),t("stop",{"stop-color":`var(${e(s).cssVarBlockName("fill-color-4")})`,offset:"100%"},null,8,G)],8,x),t("linearGradient",{id:`linearGradient-2-${e(l)}`,x1:"0%",y1:"9.5%",x2:"100%",y2:"90.5%"},[t("stop",{"stop-color":`var(${e(s).cssVarBlockName("fill-color-1")})`,offset:"0%"},null,8,S),t("stop",{"stop-color":`var(${e(s).cssVarBlockName("fill-color-6")})`,offset:"100%"},null,8,b)],8,R),t("rect",{id:`path-3-${e(l)}`,x:"0",y:"0",width:"17",height:"36"},null,8,z)]),t("g",I,[t("g",D,[t("g",M,[t("path",{id:"Oval-Copy-2",d:"M39.5,86 C61.3152476,86 79,83.9106622 79,81.3333333 C79,78.7560045 57.3152476,78 35.5,78 C13.6847524,78 0,78.7560045 0,81.3333333 C0,83.9106622 17.6847524,86 39.5,86 Z",fill:`var(${e(s).cssVarBlockName("fill-color-3")})`},null,8,P),t("polygon",{id:"Rectangle-Copy-14",fill:`var(${e(s).cssVarBlockName("fill-color-7")})`,transform:"translate(27.500000, 51.500000) scale(1, -1) translate(-27.500000, -51.500000) ",points:"13 58 53 58 42 45 2 45"},null,8,U),t("g",j,[t("polygon",{id:"Rectangle-Copy-10",fill:`var(${e(s).cssVarBlockName("fill-color-7")})`,transform:"translate(11.500000, 5.000000) scale(1, -1) translate(-11.500000, -5.000000) ",points:"2.84078316e-14 3 18 3 23 7 5 7"},null,8,A),t("polygon",{id:"Rectangle-Copy-11",fill:`var(${e(s).cssVarBlockName("fill-color-5")})`,points:"-3.69149156e-15 7 38 7 38 43 -3.69149156e-15 43"},null,8,H),t("rect",{id:"Rectangle-Copy-12",fill:`url(#linearGradient-1-${e(l)})`,transform:"translate(46.500000, 25.000000) scale(-1, 1) translate(-46.500000, -25.000000) ",x:"38",y:"7",width:"17",height:"36"},null,8,L),t("polygon",{id:"Rectangle-Copy-13",fill:`var(${e(s).cssVarBlockName("fill-color-2")})`,transform:"translate(39.500000, 3.500000) scale(-1, 1) translate(-39.500000, -3.500000) ",points:"24 7 41 7 55 -3.63806207e-12 38 -3.63806207e-12"},null,8,O)]),t("rect",{id:"Rectangle-Copy-15",fill:`url(#linearGradient-2-${e(l)})`,x:"13",y:"45",width:"40",height:"36"},null,8,T),t("g",X,[t("use",{id:"Mask",fill:`var(${e(s).cssVarBlockName("fill-color-8")})`,transform:"translate(8.500000, 18.000000) scale(-1, 1) translate(-8.500000, -18.000000) ","xlink:href":`#path-3-${e(l)}`},null,8,Z),t("polygon",{id:"Rectangle-Copy",fill:`var(${e(s).cssVarBlockName("fill-color-9")})`,mask:`url(#mask-4-${e(l)})`,transform:"translate(12.000000, 9.000000) scale(-1, 1) translate(-12.000000, -9.000000) ",points:"7 0 24 0 20 18 7 16.5"},null,8,q)]),t("polygon",{id:"Rectangle-Copy-18",fill:`var(${e(s).cssVarBlockName("fill-color-2")})`,transform:"translate(66.000000, 51.500000) scale(-1, 1) translate(-66.000000, -51.500000) ",points:"62 45 79 45 70 58 53 58"},null,8,F)])])])]))}});var Q=u(K,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/empty/src/img-empty.vue"]]);const W=g({image:{type:String,default:""},imageSize:Number,description:{type:String,default:""}}),Y=["src"],t0={key:1},e0=c({name:"ElEmpty"}),s0=c({...e0,props:W,setup(d){const s=d,{t:l}=k(),a=_("empty"),m=f(()=>s.description||l("el.table.emptyText")),y=f(()=>({width:v(s.imageSize)}));return(o,o0)=>(n(),r("div",{class:i(e(a).b())},[t("div",{class:i(e(a).e("image")),style:B(e(y))},[o.image?(n(),r("img",{key:0,src:o.image,ondragstart:"return false"},null,8,Y)):p(o.$slots,"image",{key:1},()=>[$(Q)])],6),t("div",{class:i(e(a).e("description"))},[o.$slots.description?p(o.$slots,"description",{key:0}):(n(),r("p",t0,C(e(m)),1))],2),o.$slots.default?(n(),r("div",{key:0,class:i(e(a).e("bottom"))},[p(o.$slots,"default")],2)):w("v-if",!0)],2))}});var l0=u(s0,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/empty/src/empty.vue"]]);const n0=N(l0);export{n0 as E};
