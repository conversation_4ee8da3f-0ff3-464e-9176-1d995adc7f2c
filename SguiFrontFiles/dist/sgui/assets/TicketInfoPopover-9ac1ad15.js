import{h9 as O,i as U,ha as $,hb as G,hc as V,hd as x,he as j,hf as z,aR as Y,a$ as q,R as J,ab as K,r as v,aW as W,ac as H,bh as Q,aX as X,bg as Z,eK as ee,eL as te,q as oe,x as f,y as A,z as _,B as T,G as B,P as I,Q as b,A as p,bs as E,E as se,D as ne,J as D,ak as ae,b3 as F,ai as re,aj as ce,al as ie,am as pe,an as le}from"./index-a2fbd71b.js";import{q as de}from"./dictApi-14e8db9b.js";import{a as ue}from"./add-2f19224f.js";import{x as fe}from"./TicketPopItem.vue_vue_type_script_setup_true_lang-eb783de8.js";import{E as me,a as ke}from"./index-8ff7f67e.js";import{E as ye}from"./index-35a05a15.js";import{_ as _e}from"./_plugin-vue_export-helper-c27b6911.js";var Te="[object Map]",be="[object Set]",ge=Object.prototype,he=ge.hasOwnProperty;function He(e){if(e==null)return!0;if(O(e)&&(U(e)||typeof e=="string"||typeof e.splice=="function"||$(e)||G(e)||V(e)))return!e.length;var t=x(e);if(t==Te||t==be)return!e.size;if(j(e))return!z(e).length;for(var n in e)if(he.call(e,n))return!1;return!0}const ve=/[\u4e00-\u9fa5]+/,L=/\(UM\d+\)$/,Ee=/[A-Za-z]+$/,M=/^\(UM\d+\)$/;let C=[];const Ie=(e,t)=>!e||e===""||!J((e==null?void 0:e.updateTime)??"").isSame(t,"day"),Pe=async()=>{var s;const e=await Y("PASSENGER_SPECIAL_TYPE_DATA"),t=new Date().getTime();if(Ie(e,t)){const r={pageNumber:1,pageSize:500,content:{dictCode:"",dictName:"",dictTypeCode:"PASSENGER_TYPE_DETAIL"}},{data:c}=await de(r),l=((s=c==null?void 0:c.value)==null?void 0:s.content)??"";return await q("PASSENGER_SPECIAL_TYPE_DATA",JSON.stringify(l),t),l}return JSON.parse(e==null?void 0:e.localData)},Ne=async(e,t)=>{let n=t??"";if(n!=null&&n.includes("UM")){if(!M.test(t))return e;n="UM"}const s=(C??[]).find(r=>r.field1===e&&r.field2===n);return(s==null?void 0:s.field3)??e},we=async(e,t)=>{const n=t??"";if(t.includes("UM")&&M.test(t))return t;const s=(C??[]).find(r=>r.field1===e&&r.field2===n);return(s==null?void 0:s.field2)??""},Ce=e=>{const t=e.match(L);return t?t[0]:""},Re=e=>{const t=e.match(L);if(t)return t[0];const n=e.match(Ee);return n&&C.some(r=>r.field2===n[0])?n[0]:""},Se=async(e,t,n)=>{var l;let s="",r="",c="";if(n){const m=e.lastIndexOf(n)===-1?e.length:e.lastIndexOf(n);r=(l=e==null?void 0:e.substring(0,m))==null?void 0:l.trim(),c=n,s=await Ne(t,c)}else r=e,s=t;return{specialPassengerType:s,fullName:r,nameSuffix:c}},Qe=async(e,t)=>{if(!e.trim())return{};const n=e.trim();let s;if(C=await Pe(),ve.test(n))s=Re(e);else if(s=Ce(e),!s){const c=e.split("/")[1],l=c==null?void 0:c.lastIndexOf(" ");l>-1&&(s=await we(t,c.substring(l+1)))}return await Se(e,t,s??"")},Ae=(e,t)=>{var w;const{t:n}=K(),s=v([]),{copy:r,isSupported:c}=W({legacy:!0}),l=v((w=e.tktInfo)==null?void 0:w.showTktPopover),m=v(""),P=v(),y=v(e.secondFactor??{}),N=()=>{l.value=!1,s.value=[],t("close-popover",e.tktIndex)},R=()=>{const o=e.formTicket;if(o.secondFactorValue){if(o.secondFactorType==="PNR"&&!Z.test(o.secondFactorValue))return!1;if(o.secondFactorType==="name"&&!ee.test(o.secondFactorValue))return!1;if(o.secondFactorType==="certificate"&&o.secondFactorCode==="NI"&&!te.test(o.secondFactorValue))return!1}else return o.secondFactorType==="PNR"||o.secondFactorType==="name",!1;return!0},g=async()=>{var o,u;if(e.isOldFare&&(t("fare-vaild"),!R()))return N(),!1;setTimeout(()=>{var a,k,h;(h=(k=(a=P.value)==null?void 0:a.popperRef)==null?void 0:k.popperInstanceRef)==null||h.forceUpdate()},100),s.value=((o=e.conjunctionTicketNos)==null?void 0:o.length)>0?e.conjunctionTicketNos:[((u=e.tktInfo)==null?void 0:u.etNumber)||i(e.ticketNumber)],m.value=s.value[0]},i=o=>{const u=(o??"").match(Q);return(u==null?void 0:u[1])??""},d=(o,u)=>{if(!s.value.includes(o))for(let a=0;a<u;a++){const k=`${o.slice(0,o.length-10)}${String(ue(Number(o.slice(-10)),a)).padStart(10,"0")}`;s.value.push(k)}m.value=o},S=o=>{c&&(r(o),X({message:n("app.batchRefund.copySuccess"),type:"success"}))};return H(()=>{var o;return(o=e.tktInfo)==null?void 0:o.showTktPopover},()=>{var o;l.value=(o=e.tktInfo)==null?void 0:o.showTktPopover},{immediate:!0,deep:!0}),{showTktPopover:l,closePopover:N,tickets:s,openPopover:g,addTicketTab:d,activeName:m,showTktRef:P,copyInfo:S,ticketSecondFactor:y}},Fe=Ae,Be=e=>(pe("data-v-94eef4cb"),e=e(),le(),e),De={key:0},Le={class:"font-bold text-base"},Me={key:1,class:"text-brand-2 text-base font-bold leading-normal cursor-pointer"},Oe={key:2,class:"text-brand-2 text-xs font-bold leading-tight cursor-pointer w-[125px]"},Ue={key:3,class:"text-brand-2 text-xs font-normal leading-tight cursor-pointer","data-gid":"02080114"},$e={class:"ticket-no cursor-pointer mr-5 text-sm"},Ge=Be(()=>I("i",{class:"iconfont icon-close"},null,-1)),Ve=[Ge],xe=oe({__name:"TicketInfoPopover",props:{tktInfo:{},isInternational:{type:Boolean},tktIndex:{},ticketNumber:{},secondFactor:{},outClass:{default:""},refundClassType:{},level:{default:1},queryType:{},conjunctionTicketNos:{},ticketNumberColorClass:{},prefix:{},title:{type:Boolean},isCdsTicket:{type:Boolean},formTicket:{},isOldFare:{type:Boolean,default:!1}},emits:["close-popover"],setup(e,{emit:t}){const n=e,s=t,{showTktPopover:r,closePopover:c,tickets:l,openPopover:m,addTicketTab:P,activeName:y,showTktRef:N,copyInfo:R,ticketSecondFactor:g}=Fe(n,s);return(i,d)=>{const S=ie,w=me,o=ke,u=ye;return f(),A(u,{ref_key:"showTktRef",ref:N,visible:p(r),"onUpdate:visible":d[4]||(d[4]=a=>F(r)?r.value=a:null),placement:"right",teleported:!0,"popper-class":`tkt-crs-popper ${i.outClass}tkt-crs-popper${i.tktIndex} max-h-[calc(100vh_-_20px)]`,"popper-options":{modifiers:[{name:"preventOverflow",options:{padding:10,rootBoundary:"viewport"}}]},width:792,trigger:"click",onBeforeLeave:p(c),onBeforeEnter:p(m)},{reference:_(()=>{var a;return[(a=i.tktInfo)!=null&&a.etNumber?(f(),T("span",De,[B(S,{link:"",type:"primary"},{default:_(()=>[I("span",Le,b(p(E)(i.tktInfo.etNumber)),1)]),_:1}),I("em",{class:"iconfont icon-copy text-brand-2 relative top-[2px] cursor-pointer",onClick:d[0]||(d[0]=se(k=>{var h;return p(R)(((h=i.tktInfo)==null?void 0:h.etNumber)??"")},["stop"]))})])):i.refundClassType==="0"?(f(),T("span",Me,b(p(E)(i.ticketNumber??"")),1)):i.refundClassType==="1"?(f(),T("span",Oe,b(p(E)(i.ticketNumber??"")),1)):(f(),T("span",Ue,[i.prefix?(f(),T("span",{key:0,class:ne(["prefix",i.ticketNumberColorClass])},b(i.prefix),3)):D("",!0),ae(" "+b(i.title&&i.ticketNumber?i.$t("app.fareQuery.freightate.details"):p(E)(i.ticketNumber??"")),1)]))]}),default:_(()=>[B(o,{modelValue:p(y),"onUpdate:modelValue":d[2]||(d[2]=a=>F(y)?y.value=a:null),type:"border-card",class:"demo-tabs"},{default:_(()=>[(f(!0),T(re,null,ce(p(l),a=>(f(),A(w,{key:a,name:a},{label:_(()=>[I("span",$e,b(p(E)(a)),1)]),default:_(()=>[p(y)===a?(f(),A(fe,{key:0,secondFactor:p(g),"onUpdate:secondFactor":d[1]||(d[1]=k=>F(g)?g.value=k:null),"ticket-number":a,"is-show-window":!1,onClickTicketNo:p(P),onOpenRtktDetailWindow:p(c)},null,8,["secondFactor","ticket-number","onClickTicketNo","onOpenRtktDetailWindow"])):D("",!0)]),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"]),I("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-6 z-10",onClick:d[3]||(d[3]=(...a)=>p(c)&&p(c)(...a))},Ve)]),_:1},8,["visible","popper-class","onBeforeLeave","onBeforeEnter"])}}});const Xe=_e(xe,[["__scopeId","data-v-94eef4cb"]]);export{Xe as T,Pe as a,Qe as g,He as i};
