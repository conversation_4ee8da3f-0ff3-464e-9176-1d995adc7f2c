import{ao as D,b2 as q,bS as L,ab as V,r,bv as j,iM as G,aY as O,at as Q,aG as _,cJ as z,q as K,x as F,y as J,z as R,a5 as X,B as Y,G as b,A as c,D as Z,au as H,P as W,ak as I,Q as P,ah as ee,al as ae,aZ as te,w as S,iN as se,aX as k}from"./index-a2fbd71b.js";import{E as le,a as ne}from"./index-96be5bee.js";import{E as oe}from"./index-d88b3135.js";const ie=(e,t)=>D(`${q}/queue/delete`,{headers:{gid:t}}).post(e).json(),Ee=(e,t)=>D(`${q}/queue/crs/delete`,{headers:{gid:t}}).post(e).json(),xe=(e,t)=>D(`${q}/queue/getQueueInfo`,{headers:{gid:t}}).post(e).json(),Se=(e,t)=>D(`${q}/queue/getcrsq`,{headers:{gid:t}}).post(e).json(),ce=(e,t)=>L.post(`${q}/queue/sendQ`,e,{headers:{gid:t}}),re=(e,t)=>L.post(`${q}/queue/moveQ`,e,{headers:{gid:t}}),$e=(e,t)=>D(`${q}/queue/crs/create`,{headers:{gid:t}}).post(e).json(),we=(e,t)=>D(`${q}/queue/crs/update`,{headers:{gid:t}}).post(e).json(),ue=(e,t)=>{const{t:l}=V(),m=r(!1),g=r(),p=r({officeNum:e.office??"",mailName:""}),C={officeNum:[{required:!0,message:l("app.intlPassengerForm.required"),trigger:"blur"},{pattern:j,message:l("app.qMessage.agentErrorTips"),trigger:"blur"}],mailName:[{required:!0,message:l("app.intlPassengerForm.required"),trigger:"blur"},{pattern:G,message:l("app.qMessage.agentErrorTips"),trigger:"blur"}]},i=()=>t("update:modelValue",!1);return{FORM_RULES:C,formRef:g,closeDialog:i,relayForm:p,handleSave:()=>{var N;(N=g.value)==null||N.validate(async u=>{if(u)try{m.value=!0;const o={office:p.value.officeNum,queueNm:p.value.mailName};e.type==="relay"?(o.qid=e.relayData.qid,o.orgQueueNm=e.queueNm,o.orgOffice=e.relayData.office):(o.qcontent=e.relayData.content,o.qtype=e.relayData.qtype);const y=O("122C0108"),f=O("122C0109"),s=e.type==="relay"?await await re(o,y):await await ce(o,f);if(s.data==="OK"){m.value=!1;const h=e.type==="relay"?l("app.qMessage.relaySuccess"):l("app.qMessage.sendSuccess");await Q.confirm(_("div",{class:"batch-delete-tip-box"},h),{icon:_("em",{class:"iconfont icon-check-circle text-green-2"}),customClass:"crs-btn-ui crs-btn-message-ui",confirmButtonText:l("app.button.ensure"),showCancelButton:!1,showClose:!1}).then(()=>{i(),t("reSearch")})}else z(s==null?void 0:s.code,s==null?void 0:s.extResponseIDList,s==null?void 0:s.transactionID,s==null?void 0:s.time,s==null?void 0:s.firstError,s==null?void 0:s.satTransactionID)}finally{m.value=!1}})},handleCancel:()=>{i()},loading:m}},de=ue,me={class:"text-center mt-2.5"},Oe=K({__name:"RelayDialog",props:{queueNm:{},type:{},relayData:{},office:{}},emits:["update:modelValue","reSearch"],setup(e,{emit:t}){const l=e,m=t,{formRef:g,closeDialog:p,FORM_RULES:C,relayForm:i,handleSave:A,handleCancel:d,loading:N}=de(l,m);return(u,o)=>{const y=ee,f=le,s=ne,h=ae,M=oe,$=te;return F(),J(M,{class:"relay-dialog",width:"680px",title:u.type==="relay"?u.$t("app.qMessage.relayTitle"):u.$t("app.qMessage.sendTitle"),onClose:c(p)},{default:R(()=>[X((F(),Y("div",null,[b(s,{ref_key:"formRef",ref:g,class:"relay-form","label-width":"60px","label-position":"left","require-asterisk-position":"right",rules:c(C),model:c(i)},{default:R(()=>[b(f,{class:Z([c(H)()==="en"?"two-line":""]),label:u.$t("app.qMessage.officeNum"),prop:"officeNum"},{default:R(()=>[b(y,{modelValue:c(i).officeNum,"onUpdate:modelValue":o[0]||(o[0]=v=>c(i).officeNum=v),placeholder:u.$t("app.qMessage.enterOfficeNum"),clearable:"",onInput:o[1]||(o[1]=v=>c(i).officeNum=c(i).officeNum.toUpperCase())},null,8,["modelValue","placeholder"])]),_:1},8,["class","label"]),b(f,{label:u.$t("app.qMessage.mailName"),prop:"mailName"},{default:R(()=>[b(y,{modelValue:c(i).mailName,"onUpdate:modelValue":o[2]||(o[2]=v=>c(i).mailName=v),placeholder:u.$t("app.qMessage.enterMailName"),clearable:"",onInput:o[3]||(o[3]=v=>c(i).mailName=c(i).mailName.toUpperCase())},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["rules","model"]),W("div",me,[b(h,{type:"primary",class:"w-[80px]",onClick:c(A)},{default:R(()=>[I(P(u.$t("app.button.ensure")),1)]),_:1},8,["onClick"]),b(h,{class:"w-[80px]",onClick:c(d)},{default:R(()=>[I(P(u.$t("app.button.cancel")),1)]),_:1},8,["onClick"])])])),[[$,c(N)]])]),_:1},8,["title","onClose"])}}});const ge=(e,t)=>{const{t:l}=V(),m=r(!1),g=r(!1),p=r(""),C=r(""),i=S(()=>e.currentPage??1),A=S(()=>(e.qMailRes??[]).length===20?(e.qMailRes??[]).length-1:(e.qMailRes??[]).length),d=r([]),N=S(()=>(e.qMailRes??[]).length===20?(e.qMailRes??[]).slice(0,-1):e.qMailRes??[]),u=S(()=>e.hasCustomMailAuth),o=r([]),y=r(),f=r(!1),s=r({}),h=r(),M=r({agent:""}),$={agent:[{pattern:se,message:l("app.qMessage.agentErrorTips"),trigger:"blur"}]},v=a=>(a==="pre"||!a)&&i.value===1?!1:!(a==="next"&&(e.qMailRes??[]).length<20),U=a=>{a.length?(g.value=!0,d.value=[],d.value=a.map(n=>n.qid)):(g.value=!1,d.value=[])},T=async a=>{m.value=!0;try{const n={targetOffice:e.office,targetQueueNm:e.queueNm,qids:a==="clear"?[]:d.value,agent:0,xflag:0},w=O("122C0103"),{data:E}=await ie(n,w);if(E.value==="OK"){const x=a==="clear"?l("app.qMessage.clearCustomSuccess",{code:e.queueNm}):l("app.qMessage.deleteSuccess");await k({message:x,type:"success"}),g.value=!1,d.value=[],t("refresh",e.operationAgent??"")}}finally{m.value=!1}};return{isSeleted:g,currentPage:i,totalSize:A,loading:m,qMailData:N,handleSelectionChange:U,deleteOperation:async a=>{if(!a&&!d.value.length)return;const n=l(a?"app.qMessage.deleteTips":"app.qMessage.batchDeleteTips");await Q.confirm(_("div",{class:"batch-delete-tip-box"},n),{icon:_("em",{class:"iconfont icon-info-circle-line text-brand-2"}),customClass:"crs-btn-ui crs-btn-message-ui",confirmButtonText:l("app.button.ensure"),cancelButtonText:l("app.pnrManagement.flight.cancel"),showClose:!1}).then(()=>{a&&(d.value=[])&&d.value.push(a.qid),T("del")})},doCopy:async a=>{const n=document.createElement("input");n.value=a,document.body.appendChild(n),n.select(),document.execCommand("Copy"),n.style.display="none",k({message:l("app.agentReport.tip"),type:"success",duration:2*1e3})},goOrderPage:async a=>{a.isRead||(a.isRead=!a.isRead),p.value=a.content,C.value=a.qid},isAllowTurn:v,turnPre:()=>{if(i.value===1)return;const a=o.value.pop(),n={agent:e.operationAgent?e.operationAgent:M.value.agent,qid:a,page:"P"};t("search",n)},turnNext:()=>{var E,x,B;if((e.qMailRes??[]).length<20)return;const a=(e.qMailRes??[]).length,n=((x=(E=e.qMailRes??[])==null?void 0:E[a-1])==null?void 0:x.qid)??"";o.value.push(((B=e.qMailRes??[])==null?void 0:B[0].qid)??"");const w={agent:e.operationAgent?e.operationAgent:M.value.agent,qid:n,page:"N"};t("search",w)},clickPnr:p,clickQid:C,formRef:y,queryForm:M,FORM_RULES:$,queryAgentQMail:()=>{y.value.validate(a=>{if(!a)return;const n={agent:M.value.agent,qid:"",page:""};t("search",n)})},resetAgent:()=>{M.value.agent=""},relayOperation:(a,n)=>{f.value=!0,s.value=a,h.value=n},showRelayDialog:f,relaySearch:()=>{t&&t("refresh",e.operationAgent??"")},relayData:s,relayType:h,relayOperationForPnrDetail:(a,n)=>{f.value=!0,s.value.qtype=n??"",s.value.content=a??""},clearOperation:async()=>{await Q.confirm(_("div",{class:"batch-delete-tip-box"},l("app.qMessage.clearCustomTips",{code:e.queueNm})),{icon:_("em",{class:"iconfont icon-info-circle-line text-brand-2"}),customClass:"crs-btn-ui crs-btn-message-ui",confirmButtonText:l("app.button.ensure"),cancelButtonText:l("app.pnrManagement.flight.cancel"),showClose:!1}).then(()=>{T("clear")})},hasCustomAuth:u}},Qe=ge;export{Oe as _,Ee as a,xe as b,Se as c,$e as d,we as e,ie as q,Qe as u};
