import{ae as n,af as a}from"./index-5ab303b6.js";const i=e=>n(`${a}/apiRefundTicket/findRefundTicket`,{originalValue:!0}).post(e).json(),o=e=>n(`${a}/apiRefundTicket/findRefundFee`,{originalValue:!0,ignoreError:!0}).post(e).json(),s=e=>n(`${a}/apiRefundTicket/autoRefund`,{originalValue:!0}).post(e).json(),r=e=>n(`${a}/apiRefundTicket/manualRefundTicket`,{originalValue:!0}).post(e).json(),u=e=>n(`${a}/apiRefundTicket/manualRefundTicket`,{originalValue:!0}).post(e).json(),p=e=>n(`${a}/apiRefundTicket/batchManualRefundTicket`,{originalValue:!0}).post(e).json(),c=e=>n(`${a}/pnrManager/deletePnr`).post({pnrNo:e}).json(),d=e=>n(`${a}/apiRefundTicket/previewRefundTicket`).post(e).json(),f=e=>n(`${a}/crs/involuntary/queryPnrMessage`,{ignoreError:!0}).post(e).json(),l=e=>n(`${a}/pnrManager/deletePnrAndDeleteInfantInfo`).post(e).json(),R=e=>n(`${a}/apiRefundTicket/queryRtktDetail`,{ignoreError:!0}).post(e).json(),k=e=>n(`${a}/apiRefundTicket/batchFindRefundFee`).post(e).json();export{f as a,k as b,p as c,s as d,d as e,o as f,u as g,r as m,i as o,l as p,R as q,c as x};
