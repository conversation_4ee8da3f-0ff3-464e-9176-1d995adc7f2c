import{bS as W,bT as k,ao as re,ab as ie,a9 as ne,r as L,aa as pe,w as ue,s as de,aG as ge,aX as Q,aY as le,cJ as oe,R as se,b5 as fe,q as ce,x as F,y as M,z as c,P as t,Q as m,G as h,A as e,ak as P,J as q,D as A,B as K,ai as me,aj as Re,a5 as he,a6 as xe,ah as ve,al as _e,am as be,an as ye}from"./index-a2fbd71b.js";import{m as U}from"./Tip-ea88506b.js";import{E as De}from"./index-3fe919b5.js";import{E as we,a as Fe}from"./index-96be5bee.js";import{E as Ve}from"./index-f34f831f.js";import{E as Ce}from"./index-d88b3135.js";import{_ as <PERSON>}from"./_plugin-vue_export-helper-c27b6911.js";const ke=(p,g)=>W.post(`${k}/tosp/faultreport/upload`,p,{headers:{"Content-Type":"multipart/form-data",gid:g}}),$e=(p,g)=>W.post(`${k}/tosp/faultreport/newFault`,p,{headers:{gid:g}}),yt=(p,g)=>re(`${k}/tosp/faultreport/queryFaultReport`,{headers:{gid:g}}).post(p).json(),Dt=(p,g)=>re(`${k}/tosp/faultreport/updatestatus`,{headers:{gid:g}}).post(p).json(),wt=(p,g)=>W.post(`${k}/tosp/faultreport/feedback`,p,{headers:{gid:g}}),Te=(p,g)=>{var D;const{t:d}=ie(),N=ne(),x=L(),O=L(),b=L(""),V=L(!1),_=pe("bugTipsObject",""),l=ue(()=>N.state.user),G=()=>l.value.securityLevel===1?l.value.mobile:l.value.securityLevel===4?l.value.email:l.value.mobile,R=L({reportTelphone:((D=l.value)==null?void 0:D.mobile)??"",remark:"",imageListVo:[],faultTime:"",exception:"",description:"",transactionCode:"",reportReason:"",gid:""}),$=L({reportTelphone:[{required:!0,message:d("app.faultReport.faultReportingDialog.required"),trigger:"blur"}],remark:[{required:!0,message:d("app.faultReport.faultReportingDialog.required"),trigger:"blur"},{max:250,message:d("app.faultReport.faultReportingDialog.maximum250Characters"),trigger:"blur"}],imageListVo:[{required:!0,message:d("app.faultReport.faultReportingDialog.required"),trigger:"blur"}],faultTime:[{required:!0,message:d("app.faultReport.faultReportingDialog.required"),trigger:"blur"}],exception:[{required:!0,message:d("app.faultReport.faultReportingDialog.required"),trigger:"blur"},{max:50,message:d("app.faultReport.faultReportingDialog.maximumCharacters"),trigger:"blur"}],description:[{required:!0,message:d("app.faultReport.faultReportingDialog.required"),trigger:"blur"},{max:50,message:d("app.faultReport.faultReportingDialog.maximumCharacters"),trigger:"blur"}],transactionCode:[{required:!0,message:d("app.faultReport.faultReportingDialog.required"),trigger:"blur"},{max:50,message:d("app.faultReport.faultReportingDialog.maximumCharacters"),trigger:"blur"}],reportReason:[{required:!0,message:d("app.faultReport.faultReportingDialog.required"),trigger:"blur"}]}),B=de({render(){return ge("em",{class:"iconfont icon-calendar"})}}),z=a=>{var v,w;let i=!1,s=!1;const u=["PNG","JPG","JPEG","BMP","IMAGE/JPEG","IMAGE/BMP"],f=((w=(v=a.raw)==null?void 0:v.type)==null?void 0:w.toUpperCase())??"";return u.forEach(S=>{f.includes(S)&&(i=!0)}),a!=null&&a.size&&a.size<=10*1024*1024&&a.size>0&&(s=!0),i&&s},H=(a,i)=>{R.value.imageListVo=i,z(a)?x.value.validateField("imageListVo"):(Q.error(d("app.faultReport.faultReportingDialog.uploadFailed")),T(a.uid))},T=a=>{var s,u;const i=(s=R.value.imageListVo)==null?void 0:s.findIndex(f=>f.uid===a);(u=R.value.imageListVo)==null||u.splice(i,1),x.value.validateField("imageListVo")},E=a=>a.filter(s=>s.success).map(s=>({imageName:s.fileName,imageNewName:s.fileUrl,imageSize:s.fileSize})),I=()=>{x.value.validate(async a=>{if(!a)return;const i=new FormData;R.value.imageListVo.forEach(f=>{i.append("files",f.raw)});const s=le("081L0132"),u=await ke(i,s);if(u.code==="200")if(u.data.every(v=>v.success===!1))await oe("",d("app.faultReport.faultReportingDialog.ImageUploadFailed"),"",u.time);else{const v={...R.value,imageListVo:E(u.data),faultTimeCn:se().format("YYYY-MM-DD HH:mm:ss"),macAddress:b.value??""},w=le("081L0124");await $e(v,w),x.value.resetFields(),x.value.faultTime="",x.value.imageListVo=[],x.value.remark="",x.value.transactionCode="",x.value.exception="",x.value.description="",g("updateFormFaultReportingForm",{...R.value,faultTime:""}),_.value="",g("update:modelValue",!1),g("handleQuery"),Q.success(d("app.faultReport.faultReportingDialog.reportedSuccessfully"))}else await oe("",d("app.faultReport.faultReportingDialog.ImageUploadFailed"),"",u.time)})},J=a=>{var f;let i=!1;const s=["PNG","JPG","JPEG","BMP","IMAGE/JPEG","IMAGE/BMP"],u=((f=a.type)==null?void 0:f.toUpperCase())??"";return s.forEach(v=>{u.includes(v)&&(i=!0)}),i},j=a=>{if(!V.value)return;const s=a.clipboardData.items,u=s==null?void 0:s[0],f=u==null?void 0:u.getAsFile();if(f&&u.kind==="file"&&J(u)&&R.value.imageListVo.length<3&&f.size<10*1024*1024){const v={raw:f,url:URL.createObjectURL(f)};R.value.imageListVo.push(v),x.value.validateField("imageListVo")}else Q.error(d("app.faultReport.faultReportingDialog.uploadFailed"))},r=()=>{V.value=!1,_.value="",g("update:modelValue",!1),g("updateFormFaultReportingForm",R.value)},o=async()=>{var a;if(b.value=(a=await y())==null?void 0:a.toUpperCase(),p.faultReportingDialogForm){const i={...p.faultReportingDialogForm,faultTime:p.faultReportingDialogForm.faultTime?p.faultReportingDialogForm.faultTime:se().format("YYYY-MM-DD HH:mm:ss")};R.value=i}},X=()=>{V.value=!0},y=async()=>{var s;const a=(s=navigator==null?void 0:navigator.userAgent)==null?void 0:s.toLowerCase();let i="";return a!=null&&a.includes("electron/")&&(i=await window.electronAPI.getAuthInfo()),i&&fe.decode(i)};return{faultReportingRef:x,isClickFaultReportingDom:V,uploadRef:O,FORM_RULES:$,userInfo:l,datePrefix:B,faultReportingForm:R,bugTipsObjectSession:_,macAddress:b,uploadChange:H,handDelImag:T,handleSubmit:I,handlePaste:j,handleClose:r,handleFaultReporting:X,handleOpen:o,contactInformation:G}},Ee=Te,Y=p=>(be("data-v-7859aace"),p=p(),ye(),p),Ie={class:"justify-start text-gray-1 text-lg font-bold leading-normal"},je={class:"w-full self-stretch px-2.5 py-1 bg-gray-8 rounded inline-flex flex-col justify-center items-start"},Se={class:"self-stretch inline-flex justify-start items-center gap-2.5"},Me={class:"justify-center"},Pe={class:"text-gray-4 text-xs font-normal leading-tight"},qe={class:"text-gray-1 text-xs font-normal leading-tight"},Ae={class:"justify-center"},Ue=Y(()=>t("span",{class:"text-gray-4 text-xs font-normal leading-tight"},"OFFICE：",-1)),Ye={class:"text-gray-1 text-xs font-normal leading-tight"},Ne={class:"justify-center flex-wrap flex items-center"},Oe={class:"text-gray-4 text-xs font-normal leading-tight"},Ge={class:"text-gray-1 text-xs font-normal leading-tight truncate w-[78px]"},Be={class:"justify-center flex-wrap flex items-center"},ze={class:"text-gray-4 text-xs font-normal leading-tight"},He={class:"w-[120px] text-gray-1 text-xs font-normal leading-tight max-w-[120px] inline-block align-middle truncate"},Je={class:"self-stretch inline-flex justify-start items-center gap-3.5"},Xe={class:"justify-center flex-wrap flex items-center"},Qe={class:"text-gray-4 text-xs font-normal leading-tight"},Ke={class:"text-gray-1 text-xs font-normal leading-tight w-[100px] inline-block align-middle truncate"},We={class:"justify-center flex-wrap flex items-center"},Ze={class:"text-gray-4 text-xs font-normal leading-tight"},et={class:"text-gray-1 text-xs font-normal leading-tight w-[370px] inline-block align-middle truncate"},tt={class:"form-inline"},at={class:"upload-input-box"},lt={key:0,class:"self-stretch leading-4 justify-start text-gray-5 text-xs font-normal mb-[6px]"},ot=Y(()=>t("br",null,null,-1)),st={class:"flex justify-start items-start flex-wrap"},rt=["src"],it=["onClick"],nt=Y(()=>t("i",{class:"iconfont icon-delete text-brand-2"},null,-1)),pt=[nt],ut=Y(()=>t("div",null,[t("div",{class:"w-5 h-5 border-2 border-gray-6 border-solid rounded-sm text-gray-5 flex justify-center items-center"},[t("span",null,"+")])],-1)),dt={class:"w-64 ml-[68px] mb-[10px] justify-start text-red-1 text-xs font-normal leading-tight"},gt={class:"dialog-footer"},ft=ce({__name:"FaultReportingDialog",props:{faultReportingDialogForm:{}},emits:["update: modelValue","updateFormFaultReportingForm","handleQuery"],setup(p,{emit:g}){const d=p,N=g,{faultReportingRef:x,uploadRef:O,isClickFaultReportingDom:b,FORM_RULES:V,userInfo:_,faultReportingForm:l,datePrefix:G,bugTipsObjectSession:R,macAddress:$,uploadChange:B,handDelImag:z,handleSubmit:H,handlePaste:T,handleClose:E,handleFaultReporting:I,handleOpen:J,contactInformation:j}=Ee(d,N);return(r,o)=>{const X=De,y=we,D=ve,a=Ve,i=Fe,s=_e,u=Ce;return F(),M(u,{width:"680px","custom-class":"fault-reporting-dialog","close-on-click-modal":!1,tabindex:"1",onClose:e(E),onOpen:e(J),onPaste:e(T)},{header:c(()=>[t("div",Ie,m(r.$t("app.faultReport.faultReportingDialog.faultReporting")),1)]),footer:c(()=>[t("span",gt,[h(s,{type:"primary",class:"w-[80px]",onClick:e(H)},{default:c(()=>[P(m(r.$t("app.faultReport.faultReportingDialog.submit")),1)]),_:1},8,["onClick"]),h(s,{class:"w-[80px]",onClick:e(E)},{default:c(()=>[P(m(r.$t("app.personal.cancelConfigure")),1)]),_:1},8,["onClick"])])]),default:c(()=>{var f,v,w,S;return[t("div",null,[t("div",je,[t("div",Se,[t("div",Me,[t("span",Pe,m(r.$t("app.faultReport.faultReportingDialog.jobID"))+"：",1),t("span",qe,m(((f=e(_))==null?void 0:f.agent)||"-"),1)]),t("div",Ae,[Ue,t("span",Ye,m(((v=e(_))==null?void 0:v.defaultOffice)||"-"),1)]),t("div",Ne,[t("span",Oe,m(r.$t("app.faultReport.faultReportingDialog.workContact"))+"：",1),t("div",Ge,[h(U,{"show-val":e(j)()},{default:c(()=>[t("span",null,m(e(j)()||"-"),1)]),_:1},8,["show-val"])])]),t("div",Be,[t("span",ze,m(r.$t("app.faultReport.faultReportingDialog.MACAddress"))+"：",1),t("div",He,[h(U,{"show-val":e($)},{default:c(()=>[t("span",null,m(e($)||"-"),1)]),_:1},8,["show-val"])])])]),t("div",Je,[t("div",Xe,[t("span",Qe,m(r.$t("app.faultReport.faultReportingDialog.nameReporter"))+"：",1),t("div",Ke,[h(U,{"show-val":(w=e(_))==null?void 0:w.employeeName},{default:c(()=>{var n;return[t("span",null,m(((n=e(_))==null?void 0:n.employeeName)||"-"),1)]}),_:1},8,["show-val"])])]),t("div",We,[t("span",Ze,m(r.$t("app.faultReport.faultReportingDialog.reportingUnit"))+"：",1),t("div",et,[h(U,{"show-val":(S=e(_))==null?void 0:S.departmentName},{default:c(()=>{var n;return[t("span",null,m(((n=e(_))==null?void 0:n.departmentName)||"-"),1)]}),_:1},8,["show-val"])])])])]),h(i,{ref_key:"faultReportingRef",ref:x,model:e(l),"label-width":"70px","require-asterisk-position":"right",rules:e(V),inline:"",class:"fault-reporting-form crs-new-ui-init-cls"},{default:c(()=>[t("div",tt,[e(R)?q("",!0):(F(),M(y,{key:0,label:r.$t("app.faultReport.faultReportingDialog.mtbf"),class:"fault-time",prop:"faultTime"},{default:c(()=>[h(X,{modelValue:e(l).faultTime,"onUpdate:modelValue":o[0]||(o[0]=n=>e(l).faultTime=n),type:"datetime","prefix-icon":e(G),clearable:!1,placeholder:r.$t("app.faultReport.faultReportingDialog.enterDate"),format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",onFocus:o[1]||(o[1]=n=>b.value=!1)},null,8,["modelValue","prefix-icon","placeholder"])]),_:1},8,["label"])),h(y,{label:r.$t("app.faultReport.faultReportingDialog.contactInformation"),prop:"reportTelphone",class:A([e(R)?"report-telphone":""])},{default:c(()=>[h(D,{modelValue:e(l).reportTelphone,"onUpdate:modelValue":o[2]||(o[2]=n=>e(l).reportTelphone=n),placeholder:r.$t("app.faultReport.faultReportingDialog.pleaseContact"),onFocus:o[3]||(o[3]=n=>b.value=!1)},null,8,["modelValue","placeholder"])]),_:1},8,["label","class"]),t("div",at,[h(y,{label:r.$t("app.faultReport.faultReportingDialog.faultScreenshot"),class:"textarea-input upload-input h-[238px]",prop:"imageListVo"},{default:c(()=>{var n,Z,ee,te;return[t("div",{class:A(["image-box w-full self-stretch pl-[6px] pt-[6px] pb-0 bg-gray-0 rounded-sm outline outline-1 outline-offset-[-1px] outline-gray-6 gap-1.5 overflow-hidden",(n=e(l).imageListVo)!=null&&n.length?"":"pr-[6px]"]),onClick:o[5]||(o[5]=(...C)=>e(I)&&e(I)(...C))},[(Z=e(l).imageListVo)!=null&&Z.length?q("",!0):(F(),K("div",lt,[P(m(r.$t("app.faultReport.faultReportingDialog.faultScreenshottipOne")),1),ot,P(m(r.$t("app.faultReport.faultReportingDialog.faultScreenshottipTwo")),1)])),t("div",st,[(F(!0),K(me,null,Re(e(l).imageListVo,(C,ae)=>(F(),K("div",{key:ae,class:A(["border border-gray-6  border-solid mb-[6px] mr-[6px] rounded-sm w-[116px] h-[100px] bg-gray-0 relative image-item",ae!==e(l).imageListVo.length-1?"mr-[6px]":""])},[t("img",{class:"w-full h-full",src:C.url},null,8,rt),t("div",{class:"absolute bottom-0 bg-gray-8 w-full h-[20px] flex justify-center items-center cursor-pointer del-icon",onClick:ct=>e(z)(C.uid)},pt,8,it)],2))),128)),he(h(a,{ref_key:"uploadRef",ref:O,"file-list":e(l).imageListVo,"onUpdate:fileList":o[4]||(o[4]=C=>e(l).imageListVo=C),class:A(["add-upload-btn"]),"auto-upload":!1,limit:5,"list-type":"picture-card","show-file-list":!1,"on-change":e(B)},{trigger:c(()=>[ut]),_:1},8,["file-list","on-change"]),[[xe,((te=(ee=e(l))==null?void 0:ee.imageListVo)==null?void 0:te.length)<3]])])],2)]}),_:1},8,["label"]),t("div",dt,m(r.$t("app.faultReport.faultReportingDialog.screenshotsOnce")),1)]),t("div",null,[h(y,{label:r.$t("app.faultReport.faultReportingDialog.faultPhenomenon"),class:"textarea-input",prop:"remark"},{default:c(()=>[h(D,{modelValue:e(l).remark,"onUpdate:modelValue":o[6]||(o[6]=n=>e(l).remark=n),type:"textarea",placeholder:r.$t("app.faultReport.faultReportingDialog.faultPhenomenonTip"),onFocus:o[7]||(o[7]=n=>b.value=!1)},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),e(R)?q("",!0):(F(),M(y,{key:1,label:r.$t("app.faultReport.faultReportingDialog.errorCode"),prop:"exception"},{default:c(()=>[h(D,{modelValue:e(l).exception,"onUpdate:modelValue":o[8]||(o[8]=n=>e(l).exception=n),placeholder:`${r.$t("app.faultReport.faultReportingDialog.example")}: OM-01-33R09`,onFocus:o[9]||(o[9]=n=>b.value=!1)},null,8,["modelValue","placeholder"])]),_:1},8,["label"])),e(R)?q("",!0):(F(),M(y,{key:2,label:r.$t("app.faultReport.faultReportingDialog.transactionNumber"),prop:"transactionCode"},{default:c(()=>[h(D,{modelValue:e(l).transactionCode,"onUpdate:modelValue":o[10]||(o[10]=n=>e(l).transactionCode=n),placeholder:`${r.$t("app.faultReport.faultReportingDialog.example")}: XXXSAT41862025041319073900733488`,onFocus:o[11]||(o[11]=n=>b.value=!1)},null,8,["modelValue","placeholder"])]),_:1},8,["label"]))])]),_:1},8,["model","rules"])])]}),_:1},8,["onClose","onOpen","onPaste"])}}});const Ft=Le(ft,[["__scopeId","data-v-7859aace"]]);export{Ft as F,yt as q,wt as s,Dt as u};
