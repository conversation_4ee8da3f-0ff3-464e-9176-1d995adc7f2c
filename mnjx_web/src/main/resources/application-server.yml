#=============================端口===========================#
server:
  port: 8351
  tomcat:
    max-connections: 200
    max-threads: 300
    uri-encoding: utf-8
    max-swallow-size: 5MB
  servlet:
    context-path: /mnjx
#======================spring配置===============================#
spring:
  # 项目名称
  application:
    name: mnjx_web
  # 个人理解为禁用了开发属性(默认为true，生产我们设置为false)
  devtools:
    add-properties: false
  servlet:
    multipart:
      max-file-size: 10MB  # 单个文件上传的最大大小，这里设置为10MB
      max-request-size: 20MB  # 整个请求（包含所有文件和其他表单数据）的最大大小，这里设置为20MB
#======================配置允许跨域源=============================#
allow:
  origin: ${ALLOW_ORIGIN}