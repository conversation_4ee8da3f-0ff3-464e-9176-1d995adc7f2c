<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.PlaneMapper">


    <select id="retrieveByCond" resultType="com.swcares.obj.vo.PlaneRetrieveVo">
        select
            mpm.plane_model_type ,
            mpm.plane_model_version,
            mp.plane_no,
            ifnull(ma.airline_code,'') as airline_code,
            ifnull(mc.cnd_no,'') as cnd_no,
            mp.is_use,
           ifnull(mp.cnd_id,'') as cnd_id,
           ifnull(mpm.plane_model_id,'') as  plane_model_id,
           mp.plane_id,
           ifnull(ma.airline_id,'')   as airline_id,
           mp.purchase_date
        from
            mnjx_plane mp
                left join mnjx_plane_model mpm on
                mp.plane_model_id = mpm.plane_model_id
                left join mnjx_airline ma on mp.airline_id  = ma.airline_id
                left join mnjx_cnd mc on mc.cnd_id = mp.cnd_id
        where 1=1
        <if test="isUse!=null and isUse!=''">
            and mp.is_use = #{isUse}
        </if>

        <if test="planeNo!=null and planeNo!=''">
            and mp.plane_no like concat('%',#{planeNo},'%')
        </if>

        <if test="airlineCode!=null and airlineCode!=''">
            and ma.airline_code like concat('%',#{airlineCode},'%')
        </if>

        <if test="airlineId!=null and airlineId!=''">
            and ma.airline_id = #{airlineId}
        </if>

        <if test="planeModelId!=null and planeModelId!=''">
            and mp.plane_model_id = #{planeModelId}
        </if>


        order by mp.purchase_date
    </select>
    <select id="findPlaneByCndNo" resultType="com.swcares.entity.MnjxPlane">
        SELECT
        mpm.plane_model_id,
        mc.cnd_id,
        mc.cnd_no,
        mpm.plane_model_type,
        mpm.plane_model_version
        FROM
	mnjx_cnd mc
	LEFT JOIN mnjx_plane_model mpm ON mc.plane_model_id = mpm.plane_model_id
    </select>
    <select id="findPlaneByThr" resultType="com.swcares.entity.MnjxPlane">
        SELECT
        mpm.plane_model_id,
        mc.cnd_id,
        mc.cnd_no,
        mpm.plane_model_type,
        mpm.plane_model_version
        FROM
	mnjx_cnd mc
	LEFT JOIN mnjx_plane_model mpm ON mc.plane_model_id = mpm.plane_model_id
	WHERE mpm.plane_model_type  LIKE '3%'
    </select>
    <select id="findPlaneByNotThr" resultType="com.swcares.entity.MnjxPlane">
        SELECT
mpm.plane_model_id,
mc.cnd_id,
	mc.cnd_no,
	mpm.plane_model_type,
	mpm.plane_model_version
FROM
	mnjx_cnd mc
	LEFT JOIN mnjx_plane_model mpm ON mc.plane_model_id = mpm.plane_model_id
	WHERE mpm.plane_model_type NOT LIKE '3%'
    </select>
    <select id="findPlaneByThrOrARJ" resultType="com.swcares.entity.MnjxPlane">
        SELECT
mpm.plane_model_id,
mc.cnd_id,
	mc.cnd_no,
	mpm.plane_model_type,
	mpm.plane_model_version
FROM
	mnjx_cnd mc
	LEFT JOIN mnjx_plane_model mpm ON mc.plane_model_id = mpm.plane_model_id
	WHERE mpm.plane_model_type  LIKE '3%' OR mpm.plane_model_type  LIKE 'ARJ%'
    </select>

    <insert id="saveBatch">
        INSERT INTO mnjx_plane values
         <foreach collection="ps" item="p" separator=",">
            (#{p.planeId},#{p.airlineId},#{p.planeModelId},#{p.cndId},#{p.planeNo},#{p.isUse},#{p.purchaseDate})
         </foreach>
    </insert>
</mapper>
