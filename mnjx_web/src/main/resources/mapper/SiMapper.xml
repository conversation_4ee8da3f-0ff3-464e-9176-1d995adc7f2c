<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.SiMapper">

    <sql id="selectCol">
      	mo.office_type ,
      	mo.org_id,
		ma.agent_cname agent,
		map.airport_cname airport,
		mal.airline_full_name airline,
		mo.office_no ,
		ms.si_id,
		ms.si_no ,
		ms.si_pid ,
		ml.level_code ,
		ms.si_status,
		ms.remark
    </sql>

    <select id="retrieveListByPage" resultType="com.swcares.obj.vo.SiReturnVo">
        select
        <include refid="selectCol"/>
        from
		mnjx_office mo
		inner join mnjx_si ms on mo.office_id = ms.office_id
		left join  mnjx_agent ma on mo.org_id = ma.agent_id
		left join  mnjx_airport map on mo.org_id = map.airport_id
		left join  mnjx_airline mal on mo.org_id = mal.airline_id
		LEFT JOIN mnjx_level ml on ms.level_id = ml.level_id
		${ew.customSqlSegment}
		order by ms.si_no
	</select>
</mapper>
