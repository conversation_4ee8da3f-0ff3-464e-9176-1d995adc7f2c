<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.OfficeMapper">

    <select id="retrieveListByPage" resultType="com.swcares.obj.vo.OfficeVo">
        SELECT
	        mo.office_Id,
	        mo.office_type,
	        ma.agent_cname,
	        map.airport_cname,
	        mal.airline_full_name,
	        mo.org_id,
	        mo.office_no,
	        mo.office_status
        FROM
	        mnjx_office mo
	    LEFT JOIN mnjx_agent ma ON mo.org_id = ma.agent_id
	    LEFT JOIN mnjx_airport map ON mo.org_id = map.airport_id
	    LEFT JOIN mnjx_airline mal ON mo.org_id = mal.airline_id
	    ${ew.customSqlSegment}
    </select>

</mapper>
