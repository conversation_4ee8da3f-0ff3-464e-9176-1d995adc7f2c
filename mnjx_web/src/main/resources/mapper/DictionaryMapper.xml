<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.DictionaryMapper">
    <resultMap id="dictionaryResultVo" type="com.swcares.obj.vo.DictionaryResultVo">
        <collection property="mnjxDictCodes" ofType="com.swcares.entity.MnjxDictCode"/>
    </resultMap>

    <select id="retrievePageByCond" resultMap="dictionaryResultVo">
        SELECT
        mdt.*,
        mdc.*
        FROM
        mnjx_dict_type mdt,
        mnjx_dict_code mdc
        <where>
            mdt.dict_type_key = mdc.dict_type_key
            <if test="dictionaryQueryVo.dictTypeName">
                AND mdt.dict_type_name like concat('%',#{dictionaryQueryVo.dictTypeName},'%')
            </if>
        </where>
    </select>
</mapper>
