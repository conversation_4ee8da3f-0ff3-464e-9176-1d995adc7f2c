<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.CityMapper">
    <sql id="getCity">
        SELECT
            mci.city_id,
            mci.city_cname,
            mci.city_code,
            mci.city_status,
            mci.city_abbr,
            mco.country_id,
            mco.country_cname,
            ms.state_code,
            ms.state_cname,
            ms.state_id
        FROM
            mnjx_city mci
            LEFT JOIN mnjx_country mco ON mco.country_id = mci.country_id
            LEFT JOIN mnjx_state ms ON mci.state_id = ms.state_id
    </sql>

    <select id="retrievePageByCond" resultType="com.swcares.obj.vo.CityVo">
        <include refid="getCity"/>
        where 1 = 1
        <if test="cityQueryVo.cityCname != null and cityQueryVo.cityCname !=''">
            and mci.city_cname LIKE CONCAT('%',#{cityQueryVo.cityCname},'%')
        </if>
        <if test="cityQueryVo.cityCode != null and cityQueryVo.cityCode !=''">
            and mci.city_code LIKE CONCAT('%',#{cityQueryVo.cityCode},'%')
        </if>
        <if test="cityQueryVo.countryId != null and cityQueryVo.countryId !=''">
            and mci.country_id = #{cityQueryVo.countryId}
        </if>
        <if test="cityQueryVo.stateName != null and cityQueryVo.stateName !=''">
            and (ms.state_cname like CONCAT('%',#{cityQueryVo.stateName},'%') or ms.state_ename like CONCAT('%',#{cityQueryVo.stateName},'%'))
        </if>
        order by mci.city_code
    </select>

</mapper>
