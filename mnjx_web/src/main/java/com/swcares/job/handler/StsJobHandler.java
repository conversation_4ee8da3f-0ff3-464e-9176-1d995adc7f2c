package com.swcares.job.handler;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.StrUtils;
import com.swcares.job.service.IStsJobService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;

/**
 * 使用xxl-job使用实现定时任务，
 * handler就相当于controller层
 * 要同层调用
 * 此被自动任务调用，所用一定 不能删除
 * 不考虑P3C的这个建议
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class StsJobHandler {

    @Resource
    private IStsJobService iStsJobService;

    /**
     * 航班生成的自动任务<br/>
     * 参数说明：<br/>
     * 1、不输入的情况下：开始时间为系统当前时间，结束时间为2个月后的时间<br/>
     * 2、输入开始时间，不输入结束时间的情况下（输入格式【2022-10-01 】）： 开始时间为输入时间，结束时间是基于开始时间2个月后的时间<br/>
     * 3、不输入开始时间，输入结束时间的情况下（输入格式【 :2023-02-28】）：开始时间为系统当前时间，结束时间就是输入的结束时间<br/>
     * 4、输入开始时间，结束时间的情况下（输入格式【2022-10-01 :2023-02-28】）：开始时间就是输入的开始时间，结束时间就是输入的结束时间
     */
    @XxlJob(value = "flightJobHandler")
    public void flightJobHandler() throws UnifiedResultException {
        if (iStsJobService.isExecute("flightJobHandler")) {
            XxlJobHelper.log("任务已执行过，不进行重调");
            XxlJobHelper.handleFail("任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录");
            return;
        }
        log.info(StrUtils.format("{}自动任务开始执行：{}", "航班生成", DateUtil.now()));
        // 传入的参数(都是一个字符串)
        String param = XxlJobHelper.getJobParam().trim();
        try {
            // 获得结束日期
            String[] args = iStsJobService.standardParameterFormatFlight(param);
            // 开始日期
            DateTime startDateTime = DateUtil.parse(args[0]);
            // 结束日期
            DateTime endDateTime = DateUtil.parse(args[1]);
            // 开始日期与结束日期对比
            if (DateUtil.compare(startDateTime, endDateTime) >= 1) {
                throw new UnifiedResultException(StrUtils.format("开始时间{}不能大于结束时间{}", startDateTime, endDateTime));
            }
            iStsJobService.takeEffect(startDateTime, endDateTime);
            // 获取所有待生效的航班
            XxlJobHelper.handleSuccess(StrUtils.format("自动任务成功执行完成:{},传入的参数为：{}", DateUtil.now(), param));
            iStsJobService.recordJobExecute("flightJobHandler");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("自动任务执行发生错误:{}", e.getMessage());
            XxlJobHelper.handleFail(e.getMessage());
            throw new UnifiedResultException(e.getMessage());
        }
    }

    /**
     * 自动生成航班的旅客数据<br/>
     * 参数说明：<br/>
     * 1、不输入的情况下：开始时间为系统当前时间，结束时间为2个月后的时间<br/>
     * 2、输入开始时间，不输入结束时间的情况下（输入格式【2022-10-01::东星】）： 开始时间为输入时间，结束时间是基于开始时间2个月后的时间<br/>
     * 3、不输入开始时间，输入结束时间的情况下（输入格式【:2023-02-28:东星】）：开始时间为系统当前时间，结束时间就是输入的结束时间<br/>
     * 4、输入开始时间，结束时间的情况下（输入格式【2022-10-01:2023-02-28:东星】）：开始时间就是输入的开始时间，结束时间就是输入的结束时间,使用东星数据
     */
    @XxlJob(value = "psgJobHandler")
    public void psgJobHandler() throws UnifiedResultException {
        if (iStsJobService.isExecute("psgJobHandler")) {
            XxlJobHelper.log("任务已执行过，不进行重调");
            XxlJobHelper.handleFail("任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录");
            return;
        }
        log.info(StrUtils.format("{}自动任务开始执行：{}", "旅客生成", DateUtil.now()));
        // 传入的参数(都是一个字符串)
        String param = XxlJobHelper.getJobParam().trim();
        try {
            // 参数列表
            String[] args = iStsJobService.standardParameterFormatPsg(param);
            // 开始日期
            DateTime startDateTime = DateUtil.parse(args[0]);
            // 结束日期
            DateTime endDateTime = DateUtil.parse(args[1]);
            if (DateUtil.compare(startDateTime, endDateTime) >= 1) {
                throw new UnifiedResultException(StrUtils.format("开始时间{}不能大于结束时间{}", startDateTime, endDateTime));
            }
            // 数据源
            String datas = args[2];
            // 获得类路径下的配置文件
            ClassPathResource classPathResource;
            if (StrUtils.containsAnyIgnoreCase(datas, "东星")) {
                log.info("==============开始生成东星的旅客数据====================");
                classPathResource = new ClassPathResource("/psgData/DongXingPassengerDataV1.1.xlsx");
            } else if (StrUtils.containsAnyIgnoreCase(datas, "比赛")) {
                log.info("==============开始生成比赛的旅客数据====================");
                classPathResource = new ClassPathResource("/psgData/RiZhaoCompetitionPassengerDataV1.0.xls");
            } else {
                log.info("==============开始生成通用的旅客数据====================");
                classPathResource = new ClassPathResource("/psgData/PassengerDataV8.0.xlsx");
            }
            InputStream inputStream = classPathResource.getStream();
            iStsJobService.bookingSeats(inputStream, startDateTime, endDateTime);
            XxlJobHelper.handleSuccess(StrUtils.format("自动任务成功执行完成:{},传入的参数为：{}", DateUtil.now(), param));
            iStsJobService.recordJobExecute("psgJobHandler");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("自动任务执行发生错误:{}", e.getMessage());
            XxlJobHelper.handleFail(e.getMessage());
            throw new UnifiedResultException(e.getMessage());
        }
    }

    /**
     * 每天晚11点30分执行
     * 将该天已登机旅客的客票状态从LIFT/BOARDED变成USED
     */
    @XxlJob(value = "ticketStatusChangeToUsedHandler")
    public void ticketStatusChangeToUsedHandler() throws UnifiedResultException {
        log.info(StrUtils.format("{}自动任务开始执行：{}", "客票更新", DateUtil.now()));
        // 传入的参数(都是一个字符串)
        String param = XxlJobHelper.getJobParam().trim();
        try {
            iStsJobService.changeTicketStatus();
            XxlJobHelper.handleSuccess(StrUtils.format("自动任务成功执行完成:{},传入的参数为：{}", DateUtil.now(), param));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("自动任务执行发生错误:{}", e.getMessage());
            XxlJobHelper.handleFail(e.getMessage());
            throw new UnifiedResultException(e.getMessage());
        }
    }

    /**
     * 历史数据清理
     */
    @XxlJob(value = "deleteHistoryDate")
    public void deleteHistoryDate() {
        log.info(StrUtils.format("{}自动任务开始执行：{}", "历史数据清理", DateUtil.now()));
        iStsJobService.deleteHistoryDateGroup();
    }


    /**
     * 登机牌行李牌打印数据清理
     */
    @XxlJob(value = "deletePrintData")
    public void deletePrintData() {
        log.info(StrUtils.format("{}自动任务开始执行：{}", "登机牌行李牌打印数据清理", DateUtil.now()));
        iStsJobService.deletePrintData();
    }
}
