package com.swcares.obj.dto;

import com.swcares.entity.MnjxFlight;
import com.swcares.entity.MnjxTcard;
import com.swcares.entity.MnjxTcardSection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 这个主要用于优化航班生成模块（目前还没使用，等后期来看看）
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlightDto {
    /**
     * 航班
     */
    private MnjxFlight mnjxFlight;

    /**
     * 航班的tcard数据
     */
    private MnjxTcard mnjxTcard;
    /**
     * 航班的tcard详情数据
     */
    private List<MnjxTcardSection> mnjxTcardSections;
}
