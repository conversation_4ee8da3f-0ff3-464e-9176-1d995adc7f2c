package com.swcares.obj.dto;

import cn.hutool.core.date.DateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FlightGroupDto {
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 登机口
     */
    private String gate;
    /**
     * 出发时间
     */
    private String depTime;
    /**
     * 出发时间
     */
    private DateTime depDateTime;
    /**
     * 是否是120名旅客的航班:0无，1有
     */
    private String havePsg;

    /**
     * 屏显预设航班状态：OP正常，XX取消，DELAY延误
     */
    private String screenStatus;

}
