package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/3/23
 */
@Data
@ApiModel("航节计划分页列表请求对象")
public class PlanSectionRetrieveVo {

    @NotNull(message = "当前页码不能为空")
    @ApiModelProperty("当前页码")
    private Integer current;

    @NotNull(message = "每页条数不能为空")
    @ApiModelProperty("每页条数")
    private Integer limit;

    @ApiModelProperty("航班开始日期")
    private String flightDateStart;

    @ApiModelProperty("航班结束日期")
    private String flightDateEnd;

    @ApiModelProperty("航空公司")
    private String airlineCode;

    @ApiModelProperty("航班号")
    private String flightNo;

    @ApiModelProperty("出发航站id")
    private String offAirportId;

    @ApiModelProperty("到达航站id")
    private String arrAirportId;

    @ApiModelProperty("航班状态")
    private String flightStatus;
}
