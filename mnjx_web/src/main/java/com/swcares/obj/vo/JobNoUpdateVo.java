package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Api(tags = "工作号更新Vo(4个字段)")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
public class JobNoUpdateVo extends SiCreateVo {
    @ApiModelProperty(value = "账号与工作号关系ID")
    @NotNull(message = "账号与工作号关系ID不能为空")
    private String accountJobNoId;

    @ApiModelProperty(value = "账号数据ID，这个是账号表的主键")
    @NotNull(message = "账号数据ID不能为空")
    private String accountId;

    @ApiModelProperty(value = "工作号ID")
    @NotNull(message = "工作号ID不能为空")
    private String jobNoId;

    @ApiModelProperty(value = "工作号状态")
    @NotNull(message = "工作号状态")
    private String jobNoStatus;
}
