package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * [查询参数]
 *
 * <AUTHOR> by yaodan
 * 2021/8/25-15:00
 */
@Api(tags = "登机口返回对象")
@Data
public class GateVo {
    @ApiModelProperty(value = "登机口id")
    private String gateId;
    @ApiModelProperty(value = "机场代码")
    private String airportCode;
    @ApiModelProperty(value = "机场id")
    private String airportId;
    @ApiModelProperty(value = "机场区域")
    private String gateArea;

    @ApiModelProperty(value = "机场分区")
    private String gateZone;

    @ApiModelProperty(value = "登机口")
    private String gateNo;
}
