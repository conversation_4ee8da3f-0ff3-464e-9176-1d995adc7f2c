package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Api(tags = "行李，旅客，航空相关信息对象")
@Data
public class LuggageVo {
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "旅客姓名")
    private String name;

    @ApiModelProperty(value = "行李号")
    private String luggageNo;

    @ApiModelProperty(value = "客票号")
    private String ticketNo;
}
