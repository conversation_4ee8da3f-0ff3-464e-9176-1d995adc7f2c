package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 【查询参数】
 *
 * <AUTHOR> by yaodan
 * 2021/8/19-11:22
 */
@Api(tags = "航空公司查询参数")
@Data
@EqualsAndHashCode(callSuper=false)
public class AirlineRetrieveBaseVo {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("国家代码")
    private String countryIso;

    @ApiModelProperty("航空公司二字码")
    private String airlineCode;

    @ApiModelProperty("航空公司三字码")
    private String airlineThreeCode;

    @ApiModelProperty("航空公司状态，1,启用(默认) 0,停用")
    private String airlineStatus = "1";

    @ApiModelProperty(value = "国家表主键ID")
    private String countryId;
}
