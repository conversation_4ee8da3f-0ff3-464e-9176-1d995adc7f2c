package com.swcares.obj.vo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.Date;

/**
 * 【查询参数】
 *
 * <AUTHOR> by yaodan
 * 2021/8/19-11:22
 */
@Api(tags = "Plane查询参数")
@Data
public class PlaneRetrieveVo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "ID")
    private String planeId;

    @ApiModelProperty(value = "飞机号")
    private String planeNo;

    @ApiModelProperty(value = "航空公司ID")
    private String airlineId;

    @ApiModelProperty(value = "乘务队编号")
    private String crewId;

    @ApiModelProperty(value = "机型表ID")
    private String planeModelId;

    @ApiModelProperty(value = "飞机状态")
    private String isUse;

    @ApiModelProperty(value = "采购日期")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate purchaseDate;

    @ApiModelProperty("航空公司二字码")
    private String airlineCode;

    @ApiModelProperty("机型")
    private String planeModelType;

    @ApiModelProperty("版本号")
    private String planeModelVersion;


    @ApiModelProperty("CND号")
    private String cndNo;

    @ApiModelProperty("CND主键ID")
    private String cndId;


}
