package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("航班计划分页列表请求对象")
public class PlanFlightRetrieveVo {

    @NotNull(message = "当前页码不能为空")
    @ApiModelProperty("当前页码")
    private Integer current;

    @NotNull(message = "每页条数不能为空")
    @ApiModelProperty("每页条数")
    private Integer limit;

    @ApiModelProperty("航班开始日期")
    private String flightDateStart;

    @ApiModelProperty("航班结束日期")
    private String flightDateEnd;

    @ApiModelProperty("航空公司")
    private String airlineCode;

    @ApiModelProperty("航班号")
    private String flightNo;

    @ApiModelProperty("航站")
    private String airport;

    @ApiModelProperty("航班状态")
    private String flightStatus;
}
