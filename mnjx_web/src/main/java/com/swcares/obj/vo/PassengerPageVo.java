package com.swcares.obj.vo;

import com.swcares.obj.type.EnumCertificateType;
import com.swcares.obj.type.EnumPassengerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 旅客数据
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PassengerPageVo {
    /**
     * 全名（中文）
     */
    private String fullName;
    /**
     * 证件类型(中文)
     */
    private EnumCertificateType certificateType;
    /**
     * 证件号码
     */
    private String certificateNo;
    /**
     * 旅客类型
     */
    private EnumPassengerType passengerType;
    /**
     * 餐食类型
     */
    private String mealType;
    /**
     * 是否无陪
     */
    private boolean isUm;
    /**
     * 是否VIP
     */
    private boolean isVip;
    /**
     * 特殊服务项
     */
    private Map<Enum, String> ssrInfos;
}
