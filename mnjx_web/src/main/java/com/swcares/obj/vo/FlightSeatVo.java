package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 【航班座位图返回结果】
 *
 * <AUTHOR> by yaodan
 * 2021/10/9-14:59
 */
@Api(tags = "航班座位图返回结果")
@Data
public class FlightSeatVo {

    @ApiModelProperty(value = "飞机机型")
    private String planeType;

    @ApiModelProperty(value = "飞机版本号")
    private String planeVersion;

    @ApiModelProperty(value = "飞机总座位数")
    private String seatTotal;

    @ApiModelProperty(value = "头等舱座位范围")
    private String firstClass;

    @ApiModelProperty(value = "头等舱座位数")
    private String firstSeats;

    @ApiModelProperty(value = "商务舱座位范围")
    private String secondClass;

    @ApiModelProperty(value = "商务舱座位数")
    private String secondSeats;

    @ApiModelProperty(value = "经济舱座位范围")
    private String thirdClass;

    @ApiModelProperty(value = "经济舱座位数")
    private String thirdSeats;

    @ApiModelProperty(value = "详细座位数据")
    private Map<String, Object> seatMap;
}
