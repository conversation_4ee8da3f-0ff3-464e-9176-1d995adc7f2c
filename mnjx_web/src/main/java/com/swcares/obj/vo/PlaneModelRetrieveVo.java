package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * [查询参数]
 *
 * <AUTHOR> by yaodan
 * 2021/8/25-15:59
 */
@Api(tags = "飞机机型查询参数")
@Data
public class PlaneModelRetrieveVo {
    @ApiModelProperty(value = "机型id", example = "1494549521114755074")
    private String planeModelId;
    @ApiModelProperty(value = "机型", example = "A320")
    private String planeModelType;
    @ApiModelProperty(value = "版本号", example = "V3A5X8Y2")
    private String planeModelVersion;
    @ApiModelProperty(value = "机型版本", example = "A320/V3A5X8Y2")
    private String planeModelVersionAndType;

}
