package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/***
 * <AUTHOR>
 */
@Api(tags = "指令权限等级类型查询返回对象")
@Data
public class OrderLevelTypeQueryVo {
    @ApiModelProperty("指令id")
    private String orderId;

    @ApiModelProperty("指令类型")
    private String orderType;

    @ApiModelProperty("指令名")
    private String orderName;

    @ApiModelProperty("权限等级")
    private String levelCode;

    @ApiModelProperty("是否被当前指令绑定:1表示已被绑定")
    private String isBound;
}
