package com.swcares.obj.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName  CabinMoreVo
 * @Description
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CabinMoreVo {
    @ApiModelProperty(value = "舱等")
    private String cabinClass;
    @ApiModelProperty(value = "销售舱位")
    private String sellCabin;
    @ApiModelProperty(value = "座位总数")
    private String seats;
    @ApiModelProperty(value = "舱位状态")
    private String cabinStatus;
    @ApiModelProperty(value = "航节计划编号")
    private String sectionPlanNo;
    @ApiModelProperty(value = "舱位")
    private String cabin;
    @ApiModelProperty(value = "座位数")
    private String seatNum;
    @ApiModelProperty(value = "有效座位")
    private String avliableCount;


}
