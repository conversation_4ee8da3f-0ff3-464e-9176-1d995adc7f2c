package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Api(tags = "票号范围分页查询参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketLimitsRetrieveVo {

    @ApiModelProperty(value = "OFFICE所属机构分类 0:代理人 , 1:机场 , 2:航空公司")
    private String officeType;

    @ApiModelProperty(value = "机构名称")
    private String organizationName;

    @ApiModelProperty(value = "office号")
    private String officeNo;

    @ApiModelProperty(value = "票号")
    private String ticketNumber;
}
