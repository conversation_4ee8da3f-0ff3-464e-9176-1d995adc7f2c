package com.swcares.obj.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FlightTcardVo implements Serializable {

    @ApiModelProperty(value = "TCardId")
    private String tcardId;

    @ApiModelProperty(value = "存储如 D,X12,123这样的日期规则代码，最多4位")
    private String cycle;

    @ApiModelProperty(value = "规则有效起始日期")
    private String startDate;

    @ApiModelProperty(value = "规则有效结束日期")
    private String endDate;

    @ApiModelProperty(value = "N，夜航")
    private String isN;

    @ApiModelProperty(value = "机型")
    private String eqt;

    @ApiModelProperty(value = "版本号")
    private String vers;

    @ApiModelProperty(value = "航班类型:国际/国内")
    private String tType;

    @ApiModelProperty(value = "CND No")
    private String cndNo;

    @ApiModelProperty(value = "CND ID")
    private String cndId;

    @ApiModelProperty(value = "航节信息")
    private List<TcardSectionVo> sections;
}
