package com.swcares.core.security.bypass;

import cn.hutool.http.HttpStatus;
import com.swcares.core.unified.UnifiedResult;
import org.springframework.security.web.session.SessionInformationExpiredEvent;
import org.springframework.security.web.session.SessionInformationExpiredStrategy;
import org.springframework.stereotype.Component;

/**
 * 限制账户登录数量
 *
 * <AUTHOR>
 */
@Component
public class SecuritySessionInformationExpiredStrategy implements SessionInformationExpiredStrategy {
    @Override
    public void onExpiredSessionDetected(SessionInformationExpiredEvent event) {
        UnifiedResult.writeJson(event.getResponse(), UnifiedResult.fail(HttpStatus.HTTP_UNAUTHORIZED, "你的用户已经在其他地方登录了，你被迫下线了"));
    }
}
