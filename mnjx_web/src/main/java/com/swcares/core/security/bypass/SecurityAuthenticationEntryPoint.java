package com.swcares.core.security.bypass;

import cn.hutool.http.HttpStatus;
import com.swcares.core.unified.UnifiedResult;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
/**
 * 未登陆的处理方式
 *
 * <AUTHOR>
 */
@Component
public class SecurityAuthenticationEntryPoint implements AuthenticationEntryPoint {
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException {
        UnifiedResult.writeJson(response, UnifiedResult.fail(HttpStatus.HTTP_UNAUTHORIZED, "未认证"));
    }
}
