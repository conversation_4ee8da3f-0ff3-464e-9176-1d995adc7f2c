package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.obj.vo.PnrRecordQueryVo;
import com.swcares.obj.vo.PnrRecordVo;

/**
 * description：IPnrRecordService
 * <AUTHOR>
 * @date 2021/10/14
*/
public interface IPnrRecordService {
    /**
     * description：分页查询
     * @param current
     * @param limit
     * @param pnrRecordQueryVo 查询参数对象
     * @return  com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.vo.PnrRecordVo>
     * <AUTHOR>
     * @date 2021/10/14
    */
    IPage<PnrRecordVo> retrieveListByPage(long current, long limit, PnrRecordQueryVo pnrRecordQueryVo);
}
