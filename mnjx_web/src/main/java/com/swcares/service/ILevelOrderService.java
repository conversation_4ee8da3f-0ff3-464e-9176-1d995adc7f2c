package com.swcares.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxLevelOrder;

import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2021/8/24-16:14
 */
public interface ILevelOrderService {
    /**
     * 新增指令信息
     *
     * @param mnjxLevelOrder mnjxLevelOrder
     * @return 新增指令信息
     * @throws UnifiedResultException 统一异常
     */
    String createLevelOrder(MnjxLevelOrder mnjxLevelOrder) throws UnifiedResultException;

    /**
     * 根据级别号查询指令
     *
     * @param levelCode levelCode
     * @return 根据级别号查询指令
     */
    List<MnjxLevelOrder> retrieveOrderByLevelCode(Integer levelCode);


}
