package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxAirport;
import com.swcares.entity.MnjxGate;
import com.swcares.obj.vo.GateQueryVo;
import com.swcares.obj.vo.GateVo;

import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2021/8/25-14:58
 */
public interface IGateService {

    /**
     * save
     *
     * @param mnjxGate mnjxGate
     * @return save
     * @throws UnifiedResultException UnifiedResultException
     */
    boolean create(MnjxGate mnjxGate) throws UnifiedResultException;

    /**
     * 条件+分页查询
     *
     * @param page        page
     * @param gateQueryVo gateQueryVo
     * @return 条件+分页查询
     */
    IPage<GateVo> retrievePageByCond(IPage<GateVo> page, GateQueryVo gateQueryVo);

    /**
     * Title: getById
     * Description: 通过ID查询登机口信息<br>
     *
     * @param id id
     * @return {@link MnjxGate}
     * <AUTHOR>
     * @date 2022/2/10 11:23
     */
    MnjxGate retrieveById(String id);

    /**
     * 通过条件查询
     * （查询机场下的所有登机口）
     *
     * @param gateQueryVo gateQueryVo
     * @return 查询机场下的所有登机口
     */
    List<MnjxGate> retrieveByCond(GateQueryVo gateQueryVo);

    /**
     * 通过条件查询所有机场
     *
     * @param airportCode airportCode
     * @return 所有机场
     */
    List<MnjxAirport> retrieveByAirportCode(String airportCode);

    /**
     * modify
     *
     * @param gateCreateVo gateQueryVo
     * @return modify
     * @throws UnifiedResultException UnifiedResultException
     */
    boolean updateById(MnjxGate gateCreateVo) throws UnifiedResultException;

    /**
     * Title: removeByIds
     * Description: 批量删除登机口信息<br>
     *
     * @param ids ids
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2022/2/10 11:24
     */
    boolean deleteByIds(List<String> ids);
}
