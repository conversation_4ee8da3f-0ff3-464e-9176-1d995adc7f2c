package com.swcares.service;

import com.swcares.core.unified.UnifiedResultException;

/**
 * <AUTHOR>
 * @date 2022/10/20
 */
public interface IIssueTicketService {

    /**
     * issue
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @param siNo       siNo
     * @return issue
     * @throws UnifiedResultException 统一异常
     */
    boolean issue(String flightNo, String flightDate, String siNo) throws UnifiedResultException;

    /**
     * issueAllFlight
     *
     * @param limitDate limitDate
     * @return issueAllFlight
     * @throws UnifiedResultException 统一异常
     */
    boolean issueAllFlight(String limitDate) throws UnifiedResultException;
}
