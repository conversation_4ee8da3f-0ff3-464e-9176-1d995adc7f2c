package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.utils.PlaneModelEnum;
import com.swcares.entity.MnjxPlaneModel;
import com.swcares.obj.vo.PlaneModelRetrieveVo;
import com.swcares.obj.vo.PlaneModelVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> by yaodan
 * 2021/8/25-15:58
 */
public interface IPlaneModelService {
    /**
     * Title：createPlaneModel <br>
     * Description：保存方法 <br>
     * author：王磊 <br>
     * date：2021/9/28 14:04 <br>
     *
     * @param mnjxPlaneModel <br>
     * @return <br>
     * @throws UnifiedResultException UnifiedResultException
     */
    boolean create(MnjxPlaneModel mnjxPlaneModel) throws UnifiedResultException;

    /**
     * retrieveById
     *
     * @param id id
     * @return queryById
     */
    PlaneModelVo retrieveById(String id);

    /**
     * 分页+条件查询
     *
     * @param page page
     * @param vo   planeTypeQueryVo
     * @return 分页+条件查询
     */
    IPage<PlaneModelVo> retrieveByCond(IPage<PlaneModelVo> page, PlaneModelRetrieveVo vo);

    /**
     * 查询机型版本号并瓶装成 形如:机型/版本号
     *
     * @return List<MnjxPlaneModel>
     */
    List<PlaneModelRetrieveVo> retrieveVersionAndType();

    /**
     * 查询飞机类型
     *
     * @return 查询飞机类型
     */
    Map<PlaneModelEnum, String> retrieveKind();

    /**
     * Title：updatePlaneModel <br>
     * Description：修改 <br>
     * author：王磊 <br>
     * date：2021/9/28 14:08 <br>
     *
     * @param mnjxPlaneModel <br>
     * @return <br>
     * @throws UnifiedResultException UnifiedResultException
     */
    boolean updateById(com.swcares.entity.MnjxPlaneModel mnjxPlaneModel) throws UnifiedResultException;

    /**
     * Title：deletePlaneModels <br>
     * Description：批量删除 <br>
     * author：王磊 <br>
     * date：2021/9/28 14:07 <br>
     *
     * @param ids <br>
     * @return 批量删除
     * @throws UnifiedResultException 统一异常
     */
    boolean deleteByIds(List<String> ids) throws UnifiedResultException;


}
