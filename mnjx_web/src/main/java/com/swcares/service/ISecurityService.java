package com.swcares.service;

import cn.hutool.captcha.ICaptcha;
import com.swcares.entity.SysUser;

/**
 * 使用security框架，所以登录接口需要继承框架的接口
 *
 * <AUTHOR>
 */
public interface ISecurityService {
    /**
     * 获得当前系统的账户
     *
     * @param username 用户名
     * @return 当前系统账户
     */
    SysUser getUserByUsername(String username);

    /**
     * 线段干扰的验证码
     *
     * @return 线段干扰的验证码
     */
    ICaptcha getLineCaptcha();


    /**
     * getCurrentUserName
     *
     * @return getCurrentUserName
     */
    String getCurrentUserName();

    /**
     * 防重 Token 令牌方案，该方案能保证在不同请求动作下的幂等性，实现逻辑可以看上面写的”防重 Token 令牌”方案
     *
     * @return 幂等性需要使用的token的KEY
     */
    String getIdempotentTokenKey();

}
