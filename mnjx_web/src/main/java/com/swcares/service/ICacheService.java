package com.swcares.service;

import java.util.List;
import java.util.Map;

/**
 * 缓存操作接口
 *
 * <AUTHOR>
 */
public interface ICacheService {
    /**
     * 清理所有的缓存数据
     *
     * @return 清楚是否成功
     */
    boolean clearAll();

    /**
     * retrieveAll
     *
     * @return retrieveAll
     * @throws IllegalAccessException 异常
     */
    Map<String, List<Map<String, Object>>> retrieveAll() throws IllegalAccessException;
}
