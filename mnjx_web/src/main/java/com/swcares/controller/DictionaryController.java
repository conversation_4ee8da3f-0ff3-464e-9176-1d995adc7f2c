package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxCountry;
import com.swcares.entity.MnjxDictCode;
import com.swcares.entity.MnjxDictType;
import com.swcares.obj.vo.CountryQueryVo;
import com.swcares.obj.vo.DictCodeVo;
import com.swcares.obj.vo.DictTypeVo;
import com.swcares.obj.vo.DictionaryQueryVo;
import com.swcares.service.IDictionaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;

/**
 * 数据字典管理接口
 *
 * <AUTHOR>
 */
@Api(tags = "数据字典管理接口")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/dictionary")
public class DictionaryController {

    @Resource
    private IDictionaryService iDictionaryService;


    @ApiOperation("新增数据字典类型")
    @ApiImplicitParam(value = "字典类型", name = "dictionaryTypeVo", paramType = Constant.PARAM_TYPE_BODY, dataTypeClass = DictTypeVo.class)
    @PostMapping("/createDictType")
    public String createDictType(@RequestBody @Valid DictTypeVo dictTypeVo) throws UnifiedResultException {
        MnjxDictType mnjxDictType = new MnjxDictType();
        mnjxDictType.setDictTypeKey(dictTypeVo.getDictTypeKey());
        mnjxDictType.setDictTypeName(dictTypeVo.getDictTypeName());
        mnjxDictType.setDictTypeStatus(BigDecimal.ONE.toString());
        boolean isOk = iDictionaryService.createDictType(mnjxDictType);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }

    @ApiOperation("新增数据字典值")
    @ApiImplicitParam(value = "字典值", name = "dictCodeVo", paramType = Constant.PARAM_TYPE_BODY, dataTypeClass = DictCodeVo.class)
    @PostMapping("/createDictCode")
    public String createDictCode(@RequestBody @Valid DictCodeVo dictCodeVo) throws UnifiedResultException {
        // 构造要保存的对象
        MnjxDictCode mnjxDictCode = iDictionaryService.constructionDictCodeObj(dictCodeVo);
        // 保存对象
        boolean isOk = iDictionaryService.createDictCode(mnjxDictCode);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }

    @ApiOperation("分页查询数据字典类型与数据字典值")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "当前页码",
                    required = true,
                    name = "current",
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    value = "每页记录数",
                    required = true,
                    name = "limit",
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    value = "查询对象",
                    name = "countryQueryVo",
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = CountryQueryVo.class
            )
    })
    @PostMapping("/retrieveListByPage/{current}/{limit}")
    @PageCheck
    public IPage<MnjxCountry> retrievePageByCond(@PathVariable long current,
                                                 @PathVariable long limit,
                                                 @RequestBody DictionaryQueryVo dictionaryQueryVo) {
        return iDictionaryService.retrievePageByCond(new Page<>(current, limit), dictionaryQueryVo);
    }
}
