package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxOrder;
import com.swcares.service.IOrderService;
import com.swcares.obj.vo.OrderQueryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "指令管理")
@SwaggerGroup(SwaggerGroup.GroupType.BASIC)
@RestController
@RequestMapping("/order")
@Slf4j
public class OrderController {

    @Resource
    private IOrderService iOrderService;

    @ApiOperation("新增指令信息")
    @PostMapping("/createOrder")
    public String createOrder(@RequestBody @Valid MnjxOrder mnjxOrder) throws UnifiedResultException {
        return iOrderService.createOrder(mnjxOrder);
    }

    @ApiOperation("查询所有指令信息")
    @PostMapping("/retrieveList")
    public List<MnjxOrder> retrieveList() {
        return iOrderService.retrieveList();
    }

    @ApiOperation("分页查询指令信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "当前页码",
                    name = "current",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    value = "每页记录数",
                    name = "limit",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    name = "orderQueryVo",
                    value = "查询条件",
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = OrderQueryVo.class
            )
    })
    @PostMapping("/retrieveListByPage/{current}/{limit}")
    @PageCheck
    public IPage<MnjxOrder> retrieveListByPage(@PathVariable long current,
                                               @PathVariable long limit,
                                               @RequestBody OrderQueryVo orderQueryVo) {
        return iOrderService.retrieveListByPage(current, limit, orderQueryVo);
    }


    @ApiOperation("根据id获取指令级别信息")
    @GetMapping("/retrieveById/{id}")
    @ApiImplicitParam(
            value = "指令级别id",
            required = true,
            name = "id",
            paramType = Constant.PARAM_TYPE_PATH,
            dataTypeClass = String.class
    )
    public MnjxOrder retrieveById(@PathVariable String id) {
        return iOrderService.retrieveById(id);
    }

    @ApiOperation("获取所有分类后的指令")
    @GetMapping("/retrieveOrderType")
    public List<Map<String, String>> retrieveOrderType() {
        return iOrderService.retrieveOrderType();
    }


    @ApiOperation("修改指令信息")
    @PostMapping("/updateOrder")
    public String updateOrder(@RequestBody MnjxOrder mnjxOrder) throws UnifiedResultException {
        return iOrderService.updateOrder(mnjxOrder);
    }


    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @DeleteMapping("/deleteOrders")
    @ApiImplicitParam(
            value = "指令ID列表",
            required = true,
            name = "ids",
            dataTypeClass = List.class,
            paramType = Constant.PARAM_TYPE_BODY
    )
    public Object deleteOrders(@RequestBody List<String> ids) throws UnifiedResultException {
        return iOrderService.deleteOrders(ids);
    }
}
