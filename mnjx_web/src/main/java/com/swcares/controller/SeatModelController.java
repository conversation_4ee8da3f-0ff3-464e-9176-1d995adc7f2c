package com.swcares.controller;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxSeatModel;
import com.swcares.service.ISeatModelService;
import com.swcares.core.util.ObjectUtils;
import com.swcares.obj.vo.SeatModelCreateVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "座位模型管理")
@SwaggerGroup(SwaggerGroup.GroupType.BASIC)
@RequestMapping(value = "/seatModel")
@RestController
public class SeatModelController {

    @Resource
    private ISeatModelService iSeatModelService;

    @ApiOperation("新增座位模型数据")
    @PostMapping("/create")
    public String create(@RequestBody @Validated SeatModelCreateVo seatModelCreateVo) throws InstantiationException, IllegalAccessException, UnifiedResultException {
        MnjxSeatModel mnjxSeatModel = ObjectUtils.vo2Entity(seatModelCreateVo, MnjxSeatModel.class);
        boolean isOk = iSeatModelService.create(mnjxSeatModel);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }

    @ApiOperation("跟进ID获取座位模型")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "id",
                    name = "id",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_PATH
            )
    })
    @GetMapping("/retrieveById/{id}")
    public MnjxSeatModel retrieveById(@PathVariable("id") String id) {
        return iSeatModelService.retrieveById(id);
    }

    @ApiOperation("更新座位模型数据")
    @PostMapping("/update")
    public String update(@RequestBody @Validated MnjxSeatModel mnjxSeatModel) throws UnifiedResultException {
        boolean isOk = iSeatModelService.update(mnjxSeatModel);
        if (isOk) {
            return Constant.UPDATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.UPDATE_FAIL);
        }
    }

    @ApiOperation("跟进ID删除座位模型")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "id",
                    name = "id",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_PATH
            )
    })
    @GetMapping("/deleteById/{id}")
    public String deleteById(@PathVariable("id") String id) throws UnifiedResultException {
        boolean isOk = iSeatModelService.deleteById(id);
        if (isOk) {
            return Constant.DELETE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.DELETE_FAIL);
        }
    }
}
