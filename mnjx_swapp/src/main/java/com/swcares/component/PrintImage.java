package com.swcares.component;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;

/**
 * description：图片 <br>
 *
 * <AUTHOR> <br>
 * date 2023/06/30 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class PrintImage {

    public void create(Map<String, Object> map) {

        InputStream is = null;
        OutputStream os = null;
        try {
            String path = System.getProperty("user.dir");
            String oldPath = path.endsWith("mnjx_swapp") ? path + "/image/print_old.jpg" : path + "/mnjx_swapp/image/print_old.jpg";
            log.info("line 33 old path:{}", oldPath);
            String newPath = path.endsWith("mnjx_swapp") ? path + "/image/print_new.jpg" : path + "/mnjx_swapp/image/print_new.jpg";
            log.info("line 34 new path:{}", newPath);

            // 原图片
            BufferedImage oldImg = ImageIO.read(new File(oldPath));
            //
            BufferedImage buffImg = new BufferedImage(oldImg.getWidth(null), oldImg.getHeight(null), BufferedImage.TYPE_INT_RGB);

            // 2、得到画笔对象
            Graphics2D g2d = buffImg.createGraphics();
            g2d.clearRect(0, 0, 4000, 4000);
            g2d.drawImage(oldImg.getScaledInstance(oldImg.getWidth(), oldImg.getHeight(), Image.SCALE_SMOOTH), 0, 0, null);

            g2d.setColor(Color.black);
            g2d.setFont(new Font("微软雅黑", Font.PLAIN, 13));
            g2d.drawString(map.get("psgName").toString(), 100, 90);
            g2d.drawString(map.get("IDNO").toString(), 260, 90);
            g2d.drawString(map.get("eiInfo").toString(), 500, 90);
            g2d.drawString(map.get("pnrICs").toString(), 100, 120);

            int segSize = Integer.parseInt(map.get("segSize").toString());
            int y = 0;
            int dstY = 0;
            for (int i = 0; i < segSize; i++) {
                if (i == 0) {
                    y = 155;
                    dstY = 185;
                } else {
                    y = y + 30;
                    dstY = dstY + 30;
                }
                g2d.drawString(map.get("orgName" + i).toString(), 100, y);
                g2d.drawString(map.get("org" + i).toString(), 140, y);
                g2d.drawString(map.get("dstName" + i).toString(), 100, dstY);
                g2d.drawString(map.get("dst" + i).toString(), 140, dstY);
                String fltNo = map.get("fltNo" + i).toString();
                // SA航段组处理
                if ("VOID".equals(fltNo)) {
                    g2d.drawString(fltNo, 280, y);
                } else {
                    g2d.drawString(fltNo.substring(0, 2), 240, y);
                    g2d.drawString(fltNo.substring(2), 280, y);
                    g2d.drawString(map.get("cabinClass" + i).toString(), 340, y);
                    g2d.drawString(map.get("fltDate" + i).toString(), 370, y);
                    g2d.drawString(map.get("time" + i).toString(), 450, y);
                    g2d.drawString(map.get("fareBasis" + i).toString(), 530, y);
                    g2d.drawString(map.get("CABINKG" + i).toString(), 770, y);
                }
                if (i == segSize - 1 && i < 3) {
                    g2d.drawString("VOID", 100, dstY + 30);
                }
            }
            g2d.drawString("VOID", 240, y + 30);
            String fcny = map.get("fcny").toString();
            g2d.drawString(fcny, 240, 280);
            String fcnyPrice = fcny.replace("CNY", "");
            String cnyYq = (String) map.get("cnyYq");
            String cnyCn = (String) map.get("cnyCn");
            if (!"0.00".equals(fcnyPrice)) {
                String tmpYq = cnyYq.replace("CNY", "");
                if ("0.00".equals(tmpYq) || "EXEMPTYQ".equals(tmpYq)) {
                    cnyYq = "YQ EXEMPT";
                } else {
                    cnyYq = "YQ" + tmpYq;
                }

                String temCn = cnyCn.replace("CNY", "");
                if ("0.00".equals(temCn)) {
                    cnyCn = "CN EXEMPT";
                } else {
                    cnyCn = "CN" + temCn;
                }
            } else {
                cnyCn = "CN0.00";
                cnyYq = "YQ0.00";
            }

            g2d.drawString(cnyCn, 340, 280);
            g2d.drawString(cnyYq, 435, 280);
            g2d.drawString(map.get("acny").toString(), 655, 280);

            g2d.drawString(map.get("totalTicketNo").toString(), 140, 305);

            g2d.drawString(map.get("officeNo").toString(), 140, 330);
            g2d.drawString(map.get("agentCode").toString(), 140, 345);
            g2d.drawString("民航行程单测试有限公司", 340, 340);
            g2d.drawString(DateUtil.today(), 710, 340);

            // 9、释放资源
            g2d.dispose();
            // 10、生成图片
            os = Files.newOutputStream(Paths.get(newPath));
            ImageIO.write(buffImg, "JPG", os);
        } catch (Exception e) {
            log.error(e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                if (null != is) {
                    is.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage());
                e.printStackTrace();
            }
            try {
                if (null != os) {
                    os.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage());
                e.printStackTrace();
            }
        }
    }
}
