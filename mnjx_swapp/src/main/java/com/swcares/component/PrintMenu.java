package com.swcares.component;

import javax.swing.*;

/**
 * description：菜单 <br>
 *
 * <AUTHOR> <br>
 * date 2023/06/30 <br>
 * @version v1.0 <br>
 */
public class PrintMenu {

    public JMenuBar createMenu() {
        //菜单栏JMenuBar
        JMenuBar menuBar = new JMenuBar();
        //
        JMenu jmenu1 = new JMenu("文件");
        JMenu jmenu2 = new JMenu("Eterm3服务器连接");
        JMenu jmenu3 = new JMenu("设置");
        JMenu jmenu4 = new JMenu("察看");
        JMenu jmenu5 = new JMenu("帮助");
        JMenu jmenu6 = new JMenu("保存行程单");

        menuBar.add(jmenu1);
        menuBar.add(jmenu2);
        menuBar.add(jmenu3);
        menuBar.add(jmenu4);
        menuBar.add(jmenu5);
        menuBar.add(jmenu6);

        return menuBar;
    }
}
