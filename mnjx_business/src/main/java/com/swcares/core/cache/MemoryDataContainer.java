package com.swcares.core.cache;

import com.swcares.core.util.Constant;
import com.swcares.core.util.ObjectUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;
import java.util.Vector;

/**
 * <AUTHOR>
 */
@Slf4j
@Getter
public class MemoryDataContainer {
    /**
     * 每个office登录的时候，最多允许5个工作号登录
     */
    private final Vector<MemoryDataCabinet> container;

    {
        log.info("{}初始化工作区空间{}", Thread.currentThread().getName(), this);
        container = new Vector<>(5);
        container.add(new MemoryDataCabinet(1, "A", Constant.COMMON_N, null, null));
        container.add(new MemoryDataCabinet(2, "B", Constant.COMMON_N, null, null));
        container.add(new MemoryDataCabinet(3, "C", Constant.COMMON_N, null, null));
        container.add(new MemoryDataCabinet(4, "D", Constant.COMMON_N, null, null));
        container.add(new MemoryDataCabinet(5, "E", Constant.COMMON_N, null, null));
    }


    /**
     * 获取活动的用户空间(登录用户的空间)
     *
     * @return 活动的用户空间
     */
    public MemoryDataCabinet getActiveMemoryDataCabinet() {
        return container.stream()
                // 根据需要从小到大排列
                .sorted(Comparator.comparingInt(MemoryDataCabinet::getSeqNo))
                // 排查未登录过工作好的区域
                .filter(memoryDataCabinet -> ObjectUtils.isNotNull(memoryDataCabinet.getMnjxSi()))
                // 排查登录过工作号，但是非活动的区域
                .filter(memoryDataCabinet -> Constant.COMMON_Y.equalsIgnoreCase(memoryDataCabinet.getActivity()))
                // 找第一个，如果找到了，就直接返回
                .findFirst()
                // 如果没有找到，就返回空对象
                .orElse(null);
    }

    /**
     * 获取空闲的区域
     *
     * @return 空闲的区域
     */
    public MemoryDataCabinet getFreeMemoryDataCabinet() {
        return container.stream()
                // 根据需要从小到达排列
                .sorted(Comparator.comparingInt(MemoryDataCabinet::getSeqNo))
                // 排查登录过工作好的区域
                .filter(memoryDataCabinet -> ObjectUtils.isNull(memoryDataCabinet.getMnjxSi()))
                .findFirst()
                .orElse(null);
    }
}
