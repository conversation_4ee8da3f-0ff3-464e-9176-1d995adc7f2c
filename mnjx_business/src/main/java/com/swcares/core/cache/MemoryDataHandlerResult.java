package com.swcares.core.cache;

import cn.hutool.core.collection.ListUtil;
import com.swcares.core.util.Constant;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MemoryDataHandlerResult {
    /**
     * handler的原有数据
     */
    private String result;
    /**
     * 根据原始记录解析的记录数
     */
    private List<String> records;
    /**
     * 记录PNR相关指令执行回显的记录
     */
    private List<String> pnrRecallRecords;
    /**
     * 当前页号，默认值为0，就是第一页
     */
    private List<Integer> currentPages = ListUtil.toList(BigDecimal.ZERO.intValue());
    /**
     * 总的页码数
     */
    private int totalPage;
    /**
     * 操作的指令（对下一个指令来说就是上次执行指令）
     */
    private String lastAction;

    /**
     * 存储PD SBY参数
     */
    private String pdParamSby;

    /**
     * 存储PD ABC参数
     */
    private String pdParamAbc;

    /**
     * 标识是否需要更改翻页提示+-号的位置
     * true  需要修改，+-号位置改动到最后一行的后面
     * false 按默认的+-号换行展示
     */
    private boolean changePageMarkPosition;

    /**
     * AV翻页跨日期时，日期变化标识
     */
    private boolean dateChange;

    private int pageType = Constant.HALF_SCREEN;

    /**
     * 是否全屏展示
     * @return
     */
    private boolean fullScreen;

    /**
     * 翻页大小随动
     * @return
     */
    private boolean canChangePageSize;
}
