package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.MnjxOffice;
import com.swcares.entity.MnjxPnr;
import com.swcares.entity.MnjxPnrTk;
import com.swcares.entity.MnjxSi;
import com.swcares.obj.dto.PnrTkDto;
import com.swcares.obj.dto.TkDto;
import com.swcares.service.IPnrOperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * pnr操作的相关实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PnrCommandServicePartTk {
    @Resource
    private IPnrOperationService iPnrOperationService;

    public void tk(MemoryDataPnr memoryDataPnr, TkDto tkDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        // 如果内存PNR对象中的PNR为空，则此处新建PNR
        MnjxPnr mnjxPnr;
        if (ObjectUtil.isNotEmpty(memoryDataPnr.getMnjxPnr().getPnrId())) {
            mnjxPnr = memoryDataPnr.getMnjxPnr();
        } else {
            // 新建PNR
            mnjxPnr = iPnrOperationService.createNewPnr(memoryDataPnr, mnjxOffice, mnjxSi);
        }
        List<PnrTkDto> pnrTkDtos = memoryDataPnr.getPnrTkDtos();
        if (CollUtil.isNotEmpty(pnrTkDtos) && pnrTkDtos.stream().anyMatch(t -> !t.isXe())) {
            throw new UnifiedResultException(Constant.DUP_TK);
        }
        PnrTkDto pnrTkDto = new PnrTkDto();
        MnjxPnrTk mnjxPnrTk = new MnjxPnrTk();
        mnjxPnrTk.setPnrId(mnjxPnr.getPnrId());
        BeanUtils.copyProperties(tkDto, mnjxPnrTk);
        mnjxPnrTk.setInputValue(StrUtil.format("{}/{}/{}/{}", tkDto.getPnrTkType(), tkDto.getPlanEtdzTime(), DateUtils.ymd2Com(tkDto.getPlanEtdzDate()), tkDto.getEtdzOffice()));
        pnrTkDto.setMnjxPnrTk(mnjxPnrTk);
        pnrTkDtos.add(pnrTkDto);
    }
}
