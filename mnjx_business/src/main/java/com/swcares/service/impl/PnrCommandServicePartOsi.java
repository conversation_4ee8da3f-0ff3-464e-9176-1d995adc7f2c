package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxAirline;
import com.swcares.entity.MnjxNmOsi;
import com.swcares.entity.MnjxPnrOsi;
import com.swcares.obj.dto.*;
import com.swcares.service.IMnjxAirlineService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class PnrCommandServicePartOsi{

    /**
     * 其它联系信息，可为字母或“/"，”-“，可包含空格
     */
    private static final String REG_OTHER = "([\\sA-Z/.-]+)";
    /**
     * 自由文本
     * 可为字母、数字、空格、’/‘、'-'
     */
    private static final String REG_TEXT = "([\\sA-Z0-9/.-]+)";
    /**
     * String 长度
     */
    private static final int STR_LENGTH = 30;

    /**
     * 长度
     */
    private static final int FIFTY = 50;

    /**
     * CTCT
     */
    private static final String CTCT = "CTCT";

    /**
     * CTCM
     */
    private static final String CTCM = "CTCM";
    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    public void osi(MemoryDataPnr memoryDataPnr, OsiDto osiDto) throws UnifiedResultException {
        if (StrUtil.isNotEmpty(osiDto.getContactPhone()) && osiDto.getContactPhone().length() > STR_LENGTH) {
            throw new UnifiedResultException(Constant.SIZE);
        }
        // 其它联系信息
        if (StrUtil.isNotEmpty(osiDto.getOtherContactInfo())) {
            if (osiDto.getOtherContactInfo().length() > STR_LENGTH) {
                throw new UnifiedResultException(Constant.SIZE);
            }
            if (!osiDto.getOtherContactInfo().matches(REG_OTHER)) {
                throw new UnifiedResultException(Constant.INVALID_CHARACTER);
            }
        }
        // 自由文本
        if (StrUtil.isNotEmpty(osiDto.getText())) {
            if (osiDto.getText().length() > FIFTY) {
                throw new UnifiedResultException(Constant.SIZE);
            }
            if (!osiDto.getText().matches(REG_TEXT)) {
                throw new UnifiedResultException(Constant.INVALID_CHARACTER);
            }
        }
        MnjxAirline airline = iMnjxAirlineService.lambdaQuery().eq(MnjxAirline::getAirlineCode, osiDto.getAirlineCode()).one();
        if (ObjectUtil.isEmpty(airline)) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos().stream()
                .filter(k -> !k.isXe())
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(pnrSegDtos)) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }
        // 从航班号中取出航空公司
        List<String> airlineCodeList = pnrSegDtos.stream()
                .filter(k -> !"SA".equals(k.getMnjxPnrSeg().getPnrSegType()))
                .map(k -> k.getMnjxPnrSeg().getFlightNo().substring(0, 2))
                .distinct()
                .collect(Collectors.toList());
        if (!airlineCodeList.contains(osiDto.getAirlineCode())) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }
        if (StrUtil.isNotEmpty(osiDto.getPNo())) {
            this.handlePnrNm(memoryDataPnr, osiDto);
        } else {
            this.handlePnr(memoryDataPnr, osiDto);
        }
    }

    private void handlePnr(MemoryDataPnr memoryDataPnr, OsiDto osiDto) throws UnifiedResultException {
        String inputValue;
        if (StrUtil.isNotEmpty(osiDto.getOsiType()) && CTCT.equals(osiDto.getOsiType())) {
            if (StrUtil.isNotEmpty(osiDto.getOtherContactInfo())) {
                inputValue = String.format("OSI %s %s%s/%s", osiDto.getAirlineCode(), osiDto.getOsiType(), osiDto.getContactPhone(), osiDto.getOtherContactInfo());
            } else {
                inputValue = String.format("OSI %s %s%s", osiDto.getAirlineCode(), osiDto.getOsiType(), osiDto.getContactPhone());
            }
            List<PnrOsiDto> pnrOsiDtos = memoryDataPnr.getPnrOsiDtos().stream()
                    .filter(k -> !k.isXe())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrOsiDtos)) {
                List<String> inputValueList = pnrOsiDtos.stream()
                        .filter(o -> CTCT.equals(o.getMnjxPnrOsi().getPnrOsiType()))
                        .map(k -> k.getMnjxPnrOsi().getInputValue().split("/")[0])
                        .collect(Collectors.toList());
                String tmpInputValue = inputValue.split("/")[0];
                if (inputValueList.contains(tmpInputValue)) {
                    throw new UnifiedResultException("DUPLICATE TEL NUMBER");
                }
            }
        } else {
            inputValue = String.format("OSI %s %s", osiDto.getAirlineCode(), osiDto.getText());
        }
        osiDto.setInputValue(inputValue);
        buildPnr(memoryDataPnr, osiDto);
    }

    private void handlePnrNm(MemoryDataPnr memoryDataPnr, OsiDto osiDto) throws UnifiedResultException {
        // 旅客编号
        int psgId = Integer.parseInt(osiDto.getPNo());
        // 查找旅客
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos().stream()
                .filter(k -> !k.isXe())
                .filter(k -> k.getMnjxPnrNm().getPsgIndex().equals(psgId))
                .filter(k -> !Constant.BE_UPDATED.equals(k.getUpdateMark()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(pnrNmDtos)) {
            throw new UnifiedResultException(Constant.PSGR_ID);
        }
        PnrNmDto pnrNmDto = pnrNmDtos.get(0);
        // CTCM
        if (StrUtil.isNotEmpty(osiDto.getOsiType()) && CTCM.equals(osiDto.getOsiType())) {
            List<PnrNmOsiDto> pnrNmOsiDtos = pnrNmDto.getPnrNmOsiDtos().stream()
                    .filter(k -> !k.isXe())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrNmOsiDtos)) {
                List<String> nmOsiList = pnrNmOsiDtos.stream()
                        .filter(o -> CTCM.equals(o.getMnjxNmOsi().getPnrOsiType()))
                        .map(k -> {
                            String value = k.getMnjxNmOsi().getInputValue().split("/")[0];
                            return value.substring(11);
                        }).collect(Collectors.toList());
                if (nmOsiList.contains(osiDto.getContactPhone())) {
                    throw new UnifiedResultException("DUPLICATE TEL NUMBER");
                }
            }
            String value = StrUtil.format("OSI {} {}{}/P{}", osiDto.getAirlineCode(), osiDto.getOsiType(), osiDto.getContactPhone(), osiDto.getPNo());
            osiDto.setInputValue(value);
        } else {
            String value = StrUtil.format("OSI {} {}/P{}", osiDto.getAirlineCode(), osiDto.getText(), osiDto.getPNo());
            osiDto.setInputValue(value);
        }
        buildPnrNm(pnrNmDto, osiDto);
    }

    /**
     * Pnr Osi
     *
     * @param memoryDataPnr
     * @param osiDto
     */
    private void buildPnr(MemoryDataPnr memoryDataPnr, OsiDto osiDto) {
        String pnrId = memoryDataPnr.getMnjxPnr().getPnrId();
        if (StrUtil.isNotEmpty(osiDto.getText()) && osiDto.getText().startsWith(Constant.VIP_TYPE)) {
            osiDto.setOsiType(Constant.VIP_TYPE);
        }
        PnrOsiDto pnrOsiDto = buildPnrOsiDto(pnrId, osiDto);
        memoryDataPnr.getPnrOsiDtos().add(pnrOsiDto);
    }

    /**
     * PnrNmOSI
     *
     * @param pnrNmDto
     * @param osiDto
     */
    private void buildPnrNm(PnrNmDto pnrNmDto, OsiDto osiDto) {
        String pnrNmId = pnrNmDto.getMnjxPnrNm().getPnrNmId();
        if (StrUtil.isNotEmpty(osiDto.getText()) && osiDto.getText().startsWith(Constant.VIP_TYPE)) {
            osiDto.setOsiType(Constant.VIP_TYPE);
        }
        PnrNmOsiDto pnrNmOsiDto = buildPnrNmOsiDto(pnrNmId, osiDto);
        pnrNmDto.getPnrNmOsiDtos().add(pnrNmOsiDto);
    }

    /**
     * 构建pnrOsiDTO
     *
     * @param pnrId
     * @param osiDto
     * @return
     */
    private PnrOsiDto buildPnrOsiDto(String pnrId, OsiDto osiDto) {
        PnrOsiDto pnrOsiDto = new PnrOsiDto();
        MnjxPnrOsi mnjxPnrOsi = new MnjxPnrOsi();
        mnjxPnrOsi.setPnrId(pnrId);
        mnjxPnrOsi.setPnrOsiType(osiDto.getOsiType());
        mnjxPnrOsi.setAirlineCode(osiDto.getAirlineCode());
        mnjxPnrOsi.setPnrOsiInfo(osiDto.getInputValue());
        mnjxPnrOsi.setInputValue(osiDto.getInputValue());
        pnrOsiDto.setMnjxPnrOsi(mnjxPnrOsi);
        return pnrOsiDto;
    }

    /**
     * 构建pnrNmOsiDTO
     *
     * @param pnrNmId
     * @param osiDto
     * @return
     */
    private PnrNmOsiDto buildPnrNmOsiDto(String pnrNmId, OsiDto osiDto) {
        PnrNmOsiDto pnrNmOsiDto = new PnrNmOsiDto();
        MnjxNmOsi nmOsi = new MnjxNmOsi();
        nmOsi.setPnrNmId(pnrNmId);
        nmOsi.setInputValue(osiDto.getInputValue());
        nmOsi.setPnrOsiType(osiDto.getOsiType());
        nmOsi.setPnrOsiInfo(osiDto.getInputValue());
        pnrNmOsiDto.setMnjxNmOsi(nmOsi);
        return pnrNmOsiDto;
    }
}
