package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.*;
import com.swcares.entity.*;
import com.swcares.mapper.PnrCommandMapper;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.AvVo;
import com.swcares.obj.vo.TriVo;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * pnr操作的相关实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PnrCommandServicePartAt {
    @Resource
    private PnrCommandMapper pnrCommandMapper;
    @Resource
    private IMnjxPnrService iMnjxPnrService;
    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;
    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;
    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;
    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;
    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;
    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;
    @Resource
    private IMnjxNmXnService iMnjxNmXnService;
    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;
    @Resource
    private IMnjxAirportService iMnjxAirportService;
    @Resource
    private IMnjxNmCtService iMnjxNmCtService;
    @Resource
    private IMnjxPnrCtService iMnjxPnrCtService;
    @Resource
    private IMnjxPnrFpService iMnjxPnrFpService;
    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;
    @Resource
    private IMnjxPnrTcService iMnjxPnrTcService;
    @Resource
    private IMnjxPnrEiService iMnjxPnrEiService;
    @Resource
    private IMnjxNmFcService iMnjxNmFcService;
    @Resource
    private IMnjxNmFpService iMnjxNmFpService;
    @Resource
    private IMnjxNmOiService iMnjxNmOiService;
    @Resource
    private IMnjxPnrRmkService iMnjxPnrRmkService;
    @Resource
    private IMnjxNmFnService iMnjxNmFnService;
    @Resource
    private IMnjxPnrOsiService iMnjxPnrOsiService;
    @Resource
    private IMnjxPnrNmUmService iMnjxPnrNmUmService;
    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;
    @Resource
    private IMnjxNmEiService iMnjxNmEiService;
    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;
    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;
    @Resource
    private IMnjxPnrTkService iMnjxPnrTkService;
    @Resource
    private IMnjxPnrGnService iMnjxPnrGnService;
    @Resource
    private IMnjxPnrFcService iMnjxPnrFcService;
    @Resource
    private IMnjxCityService iMnjxCityService;
    @Resource
    private PnrCommandServicePartNm pnrCommandServicePartNm;
    @Resource
    private PnrCommandServicePartAv pnrCommandServicePartAv;
    @Resource
    private PnrCommandServicePartSd pnrCommandServicePartSd;
    @Resource
    private PnrCommandServicePartSsr pnrCommandServicePartSsr;
    @Resource
    private PnrCommandServicePartPat pnrCommandServicePartPat;
    @Resource
    private IPnrOperationService iPnrOperationService;
    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;
    @Resource
    private IMnjxPsgCkiOptionService iMnjxPsgCkiOptionService;
    @Resource
    private IMnjxPsgOperateRecordService iMnjxPsgOperateRecordService;
    @Resource
    private IMnjxPsgSeatService iMnjxPsgSeatService;
    @Resource
    private IMnjxLuggageCarryonService iMnjxLuggageCarryonService;
    @Resource
    private IMnjxLuggageService iMnjxLuggageService;
    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;
    @Resource
    private IMnjxFlightService iMnjxFlightService;
    @Resource
    private IMnjxFrequenterService iMnjxFrequenterService;
    @Resource
    private IMnjxTicketPriceService iMnjxTicketPriceService;
    @Resource
    private IMnjxSpInfoService iMnjxSpInfoService;

    /**
     * 生成PNR的大小编码
     *
     * @return 生成PNR的大小编码
     */
    private String createPnrCode(SFunction<MnjxPnr, ?> column) {
        // 随机获取一个PNR编码
        String pnrCode = this.constPnrCode();
        // 设置标识量
        boolean isExist = true;
        // 循环当前编码是否存在
        while (isExist) {
            // 统计当前PNR编码是否存在了
            int count = iMnjxPnrService.lambdaQuery().eq(column, pnrCode).count();
            // 不存在就跳出循环
            if (count == Constant.ZERO) {
                isExist = false;
            }
            // 存在就重新获取一下
            else {
                pnrCode = this.constPnrCode();
            }
        }
        return pnrCode;
    }

    private void seal(MemoryDataPnr memoryDataPnr, MnjxSi mnjxSi) throws UnifiedResultException {
        // 生成ICS和CRS编码（如果没有）
        this.createCrsIcs(memoryDataPnr);
        // 插入封口数据之前先获取之前数据库里面的航段组所用座位数
        List<MnjxPnrSeg> lastDbSegList = this.getSegFromDb(memoryDataPnr);
        // 封口插入PNR项数据
        this.handlePnr(memoryDataPnr, mnjxSi);
        // 前面的记录保存无误后，更新该PNR所选航班航段的销售舱位可用座位数减少（第一次封口）
        if (memoryDataPnr.isNewPnr()) {
            this.updateAvailableSeat(memoryDataPnr);
            memoryDataPnr.setNewPnr(false);
        } else {
            // 非第一次封口，如果人数减少，则将空余出来的座位数加回销售舱位可用座位记录
            // 如果存在换开的情况，需要将新的航班的销售舱位可用座位数进行减少，换掉的航班可用座位数恢复
            List<MnjxPnrSeg> thisDbSegList = this.getSegFromDb(memoryDataPnr);
            this.addAvailableSeat(memoryDataPnr, lastDbSegList, thisDbSegList);
        }
    }

    /**
     * Title: setOtherItemPnrIndexChange
     * Description: 除NM和GN的其他项的pnrIndex进行改变
     * 当由AT调用的时候，说明旅客姓名有修改或删除，需要同时调整其他项的 P 序号
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/5/27 15:47
     */
    private void setOtherItemPnrIndexChange(MemoryDataPnr memoryDataPnr, PnrNmDto pnrNmDto) {
        this.changePnrSegIndex(memoryDataPnr);
        this.changePnrCtIndex(memoryDataPnr);
        this.changePnrRmkIndex(memoryDataPnr);
        this.changePnrFcIndex(memoryDataPnr);
        this.changePnrFnIndex(memoryDataPnr);
        this.changePnrFpIndex(memoryDataPnr);
        this.changePnrOsiIndex(memoryDataPnr);
        this.changePnrTcIndex(memoryDataPnr);
        this.changePnrTkIndex(memoryDataPnr);

        List<MnjxPnrRecord> mnjxPnrRecords = memoryDataPnr.getMnjxPnrRecords();
        List<String> existPnrItem = memoryDataPnr.getExistHistoryPnrItem();
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos().stream().filter(p -> StrUtil.isEmpty(p.getMnjxPnrNm().getChangeType())).collect(Collectors.toList());
        pnrNmDtos.forEach(n -> {
            // 比修改姓名的序号大的姓名项才需要将附属项的/P序号减1
            boolean needChangeIndexP = n.getPnrIndex() >= pnrNmDto.getPnrIndex();
            //TN
            List<PnrNmTnDto> pnrNmTnDtos = memoryDataPnr.getPnrNmTnDtos();
            this.changeNmTnIndex(pnrNmTnDtos, n, existPnrItem, mnjxPnrRecords, needChangeIndexP);
            this.changeNmXnIndex(pnrNmTnDtos, n, existPnrItem, mnjxPnrRecords, needChangeIndexP);
            this.changeNmSsrIndex(n, existPnrItem, mnjxPnrRecords, needChangeIndexP);
            this.changeNmOsiIndex(n, existPnrItem, mnjxPnrRecords, needChangeIndexP);
            this.changeNmCtIndex(n, existPnrItem, mnjxPnrRecords, needChangeIndexP);
            this.changeNmRmkIndex(n, existPnrItem, mnjxPnrRecords, needChangeIndexP);
            this.changeNmEiIndex(n, existPnrItem, mnjxPnrRecords, needChangeIndexP);
            this.changeNmOiIndex(n, existPnrItem, mnjxPnrRecords, needChangeIndexP);
            this.changeNmFcIndex(n, existPnrItem, mnjxPnrRecords, needChangeIndexP);
            this.changeNmFnIndex(n, existPnrItem, mnjxPnrRecords, needChangeIndexP);
            this.changeNmFpIndex(n, existPnrItem, mnjxPnrRecords, needChangeIndexP);
        });
    }

    private void changeNmFpIndex(PnrNmDto n, List<String> existPnrItem, List<MnjxPnrRecord> mnjxPnrRecords, boolean needChangeIndexP) {
        List<PnrNmFpDto> pnrNmFpDtos = n.getPnrNmFpDtos();
        if (ObjectUtil.isNotEmpty(pnrNmFpDtos)) {
            pnrNmFpDtos.stream().filter(p -> !p.isXe()).forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxNmFp().setPnrIndex(p.getPnrIndex());
                MnjxNmFp item = p.getMnjxNmFp();
                item.setPnrIndex(p.getPnrIndex());
                if (needChangeIndexP) {
                    String inputValue = item.getInputValue();
                    String orderType = "NMFP";
                    // 记录item更新
                    existPnrItem.remove(StrUtil.format("{}:{}", orderType, inputValue));
                    inputValue = this.updatePsgNumber(inputValue, mnjxPnrRecords, existPnrItem, orderType);
                    item.setInputValue(inputValue);
                }
            });
        }
    }

    private void changeNmFnIndex(PnrNmDto n, List<String> existPnrItem, List<MnjxPnrRecord> mnjxPnrRecords, boolean needChangeIndexP) {
        List<PnrNmFnDto> pnrNmFnDtos = n.getPnrNmFnDtos();
        if (ObjectUtil.isNotEmpty(pnrNmFnDtos)) {
            pnrNmFnDtos.stream().filter(p -> !p.isXe()).forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxNmFn().setPnrIndex(p.getPnrIndex());
                MnjxNmFn item = p.getMnjxNmFn();
                item.setPnrIndex(p.getPnrIndex());
                if (needChangeIndexP) {
                    String inputValue = item.getInputValue();
                    String orderType = "NMFN";
                    // 记录item更新
                    existPnrItem.remove(StrUtil.format("{}:{}", orderType, inputValue));
                    inputValue = this.updatePsgNumber(inputValue, mnjxPnrRecords, existPnrItem, orderType);
                    item.setInputValue(inputValue);
                }
            });
        }
    }

    private void changeNmFcIndex(PnrNmDto n, List<String> existPnrItem, List<MnjxPnrRecord> mnjxPnrRecords, boolean needChangeIndexP) {
        List<PnrNmFcDto> pnrNmFcDtos = n.getPnrNmFcDtos();
        if (ObjectUtil.isNotEmpty(pnrNmFcDtos)) {
            pnrNmFcDtos.stream().filter(p -> !p.isXe()).forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxNmFc().setPnrIndex(p.getPnrIndex());
                MnjxNmFc item = p.getMnjxNmFc();
                item.setPnrIndex(p.getPnrIndex());
                if (needChangeIndexP) {
                    String inputValue = item.getInputValue();
                    String orderType = "NMFC";
                    // 记录item更新
                    existPnrItem.remove(StrUtil.format("{}:{}", orderType, inputValue));
                    inputValue = this.updatePsgNumber(inputValue, mnjxPnrRecords, existPnrItem, orderType);
                    item.setInputValue(inputValue);
                }
            });
        }
    }

    private void changeNmOiIndex(PnrNmDto n, List<String> existPnrItem, List<MnjxPnrRecord> mnjxPnrRecords, boolean needChangeIndexP) {
        List<PnrOiDto> pnrOiDtos = n.getPnrOiDtos();
        if (ObjectUtil.isNotEmpty(pnrOiDtos)) {
            pnrOiDtos.stream().filter(o -> !o.isXe()).forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxNmOi().setPnrIndex(p.getPnrIndex());
                MnjxNmOi item = p.getMnjxNmOi();
                item.setPnrIndex(p.getPnrIndex());
                if (needChangeIndexP) {
                    String inputValue = item.getOiInfo();
                    String orderType = "OI";
                    // 记录item更新
                    existPnrItem.remove(StrUtil.format("{}:{}", orderType, inputValue));
                    inputValue = this.updatePsgNumber(inputValue, mnjxPnrRecords, existPnrItem, orderType);
                    item.setOiInfo(inputValue);
                }
            });
        }
    }

    private void changeNmEiIndex(PnrNmDto n, List<String> existPnrItem, List<MnjxPnrRecord> mnjxPnrRecords, boolean needChangeIndexP) {
        List<PnrNmEiDto> pnrNmEiDtos = n.getPnrNmEiDtos();
        if (ObjectUtil.isNotEmpty(pnrNmEiDtos)) {
            pnrNmEiDtos.stream().filter(e -> !e.isXe()).forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxNmEi().setPnrIndex(p.getPnrIndex());
                MnjxNmEi item = p.getMnjxNmEi();
                item.setPnrIndex(p.getPnrIndex());
                if (needChangeIndexP) {
                    String inputValue = item.getInputValue();
                    String orderType = "NMEI";
                    // 记录item更新
                    existPnrItem.remove(StrUtil.format("{}:{}", orderType, inputValue));
                    inputValue = this.updatePsgNumber(inputValue, mnjxPnrRecords, existPnrItem, orderType);
                    item.setInputValue(inputValue);
                    item.setEiInfo(inputValue);
                }
            });
        }
    }

    private void changeNmRmkIndex(PnrNmDto n, List<String> existPnrItem, List<MnjxPnrRecord> mnjxPnrRecords, boolean needChangeIndexP) {
        List<PnrNmRmkDto> pnrNmRmkDtos = n.getPnrNmRmkDtos();
        if (CollUtil.isNotEmpty(pnrNmRmkDtos)) {
            pnrNmRmkDtos.forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxNmRmk().setPnrIndex(p.getPnrIndex());
                MnjxNmRmk item = p.getMnjxNmRmk();
                item.setPnrIndex(p.getPnrIndex());
                if (needChangeIndexP) {
                    String inputValue = item.getInputValue();
                    String orderType = "NMRMK";
                    // 记录item更新
                    existPnrItem.remove(StrUtil.format("{}:{}", orderType, inputValue));
                    inputValue = this.updatePsgNumber(inputValue, mnjxPnrRecords, existPnrItem, orderType);
                    item.setInputValue(inputValue);
                    item.setRmkInfo(inputValue);
                }
            });
        }
    }

    private void changeNmCtIndex(PnrNmDto n, List<String> existPnrItem, List<MnjxPnrRecord> mnjxPnrRecords, boolean needChangeIndexP) {
        List<PnrNmCtDto> pnrNmCtDtos = n.getPnrNmCtDtos();
        if (CollUtil.isNotEmpty(pnrNmCtDtos)) {
            pnrNmCtDtos.stream().filter(c -> !c.isXe()).forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxNmCt().setPnrIndex(p.getPnrIndex());
                MnjxNmCt item = p.getMnjxNmCt();
                item.setPnrIndex(p.getPnrIndex());
                if (needChangeIndexP) {
                    String inputValue = item.getInputValue();
                    String orderType = "NMCT";
                    // 记录item更新
                    existPnrItem.remove(StrUtil.format("{}:{}", orderType, inputValue));
                    inputValue = this.updatePsgNumber(inputValue, mnjxPnrRecords, existPnrItem, orderType);
                    item.setInputValue(inputValue);
                    item.setCtText(inputValue);
                }
            });
        }
    }

    private void changeNmOsiIndex(PnrNmDto n, List<String> existPnrItem, List<MnjxPnrRecord> mnjxPnrRecords, boolean needChangeIndexP) {
        List<PnrNmOsiDto> pnrNmOsiDtos = n.getPnrNmOsiDtos();
        if (CollUtil.isNotEmpty(pnrNmOsiDtos)) {
            pnrNmOsiDtos.stream().filter(o -> !o.isXe()).forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxNmOsi().setPnrIndex(p.getPnrIndex());
                MnjxNmOsi item = p.getMnjxNmOsi();
                item.setPnrIndex(p.getPnrIndex());
                if (needChangeIndexP) {
                    String inputValue = item.getInputValue();
                    String orderType = "NMOSI";
                    // 记录item更新
                    existPnrItem.remove(StrUtil.format("{}:{}", orderType, inputValue));
                    inputValue = this.updatePsgNumber(inputValue, mnjxPnrRecords, existPnrItem, orderType);
                    item.setInputValue(inputValue);
                    item.setPnrOsiInfo(inputValue);
                }
            });
        }
    }

    private void changeNmSsrIndex(PnrNmDto n, List<String> existPnrItem, List<MnjxPnrRecord> mnjxPnrRecords, boolean needChangeIndexP) {
        List<PnrSsrDto> pnrSsrDtos = n.getPnrSsrDtos();
        if (CollUtil.isNotEmpty(pnrSsrDtos)) {
            pnrSsrDtos.stream().filter(s -> (!s.isXe() && !Constant.ACTION_CODE_XX.equals(s.getMnjxNmSsr().getActionCode()))).forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxNmSsr().setPnrIndex(p.getPnrIndex());
                MnjxNmSsr item = p.getMnjxNmSsr();
                item.setPnrIndex(p.getPnrIndex());
                if (needChangeIndexP) {
                    String inputValue = item.getInputValue();
                    String orderType = "SSR";
                    // 记录item更新
                    existPnrItem.remove(StrUtil.format("{}:{}", orderType, inputValue));
                    inputValue = this.updatePsgNumber(inputValue, mnjxPnrRecords, existPnrItem, orderType);
                    item.setInputValue(inputValue);
                    item.setSsrInfo(inputValue);
                    p.setChange(true);
                }
            });
        }
    }

    private void changeNmXnIndex(List<PnrNmTnDto> pnrNmTnDtos, PnrNmDto n, List<String> existPnrItem, List<MnjxPnrRecord> mnjxPnrRecords, boolean needChangeIndexP) {
        List<PnrXnDto> pnrXnDtos = n.getPnrXnDtos();
        if (CollUtil.isNotEmpty(pnrXnDtos)) {
            pnrXnDtos.stream().filter(x -> !x.isXe()).forEach(pnrXnDto -> {
                pnrXnDto.setPnrIndex(pnrXnDto.getPnrIndex() - 1);
                MnjxNmXn mnjxNmXn = pnrXnDto.getMnjxNmXn();
                mnjxNmXn.setPnrIndex(pnrXnDto.getPnrIndex());
                if (needChangeIndexP) {
                    String inputValue = mnjxNmXn.getInputValue();
                    String orderType = "XN";
                    // 记录item更新
                    existPnrItem.remove(StrUtil.format("{}:{}", orderType, inputValue));
                    inputValue = this.updatePsgNumber(inputValue, mnjxPnrRecords, existPnrItem, orderType);
                    mnjxNmXn.setInputValue(inputValue);
                }
                //婴儿 TN项
                if (ObjectUtil.isNotEmpty(pnrNmTnDtos)) {
                    pnrNmTnDtos.stream().filter(x -> !x.isXe()).forEach(pnrNmTnDto -> {
                        if (ObjectUtil.isNotEmpty(pnrNmTnDto.getMnjxPnrNmTn().getNmXnId()) && pnrNmTnDto.getMnjxPnrNmTn().getNmXnId().equals(pnrXnDto.getMnjxNmXn().getNmXnId())) {
                            pnrNmTnDto.setPnrIndex(pnrNmTnDto.getPnrIndex() - 1);
                            MnjxPnrNmTn mnjxPnrNmTn = pnrNmTnDto.getMnjxPnrNmTn();
                            mnjxPnrNmTn.setPnrIndex(pnrNmTnDto.getPnrIndex());
                            if (needChangeIndexP) {
                                String inputValue = mnjxPnrNmTn.getInputValue();
                                String orderType = "TN";
                                // 记录item更新
                                existPnrItem.remove(StrUtil.format("{}:{}", orderType, inputValue));
                                inputValue = this.updatePsgNumber(inputValue, mnjxPnrRecords, existPnrItem, orderType);
                                mnjxPnrNmTn.setInputValue(inputValue);
                            }
                        }
                    });
                }
            });
        }
    }

    private void changeNmTnIndex(List<PnrNmTnDto> pnrNmTnDtos, PnrNmDto n, List<String> existPnrItem, List<MnjxPnrRecord> mnjxPnrRecords, boolean needChangeIndexP) {
        if (ObjectUtil.isNotEmpty(pnrNmTnDtos)) {
            pnrNmTnDtos.stream().filter(x -> !x.isXe()).forEach(pnrNmTnDto -> {
                if (ObjectUtil.isNotEmpty(pnrNmTnDto.getMnjxPnrNmTn().getPnrNmId()) && pnrNmTnDto.getMnjxPnrNmTn().getPnrNmId().equals(n.getMnjxPnrNm().getPnrNmId())) {
                    pnrNmTnDto.setPnrIndex(pnrNmTnDto.getPnrIndex() - 1);
                    MnjxPnrNmTn mnjxPnrNmTn = pnrNmTnDto.getMnjxPnrNmTn();
                    mnjxPnrNmTn.setPnrIndex(pnrNmTnDto.getPnrIndex());
                    if (needChangeIndexP) {
                        String inputValue = mnjxPnrNmTn.getInputValue();
                        String orderType = "TN";
                        // 记录item更新
                        existPnrItem.remove(StrUtil.format("{}:{}", orderType, inputValue));
                        inputValue = this.updatePsgNumber(inputValue, mnjxPnrRecords, existPnrItem, orderType);
                        mnjxPnrNmTn.setInputValue(inputValue);
                    }
                }
            });
        }
    }

    private void changePnrTkIndex(MemoryDataPnr memoryDataPnr) {
        List<PnrTkDto> pnrTkDtos = memoryDataPnr.getPnrTkDtos();
        for (PnrTkDto pnrTkDto : pnrTkDtos) {
            if (ObjectUtil.isNotEmpty(pnrTkDto)) {
                pnrTkDto.setPnrIndex(pnrTkDto.getPnrIndex() - 1);
                pnrTkDto.getMnjxPnrTk().setPnrIndex(pnrTkDto.getPnrIndex());
            }
        }
    }

    private void changePnrTcIndex(MemoryDataPnr memoryDataPnr) {
        List<PnrTcDto> pnrTcDtos = memoryDataPnr.getPnrTcDtos();
        if (CollUtil.isNotEmpty(pnrTcDtos)) {
            pnrTcDtos.forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxPnrTc().setPnrIndex(p.getPnrIndex());
            });
        }
    }

    private void changePnrOsiIndex(MemoryDataPnr memoryDataPnr) {
        List<PnrOsiDto> pnrOsiDtos = memoryDataPnr.getPnrOsiDtos();
        if (CollUtil.isNotEmpty(pnrOsiDtos)) {
            pnrOsiDtos.forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxPnrOsi().setPnrIndex(p.getPnrIndex());
            });
        }
    }

    private void changePnrFpIndex(MemoryDataPnr memoryDataPnr) {
        List<PnrFpDto> pnrFpDtos = memoryDataPnr.getPnrFpDtos();
        if (CollUtil.isNotEmpty(pnrFpDtos)) {
            pnrFpDtos.forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxPnrFp().setPnrIndex(p.getPnrIndex());
            });
        }
    }

    private void changePnrFnIndex(MemoryDataPnr memoryDataPnr) {
        List<PnrFnDto> pnrFnDtos = memoryDataPnr.getPnrFnDtos();
        if (CollUtil.isNotEmpty(pnrFnDtos)) {
            pnrFnDtos.forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxPnrFn().setPnrIndex(p.getPnrIndex());
            });
        }
    }

    private void changePnrFcIndex(MemoryDataPnr memoryDataPnr) {
        List<PnrFcDto> pnrFcDtos = memoryDataPnr.getPnrFcDtos();
        if (CollUtil.isNotEmpty(pnrFcDtos)) {
            pnrFcDtos.forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxPnrFc().setPnrIndex(p.getPnrIndex());
            });
        }
    }

    private void changePnrRmkIndex(MemoryDataPnr memoryDataPnr) {
        List<PnrRmkDto> pnrRmkDtos = memoryDataPnr.getPnrRmkDtos();
        if (CollUtil.isNotEmpty(pnrRmkDtos)) {
            pnrRmkDtos.forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxPnrRmk().setPnrIndex(p.getPnrIndex());
            });
        }
    }

    private void changePnrCtIndex(MemoryDataPnr memoryDataPnr) {
        List<PnrCtDto> pnrCtDtos = memoryDataPnr.getPnrCtDtos();
        if (CollUtil.isNotEmpty(pnrCtDtos)) {
            pnrCtDtos.forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxPnrCt().setPnrIndex(p.getPnrIndex());
            });
        }
    }

    private void changePnrSegIndex(MemoryDataPnr memoryDataPnr) {
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        if (CollUtil.isNotEmpty(pnrSegDtos)) {
            pnrSegDtos.forEach(p -> {
                p.setPnrIndex(p.getPnrIndex() - 1);
                p.getMnjxPnrSeg().setPnrIndex(p.getPnrIndex());
            });
        }
    }

    public String at(MemoryDataPnr memoryDataPnr, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        // 当前无活动PNR，报错：NO PNR
        if (ObjectUtil.isEmpty(mnjxPnr.getPnrId())) {
            throw new UnifiedResultException(Constant.NO_PNR);
        }
        // 检查PNR是否被控或超时
        this.checkPnrInControl(memoryDataPnr);
        // 当前有活动PNR，但是PNR无信息组处于未提交状态，直接返回空
        if (!memoryDataPnr.isPnrChanged() && !memoryDataPnr.isByAtK()) {
//            throw new UnifiedResultException(StrUtil.EMPTY);
            return "NO CHANGE";
        }
        // 检查GN信息组和NM信息组
        this.checkGnOrNm(memoryDataPnr);
        // 检查航段组数量
        this.checkSeg(memoryDataPnr);
        // 检查SSR FOID
        this.checkSsrFoid(memoryDataPnr);
        // 检查SSR FQTV
        this.checkSsrFqtv(memoryDataPnr);
        // 检查OSI CTCT或CTCM数量
        this.checkOsi(memoryDataPnr);
        // 检查CT
        this.checkCt(memoryDataPnr);
        // 检查婴儿信息
        this.checkXn(memoryDataPnr);
        // 检查多航段时空间连续性和时间连续性
        this.checkSegContinuity(memoryDataPnr);
        // 检查TK/TL
        this.checkTk(memoryDataPnr, mnjxOffice);
        // 行动代码处理
        this.handleActionCode(memoryDataPnr);
        // 封口
        this.seal(memoryDataPnr, mnjxSi);
        // 清除cki信息
        this.removeCki(memoryDataPnr);
        memoryDataPnr.setPnrChanged(false);
        return Constant.ACCEPTED;
    }


    /**
     * Title: updatePsgNumber
     * Description: 获取旅客序号更新后其他项的/P更新的值
     *
     * @param inputValue     inputValue
     * @param mnjxPnrRecords mnjxPnrRecords
     * @param existPnrItem   existPnrItem
     * @param orderType      orderType
     * @return {@link String}
     * <AUTHOR>
     * @date 2022/7/14 16:29
     */
    private String updatePsgNumber(String inputValue, List<MnjxPnrRecord> mnjxPnrRecords, List<String> existPnrItem, String orderType) {
        existPnrItem.add(StrUtil.format("{}:{}", orderType, inputValue));
        String[] split = inputValue.split("/P");
        String lastSplit = split[split.length - 1];
        StringBuilder inputValueSb = new StringBuilder();
        for (int i = 0; i < split.length - 1; i++) {
            inputValueSb.append(split[i]).append("/P");
        }
        if (ObjectUtil.isEmpty(inputValueSb)) {
            return inputValue;
        }
        inputValueSb.append(Integer.parseInt(lastSplit) - 1);
        String res = inputValueSb.toString();
        // record更新
        mnjxPnrRecords.stream().filter(r -> inputValue.equals(r.getInputValue())).collect(Collectors.toList()).get(0).setInputValue(res);
        return res;
    }

    /**
     * Title: constPnrCode
     * Description: 生成PNR编码 格式：前2位英文，后4位英文数字混合组合
     *
     * @return {@link String}
     * <AUTHOR>
     * @date 2022/6/16 10:05
     */
    private String constPnrCode() {
        return StrUtil.format("{}{}", RandomUtil.randomString(Constant.LETTER, 2), RandomUtil.randomString(4)).toUpperCase();
    }

    /**
     * Title: createCrsIcs
     * Description: 创建PNR编码
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 16:10
     */
    private void createCrsIcs(MemoryDataPnr memoryDataPnr) {
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        if (StrUtil.isEmpty(mnjxPnr.getPnrCrs()) && StrUtil.isEmpty(mnjxPnr.getPnrIcs())) {
            // 小编码
            String crsPnr = this.createPnrCode(MnjxPnr::getPnrCrs);
            // 大编码
            String icsPnr = this.createPnrCode(MnjxPnr::getPnrIcs);
            mnjxPnr.setPnrCrs(crsPnr);
            mnjxPnr.setPnrIcs(icsPnr);
            // 自动生成一条RMK信息
            PnrRmkDto pnrRmkDto = new PnrRmkDto();
            MnjxPnrRmk mnjxPnrRmk = new MnjxPnrRmk();
            mnjxPnrRmk.setPnrRmkId(IdUtils.getId());
            mnjxPnrRmk.setPnrId(mnjxPnr.getPnrId());
            //分离时，如果有原pnr的备注信息，直接删掉，重新加
            List<PnrRmkDto> pnrRmkDtos = memoryDataPnr.getPnrRmkDtos();
            if (CollUtil.isNotEmpty(pnrRmkDtos)) {
                memoryDataPnr.getPnrRmkDtos().removeIf(rmk -> rmk.getMnjxPnrRmk().getInputValue().startsWith("RMK CA/"));
            }
            String rmkInputValue = StrUtil.format("RMK CA/{}", icsPnr);
            mnjxPnrRmk.setInputValue(rmkInputValue);
            mnjxPnrRmk.setRmkInfo(rmkInputValue);
            mnjxPnrRmk.setPnrIndex(mnjxPnr.getMaxIndex() + 1);
            pnrRmkDto.setPnrIndex(mnjxPnrRmk.getPnrIndex());
            pnrRmkDto.setMnjxPnrRmk(mnjxPnrRmk);
            memoryDataPnr.getPnrRmkDtos().add(pnrRmkDto);
            mnjxPnr.setMaxIndex(mnjxPnr.getMaxIndex() + 1);
            // 添加RMK历史记录
            MnjxPnrRecord mnjxPnrRecord = new MnjxPnrRecord();
            mnjxPnrRecord.setPnrId(mnjxPnr.getPnrId());
            mnjxPnrRecord.setAtNo(memoryDataPnr.getThisAtNo());
            mnjxPnrRecord.setPnrIndex(pnrRmkDto.getPnrIndex());
            mnjxPnrRecord.setPnrType(Constant.PNR_RMK);
            mnjxPnrRecord.setInputValue(mnjxPnrRmk.getInputValue());
            memoryDataPnr.getMnjxPnrRecords().add(mnjxPnrRecord);
        }
    }

    /**
     * Title: getSegFromDb
     * Description: 获取数据库中的航段组
     *
     * @param memoryDataPnr memoryDataPnr
     * @return 获取数据库中的航段组
     * <AUTHOR>
     * @date 2022/8/4 11:46
     */
    private List<MnjxPnrSeg> getSegFromDb(MemoryDataPnr memoryDataPnr) {
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        List<MnjxPnrSeg> pnrSegList = pnrSegDtos.stream().map(PnrSegDto::getMnjxPnrSeg).collect(Collectors.toList());
        List<String> segIdList = pnrSegList.stream().map(MnjxPnrSeg::getPnrSegId).collect(Collectors.toList());
        return iMnjxPnrSegService.lambdaQuery().in(MnjxPnrSeg::getPnrSegId, segIdList).orderByAsc(MnjxPnrSeg::getPnrSegNo).list();
    }

    /**
     * Title: handlePnr
     * Description: PNR各项数据入库
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 16:13
     */
    private void handlePnr(MemoryDataPnr memoryDataPnr, MnjxSi mnjxSi) {
        // 处理附加到PNR的项数据
        this.handlePnrData(memoryDataPnr);
        // 处理附加到NM的项数据
        this.handleNmData(memoryDataPnr);
        // 最后更新PNR，主要是最大序号的更新
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        mnjxPnr.updateById();
        // 生成封口记录
        this.handleAtNo(memoryDataPnr, mnjxSi);
        // sp info
        this.handleSpInfo(memoryDataPnr);
        // 生成历史记录
        this.createHistory(memoryDataPnr);
        // 生成航司系统反馈记录
        this.createAirlineResponse(memoryDataPnr, mnjxSi);
    }

    /**
     * Title: handleNmData
     * Description: 处理附加到NM的项数据
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/12/1 10:26
     */
    private void handleNmData(MemoryDataPnr memoryDataPnr) {
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        List<String> nmIdList = pnrNmDtos.stream()
                .map(p -> p.getMnjxPnrNm().getPnrNmId())
                .distinct()
                .collect(Collectors.toList());
        if (CollUtils.isEmpty(nmIdList)) {
            return;
        }
        List<MnjxNmCt> nmCtList = iMnjxNmCtService.lambdaQuery()
                .in(MnjxNmCt::getPnrNmId, nmIdList)
                .list();
        List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                .in(MnjxNmFc::getPnrNmId, nmIdList)
                .list();
        List<MnjxNmSsr> nmSsrList = iMnjxNmSsrService.lambdaQuery()
                .in(MnjxNmSsr::getPnrNmId, nmIdList)
                .list();
        List<MnjxNmOsi> nmOsiList = iMnjxNmOsiService.lambdaQuery()
                .in(MnjxNmOsi::getPnrNmId, nmIdList)
                .list();
        List<MnjxNmRmk> nmRmkList = iMnjxNmRmkService.lambdaQuery()
                .in(MnjxNmRmk::getPnrNmId, nmIdList)
                .list();
        List<MnjxNmFn> nmFnList = iMnjxNmFnService.lambdaQuery()
                .in(MnjxNmFn::getPnrNmId, nmIdList)
                .list();
        List<MnjxNmEi> nmEiList = iMnjxNmEiService.lambdaQuery()
                .in(MnjxNmEi::getPnrNmId, nmIdList)
                .list();
        List<MnjxNmOi> nmOiList = iMnjxNmOiService.lambdaQuery()
                .in(MnjxNmOi::getPnrNmId, nmIdList)
                .list();
        List<MnjxNmXn> nmXnList = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId, nmIdList)
                .list();
        List<MnjxNmFp> nmFpList = iMnjxNmFpService.lambdaQuery()
                .in(MnjxNmFp::getPnrNmId, nmIdList)
                .list();

        List<String> deleteNmCtIdList = new ArrayList<>();
        List<MnjxNmCt> insertNmCtList = new ArrayList<>();
        List<MnjxNmCt> updateNmCtList = new ArrayList<>();
        List<String> deleteNmFcIdList = new ArrayList<>();
        List<MnjxNmFc> insertNmFcList = new ArrayList<>();
        List<String> deleteNmSsrIdList = new ArrayList<>();
        List<MnjxNmSsr> insertNmSsrList = new ArrayList<>();
        List<MnjxNmSsr> updateNmSsrList = new ArrayList<>();
        List<String> deleteNmOsiIdList = new ArrayList<>();
        List<MnjxNmOsi> insertNmOsiList = new ArrayList<>();
        List<MnjxNmOsi> updateNmOsiList = new ArrayList<>();
        List<String> deleteNmRmkIdList = new ArrayList<>();
        List<MnjxNmRmk> insertNmRmkList = new ArrayList<>();
        List<String> deleteNmFnIdList = new ArrayList<>();
        List<MnjxNmFn> insertNmFnList = new ArrayList<>();
        List<String> deleteNmEiIdList = new ArrayList<>();
        List<MnjxNmEi> insertNmEiList = new ArrayList<>();
        List<String> deleteNmOiIdList = new ArrayList<>();
        List<MnjxNmOi> insertNmOiList = new ArrayList<>();
        List<String> deleteNmXnIdList = new ArrayList<>();
        List<MnjxNmXn> insertNmXnList = new ArrayList<>();
        List<MnjxNmXn> updateNmXnList = new ArrayList<>();
        List<String> deleteNmFpIdList = new ArrayList<>();
        List<MnjxNmFp> insertNmFpList = new ArrayList<>();
        List<String> deleteNmUmIdList = new ArrayList<>();
        List<MnjxPnrNmUm> insertNmUmList = new ArrayList<>();
        pnrNmDtos.stream().filter(p -> !Constant.BE_UPDATED.equals(p.getUpdateMark())).forEach(p -> {
            // NM CT
            this.handleNmCt(p, nmCtList, deleteNmCtIdList, insertNmCtList, updateNmCtList);
            // NM FC
            this.handleNmFc(p, nmFcList, deleteNmFcIdList, insertNmFcList);
            // SSR
            this.handleSsr(p, memoryDataPnr, nmSsrList, deleteNmSsrIdList, insertNmSsrList, updateNmSsrList);
            // NM OSI
            this.handleNmOsi(p, nmOsiList, deleteNmOsiIdList, insertNmOsiList, updateNmOsiList);
            // NM RMK
            this.handleNmRmk(p, nmRmkList, deleteNmRmkIdList, insertNmRmkList);
            // NM FN
            this.handleNmFn(p, nmFnList, deleteNmFnIdList, insertNmFnList);
            // NM EI
            this.handleNmEi(p, nmEiList, deleteNmEiIdList, insertNmEiList);
            // OI
            this.handleOi(p, nmOiList, deleteNmOiIdList, insertNmOiList);
            // XN
            this.handleXn(p, nmXnList, deleteNmXnIdList, insertNmXnList, updateNmXnList);
            // NM FP
            this.handleNmFp(p, nmFpList, deleteNmFpIdList, insertNmFpList);
            // NM UM
            this.handleNmUm(p);
        });
        // NM CT
        if (CollUtil.isNotEmpty(deleteNmCtIdList)) {
            iMnjxNmCtService.removeByIds(deleteNmCtIdList);
        }
        if (CollUtil.isNotEmpty(insertNmCtList)) {
            iMnjxNmCtService.saveBatch(insertNmCtList);
        }
        if (CollUtil.isNotEmpty(updateNmCtList)) {
            iMnjxNmCtService.updateBatchById(updateNmCtList);
        }
        // NM FC
        if (CollUtil.isNotEmpty(deleteNmFcIdList)) {
            iMnjxNmFcService.removeByIds(deleteNmFcIdList);
        }
        if (CollUtil.isNotEmpty(insertNmFcList)) {
            iMnjxNmFcService.saveBatch(insertNmFcList);
        }
        // SSR
        if (CollUtil.isNotEmpty(deleteNmSsrIdList)) {
            iMnjxNmSsrService.removeByIds(deleteNmSsrIdList);
        }
        if (CollUtil.isNotEmpty(insertNmSsrList)) {
            iMnjxNmSsrService.saveBatch(insertNmSsrList);
        }
        if (CollUtil.isNotEmpty(updateNmSsrList)) {
            iMnjxNmSsrService.updateBatchById(updateNmSsrList);
        }
        // NM OSI
        if (CollUtil.isNotEmpty(deleteNmOsiIdList)) {
            iMnjxNmOsiService.removeByIds(deleteNmOsiIdList);
        }
        if (CollUtil.isNotEmpty(insertNmOsiList)) {
            iMnjxNmOsiService.saveBatch(insertNmOsiList);
        }
        if (CollUtil.isNotEmpty(updateNmOsiList)) {
            iMnjxNmOsiService.updateBatchById(updateNmOsiList);
        }
        // NM RMK
        if (CollUtil.isNotEmpty(deleteNmRmkIdList)) {
            iMnjxNmRmkService.removeByIds(deleteNmRmkIdList);
        }
        if (CollUtil.isNotEmpty(insertNmRmkList)) {
            iMnjxNmRmkService.saveBatch(insertNmRmkList);
        }
        // NM FN
        if (CollUtil.isNotEmpty(deleteNmFnIdList)) {
            iMnjxNmFnService.removeByIds(deleteNmFnIdList);
        }
        if (CollUtil.isNotEmpty(insertNmFnList)) {
            iMnjxNmFnService.saveBatch(insertNmFnList);
        }
        // NM EI
        if (CollUtil.isNotEmpty(deleteNmEiIdList)) {
            iMnjxNmEiService.removeByIds(deleteNmEiIdList);
        }
        if (CollUtil.isNotEmpty(insertNmEiList)) {
            iMnjxNmEiService.saveBatch(insertNmEiList);
        }
        // OI
        if (CollUtil.isNotEmpty(deleteNmOiIdList)) {
            iMnjxNmOiService.removeByIds(deleteNmOiIdList);
        }
        if (CollUtil.isNotEmpty(insertNmOiList)) {
            iMnjxNmOiService.saveBatch(insertNmOiList);
        }
        // XN
        if (CollUtil.isNotEmpty(deleteNmXnIdList)) {
            iMnjxNmXnService.removeByIds(deleteNmXnIdList);
        }
        if (CollUtil.isNotEmpty(insertNmXnList)) {
            iMnjxNmXnService.saveBatch(insertNmXnList);
        }
        if (CollUtil.isNotEmpty(updateNmXnList)) {
            iMnjxNmXnService.updateBatchById(updateNmXnList);
        }
        // NM FP
        if (CollUtil.isNotEmpty(deleteNmFpIdList)) {
            iMnjxNmFpService.removeByIds(deleteNmFpIdList);
        }
        if (CollUtil.isNotEmpty(insertNmFpList)) {
            iMnjxNmFpService.saveBatch(insertNmFpList);
        }
        // NM UM
        if (CollUtil.isNotEmpty(deleteNmUmIdList)) {
            iMnjxPnrNmUmService.removeByIds(deleteNmUmIdList);
        }
        if (CollUtil.isNotEmpty(insertNmUmList)) {
            iMnjxPnrNmUmService.saveBatch(insertNmUmList);
        }
        // 删除NM
        List<MnjxPsgCki> psgCkiList = iMnjxPsgCkiService.lambdaQuery()
                .in(MnjxPsgCki::getPnrNmId, nmIdList)
                .list();
        List<String> deleteNmIdList = new ArrayList<>();
        List<String> deletePsgCkiIdList = new ArrayList<>();
        this.deleteNm(memoryDataPnr, pnrNmDtos, psgCkiList, deleteNmIdList, deletePsgCkiIdList);
        if (CollUtil.isNotEmpty(deletePsgCkiIdList)) {
            iMnjxPsgCkiOptionService.lambdaUpdate()
                    .in(MnjxPsgCkiOption::getPsgCkiId, deletePsgCkiIdList)
                    .remove();
            iMnjxPsgOperateRecordService.lambdaUpdate()
                    .in(MnjxPsgOperateRecord::getPsgCkiId, deletePsgCkiIdList)
                    .remove();
            iMnjxPsgSeatService.lambdaUpdate()
                    .in(MnjxPsgSeat::getPsgCkiId, deletePsgCkiIdList)
                    .remove();
            iMnjxPsgCkiService.removeByIds(deletePsgCkiIdList);
        }
        if (CollUtil.isNotEmpty(deleteNmIdList)) {
            iMnjxLuggageCarryonService.lambdaUpdate()
                    .in(MnjxLuggageCarryon::getPnrNmId, deleteNmIdList)
                    .remove();
            iMnjxLuggageService.lambdaUpdate()
                    .in(MnjxLuggage::getPnrNmId, deleteNmIdList)
                    .remove();
            iMnjxPnrNmService.removeByIds(deleteNmIdList);
        }
    }

    /**
     * Title: handlePnrData
     * Description: 处理附加到PNR的项数据
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/12/1 10:27
     */
    private void handlePnrData(MemoryDataPnr memoryDataPnr) {
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        String pnrId = mnjxPnr.getPnrId();
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        // 新建PNR时新增
        this.insertPnr(mnjxPnr);
        // GN
        this.handleGn(memoryDataPnr);
        // NM
        this.handleNm(memoryDataPnr, mnjxPnr, pnrNmDtos);
        // SS
        this.handleSeg(memoryDataPnr, pnrId);
        // CT
        this.handleCt(memoryDataPnr.getPnrCtDtos(), pnrId);
        // TK
        this.handleTk(memoryDataPnr, pnrId);
        // FC
        this.handleFc(memoryDataPnr, pnrId);
        // OSI
        this.handleOsi(memoryDataPnr, pnrId);
        // RMK
        this.handleRmk(memoryDataPnr, pnrId);
        // FN
        this.handleFn(memoryDataPnr, pnrId);
        // EI
        this.handleEi(memoryDataPnr, pnrId);
        // FP
        this.handleFp(memoryDataPnr, pnrId);
        // TN
        this.handleTn(memoryDataPnr);
        // NM TICKET
        this.handleNmTicket(memoryDataPnr);
    }

    private void handleSpInfo(MemoryDataPnr memoryDataPnr) {
        List<MnjxSpInfo> mnjxSpInfoList = memoryDataPnr.getMnjxSpInfoList();
        if (CollUtil.isNotEmpty(mnjxSpInfoList)) {
            mnjxSpInfoList.forEach(s -> s.setAtNo(memoryDataPnr.getThisAtNo()));
            MemoryData memoryData = MemoryDataUtils.getMemoryData();
            if (ObjectUtil.isNotEmpty(memoryData)) {
                MemoryDataPnr oldMemoryDataPnr = memoryData.getMemoryDataPnr();
                MnjxSpInfo mnjxSpInfo = new MnjxSpInfo();
                mnjxSpInfo.setPnrId(oldMemoryDataPnr.getMnjxPnr().getPnrId());
                mnjxSpInfo.setSpToCrsPnr(memoryDataPnr.getMnjxPnr().getPnrCrs());
                mnjxSpInfo.setAtNo(oldMemoryDataPnr.getThisAtNo());
                mnjxSpInfoList.add(mnjxSpInfo);
            }
            iMnjxSpInfoService.saveBatch(mnjxSpInfoList);
        }
    }

    private void handleNmTicket(MemoryDataPnr memoryDataPnr) {
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        List<MnjxPnrNmTicket> nmTicketList = pnrNmDtos.stream()
                .flatMap(p -> p.getMnjxPnrNmTickets().stream())
                .collect(Collectors.toList());
        //入库 新增mnjxNmTicket的数据
        if (CollUtil.isNotEmpty(nmTicketList)) {
            List<String> ticketIds = nmTicketList.stream().map(MnjxPnrNmTicket::getNmTicketId).collect(Collectors.toList());
            //查询数据库，如果有进行更新，如果没有进行新增
            List<MnjxPnrNmTicket> dbTickets = iMnjxPnrNmTicketService.listByIds(ticketIds);
            if (CollUtil.isNotEmpty(dbTickets)) {
                List<String> dbTicketIds = dbTickets.stream().map(MnjxPnrNmTicket::getNmTicketId).collect(Collectors.toList());
                //筛选出已经入库的进行更新
                List<MnjxPnrNmTicket> pnrNmTickets = nmTicketList.stream().filter(t -> dbTicketIds.contains(t.getNmTicketId())).collect(Collectors.toList());
                iMnjxPnrNmTicketService.updateBatchById(pnrNmTickets);
                //筛选出数据库没有的数据进行入库
                List<MnjxPnrNmTicket> noExistTickets = nmTicketList.stream().filter(t -> !dbTicketIds.contains(t.getNmTicketId())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(noExistTickets)) {
                    this.insertNmTicket(nmTicketList);
                }
            } else {
                this.insertNmTicket(nmTicketList);
            }
        }
    }

    /**
     * Title: insertOrUpdatePnr
     * Description: PNR入库
     *
     * @param mnjxPnr mnjxPnr
     * <AUTHOR>
     * @date 2022/6/21 16:40
     */
    private void insertPnr(MnjxPnr mnjxPnr) {
        MnjxPnr dbPnr = mnjxPnr.selectById();
        if (ObjectUtil.isEmpty(dbPnr)) {
            mnjxPnr.insert();
        }
    }

    /**
     * Title: handleGn
     * Description: GN入库
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/21 16:40
     */
    private void handleGn(MemoryDataPnr memoryDataPnr) {
        List<PnrGnDto> pnrGnDtos = memoryDataPnr.getPnrGnDtos();
        if (CollUtil.isNotEmpty(pnrGnDtos)) {
            List<MnjxPnrRecord> mnjxPnrRecords = memoryDataPnr.getMnjxPnrRecords();
            MnjxPnrRecord record = mnjxPnrRecords.stream().filter(r -> Constant.PNR_GN.equals(r.getPnrType())).collect(Collectors.toList()).get(0);
            for (PnrGnDto pnrGnDto : pnrGnDtos) {
                MnjxPnrGn mnjxPnrGn = pnrGnDto.getMnjxPnrGn();
                record.setInputValue(mnjxPnrGn.getInputValue());
                boolean isExistInDb = StrUtil.isNotEmpty(mnjxPnrGn.getPnrGnId()) && ObjectUtil.isNotEmpty(mnjxPnrGn.selectById());
                if (pnrGnDto.isXe() && isExistInDb) {
                    mnjxPnrGn.deleteById();
                    memoryDataPnr.setNeedHdqca(true);
                } else if (!pnrGnDto.isXe() && !isExistInDb) {
                    mnjxPnrGn.insert();
                    memoryDataPnr.setNeedHdqca(true);
                } else if (!pnrGnDto.isXe() && isExistInDb && pnrGnDto.isChange()) {
                    mnjxPnrGn.updateById();
                    memoryDataPnr.setNeedHdqca(true);
                }
            }
        }
    }

    /**
     * Title: handleNm
     * Description: NM入库，重新处理序号
     *
     * @param memoryDataPnr memoryDataPnr
     * @param mnjxPnr       mnjxPnr
     * @param pnrNmDtos     pnrNmDtos
     * <AUTHOR>
     * @date 2022/6/21 16:40
     */
    private void handleNm(MemoryDataPnr memoryDataPnr, MnjxPnr mnjxPnr, List<PnrNmDto> pnrNmDtos) {
        // 处理姓名 及 归属姓名的项的序号
        List<MnjxPnrNm> updateNmList = new ArrayList<>();
        List<MnjxPnrNm> insertNmList = new ArrayList<>();
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            MnjxPnrNm mnjxPnrNm = pnrNmDto.getMnjxPnrNm();
            String pnrNmId = mnjxPnrNm.getPnrNmId();
            // 如果修改过姓名或删除过姓名，则需要将序号重新调整再入库
            String updateMark = pnrNmDto.getUpdateMark();
            if (pnrNmDto.isXe() || Constant.BE_UPDATED.equals(updateMark)) {
                int xeIndex = mnjxPnrNm.getPnrIndex();
                // 如果不是最后一个姓名，则需要把后面的姓名组序号减一
                if (xeIndex < pnrNmDtos.size()) {
                    for (int i = xeIndex; i < pnrNmDtos.size(); i++) {
                        pnrNmDtos.get(i).setPnrIndex(pnrNmDtos.get(i).getPnrIndex() - 1);
                        pnrNmDtos.get(i).getMnjxPnrNm().setPnrIndex(pnrNmDtos.get(i).getPnrIndex());
                        pnrNmDtos.get(i).getMnjxPnrNm().setPsgIndex(pnrNmDtos.get(i).getPnrIndex());
                    }
                }
                // 其他项的序号减一
                this.setOtherItemPnrIndexChange(memoryDataPnr, pnrNmDto);
                // 最大序号减一
                mnjxPnr.setMaxIndex(mnjxPnr.getMaxIndex() - 1);
            }
            // 常规通过pnrId查询姓名判断是否已存在
            List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, mnjxPnr.getPnrId())
                    .list();
            boolean isExistInDb = StrUtil.isNotEmpty(pnrNmId) && CollUtil.isNotEmpty(pnrNmList) && pnrNmList.stream().anyMatch(n -> pnrNmId.equals(n.getPnrNmId()));
            // 分离产生的判断需要用pnrNmId查询
            isExistInDb = isExistInDb || (StrUtil.isNotEmpty(pnrNmId) && ObjectUtil.isNotEmpty(mnjxPnrNm.selectById()));
            // 更新修改的NM
            if (!Constant.BE_UPDATED.equals(updateMark) && isExistInDb) {
                updateNmList.add(mnjxPnrNm);
                memoryDataPnr.setNeedHdqca(true);
            }
            // 新增NM
            else if (!pnrNmDto.isXe() && !isExistInDb && !Constant.BE_UPDATED.equals(pnrNmDto.getUpdateMark())) {
                insertNmList.add(mnjxPnrNm);
                memoryDataPnr.setNeedHdqca(true);
            }
        }
        if (CollUtil.isNotEmpty(updateNmList)) {
            iMnjxPnrNmService.updateBatchById(updateNmList);
        }
        if (CollUtil.isNotEmpty(insertNmList)) {
            iMnjxPnrNmService.saveBatch(insertNmList);
        }
    }

    /**
     * Title: handleSeg
     * Description: SS入库
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/21 16:41
     */
    private void handleSeg(MemoryDataPnr memoryDataPnr, String pnrId) {
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        long count = pnrSegDtos.stream().filter(p -> !p.isXe()).count();
        // 标记联程标识
        if (count > 1) {
            memoryDataPnr.getMnjxPnr().setInterlink("1");
        } else {
            memoryDataPnr.getMnjxPnr().setInterlink("0");
        }
        //重新设置航段序号
        List<MnjxPnrSeg> mnjxPnrSegs = new ArrayList<>();
        pnrSegDtos.forEach(pnrSegDto -> {
            if (pnrSegDto.isXe()) {
                return;
            }
            mnjxPnrSegs.add(pnrSegDto.getMnjxPnrSeg());
        });
        List<MnjxPnrSeg> mnjxPnrSegsAsc = mnjxPnrSegs.stream().sorted(Comparator.comparing(MnjxPnrSeg::getPnrSegNo)).collect(Collectors.toList());
        for (int i = 0; i < mnjxPnrSegsAsc.size(); i++) {
            mnjxPnrSegsAsc.get(i).setPnrSegNo(i + 1);
        }
        //存
        List<String> deleteSegIdList = new ArrayList<>();
        List<MnjxPnrSeg> insertSegList = new ArrayList<>();
        List<MnjxPnrSeg> updateSegList = new ArrayList<>();
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnrId)
                .list();
        pnrSegDtos.forEach(pnrSegDto -> {
            MnjxPnrSeg mnjxPnrSeg = pnrSegDto.getMnjxPnrSeg();
            // 订座是共享航班时，封口后航段组的值需要进行修改
            String inputValue = mnjxPnrSeg.getInputValue();
            if (StrUtil.isNotEmpty(pnrSegDto.getCarrierFlight()) && !inputValue.contains("OP-")) {
                String tenSpace = "          ";
                String[] split = inputValue.split(tenSpace);
                String replacePart = split[1];
                replacePart = replacePart.replaceAll("\\s{2,}", " ");
                String[] replacePartSplit = replacePart.split(" ");
                String str = StrUtil.format("{} {} OP-{}", replacePartSplit[replacePartSplit.length - 2], replacePartSplit[replacePartSplit.length - 1], pnrSegDto.getCarrierFlight());
                mnjxPnrSeg.setInputValue(StrUtil.format("{}{}{}", split[0], tenSpace, str));
            }
            String pnrSegId = mnjxPnrSeg.getPnrSegId();
            boolean isExistInDb = StrUtil.isNotEmpty(pnrSegId) && CollUtil.isNotEmpty(pnrSegList) && pnrSegList.stream().anyMatch(s -> pnrSegId.equals(s.getPnrSegId()));
            if (pnrSegDto.isXe() && isExistInDb) {
                deleteSegIdList.add(pnrSegId);
                memoryDataPnr.setNeedHdqca(true);
            } else if (!pnrSegDto.isXe() && !isExistInDb) {
                insertSegList.add(mnjxPnrSeg);
                memoryDataPnr.setNeedHdqca(true);
            } else if (!pnrSegDto.isXe() && isExistInDb && pnrSegDto.isChange()) {
                updateSegList.add(mnjxPnrSeg);
                memoryDataPnr.setNeedHdqca(true);
            }
        });
        if (CollUtil.isNotEmpty(deleteSegIdList)) {
            iMnjxPnrSegService.removeByIds(deleteSegIdList);
        }
        if (CollUtil.isNotEmpty(insertSegList)) {
            iMnjxPnrSegService.saveBatch(insertSegList);
        }
        if (CollUtil.isNotEmpty(updateSegList)) {
            iMnjxPnrSegService.updateBatchById(updateSegList);
        }
    }

    /**
     * Title: handleCt
     * Description: PNR CT入库
     *
     * @param pnrCtDtos 所有联系数据
     * <AUTHOR>
     * @date 2022/6/21 16:41
     */
    private void handleCt(List<PnrCtDto> pnrCtDtos, String pnrId) {
        List<MnjxPnrCt> pnrCtList = iMnjxPnrCtService.lambdaQuery()
                .eq(MnjxPnrCt::getPnrId, pnrId)
                .list();
        List<MnjxPnrCt> insertCtList = new ArrayList<>();
        List<String> deleteCtIdList = new ArrayList<>();
        pnrCtDtos.forEach(pnrCtDto -> {
            MnjxPnrCt mnjxPnrCt = pnrCtDto.getMnjxPnrCt();
            String pnrCtId = mnjxPnrCt.getPnrCtId();
            boolean isExistInDb = StrUtil.isNotEmpty(pnrCtId) && CollUtil.isNotEmpty(pnrCtList) && pnrCtList.stream().anyMatch(c -> pnrCtId.equals(c.getPnrCtId()));
            if (pnrCtDto.isXe() && isExistInDb) {
                deleteCtIdList.add(pnrCtId);
            } else if (!pnrCtDto.isXe() && !isExistInDb) {
                insertCtList.add(mnjxPnrCt);
            }
        });
        if (CollUtil.isNotEmpty(insertCtList)) {
            iMnjxPnrCtService.saveBatch(insertCtList);
        }
        if (CollUtil.isNotEmpty(deleteCtIdList)) {
            iMnjxPnrCtService.removeByIds(deleteCtIdList);
        }
    }

    /**
     * Title: handleNmCt
     * Description: NM CT入库
     *
     * @param pnrNmDto pnrNmDto
     * <AUTHOR>
     * @date 2022/6/21 16:41
     */
    private void handleNmCt(PnrNmDto pnrNmDto, List<MnjxNmCt> nmCts, List<String> deleteIdList, List<MnjxNmCt> insertList, List<MnjxNmCt> updateList) {
        List<PnrNmCtDto> pnrNmCtDtos = pnrNmDto.getPnrNmCtDtos();
        pnrNmCtDtos.forEach(c -> {
            MnjxNmCt mnjxNmCt = c.getMnjxNmCt();
            String ctId = mnjxNmCt.getPnrCtId();
            boolean isExistInDb = StrUtil.isNotEmpty(ctId) && CollUtil.isNotEmpty(nmCts) && nmCts.stream().anyMatch(n -> ctId.equals(n.getPnrCtId()));
            if (c.isXe() && isExistInDb) {
                deleteIdList.add(ctId);
            } else if (!c.isXe() && !isExistInDb) {
                insertList.add(mnjxNmCt);
            } else if (isExistInDb && !nmCts.stream().filter(n -> ctId.equals(n.getPnrCtId())).findFirst().get().toString().equals(mnjxNmCt.toString())) {
                updateList.add(mnjxNmCt);
            }
        });
    }

    /**
     * Title: handleTk
     * Description: PNR TK入库
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/21 16:41
     */
    private void handleTk(MemoryDataPnr memoryDataPnr, String pnrId) {
        List<PnrTkDto> pnrTkDtos = memoryDataPnr.getPnrTkDtos();
        List<MnjxPnrTk> pnrTkList = iMnjxPnrTkService.lambdaQuery()
                .eq(MnjxPnrTk::getPnrId, pnrId)
                .list();
        List<MnjxPnrTk> insertTkList = new ArrayList<>();
        List<String> deleteTkIdList = new ArrayList<>();
        pnrTkDtos.forEach(p -> {
            MnjxPnrTk mnjxPnrTk = p.getMnjxPnrTk();
            String pnrTkId = mnjxPnrTk.getPnrTkId();
            boolean isExistInDb = StrUtil.isNotEmpty(pnrTkId) && CollUtil.isNotEmpty(pnrTkList) && pnrTkList.stream().anyMatch(t -> pnrTkId.equals(t.getPnrTkId()));
            if (p.isXe() && isExistInDb) {
                deleteTkIdList.add(pnrTkId);
                memoryDataPnr.setNeedHdqca(true);
            } else if (!p.isXe() && !isExistInDb) {
                insertTkList.add(mnjxPnrTk);
                memoryDataPnr.setNeedHdqca(true);
            }
        });
        if (CollUtil.isNotEmpty(deleteTkIdList)) {
            iMnjxPnrTkService.removeByIds(deleteTkIdList);
        }
        if (CollUtil.isNotEmpty(insertTkList)) {
            iMnjxPnrTkService.saveBatch(insertTkList);
        }
    }

    /**
     * Title: handleFc
     * Description: PNR FC入库
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/21 16:41
     */
    private void handleFc(MemoryDataPnr memoryDataPnr, String pnrId) {
        List<PnrFcDto> pnrFcDtos = memoryDataPnr.getPnrFcDtos();
        List<MnjxPnrFc> pnrFcList = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnrId)
                .list();
        List<String> deleteFcIdList = new ArrayList<>();
        List<MnjxPnrFc> insertFcList = new ArrayList<>();
        pnrFcDtos.forEach(p -> {
            MnjxPnrFc mnjxPnrFc = p.getMnjxPnrFc();
            String pnrFcId = mnjxPnrFc.getPnrFcId();
            boolean isExistInDb = StrUtil.isNotEmpty(pnrFcId) && CollUtil.isNotEmpty(pnrFcList) && pnrFcList.stream().anyMatch(f -> pnrFcId.equals(f.getPnrFcId()));
            if (p.isXe() && isExistInDb) {
                deleteFcIdList.add(pnrFcId);
            } else if (!p.isXe() && !isExistInDb) {
                insertFcList.add(mnjxPnrFc);
            }
        });
        if (CollUtil.isNotEmpty(deleteFcIdList)) {
            iMnjxPnrFcService.removeByIds(deleteFcIdList);
        }
        if (CollUtil.isNotEmpty(insertFcList)) {
            iMnjxPnrFcService.saveBatch(insertFcList);
        }
    }

    /**
     * Title: handleNmFc
     * Description: NM FC入库
     *
     * @param pnrNmDto pnrNmDto
     * <AUTHOR>
     * @date 2022/6/21 16:41
     */
    private void handleNmFc(PnrNmDto pnrNmDto, List<MnjxNmFc> nmFcs, List<String> deleteIdList, List<MnjxNmFc> insertList) {
        List<PnrNmFcDto> pnrNmFcDtos = pnrNmDto.getPnrNmFcDtos();
        pnrNmFcDtos.forEach(p -> {
            MnjxNmFc mnjxNmFc = p.getMnjxNmFc();
            String nmFcId = mnjxNmFc.getNmFcId();
            boolean isExistInDb = StrUtil.isNotEmpty(nmFcId) && CollUtil.isNotEmpty(nmFcs) && nmFcs.stream().anyMatch(f -> nmFcId.equals(f.getNmFcId()));
            if (p.isXe() && isExistInDb) {
                deleteIdList.add(nmFcId);
            } else if (!p.isXe() && !isExistInDb) {
                insertList.add(mnjxNmFc);
            }
        });
    }

    /**
     * Title: handleSsr
     * Description: SSR入库
     *
     * @param pnrNmDto      pnrNmDto
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/21 16:41
     */
    private void handleSsr(PnrNmDto pnrNmDto, MemoryDataPnr memoryDataPnr, List<MnjxNmSsr> nmSsrs, List<String> deleteIdList, List<MnjxNmSsr> insertList, List<MnjxNmSsr> updateList) {
        List<PnrSsrDto> pnrSsrDtos = pnrNmDto.getPnrSsrDtos();
        PnrSsrDto foidSsr = pnrSsrDtos.stream()
                .filter(s -> !s.isXe() && Constant.SSR_TYPE_FOID.equals(s.getMnjxNmSsr().getSsrType()))
                .collect(Collectors.toList())
                .get(0);
        String[] split = foidSsr.getMnjxNmSsr().getSsrInfo().split(StrUtil.SPACE);
        String foidInfo = split[split.length - 1].split(StrUtil.SLASH)[0];
        AtomicReference<String> foidType = new AtomicReference<>("");
        AtomicReference<String> foidNo = new AtomicReference<>("");
        foidType.set(foidInfo.substring(0, 2));
        foidNo.set(foidInfo.substring(2));
        pnrSsrDtos.forEach(p -> {
            MnjxNmSsr mnjxNmSsr = p.getMnjxNmSsr();
            String ssrId = mnjxNmSsr.getNmSsrId();
            // SSR部分特服需要设置所归属的航段组，如果没有归属航段组，则认为是通程的特服项
            List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
            if (StrUtil.isNotEmpty(mnjxNmSsr.getOrgDst()) && ObjectUtil.isEmpty(mnjxNmSsr.getPnrSegNo())) {
                String orgDst = mnjxNmSsr.getOrgDst();
                PnrSegDto pnrSegDto = pnrSegDtos.stream()
                        .filter(s -> orgDst.equals(StrUtil.format("{}{}", s.getMnjxPnrSeg().getOrg(), s.getMnjxPnrSeg().getDst())))
                        .collect(Collectors.toList())
                        .get(0);
                mnjxNmSsr.setPnrSegNo(pnrSegDto.getMnjxPnrSeg().getPnrSegNo());
            } else if (StrUtil.isEmpty(mnjxNmSsr.getOrgDst()) && ObjectUtil.isNotEmpty(mnjxNmSsr.getPnrSegNo())) {
                int pnrSegNo = mnjxNmSsr.getPnrSegNo();
                PnrSegDto pnrSegDto = pnrSegDtos.stream()
                        .filter(s -> pnrSegNo == s.getMnjxPnrSeg().getPnrSegNo())
                        .collect(Collectors.toList())
                        .get(0);
                mnjxNmSsr.setOrgDst(StrUtil.format("{}{}", pnrSegDto.getMnjxPnrSeg().getOrg(), pnrSegDto.getMnjxPnrSeg().getDst()));
            }
            boolean isExistInDb = StrUtil.isNotEmpty(ssrId) && CollUtil.isNotEmpty(nmSsrs) && nmSsrs.stream().anyMatch(s -> ssrId.equals(s.getNmSsrId()));
            if ((p.isXe() || Constant.ACTION_CODE_XX.equals(p.getMnjxNmSsr().getActionCode())) && isExistInDb) {
                deleteIdList.add(ssrId);
                memoryDataPnr.setNeedHdqca(true);
            } else if (!p.isXe() && !Constant.ACTION_CODE_XX.equals(p.getMnjxNmSsr().getActionCode()) && !isExistInDb) {
                insertList.add(mnjxNmSsr);
                memoryDataPnr.setNeedHdqca(true);
                // 如果当前是常客，使用存储的证件类型和号码新增一条常客记录
                if (Constant.SSR_TYPE_FQTV.equals(mnjxNmSsr.getSsrType())) {
                    List<MnjxFrequenter> frequenterList = pnrNmDto.getMnjxFrequenterList();
                    // 筛选出当前航段关联的常客记录
                    frequenterList = frequenterList.stream()
                            .filter(f -> f.getAirlineCode().equals(mnjxNmSsr.getAirlineCode()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(frequenterList)) {
                        MnjxFrequenter frequenter = frequenterList.get(0);
                        // 数据库查询该常客是否已录入
                        MnjxFrequenter dbFrequenter = iMnjxFrequenterService.lambdaQuery()
                                .eq(MnjxFrequenter::getAirlineCode, frequenter.getAirlineCode())
                                .eq(MnjxFrequenter::getFrequenterCard, frequenter.getFrequenterCard())
                                .one();
                        // 没有录入，新增
                        if (ObjectUtil.isEmpty(dbFrequenter)) {
                            frequenter.setFrequenterCertificateType(foidType.get());
                            frequenter.setFrequenterCertificateNo(foidNo.get());
                            iMnjxFrequenterService.save(frequenter);
                        }
                    }
                }
            } else if (!p.isXe() && !Constant.ACTION_CODE_XX.equals(p.getMnjxNmSsr().getActionCode()) && isExistInDb && p.isChange()) {
                updateList.add(mnjxNmSsr);
                memoryDataPnr.setNeedHdqca(true);
                p.setChange(false);
            }
        });
    }

    /**
     * Title: handleOsi
     * Description: PNR OSI入库
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/21 16:42
     */
    private void handleOsi(MemoryDataPnr memoryDataPnr, String pnrId) {
        List<PnrOsiDto> pnrOsiDtos = memoryDataPnr.getPnrOsiDtos();
        List<MnjxPnrOsi> pnrOsiList = iMnjxPnrOsiService.lambdaQuery()
                .eq(MnjxPnrOsi::getPnrId, pnrId)
                .list();
        List<String> deleteIdList = new ArrayList<>();
        List<MnjxPnrOsi> insertList = new ArrayList<>();
        List<MnjxPnrOsi> updateList = new ArrayList<>();
        pnrOsiDtos.forEach(pnrOsiDto -> {
            MnjxPnrOsi mnjxPnrOsi = pnrOsiDto.getMnjxPnrOsi();
            String pnrOsiId = mnjxPnrOsi.getPnrOsiId();
            boolean isExistInDb = StrUtil.isNotEmpty(pnrOsiId) && CollUtil.isNotEmpty(pnrOsiList) && pnrOsiList.stream().anyMatch(o -> pnrOsiId.equals(o.getPnrOsiId()));
            if (pnrOsiDto.isXe() && isExistInDb) {
                deleteIdList.add(pnrOsiId);
            } else if (!pnrOsiDto.isXe() && !isExistInDb) {
                insertList.add(mnjxPnrOsi);
            } else if (isExistInDb && !mnjxPnrOsi.toString().equals(pnrOsiList.stream().filter(o -> pnrOsiId.equals(o.getPnrOsiId())).findFirst().get().toString())) {
                updateList.add(mnjxPnrOsi);
            }
        });
        if (CollUtil.isNotEmpty(deleteIdList)) {
            iMnjxPnrOsiService.removeByIds(deleteIdList);
        }
        if (CollUtil.isNotEmpty(insertList)) {
            iMnjxPnrOsiService.saveBatch(insertList);
        }
        if (CollUtil.isNotEmpty(updateList)) {
            iMnjxPnrOsiService.updateBatchById(updateList);
        }
    }

    /**
     * Title: handleNmOsi
     * Description: NM OSI入库
     *
     * @param pnrNmDto pnrNmDto
     * <AUTHOR>
     * @date 2022/6/21 16:42
     */
    private void handleNmOsi(PnrNmDto pnrNmDto, List<MnjxNmOsi> nmOsis, List<String> deleteIdList, List<MnjxNmOsi> insertList, List<MnjxNmOsi> updateList) {
        List<PnrNmOsiDto> pnrSsrDtos = pnrNmDto.getPnrNmOsiDtos();
        pnrSsrDtos.forEach(p -> {
            MnjxNmOsi mnjxNmOsi = p.getMnjxNmOsi();
            String osiId = mnjxNmOsi.getPnrOsiId();
            boolean isExistInDb = StrUtil.isNotEmpty(osiId) && CollUtil.isNotEmpty(nmOsis) && nmOsis.stream().anyMatch(o -> osiId.equals(o.getPnrOsiId()));
            if (p.isXe() && isExistInDb) {
                deleteIdList.add(osiId);
            } else if (!p.isXe() && !isExistInDb) {
                insertList.add(mnjxNmOsi);
            } else if (isExistInDb && !mnjxNmOsi.toString().equals(nmOsis.stream().filter(o -> osiId.equals(o.getPnrOsiId())).findFirst().get().toString())) {
                updateList.add(mnjxNmOsi);
            }
        });
    }

    /**
     * Title: handleRmk
     * Description: PNR RMK入库
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/21 16:42
     */
    private void handleRmk(MemoryDataPnr memoryDataPnr, String pnrId) {
        List<PnrRmkDto> pnrRmkDtos = memoryDataPnr.getPnrRmkDtos();
        List<MnjxPnrRmk> pnrRmkList = iMnjxPnrRmkService.lambdaQuery()
                .eq(MnjxPnrRmk::getPnrId, pnrId)
                .list();
        List<String> deleteIdList = new ArrayList<>();
        List<MnjxPnrRmk> insertList = new ArrayList<>();
        pnrRmkDtos.forEach(p -> {
            MnjxPnrRmk mnjxPnrRmk = p.getMnjxPnrRmk();
            String rmkId = mnjxPnrRmk.getPnrRmkId();
            boolean isExistInDb = StrUtil.isNotEmpty(rmkId) && CollUtil.isNotEmpty(pnrRmkList) && pnrRmkList.stream().anyMatch(r -> rmkId.equals(r.getPnrRmkId()));
            if (p.isXe() && isExistInDb) {
                deleteIdList.add(rmkId);
            } else if (!p.isXe() && !isExistInDb) {
                insertList.add(mnjxPnrRmk);
            }
        });
        if (CollUtil.isNotEmpty(deleteIdList)) {
            iMnjxPnrRmkService.removeByIds(deleteIdList);
        }
        if (CollUtil.isNotEmpty(insertList)) {
            iMnjxPnrRmkService.saveBatch(insertList);
        }
    }

    /**
     * Title: handleNmRmk
     * Description: NM RMK入库
     *
     * @param pnrNmDto pnrNmDto
     * <AUTHOR>
     * @date 2022/6/21 16:42
     */
    private void handleNmRmk(PnrNmDto pnrNmDto, List<MnjxNmRmk> nmRmks, List<String> deleteIdList, List<MnjxNmRmk> insertList) {
        List<PnrNmRmkDto> pnrNmRmkDtos = pnrNmDto.getPnrNmRmkDtos();
        pnrNmRmkDtos.forEach(p -> {
            MnjxNmRmk mnjxNmRmk = p.getMnjxNmRmk();
            String rmkId = mnjxNmRmk.getNmRmkId();
            boolean isExistInDb = StrUtil.isNotEmpty(rmkId) && CollUtil.isNotEmpty(nmRmks) && nmRmks.stream().anyMatch(r -> rmkId.equals(r.getNmRmkId()));
            if (p.isXe() && isExistInDb) {
                deleteIdList.add(rmkId);
            } else if (!p.isXe() && !isExistInDb) {
                insertList.add(mnjxNmRmk);
            }
        });
    }

    /**
     * Title: handleFn
     * Description: PNR FN入库
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/21 16:43
     */
    private void handleFn(MemoryDataPnr memoryDataPnr, String pnrId) {
        List<PnrFnDto> pnrFnDtos = memoryDataPnr.getPnrFnDtos();
        List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnrId)
                .list();
        List<String> deleteIdList = new ArrayList<>();
        List<MnjxPnrFn> insertList = new ArrayList<>();
        pnrFnDtos.forEach(p -> {
            MnjxPnrFn mnjxPnrFn = p.getMnjxPnrFn();
            String fnId = mnjxPnrFn.getPnrFnId();
            boolean isExistInDb = StrUtil.isNotEmpty(fnId) && CollUtil.isNotEmpty(pnrFnList) && pnrFnList.stream().anyMatch(f -> fnId.equals(f.getPnrFnId()));
            if (p.isXe() && isExistInDb) {
                deleteIdList.add(fnId);
            } else if (!p.isXe() && !isExistInDb) {
                insertList.add(mnjxPnrFn);
            }
        });
        if (CollUtil.isNotEmpty(deleteIdList)) {
            iMnjxPnrFnService.removeByIds(deleteIdList);
        }
        if (CollUtil.isNotEmpty(insertList)) {
            iMnjxPnrFnService.saveBatch(insertList);
        }
    }

    /**
     * Title: handleNmFn
     * Description: NM FN入库
     *
     * @param pnrNmDto pnrNmDto
     * <AUTHOR>
     * @date 2022/6/21 16:43
     */
    private void handleNmFn(PnrNmDto pnrNmDto, List<MnjxNmFn> nmFns, List<String> deleteIdList, List<MnjxNmFn> insertList) {
        List<PnrNmFnDto> pnrNmFnDtos = pnrNmDto.getPnrNmFnDtos();
        pnrNmFnDtos.forEach(p -> {
            MnjxNmFn mnjxNmFn = p.getMnjxNmFn();
            String fnId = mnjxNmFn.getNmFnId();
            boolean isExistInDb = StrUtil.isNotEmpty(fnId) && CollUtil.isNotEmpty(nmFns) && nmFns.stream().anyMatch(f -> fnId.equals(f.getNmFnId()));
            if (p.isXe() && isExistInDb) {
                deleteIdList.add(fnId);
            } else if (!p.isXe() && !isExistInDb) {
                insertList.add(mnjxNmFn);
            }
        });
    }

    /**
     * Title: handleEi
     * Description: PNR EI入库
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/21 16:43
     */
    private void handleEi(MemoryDataPnr memoryDataPnr, String pnrId) {
        List<PnrEiDto> pnrEiDtos = memoryDataPnr.getPnrEiDtos();
        List<MnjxPnrEi> pnrEiList = iMnjxPnrEiService.lambdaQuery()
                .eq(MnjxPnrEi::getPnrId, pnrId)
                .list();
        List<String> deleteIdList = new ArrayList<>();
        List<MnjxPnrEi> insertList = new ArrayList<>();
        pnrEiDtos.forEach(p -> {
            MnjxPnrEi mnjxPnrEi = p.getMnjxPnrEi();
            String eiId = mnjxPnrEi.getPnrEiId();
            boolean isExistInDb = StrUtil.isNotEmpty(eiId) && CollUtil.isNotEmpty(pnrEiList) && pnrEiList.stream().anyMatch(e -> eiId.equals(e.getPnrEiId()));
            if (p.isXe() && isExistInDb) {
                deleteIdList.add(eiId);
            } else if (!p.isXe() && !isExistInDb) {
                insertList.add(mnjxPnrEi);
            }
        });
        if (CollUtil.isNotEmpty(deleteIdList)) {
            iMnjxPnrEiService.removeByIds(deleteIdList);
        }
        if (CollUtil.isNotEmpty(insertList)) {
            iMnjxPnrEiService.saveBatch(insertList);
        }
    }

    /**
     * Title: handleNmEi
     * Description: NM EI入库
     *
     * @param pnrNmDto pnrNmDto
     * <AUTHOR>
     * @date 2022/6/21 16:43
     */
    private void handleNmEi(PnrNmDto pnrNmDto, List<MnjxNmEi> nmEis, List<String> deleteIdList, List<MnjxNmEi> insertList) {
        List<PnrNmEiDto> pnrNmEiDtos = pnrNmDto.getPnrNmEiDtos();
        pnrNmEiDtos.forEach(p -> {
            MnjxNmEi mnjxNmEi = p.getMnjxNmEi();
            String eiId = mnjxNmEi.getNmEiId();
            boolean isExistInDb = StrUtil.isNotEmpty(eiId) && CollUtil.isNotEmpty(nmEis) && nmEis.stream().anyMatch(e -> eiId.equals(e.getNmEiId()));
            if (p.isXe() && isExistInDb) {
                deleteIdList.add(eiId);
            } else if (!p.isXe() && !isExistInDb) {
                insertList.add(mnjxNmEi);
            }
        });
    }

    /**
     * Title: handleOi
     * Description: OI入库
     *
     * @param pnrNmDto pnrNmDto
     * <AUTHOR>
     * @date 2022/6/21 16:43
     */
    private void handleOi(PnrNmDto pnrNmDto, List<MnjxNmOi> nmOis, List<String> deleteIdList, List<MnjxNmOi> insertList) {
        List<PnrOiDto> pnrOiDtos = pnrNmDto.getPnrOiDtos();
        pnrOiDtos.forEach(p -> {
            MnjxNmOi nmOi = p.getMnjxNmOi();
            String oiId = nmOi.getNmOiId();
            boolean isExistInDb = StrUtil.isNotEmpty(oiId) && CollUtil.isNotEmpty(nmOis) && nmOis.stream().anyMatch(o -> oiId.equals(o.getNmOiId()));
            if (p.isXe() && isExistInDb) {
                deleteIdList.add(oiId);
            } else if (!p.isXe() && !isExistInDb) {
                insertList.add(nmOi);
            }
        });
    }

    /**
     * Title: handleXn
     * Description: XN入库
     *
     * @param pnrNmDto pnrNmDto
     * <AUTHOR>
     * @date 2022/6/21 16:43
     */
    private void handleXn(PnrNmDto pnrNmDto, List<MnjxNmXn> nmXns, List<String> deleteIdList, List<MnjxNmXn> insertList, List<MnjxNmXn> updateList) {
        List<PnrXnDto> pnrXnDtos = pnrNmDto.getPnrXnDtos();
        pnrXnDtos.forEach(p -> {
            MnjxNmXn mnjxNmXn = p.getMnjxNmXn();
            String xnId = mnjxNmXn.getNmXnId();
            boolean isExistInDb = StrUtil.isNotEmpty(xnId) && CollUtil.isNotEmpty(nmXns) && nmXns.stream().anyMatch(x -> xnId.equals(x.getNmXnId()));
            if (p.isXe() && isExistInDb) {
                deleteIdList.add(xnId);
            } else if (!p.isXe() && !isExistInDb) {
                insertList.add(mnjxNmXn);
            } else if (isExistInDb && !mnjxNmXn.toString().equals(nmXns.stream().filter(x -> xnId.equals(x.getNmXnId())).findFirst().get().toString())) {
                updateList.add(mnjxNmXn);
            }
        });
    }

    /**
     * Title: handleFp
     * Description: PNR FP入库
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/21 16:43
     */
    private void handleFp(MemoryDataPnr memoryDataPnr, String pnrId) {
        List<PnrFpDto> pnrFpDtos = memoryDataPnr.getPnrFpDtos();
        List<MnjxPnrFp> fpList = iMnjxPnrFpService.lambdaQuery()
                .eq(MnjxPnrFp::getPnrId, pnrId)
                .list();
        List<String> deleteIdList = new ArrayList<>();
        List<MnjxPnrFp> insertList = new ArrayList<>();
        pnrFpDtos.forEach(p -> {
            MnjxPnrFp mnjxPnrFp = p.getMnjxPnrFp();
            String fpId = mnjxPnrFp.getPnrFpId();
            boolean isExistInDb = StrUtil.isNotEmpty(fpId) && CollUtil.isNotEmpty(fpList) && fpList.stream().anyMatch(f -> fpId.equals(f.getPnrFpId()));
            if (p.isXe() && isExistInDb) {
                deleteIdList.add(fpId);
            } else if (!p.isXe() && !isExistInDb) {
                insertList.add(mnjxPnrFp);
            }
        });
        if (CollUtil.isNotEmpty(deleteIdList)) {
            iMnjxPnrFpService.removeByIds(deleteIdList);
        }
        if (CollUtil.isNotEmpty(insertList)) {
            iMnjxPnrFpService.saveBatch(insertList);
        }
    }

    /**
     * TN
     *
     * @param memoryDataPnr memoryDataPnr
     */
    private void handleTn(MemoryDataPnr memoryDataPnr) {
        List<PnrNmTnDto> pnrNmTnDtos = memoryDataPnr.getPnrNmTnDtos();
        pnrNmTnDtos.forEach(p -> {
            MnjxPnrNmTn mnjxPnrNmTn = p.getMnjxPnrNmTn();
            MnjxPnrNmTn dbTn = mnjxPnrNmTn.selectById();
            boolean isExistInDb = StrUtil.isNotEmpty(mnjxPnrNmTn.getTnId()) && ObjectUtil.isNotEmpty(dbTn);
            if (p.isXe() && isExistInDb) {
                mnjxPnrNmTn.deleteById();
            } else if (!p.isXe() && !isExistInDb) {
                mnjxPnrNmTn.insert();
            } else if (isExistInDb && !mnjxPnrNmTn.toString().equals(dbTn.toString())) {
                mnjxPnrNmTn.updateById();
            }
        });
    }

    /**
     * 入库 新增mnjxNmTicket的数据
     */
    private void insertNmTicket(List<MnjxPnrNmTicket> nmTickets) {
        nmTickets = CollUtil.distinct(nmTickets);
        List<String> ticketIds = nmTickets.stream()
                .map(MnjxPnrNmTicket::getNmTicketId)
                .collect(Collectors.toList());
        List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.listByIds(ticketIds);
        //筛选已经入库的数据
        if (CollUtils.isNotEmpty(nmTicketList)) {
            List<String> nmTicketIds = nmTicketList.stream()
                    .map(MnjxPnrNmTicket::getNmTicketId)
                    .collect(Collectors.toList());
            //去除需要入库的数据
            nmTickets = nmTickets.stream()
                    .filter(t -> !nmTicketIds.contains(t.getNmTicketId()))
                    .collect(Collectors.toList());
        }
        //插入数据库
        if (CollUtil.isNotEmpty(nmTickets)) {
            iMnjxPnrNmTicketService.saveBatch(nmTickets);
        }
    }

    /**
     * Title: handleNmFp
     * Description: NM FP入库
     *
     * @param pnrNmDto pnrNmDto
     * <AUTHOR>
     * @date 2022/6/21 16:43
     */
    private void handleNmFp(PnrNmDto pnrNmDto, List<MnjxNmFp> nmFps, List<String> deleteIdList, List<MnjxNmFp> insertList) {
        List<PnrNmFpDto> pnrNmFpDtos = pnrNmDto.getPnrNmFpDtos();
        pnrNmFpDtos.forEach(p -> {
            MnjxNmFp mnjxNmFp = p.getMnjxNmFp();
            String fpId = mnjxNmFp.getNmFpId();
            boolean isExistInDb = StrUtil.isNotEmpty(fpId) && CollUtil.isNotEmpty(nmFps) && nmFps.stream().anyMatch(f -> fpId.equals(f.getNmFpId()));
            if (p.isXe() && isExistInDb) {
                deleteIdList.add(fpId);
            } else if (!p.isXe() && !isExistInDb) {
                insertList.add(mnjxNmFp);
            }
        });
    }

    private void handleNmUm(PnrNmDto pnrNmDto) {
        MnjxPnrNmUm mnjxPnrNmUm = pnrNmDto.getMnjxPnrNmUm();
        if (ObjectUtil.isNotEmpty(mnjxPnrNmUm)) {
            boolean isExistInDb = StrUtil.isNotEmpty(mnjxPnrNmUm.getNmUmId()) && ObjectUtil.isNotEmpty(mnjxPnrNmUm.selectById());
            if (!isExistInDb) {
                iMnjxPnrNmUmService.save(mnjxPnrNmUm);
            } else if (pnrNmDto.isXe()) {
                iMnjxPnrNmUmService.removeById(mnjxPnrNmUm.getNmUmId());
            } else {
                iMnjxPnrNmUmService.updateById(mnjxPnrNmUm);
            }
        }
    }

    /**
     * Title: deleteNm
     * Description: 删除NM
     *
     * @param memoryDataPnr memoryDataPnr
     * @param pnrNmDtos     pnrNmDtos
     * <AUTHOR>
     * @date 2022/7/1 16:37
     */
    private void deleteNm(MemoryDataPnr memoryDataPnr, List<PnrNmDto> pnrNmDtos, List<MnjxPsgCki> psgCkis, List<String> deleteNmIdList, List<String> deletePsgCkiIdList) {
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            MnjxPnrNm mnjxPnrNm = pnrNmDto.getMnjxPnrNm();
            String nmId = mnjxPnrNm.getPnrNmId();
            boolean isExistInDb = StrUtil.isNotEmpty(nmId) && ObjectUtil.isNotEmpty(mnjxPnrNm.selectById());
            // 删除XE的NM
            if (pnrNmDto.isXe() && isExistInDb) {
                List<MnjxPsgCki> ckis = psgCkis.stream()
                        .filter(p -> nmId.equals(p.getPnrNmId()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(ckis)) {
                    deletePsgCkiIdList.addAll(ckis.stream().map(MnjxPsgCki::getPsgCkiId).collect(Collectors.toList()));
                }
                deleteNmIdList.add(nmId);
                memoryDataPnr.setNeedHdqca(true);
            }
        }
    }

    /**
     * Title: handleAtNo
     * Description: 生成封口记录
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 16:11
     */
    private void handleAtNo(MemoryDataPnr memoryDataPnr, MnjxSi mnjxSi) {
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        String pnrId = mnjxPnr.getPnrId();
        MnjxPnrAt mnjxPnrAt = new MnjxPnrAt();
        mnjxPnrAt.setPnrId(pnrId);
        mnjxPnrAt.setAtDateTime(DateTime.now());
        mnjxPnrAt.setAtSiId(mnjxSi.getSiId());
        mnjxPnrAt.setAtNo(memoryDataPnr.getThisAtNo());
        if (memoryDataPnr.isByAtK()) {
            mnjxPnrAt.setAtType("K");
        }
        mnjxPnrAt.insert();
        memoryDataPnr.getMnjxPnrAts().add(mnjxPnrAt);
    }

    /**
     * Title: createHistory
     * Description: 生成历史记录
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/21 16:39
     */
    private void createHistory(MemoryDataPnr memoryDataPnr) {
        boolean newPnr = memoryDataPnr.isNewPnr();
        List<MnjxPnrRecord> mnjxPnrRecords = memoryDataPnr.getMnjxPnrRecords();
        // 有共享航班时，修改航段组的值
        mnjxPnrRecords.stream()
                .filter(r -> Constant.PNR_SEG.equals(r.getPnrType()))
                .forEach(r -> {
                    String inputValue = r.getInputValue();
                    if (inputValue.trim().startsWith("*") && !inputValue.contains("OP-")) {
                        int index = r.getPnrIndex();
                        PnrSegDto pnrSegDto = memoryDataPnr.getPnrSegDtos().stream()
                                .filter(s -> !s.isXe() && index == s.getPnrIndex())
                                .collect(Collectors.toList())
                                .get(0);
                        r.setInputValue(pnrSegDto.getMnjxPnrSeg().getInputValue());
                    }
                });
        // 新建的PNR封口入库的历史记录不会记录任何X C的操作
        if (newPnr) {
            List<MnjxPnrRecord> collect = mnjxPnrRecords.stream().filter(r -> StrUtil.isEmpty(r.getChangeMark())).collect(Collectors.toList());
            // 如果有 X C 的操作，需要重新排序
            if (collect.size() != mnjxPnrRecords.size()) {
                int i = 1;
                for (MnjxPnrRecord mnjxPnrRecord : collect) {
                    if (mnjxPnrRecord.getPnrIndex() == 0) {
                        continue;
                    }
                    mnjxPnrRecord.setPnrIndex(i);
                    i++;
                }
            }
            iMnjxPnrRecordService.saveBatch(collect);
        } else {
            MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
            // 查询数据库已存在的历史记录
            List<MnjxPnrRecord> dbRecords = iMnjxPnrRecordService.lambdaQuery().eq(MnjxPnrRecord::getPnrId, mnjxPnr.getPnrId()).isNull(MnjxPnrRecord::getChangeAtNo).list();
            // 获取已存在的ID
            List<String> existIdList = dbRecords.stream().map(MnjxPnrRecord::getPnrRecordId).collect(Collectors.toList());
            // 从当前历史记录列表获取对应数据库已存在的历史记录
            List<MnjxPnrRecord> existRecords = mnjxPnrRecords.stream().filter(r -> existIdList.contains(r.getPnrRecordId())).collect(Collectors.toList());
            // 获取已存在部分被X C 操作的历史记录
            List<MnjxPnrRecord> updateOrDeleteRecords = existRecords.stream().filter(r -> StrUtil.isNotEmpty(r.getChangeMark())).collect(Collectors.toList());
            // 更新改变时的封口编号
            updateOrDeleteRecords.forEach(r -> r.setChangeAtNo(memoryDataPnr.getThisAtNo()));
            iMnjxPnrRecordService.updateBatchById(updateOrDeleteRecords);
            List<MnjxPnrRecord> deleteRecords = updateOrDeleteRecords.stream().filter(m -> Constant.DELETE.equals(m.getChangeMark())).collect(Collectors.toList());
            // 修改姓名遗留的部分直接删除掉
            if (CollUtil.isNotEmpty(deleteRecords)) {
                iMnjxPnrRecordService.removeByIds(deleteRecords.stream().map(MnjxPnrRecord::getPnrRecordId).collect(Collectors.toList()));
            }
            // 其他没有被X C操作的项，序号需要进行修改
            List<MnjxPnrRecord> updateIndexRecords = mnjxPnrRecords.stream().filter(r -> StrUtil.isEmpty(r.getChangeMark())).collect(Collectors.toList());
            // 序号更新
            int i = 1;
            for (MnjxPnrRecord mnjxPnrRecord : updateIndexRecords) {
                if (mnjxPnrRecord.getPnrIndex() == 0) {
                    continue;
                }
                mnjxPnrRecord.setPnrIndex(i);
                i++;
            }
            // 拿出新增的部分
            List<MnjxPnrRecord> toInsertRecords = updateIndexRecords.stream().filter(r -> StrUtil.isEmpty(r.getPnrRecordId())).collect(Collectors.toList());
            iMnjxPnrRecordService.saveBatch(toInsertRecords);
            // 拿出更新index的部分
            List<MnjxPnrRecord> toUpdateIndexRecords = updateIndexRecords.stream().filter(r -> StrUtil.isNotEmpty(r.getPnrRecordId())).collect(Collectors.toList());
            iMnjxPnrRecordService.updateBatchById(toUpdateIndexRecords);
        }
    }

    /**
     * Title: createAirlineResponse
     * Description: 生成航司系统反馈记录
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 16:12
     */
    private void createAirlineResponse(MemoryDataPnr memoryDataPnr, MnjxSi mnjxSi) {
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        // 如果存在航司封口
        if (memoryDataPnr.isNeedHdqca()) {
            String caAtNo = StrUtil.fill(String.valueOf(Integer.parseInt(memoryDataPnr.getThisAtNo()) + 1), '0', 3, true);
            MnjxPnrAt caAt = new MnjxPnrAt();
            caAt.setPnrId(mnjxPnr.getPnrId());
            caAt.setAtNo(caAtNo);
            caAt.setAtSiId(mnjxSi.getSiId());
            caAt.setAtDateTime(DateTime.now());
            caAt.setAtType("CA");
            caAt.insert();
            memoryDataPnr.getMnjxPnrAts().add(caAt);
        }
    }

    /**
     * Title: updateAvailableSeat
     * Description: 更新对应销售舱位剩余座位数
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/7/27 11:51
     */
    private void updateAvailableSeat(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrSegDto> segDtoList = memoryDataPnr.getPnrSegDtos();
        List<PnrSegDto> filterList = segDtoList.stream().filter(s -> !s.isXe() && !"SA".equals(s.getMnjxPnrSeg().getPnrSegType())).collect(Collectors.toList());
        List<String> queryParams = new ArrayList<>();
        filterList.forEach(s -> queryParams.add(StrUtil.format("{}:{}", StrUtil.isNotEmpty(s.getCarrierFlight()) ? s.getCarrierFlight() : s.getMnjxPnrSeg().getFlightNo(), s.getMnjxPnrSeg().getFlightDate())));
        List<OpenCabinFlightDto> dtos = pnrCommandMapper.retrieveOpenCabinListByFlightNoDateList(queryParams);
        List<MnjxOpenCabin> updateList = new ArrayList<>();
        for (PnrSegDto segDto : filterList) {
            MnjxPnrSeg pnrSeg = segDto.getMnjxPnrSeg();
            String flightNo = StrUtil.isNotEmpty(segDto.getCarrierFlight()) ? segDto.getCarrierFlight() : pnrSeg.getFlightNo();
            String flightDate = pnrSeg.getFlightDate();
            List<MnjxOpenCabin> openCabins = dtos.stream()
                    .filter(d -> flightDate.equals(d.getFlightDate()) && flightNo.equals(d.getFlightNo()))
                    .collect(Collectors.toList());
            String sellCabin = pnrSeg.getSellCabin();
            int seatNumber = pnrSeg.getSeatNumber();
            List<MnjxOpenCabin> openCabinList = this.setMultiSegOpenCabin(pnrSeg.getOrg(), pnrSeg.getDst(), openCabins);
            for (MnjxOpenCabin o : openCabinList) {
                if (o.getSellCabin().equals(sellCabin)) {
                    int availableNumber = o.getSeatAvailable() - seatNumber;
                    if (availableNumber < 0) {
                        throw new UnifiedResultException(Constant.OVER_SEATS);
                    }
                    o.setSeatAvailable(availableNumber);
                }
            }
            updateList.addAll(openCabinList);
        }
        iMnjxOpenCabinService.updateBatchById(updateList);
    }

    /**
     * Title: setMultiSegOpenCabin
     * Description: 当航班存在多航节时，更新需要处理的开舱数据
     *
     * @param org            org
     * @param dst            dst
     * @param mnjxOpenCabins mnjxOpenCabins
     * @return 当航班存在多航节时，更新需要处理的开舱数据
     * <AUTHOR>
     * @date 2022/11/17 9:26
     */
    private List<MnjxOpenCabin> setMultiSegOpenCabin(String org, String dst, List<MnjxOpenCabin> mnjxOpenCabins) {
        List<String> planSectionIdList = mnjxOpenCabins.stream()
                .map(MnjxOpenCabin::getPlanSectionId)
                .distinct()
                .collect(Collectors.toList());
        // 多航节航班需要判断消耗哪一节的座位数
        if (planSectionIdList.size() > 1) {
            MnjxAirport orgAirport = iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, org)
                    .one();
            MnjxAirport dstAirport = iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, dst)
                    .one();
            List<MnjxPlanSection> planSectionList = iMnjxPlanSectionService.lambdaQuery()
                    .in(MnjxPlanSection::getPlanSectionId, planSectionIdList)
                    .orderByAsc(MnjxPlanSection::getEstimateOff)
                    .list();
            List<MnjxPlanSection> singlePlanSectionList = planSectionList.stream()
                    .filter(s -> orgAirport.getAirportId().equals(s.getDepAptId()) && dstAirport.getAirportId().equals(s.getArrAptId()))
                    .collect(Collectors.toList());
            // 如果为空说明出发到达包含了多个航节
            if (CollUtil.isEmpty(singlePlanSectionList)) {
                List<MnjxPlanSection> list = new ArrayList<>();
                for (MnjxPlanSection planSection : planSectionList) {
                    if (CollUtil.isNotEmpty(list)) {
                        list.add(planSection);
                        if (dstAirport.getAirportId().equals(planSection.getArrAptId())) {
                            break;
                        }
                    } else if (orgAirport.getAirportId().equals(planSection.getDepAptId())) {
                        list.add(planSection);
                    }
                }
                List<String> finalPlanSectionIdList = list.stream()
                        .map(MnjxPlanSection::getPlanSectionId)
                        .collect(Collectors.toList());
                return mnjxOpenCabins.stream()
                        .filter(o -> finalPlanSectionIdList.contains(o.getPlanSectionId()))
                        .collect(Collectors.toList());
            } else {
                List<String> finalPlanSectionIdList = singlePlanSectionList.stream()
                        .map(MnjxPlanSection::getPlanSectionId)
                        .collect(Collectors.toList());
                return mnjxOpenCabins.stream()
                        .filter(o -> finalPlanSectionIdList.contains(o.getPlanSectionId()))
                        .collect(Collectors.toList());
            }
        } else {
            return mnjxOpenCabins;
        }
    }

    /**
     * Title: addAvailableSeat
     * Description: 可用座位数还原
     *
     * @param lastDbSegList lastDbSegList
     * @param thisDbSegList thisDbSegList
     * <AUTHOR>
     * @date 2022/8/4 11:46
     */
    private void addAvailableSeat(MemoryDataPnr memoryDataPnr, List<MnjxPnrSeg> lastDbSegList, List<MnjxPnrSeg> thisDbSegList) throws UnifiedResultException {
        for (MnjxPnrSeg lastSeg : lastDbSegList) {
            if (Constant.SA.equals(lastSeg.getPnrSegType())) {
                continue;
            }
            for (MnjxPnrSeg thisSeg : thisDbSegList) {
                if (Constant.SA.equals(thisSeg.getPnrSegType())) {
                    continue;
                }
                // XE了旅客的情况：如果是相同的航段，检验座位数不同，则将减少的座位数释放回可用座位数
                if (lastSeg.getPnrSegId().equals(thisSeg.getPnrSegId()) && lastSeg.getSeatNumber().intValue() != thisSeg.getSeatNumber().intValue()) {
                    int seatNumberDiff = lastSeg.getSeatNumber() - thisSeg.getSeatNumber();
                    String flightNo = StrUtil.isNotEmpty(thisSeg.getCarrierFlight()) ? thisSeg.getCarrierFlight() : thisSeg.getFlightNo();
                    String flightDate = thisSeg.getFlightDate();
                    String sellCabin = thisSeg.getSellCabin();
                    List<MnjxOpenCabin> openCabinList = pnrCommandMapper.retrieveOpenCabinList(flightNo, flightDate);
                    openCabinList = this.setMultiSegOpenCabin(thisSeg.getOrg(), thisSeg.getDst(), openCabinList);
                    for (MnjxOpenCabin o : openCabinList) {
                        if (o.getSellCabin().equals(sellCabin)) {
                            int availableNumber = o.getSeatAvailable() + seatNumberDiff;
                            o.setSeatAvailable(availableNumber);
                        }
                    }
                    iMnjxOpenCabinService.updateBatchById(openCabinList);
                }
            }
        }
        // 没有出票的情况，修改了航段组，需要释放原航段航班的座位数，占用新航段航班的座位数
        if (CollUtil.isEmpty(memoryDataPnr.getPnrNmTnDtos()) && memoryDataPnr.getPnrSegDtos().stream().anyMatch(PnrSegDto::isXe)) {
            // 释放旧的座位
            for (MnjxPnrSeg lastSeg : lastDbSegList) {
                if (Constant.SA.equals(lastSeg.getPnrSegType())) {
                    continue;
                }
                String flightNo = StrUtil.isNotEmpty(lastSeg.getCarrierFlight()) ? lastSeg.getCarrierFlight() : lastSeg.getFlightNo();
                String flightDate = lastSeg.getFlightDate();
                String sellCabin = lastSeg.getSellCabin();
                List<MnjxOpenCabin> openCabinList = pnrCommandMapper.retrieveOpenCabinList(flightNo, flightDate);
                openCabinList = this.setMultiSegOpenCabin(lastSeg.getOrg(), lastSeg.getDst(), openCabinList);
                openCabinList.stream().filter(o -> o.getSellCabin().equals(sellCabin)).forEach(k -> {
                    int availableNumber = k.getSeatAvailable() + memoryDataPnr.getPnrNmDtos().size();
                    k.setSeatAvailable(availableNumber);
                });
                iMnjxOpenCabinService.updateBatchById(openCabinList);
            }
            // 占座
            for (MnjxPnrSeg thisSeg : thisDbSegList) {
                if (Constant.SA.equals(thisSeg.getPnrSegType())) {
                    continue;
                }
                String flightNo = StrUtil.isNotEmpty(thisSeg.getCarrierFlight()) ? thisSeg.getCarrierFlight() : thisSeg.getFlightNo();
                String flightDate = thisSeg.getFlightDate();
                String sellCabin = thisSeg.getSellCabin();
                List<MnjxOpenCabin> openCabinList = pnrCommandMapper.retrieveOpenCabinList(flightNo, flightDate);
                openCabinList = this.setMultiSegOpenCabin(thisSeg.getOrg(), thisSeg.getDst(), openCabinList);
                for (MnjxOpenCabin o : openCabinList) {
                    if (o.getSellCabin().equals(sellCabin)) {
                        int availableNumber = o.getSeatAvailable() - (int) memoryDataPnr.getPnrNmDtos().stream().filter(n -> !n.isXe()).count();
                        if (availableNumber < 0) {
                            throw new UnifiedResultException(Constant.OVER_SEATS);
                        }
                        o.setSeatAvailable(availableNumber);
                    }
                }
                iMnjxOpenCabinService.updateBatchById(openCabinList);
            }
        }
        // 改签的处理
        this.freeChangeReleaseSeat(memoryDataPnr, lastDbSegList, thisDbSegList);
    }

    /**
     * Title: checkPnrInControl
     * Description: 检查PNR控制是否超时
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 10:32
     */
    private void checkPnrInControl(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        // 当前控制的PNR是否超时
        String key = StrUtil.format("{}{}", memoryDataPnr.getMemoryDataPnrId(), mnjxPnr.getPnrCrs());
        Map<String, Date> pnrControlTimeMap = memoryDataPnr.getPnrControlTimeMap();
        if (pnrControlTimeMap.containsKey(key)) {
            Date controlDateTime = pnrControlTimeMap.get(key);
            Date nowDateTime = DateUtil.date();
            Calendar controlCal = Calendar.getInstance();
            controlCal.setTime(controlDateTime);
            controlCal.add(Calendar.MINUTE, 30);
            Date timeOutDateTime = controlCal.getTime();
            if (DateUtil.compare(timeOutDateTime, nowDateTime) < 0) {
                // 自动释放控制的PNR
                memoryDataPnr.clearPnr();
                throw new UnifiedResultException(Constant.PNR_MAX_TIME);
            }
        }
    }

    /**
     * Title: checkGnOrNm
     * Description: 如果PNR组无GN信息行且NM信息组数量少于1，报错：CHECK NAME
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 10:34
     */
    private void checkGnOrNm(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrGnDto> pnrGnDtos = memoryDataPnr.getPnrGnDtos();
        boolean noGn = CollUtil.isEmpty(pnrGnDtos);
        if (!noGn) {
            long count = pnrGnDtos.stream().filter(g -> !g.isXe()).count();
            noGn = count == 0;
        }
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        boolean noNm = CollUtil.isEmpty(pnrNmDtos);
        if (!noNm) {
            long xeNmCount = pnrNmDtos.stream().filter(PnrNmDto::isXe).count();
            noNm = xeNmCount == pnrNmDtos.size();
        }
        if (noGn && noNm) {
            throw new UnifiedResultException(Constant.CHECK_NAME);
        }
    }

    /**
     * Title: checkSeg
     * Description: 如果SS航段信息组数量少于1，报错：CHECK SEGMENT
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 10:52
     */
    private void checkSeg(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        boolean noSeg = CollUtils.isEmpty(pnrSegDtos);
        if (!noSeg) {
            long xeSegCount = pnrSegDtos.stream()
                    .filter(PnrSegDto::isXe)
                    .count();
            noSeg = xeSegCount == pnrSegDtos.size();
        }
        if (noSeg) {
            throw new UnifiedResultException(Constant.CHECK_SEG);
        }
        // 如果是共享航班，再次确认共享航班开启状态
        if (pnrSegDtos.stream().anyMatch(p -> !p.isXe() && StrUtils.isNotEmpty(p.getCarrierFlight()))) {
            List<String> shareFlightNos = pnrSegDtos.stream()
                    .filter(p -> !p.isXe() && StrUtils.isNotEmpty(p.getCarrierFlight()))
                    .map(p -> p.getMnjxPnrSeg().getFlightNo())
                    .distinct()
                    .collect(Collectors.toList());
            int openShareSize = iMnjxFlightService.lambdaQuery()
                    .in(MnjxFlight::getFlightNo, shareFlightNos)
                    .eq(MnjxFlight::getShareState, Constant.STR_ONE)
                    .list()
                    .size();
            if (openShareSize != shareFlightNos.size()) {
                throw new UnifiedResultException(Constant.NOT_SHARED_FLIGHT);
            }
        }
    }

    /**
     * Title: checkSsrFoid
     * Description: 如果无GN信息且存在任意非婴儿旅客无关联的SSR FOID身份信息，报错：PLEASE INPUT VALID IDENTITY INFORMATION
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 10:55
     */
    private void checkSsrFoid(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos().stream()
                .filter(nm -> !nm.isXe())
                .collect(Collectors.toList());
        List<String> nmIds = new ArrayList<>();
        List<String> ssrNmIds = new ArrayList<>();
        for (PnrNmDto p : pnrNmDtos) {
            if (!p.isXe() && !Constant.BE_UPDATED.equals(p.getUpdateMark())) {
                List<PnrSsrDto> pnrSsrDtos = p.getPnrSsrDtos();
                // 检查FOID
                List<PnrSsrDto> foidSsrs = pnrSsrDtos.stream()
                        .filter(s -> !s.isXe() && Constant.SSR_TYPE_FOID.equals(s.getMnjxNmSsr().getSsrType()))
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(foidSsrs)) {
                    throw new UnifiedResultException(Constant.NO_VALID_FOID);
                }

                for (PnrSsrDto pnrSsrDto : pnrSsrDtos) {
                    if (!pnrSsrDto.isXe() && !Constant.ACTION_CODE_XX.equals(pnrSsrDto.getMnjxNmSsr().getActionCode()) && Constant.SSR_TYPE_FOID.equals(pnrSsrDto.getMnjxNmSsr().getSsrType())) {
                        ssrNmIds.add(pnrSsrDto.getMnjxNmSsr().getPnrNmId());
                    }
                }
                nmIds.add(p.getMnjxPnrNm().getPnrNmId());
            }
        }
        nmIds = CollUtil.distinct(nmIds);
        ssrNmIds = CollUtil.distinct(ssrNmIds);
        if (memoryDataPnr.isNewPnr() && nmIds.size() != ssrNmIds.size()) {
            throw new UnifiedResultException(Constant.NO_VALID_FOID);
        }
        // 如果有多个旅客，且存在多个旅客姓名和身份证号相同，不允许封口
        if (pnrNmDtos.size() > 1) {
            Map<String, List<PnrNmDto>> nameMap = pnrNmDtos.stream().collect(Collectors.groupingBy(p -> p.getMnjxPnrNm().getName()));
            for (Map.Entry<String, List<PnrNmDto>> entry : nameMap.entrySet()) {
                List<PnrNmDto> dtoList = entry.getValue();
                if (dtoList.size() > 1) {
                    long count = dtoList.stream()
                            .flatMap(p -> p.getPnrSsrDtos().stream())
                            .filter(p -> !p.isXe() && !Constant.ACTION_CODE_XX.equals(p.getMnjxNmSsr().getActionCode()) && Constant.SSR_TYPE_FOID.equals(p.getMnjxNmSsr().getSsrType()))
                            .map(p -> p.getMnjxNmSsr().getInputValue().split(" NI")[1].split("/")[0])
                            .distinct()
                            .count();
                    if (count != dtoList.size()) {
                        String errorMsg = StrUtil.format("\u0010@ LB-0330572 P1E0T1 ERR-2 \u0010TST:DB P1E0T1$D(129)    {} 4923", DateUtil.now().split(" ")[1]);
                        throw new UnifiedResultException(errorMsg);
                    }
                }
            }
        }
    }

    /**
     * Title: checkSsrFqtv
     * Description: 验证FQTV
     *
     * @param memoryDataPnr
     * @return
     * <AUTHOR>
     * @date 2023/3/17 14:13
     */
    private void checkSsrFqtv(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            if (pnrNmDto.isXe() || Constant.BE_UPDATED.equals(pnrNmDto.getUpdateMark())) {
                continue;
            }
            List<PnrSsrDto> pnrSsrDtos = pnrNmDto.getPnrSsrDtos();
            List<PnrSsrDto> fqtvSsrDtos = pnrSsrDtos.stream()
                    .filter(p -> !p.isXe() && Constant.SSR_TYPE_FQTV.equals(p.getMnjxNmSsr().getSsrType()))
                    .collect(Collectors.toList());
            List<PnrSsrDto> foid = pnrSsrDtos.stream()
                    .filter(p -> !p.isXe() && Constant.SSR_TYPE_FOID.equals(p.getMnjxNmSsr().getSsrType()))
                    .collect(Collectors.toList());
            MnjxNmSsr foidSsr = foid.get(0).getMnjxNmSsr();
            String foidInfo = foidSsr.getInputValue().split(" ")[4].split("/")[0];
            String foidType = foidInfo.substring(0, 2);
            String foidNo = foidInfo.substring(2);
            List<MnjxFrequenter> inputFrequenterList = pnrNmDto.getMnjxFrequenterList();
            if (CollUtil.isNotEmpty(fqtvSsrDtos) && CollUtil.isNotEmpty(inputFrequenterList)) {
                // 验证和FOID证件号码是否匹配
                for (MnjxFrequenter inputFrequenter : inputFrequenterList) {
                    // 用当前输入的常客卡号查询常客记录
                    MnjxFrequenter dbFrequenter = iMnjxFrequenterService.lambdaQuery()
                            .eq(MnjxFrequenter::getAirlineCode, inputFrequenter.getAirlineCode())
                            .eq(MnjxFrequenter::getFrequenterCard, inputFrequenter.getFrequenterCard())
                            .eq(MnjxFrequenter::getFrequenterLevel, inputFrequenter.getFrequenterLevel())
                            .one();
                    // 存在常客记录，对比存在的证件信息和输入的证件信息是否匹配
                    if (ObjectUtil.isNotEmpty(dbFrequenter)) {
                        String frequenterCertificateType = dbFrequenter.getFrequenterCertificateType();
                        String frequenterCertificateNo = dbFrequenter.getFrequenterCertificateNo();
                        if (!foidSsr.getSsrInfo().contains(StrUtil.format("{}{}", frequenterCertificateType, frequenterCertificateNo))) {
                            throw new UnifiedResultException(Constant.INVALID_PROFILE_NUMBER);
                        }
                    }
                    // 不存在常客记录，用证件信息查询常客记录
                    else {
                        dbFrequenter = iMnjxFrequenterService.lambdaQuery()
                                .eq(MnjxFrequenter::getAirlineCode, inputFrequenter.getAirlineCode())
                                .eq(MnjxFrequenter::getFrequenterCertificateType, foidType)
                                .eq(MnjxFrequenter::getFrequenterCertificateNo, foidNo)
                                .one();
                        // 证件信息查询到常客，和输入的常客卡进行匹配
                        if (ObjectUtil.isNotEmpty(dbFrequenter)) {
                            String dbCard = dbFrequenter.getFrequenterCard();
                            if (!dbCard.equals(inputFrequenter.getFrequenterCard())) {
                                throw new UnifiedResultException(Constant.INVALID_PROFILE_NUMBER);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Title: checkOsi
     * Description: 如果OSI CTCT或CTCM信息组数量少于1，报错：PLEASE INPUT OSI (AIRLINE) CTCT OR OSI (AIRLINE) CTCM
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 14:14
     */
    private void checkOsi(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrOsiDto> pnrOsiDtos = memoryDataPnr.getPnrOsiDtos().stream()
                .filter(o -> !o.isXe())
                .collect(Collectors.toList());
        boolean noPnrCtctCtcm = CollUtil.isEmpty(pnrOsiDtos);
        List<PnrNmOsiDto> nmOsiDtos = new ArrayList<>();
        List<List<PnrNmOsiDto>> collect = memoryDataPnr.getPnrNmDtos().stream()
                .filter(p -> !p.isXe() && !Constant.BE_UPDATED.equals(p.getUpdateMark()))
                .map(PnrNmDto::getPnrNmOsiDtos)
                .collect(Collectors.toList());
        for (List<PnrNmOsiDto> list : collect) {
            nmOsiDtos.addAll(list);
        }
        nmOsiDtos = nmOsiDtos.stream()
                .filter(o -> !o.isXe())
                .collect(Collectors.toList());
        boolean noNmCtctCtcm = CollUtil.isEmpty(nmOsiDtos);
        if (noPnrCtctCtcm && noNmCtctCtcm) {
            throw new UnifiedResultException(Constant.PNR_NO_CT_CM);
        }
        if (!noPnrCtctCtcm) {
            long count = pnrOsiDtos.stream()
                    .filter(o -> Constant.OSI_CTCT.equals(o.getMnjxPnrOsi().getPnrOsiType()) || Constant.OSI_CTCM.equals(o.getMnjxPnrOsi().getPnrOsiType()))
                    .count();
            noPnrCtctCtcm = count == 0;
        }
        if (!noNmCtctCtcm) {
            long count = nmOsiDtos.stream()
                    .filter(o -> Constant.OSI_CTCT.equals(o.getMnjxNmOsi().getPnrOsiType()) || Constant.OSI_CTCM.equals(o.getMnjxNmOsi().getPnrOsiType()))
                    .count();
            noNmCtctCtcm = count == 0;
        }
        if (noPnrCtctCtcm && noNmCtctCtcm) {
            throw new UnifiedResultException(Constant.PNR_NO_CT_CM);
        }
    }

    /**
     * Title: checkCt
     * Description:  如果CT信息组数量小于1，报错：CHECK CONTACT AGENCY CTC；如果CT信息组数量大于1，但是CT 'T' 信息组数量少于1，报错：CHECK AGENCY CTC
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 14:31
     */
    private void checkCt(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrCtDto> pnrCtDtos = memoryDataPnr.getPnrCtDtos();
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        List<PnrNmCtDto> pnrNmCtDtos = new ArrayList<>();
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            if (pnrNmDto.isXe() || Constant.BE_UPDATED.equals(pnrNmDto.getUpdateMark())) {
                continue;
            }
            pnrNmCtDtos.addAll(pnrNmDto.getPnrNmCtDtos());
        }
        pnrCtDtos = pnrCtDtos.stream()
                .filter(c -> !c.isXe())
                .collect(Collectors.toList());
        pnrNmCtDtos = pnrNmCtDtos.stream()
                .filter(c -> !c.isXe())
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(pnrCtDtos) && CollUtil.isEmpty(pnrNmCtDtos)) {
            throw new UnifiedResultException(Constant.PNR_NO_CT);
        }
        if (CollUtil.isEmpty(pnrCtDtos) && CollUtil.isNotEmpty(pnrNmCtDtos)) {
            throw new UnifiedResultException(Constant.PNR_NO_CT_T);
        }
    }

    /**
     * Title: checkXn
     * Description: 如果没有关联的SSR INFT信息组（必须同婴儿XN信息一样，关联在同一个成人旅客上），报错：SSR INFT MISSING；如果有行动代码不是NN，DK，HK 的，皆报错：CHECK BLNK CODE
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 14:41
     */
    private void checkXn(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            if (pnrNmDto.isXe() || Constant.BE_UPDATED.equals(pnrNmDto.getUpdateMark())) {
                continue;
            }
            List<PnrXnDto> pnrXnDtos = pnrNmDto.getPnrXnDtos();
            List<PnrSsrDto> pnrSsrDtos = pnrNmDto.getPnrSsrDtos().stream()
                    .filter(x -> !x.isXe())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrXnDtos) && pnrXnDtos.stream().anyMatch(x -> !x.isXe())) {
                // 验证INFT是否存在
                boolean missingInft = CollUtil.isEmpty(pnrSsrDtos);
                if (!missingInft) {
                    long count = pnrSsrDtos.stream().filter(p -> Constant.SSR_TYPE_INFT.equals(p.getMnjxNmSsr().getSsrType())).count();
                    missingInft = count == 0;
                    int segSize = (int) memoryDataPnr.getPnrSegDtos().stream()
                            .filter(p -> !p.isXe() && !Constant.SA.equals(p.getMnjxPnrSeg().getPnrSegType()))
                            .count();
                    if (count != segSize) {
                        throw new UnifiedResultException(Constant.INPUT_SSR_INFT_CONFIRM);
                    }
                }
                if (missingInft) {
                    throw new UnifiedResultException(Constant.PNR_INFT_MISSING);
                }
                // 验证INFT的行动代码，如果有行动代码不是NN，DK，HK 的，皆报错：CHECK BLNK CODE
                List<PnrSsrDto> inftSsrs = pnrSsrDtos.stream()
                        .filter(p -> Constant.SSR_TYPE_INFT.equals(p.getMnjxNmSsr().getSsrType()))
                        .collect(Collectors.toList());
                MnjxNmSsr mnjxNmSsr = inftSsrs.get(0).getMnjxNmSsr();
                String actionCode = mnjxNmSsr.getActionCode();
                if (!StrUtil.equalsAny(actionCode, Constant.ACTION_CODE_HK, Constant.ACTION_CODE_NN, Constant.ACTION_CODE_DK, Constant.ACTION_CODE_KK)) {
                    throw new UnifiedResultException(Constant.CHECK_BLNK_CODE);
                }
            }
        }
    }

    /**
     * Title: checkSegContinuity
     * Description: 检查多航段组时的空间连续性和时间连续性
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 14:53
     */
    private void checkSegContinuity(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        List<PnrSegDto> filterPnrSegDtos = pnrSegDtos.stream().filter(p -> !p.isXe()).collect(Collectors.toList());

        PnrSegDto firstPnrSegDto = filterPnrSegDtos.get(0);
        MnjxPnrSeg mnjxPnrSeg = firstPnrSegDto.getMnjxPnrSeg();
        if (StrUtil.equalsAny(Constant.SA, mnjxPnrSeg.getPnrSegType(), filterPnrSegDtos.get(filterPnrSegDtos.size() - 1).getMnjxPnrSeg().getPnrSegType())) {
            throw new UnifiedResultException("缺口程不允许在首/尾航段");
        }

        if (filterPnrSegDtos.size() > 1) {
            for (int i = 0; i < filterPnrSegDtos.size() - 1; i++) {
                PnrSegDto thisSegDto = filterPnrSegDtos.get(i);
                PnrSegDto nextSegDto = filterPnrSegDtos.get(i + 1);
                MnjxPnrSeg thisSeg = thisSegDto.getMnjxPnrSeg();
                MnjxPnrSeg nextSeg = nextSegDto.getMnjxPnrSeg();
                // 检查空间连续性
                String thisDst = thisSeg.getDst();
                String nextOrg = nextSeg.getOrg();
                if (!thisDst.equals(nextOrg)) {
                    throw new UnifiedResultException(Constant.CHECK_CONTINUITY);
                }
            }
            // 检查时间连续性的，剔除SA航段
            filterPnrSegDtos = filterPnrSegDtos.stream()
                    .filter(s -> !"SA".equals(s.getMnjxPnrSeg().getPnrSegType()))
                    .collect(Collectors.toList());
            if (filterPnrSegDtos.size() > 1) {
                for (int i = 0; i < filterPnrSegDtos.size() - 1; i++) {
                    PnrSegDto thisSegDto = filterPnrSegDtos.get(i);
                    PnrSegDto nextSegDto = filterPnrSegDtos.get(i + 1);
                    MnjxPnrSeg thisSeg = thisSegDto.getMnjxPnrSeg();
                    String thisFlightDate = thisSeg.getFlightDate();
                    String thisArrTime = thisSeg.getEstimateArr();
                    List<MnjxPlanSection> thisPlanSections = pnrCommandMapper.retrievePlanSection(StrUtils.isNotEmpty(thisSegDto.getCarrierFlight()) ? thisSegDto.getCarrierFlight() : thisSeg.getFlightNo(), thisSeg.getFlightDate());
                    String change = "";
                    if (thisPlanSections.size() > 1) {
                        MnjxAirport dstAirport = iMnjxAirportService.lambdaQuery()
                                .eq(MnjxAirport::getAirportCode, thisSeg.getDst())
                                .one();
                        for (MnjxPlanSection mnjxPlanSection : thisPlanSections) {
                            if (mnjxPlanSection.getArrAptId().equals(dstAirport.getAirportId())) {
                                String estimateArrChange = mnjxPlanSection.getEstimateArrChange();
                                if (StrUtil.isNotEmpty(estimateArrChange)) {
                                    change = estimateArrChange.substring(1);
                                    break;
                                }
                            }
                        }
                    } else {
                        String estimateArrChange = thisPlanSections.get(0).getEstimateArrChange();
                        if (StrUtil.isNotEmpty(estimateArrChange)) {
                            change = estimateArrChange.substring(1);
                        }
                    }
                    MnjxPnrSeg nextSeg = nextSegDto.getMnjxPnrSeg();
                    String nextFlightDate = nextSeg.getFlightDate();
                    String nextOffTime = nextSeg.getEstimateOff();
                    // 检查时间连续性（转机时间）
                    thisArrTime = StrUtil.format("{} {}:{}:00", thisFlightDate, thisArrTime.substring(0, 2), thisArrTime.substring(2));
                    nextOffTime = StrUtil.format("{} {}:{}:00", nextFlightDate, nextOffTime.substring(0, 2), nextOffTime.substring(2));
                    Calendar thisCal = Calendar.getInstance();
                    Calendar nextCal = Calendar.getInstance();
                    thisCal.setTime(DateUtil.parseDateTime(thisArrTime));
                    if (StrUtil.isNotEmpty(change)) {
                        thisCal.add(Calendar.DAY_OF_MONTH, Integer.parseInt(change));
                    }
                    nextCal.setTime(DateUtil.parseDateTime(nextOffTime));
                    if (nextCal.compareTo(thisCal) < 0) {
                        throw new UnifiedResultException(Constant.CHECK_CONNECTION);
                    } else {
                        thisCal.add(Calendar.MINUTE, 120);
                        if (nextCal.compareTo(thisCal) < 0) {
                            String errorInfo = StrUtil.format("{} 和 {} 航班的最短转机时间是120分钟, 请修改联程航段!", thisSeg.getFlightNo(), nextSeg.getFlightNo());
                            throw new UnifiedResultException(errorInfo);
                        }
                    }
                }
            }
        }
    }

    /**
     * Title: checkTk
     * Description: 检查出票项
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 15:53
     */
    private void checkTk(MemoryDataPnr memoryDataPnr, MnjxOffice mnjxOffice) throws UnifiedResultException {
        List<PnrTkDto> pnrTkDtos = memoryDataPnr.getPnrTkDtos();
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        // 获取第一个航段组
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos().stream()
                .filter(p -> !p.isXe())
                .collect(Collectors.toList());
        PnrSegDto firstPnrSegDto = pnrSegDtos.get(0);
        MnjxPnrSeg mnjxPnrSeg = firstPnrSegDto.getMnjxPnrSeg();
        String flightDate = mnjxPnrSeg.getFlightDate();
        String estimateOff = mnjxPnrSeg.getEstimateOff();
        // 当首次封口时，如果没有TK，则自动生成一条
        if (StrUtil.isEmpty(mnjxPnr.getPnrCrs())) {
            //为了应付考试 如果没有tk,就不校验
            List<PnrTkDto> tkDtos = pnrTkDtos.stream().filter(p -> !p.isXe()).collect(Collectors.toList());
            if (CollUtil.isEmpty(pnrTkDtos) || CollUtil.isEmpty(tkDtos)) {
                PnrTkDto pnrTkDto = new PnrTkDto();
                MnjxPnrTk mnjxPnrTk = new MnjxPnrTk();
                mnjxPnrTk.setPnrId(mnjxPnr.getPnrId());
                mnjxPnrTk.setPnrIndex(mnjxPnr.getMaxIndex() + 1);
                mnjxPnrTk.setEtdzOffice(mnjxOffice.getOfficeNo());
                String comPlanEtdzDate = DateUtils.ymd2Com(flightDate);
                mnjxPnrTk.setPlanEtdzDate(flightDate);
                // 自动生成一条截止最近一趟航班出发前2小时的TK/TL信息
                int timeInt = Integer.parseInt(estimateOff) - 200;
                boolean yesterday = false;
                if (timeInt < 0) {
                    timeInt = timeInt + 2400;
                    yesterday = true;
                }
                // 处理如果时间到了前一天，重新设置日期
                if (yesterday) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(DateUtils.ymd2Date(flightDate));
                    calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) - 1);
                    Date yesterdayDate = calendar.getTime();
                    comPlanEtdzDate = DateUtils.ymd2Com(DateUtils.date2ymd(yesterdayDate));
                    mnjxPnrTk.setPlanEtdzDate(DateUtils.date2ymd(yesterdayDate));
                }
                String planEtdzTime = StrUtil.fill(String.valueOf(timeInt), '0', 4, true);
                mnjxPnrTk.setPlanEtdzTime(planEtdzTime);
                mnjxPnrTk.setPnrTkType(Constant.TK_TYPE_TL);
                // 拼接回显
                String inputValue = StrUtil.format("{}/{}/{}/{}", mnjxPnrTk.getPnrTkType(), planEtdzTime, comPlanEtdzDate, mnjxOffice.getOfficeNo());
                mnjxPnrTk.setInputValue(inputValue);
                pnrTkDto.setPnrIndex(mnjxPnrTk.getPnrIndex());
                pnrTkDto.setMnjxPnrTk(mnjxPnrTk);
                pnrTkDtos.add(pnrTkDto);
                mnjxPnr.setMaxIndex(mnjxPnr.getMaxIndex() + 1);
                // 添加TK历史记录
                MnjxPnrRecord mnjxPnrRecord = new MnjxPnrRecord();
                mnjxPnrRecord.setPnrId(mnjxPnr.getPnrId());
                mnjxPnrRecord.setAtNo(memoryDataPnr.getThisAtNo());
                mnjxPnrRecord.setPnrIndex(pnrTkDto.getPnrIndex());
                mnjxPnrRecord.setPnrType(Constant.PNR_TK);
                mnjxPnrRecord.setInputValue(mnjxPnrTk.getInputValue());
                memoryDataPnr.getMnjxPnrRecords().add(mnjxPnrRecord);
            } else {
                // 验证TK存在
//                if (pnrTkDtos.stream().allMatch(PnrTkDto::isXe)) {
//                    throw new UnifiedResultException(Constant.CHECK_TICKET);
//                } else {
//                    this.checkTkTime(pnrTkDtos, flightDate, estimateOff);
//                }
                this.checkTkTime(pnrTkDtos, flightDate, estimateOff);
            }
        }
        // 非首次封口
        else {
//            // 验证TK存在
//            if (CollUtil.isEmpty(pnrTkDtos) || pnrTkDtos.stream().allMatch(PnrTkDto::isXe)) {
//                throw new UnifiedResultException(Constant.CHECK_TICKET);
//            } else {
//                this.checkTkTime(pnrTkDtos, flightDate, estimateOff);
//            }
            //为了应付考试 如果没有tk,就不校验
            // 验证TK存在
            if (CollUtil.isNotEmpty(pnrTkDtos)) {
                this.checkTkTime(pnrTkDtos, flightDate, estimateOff);
            }
        }
    }

    /**
     * Title: handleActionCode
     * Description: 处理行动代码
     *
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/15 16:10
     */
    private void handleActionCode(MemoryDataPnr memoryDataPnr) {
        List<MnjxPnrRecord> mnjxPnrRecords = memoryDataPnr.getMnjxPnrRecords();
        // 航段组行动代码修改为 HK
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        pnrSegDtos = pnrSegDtos.stream().filter(s -> !"SA".equals(s.getMnjxPnrSeg().getPnrSegType())).collect(Collectors.toList());
        pnrSegDtos.forEach(p -> {
            MnjxPnrSeg mnjxPnrSeg = p.getMnjxPnrSeg();
            if (!Constant.ACTION_CODE_HK.equals(mnjxPnrSeg.getActionCode()) && !Constant.ACTION_CODE_RR.equals(mnjxPnrSeg.getActionCode())) {
                mnjxPnrSeg.setActionCode(Constant.ACTION_CODE_HK);
                // 同时将回显值也改成HK
                int seatNumber = mnjxPnrSeg.getSeatNumber();
                String oldValue = mnjxPnrSeg.getInputValue();
                String oldActionCode = StrUtil.format("DK{}", seatNumber);
                String newActionCode = StrUtil.format("HK{}", seatNumber);
                mnjxPnrSeg.setInputValue(oldValue.replace(oldActionCode, newActionCode));
                List<MnjxPnrRecord> recordList = mnjxPnrRecords.stream().filter(r -> oldValue.equals(r.getInputValue())).collect(Collectors.toList());
                // 需要判断一次空，如果XE的航段又重新加回来可能会出错
                if (CollUtil.isNotEmpty(recordList)) {
                    MnjxPnrRecord record = recordList.get(0);
                    record.setInputValue(record.getInputValue().replace(oldActionCode, newActionCode));
                    p.setChange(true);
                }
            }
        });
        // SSR项修改
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        pnrNmDtos.forEach(p -> {
            List<PnrSsrDto> pnrSsrDtos = p.getPnrSsrDtos();
            pnrSsrDtos.forEach(s -> {
                MnjxNmSsr mnjxNmSsr = s.getMnjxNmSsr();
                String oldValue = mnjxNmSsr.getInputValue();
                // INFT项的行动代码变化：刚建立 NN -> 第一次封口 @ KK -> 第二次封口 @K HK
                // 如果第一次封口就使用了@K，则变成HN，这一项的行动代码不能再做任何修改，除非删掉此项重新建立
                // 只有变成了 HK 状态，才能进行出票
                if (Constant.SSR_TYPE_INFT.equals(mnjxNmSsr.getSsrType())) {
                    if (Constant.ACTION_CODE_NN.equals(mnjxNmSsr.getActionCode()) && !s.isXe()) {
                        if (memoryDataPnr.isByAtK()) {
                            mnjxNmSsr.setActionCode(Constant.ACTION_CODE_HN);
                            mnjxNmSsr.setInputValue(mnjxNmSsr.getInputValue().replace(" NN1 ", " HN1 "));
                            mnjxNmSsr.setSsrInfo(mnjxNmSsr.getInputValue());
                            MnjxPnrRecord record = mnjxPnrRecords.stream().filter(r -> oldValue.equals(r.getInputValue())).collect(Collectors.toList()).get(0);
                            record.setInputValue(record.getInputValue().replace(" NN1 ", " HN1 "));
                            s.setChange(true);
                        } else {
                            mnjxNmSsr.setActionCode(Constant.ACTION_CODE_KK);
                            mnjxNmSsr.setInputValue(mnjxNmSsr.getInputValue().replace(" NN1 ", " KK1 "));
                            mnjxNmSsr.setSsrInfo(mnjxNmSsr.getInputValue());
                            MnjxPnrRecord record = mnjxPnrRecords.stream().filter(r -> oldValue.equals(r.getInputValue())).collect(Collectors.toList()).get(0);
                            record.setInputValue(record.getInputValue().replace(" NN1 ", " KK1 "));
                            s.setChange(true);
                        }
                    } else if (Constant.ACTION_CODE_KK.equals(mnjxNmSsr.getActionCode()) && memoryDataPnr.isByAtK()) {
                        mnjxNmSsr.setActionCode(Constant.ACTION_CODE_HK);
                        mnjxNmSsr.setInputValue(mnjxNmSsr.getInputValue().replace(" KK1 ", " HK1 "));
                        mnjxNmSsr.setSsrInfo(mnjxNmSsr.getInputValue());
                        MnjxPnrRecord record = mnjxPnrRecords.stream().filter(r -> oldValue.equals(r.getInputValue())).collect(Collectors.toList()).get(0);
                        record.setInputValue(record.getInputValue().replace(" KK1 ", " HK1 "));
                        s.setChange(true);
                    }
                }
                // 其他SSR都修改成HK；除了 XX，XX会执行删除。
                else {
                    if (!Constant.ACTION_CODE_HK.equals(mnjxNmSsr.getActionCode()) && !Constant.ACTION_CODE_XX.equals(mnjxNmSsr.getActionCode())) {
                        mnjxNmSsr.setActionCode(Constant.ACTION_CODE_HK);
                        mnjxNmSsr.setInputValue(mnjxNmSsr.getInputValue().replace(" NN1 ", " HK1 "));
                        mnjxNmSsr.setSsrInfo(mnjxNmSsr.getInputValue());
                        MnjxPnrRecord record = mnjxPnrRecords.stream().filter(r -> oldValue.equals(r.getInputValue())).collect(Collectors.toList()).get(0);
                        record.setInputValue(record.getInputValue().replace(" NN1 ", " HK1 "));
                    }
                }
            });
        });
    }

    /**
     * Title: checkTkTime
     * Description: 检查TK项
     *
     * @param pnrTkDtos   pnrTkDtos
     * @param flightDate  flightDate
     * @param estimateOff estimateOff
     * <AUTHOR>
     * @date 2022/12/1 10:29
     */
    private void checkTkTime(List<PnrTkDto> pnrTkDtos, String flightDate, String estimateOff) throws UnifiedResultException {
        // TK TL只能有一项
        List<PnrTkDto> filterTkDtos = pnrTkDtos.stream()
                .filter(p -> !p.isXe() && Constant.TK_TYPE_TL.equals(p.getMnjxPnrTk().getPnrTkType()))
                .collect(Collectors.toList());
        if (filterTkDtos.size() > 1) {
            throw new UnifiedResultException(Constant.CHECK_TICKET);
        }
        if (CollUtil.isEmpty(filterTkDtos)) {
            return;
        }
        // 如果最后截止出票日期时间大于整个PNR所有航段中最早出发的航段的出发时间前2小时，报错：CHECK TKT TIME
        PnrTkDto pnrTkDto = filterTkDtos.get(0);
        MnjxPnrTk mnjxPnrTk = pnrTkDto.getMnjxPnrTk();
        String planEtdzDate = mnjxPnrTk.getPlanEtdzDate();
        String planEtdzTime = mnjxPnrTk.getPlanEtdzTime();
        String tkTime = StrUtil.format("{} {}:{}:00", planEtdzDate, planEtdzTime.substring(0, 2), planEtdzTime.substring(2));
        String flightTime = StrUtil.format("{} {}:{}:00", flightDate, estimateOff.substring(0, 2), estimateOff.substring(2));
        long tk = DateUtil.parse(tkTime).getTime();
        long flight = DateUtil.parse(flightTime).getTime();
        long now = DateUtil.parse(DateUtil.now()).getTime();
        long diff = (flight - tk) / 1000 / 60;
        if (diff < 120) {
            throw new UnifiedResultException(Constant.CHECK_TKT_TIME);
        }
        diff = (tk - now) / 1000 / 60;
        if (diff < 0) {
            throw new UnifiedResultException(Constant.CHECK_TKT_TIME);
        }
    }

    /**
     * 使用pnrCode编码去装载MemoryDataPnr对象
     *
     * @param memoryDataPnr 内存大对象
     * @param pnrCode       pnr编码
     * @throws UnifiedResultException 统一异常
     */
    public void rt(MemoryDataPnr memoryDataPnr, String pnrCode) throws UnifiedResultException {
        MnjxPnr mnjxPnr = iMnjxPnrService.lambdaQuery().eq(MnjxPnr::getPnrCrs, pnrCode).one();
        if (ObjectUtils.isNull(mnjxPnr)) {
            throw new UnifiedResultException(Constant.NO_PNR);
        }
        // 将数据库的属性重新装载到内存对象中
        BeanUtils.copyProperties(mnjxPnr, memoryDataPnr.getMnjxPnr());
        // 装载封口数据
        List<MnjxPnrAt> mnjxPnrAts = this.loadPnrAts(mnjxPnr);
        if (CollUtil.isNotEmpty(mnjxPnrAts)) {
            memoryDataPnr.getMnjxPnrAts().addAll(mnjxPnrAts);
        }
        // 装载联系数据
        List<PnrCtDto> pnrCtDtos = this.loadPnrCtDtos(memoryDataPnr, mnjxPnr.getPnrId());
        if (CollUtil.isNotEmpty(pnrCtDtos)) {
            memoryDataPnr.getPnrCtDtos().addAll(pnrCtDtos);
        }
        // 装载票价计算组
        List<PnrFcDto> pnrFcDtos = this.loadPnrFcDtos(memoryDataPnr, mnjxPnr.getPnrId());
        if (CollUtil.isNotEmpty(pnrFcDtos)) {
            memoryDataPnr.getPnrFcDtos().addAll(pnrFcDtos);
        }
        // 装载票价组
        List<PnrFnDto> pnrFnDtos = this.loadPnrFnDtos(memoryDataPnr, mnjxPnr.getPnrId());
        if (CollUtil.isNotEmpty(pnrFnDtos)) {
            memoryDataPnr.getPnrFnDtos().addAll(pnrFnDtos);
        }
        // 票价支付组
        List<PnrFpDto> pnrFpDtos = this.loadPnrFpDtos(memoryDataPnr, mnjxPnr.getPnrId());
        if (CollUtil.isNotEmpty(pnrFpDtos)) {
            memoryDataPnr.getPnrFpDtos().addAll(pnrFpDtos);
        }
        // 团队组
        List<PnrGnDto> pnrGnDtos = this.loadPnrGnDtos(memoryDataPnr, mnjxPnr.getPnrId());
        if (CollUtil.isNotEmpty(pnrGnDtos)) {
            memoryDataPnr.getPnrGnDtos().addAll(pnrGnDtos);
        }
        // osi数据
        List<PnrOsiDto> pnrOsiDtos = this.loadPnrOsiDtos(memoryDataPnr, mnjxPnr.getPnrId());
        if (CollUtil.isNotEmpty(pnrOsiDtos)) {
            memoryDataPnr.getPnrOsiDtos().addAll(pnrOsiDtos);
        }
        // 记录组
        List<MnjxPnrRecord> mnjxPnrRecords = this.loadPnrRecord(mnjxPnr.getPnrId());
        if (CollUtil.isNotEmpty(mnjxPnrRecords)) {
            memoryDataPnr.getMnjxPnrRecords().addAll(mnjxPnrRecords);
        }
        // 备注组
        List<PnrRmkDto> rmkDtos = this.loadPnrRmkDtos(memoryDataPnr, mnjxPnr.getPnrId());
        if (CollUtil.isNotEmpty(rmkDtos)) {
            memoryDataPnr.getPnrRmkDtos().addAll(rmkDtos);
        }
        // 装载航段数据
        List<PnrSegDto> pnrSegDtos = this.loadPnrSegDtos(memoryDataPnr, mnjxPnr.getPnrId());
        if (CollUtil.isNotEmpty(pnrSegDtos)) {
            memoryDataPnr.getPnrSegDtos().addAll(pnrSegDtos);
        }
        // 装载出票时间设置
        List<PnrTkDto> pnrTkDtos = this.loadPnrTkDtos(memoryDataPnr, mnjxPnr.getPnrId());
        if (CollUtil.isNotEmpty(pnrTkDtos)) {
            memoryDataPnr.getPnrTkDtos().addAll(pnrTkDtos);
        }
        // 旅游代码组
        List<PnrTcDto> pnrTcDtos = this.loadPnrTcDtos(memoryDataPnr, mnjxPnr.getPnrId());
        if (CollUtil.isNotEmpty(pnrTcDtos)) {
            memoryDataPnr.getPnrTcDtos().addAll(pnrTcDtos);
        }
        // 获取旅客姓名列表
        List<PnrNmDto> pnrNmDtos = this.loadPnrNmDtos(memoryDataPnr, mnjxPnr.getPnrId());
        if (CollUtil.isNotEmpty(pnrNmDtos)) {
            memoryDataPnr.getPnrNmDtos().addAll(pnrNmDtos);
        }

        //EI选项
        List<PnrEiDto> pnrEiDtos = this.loadPnrEiDtos(memoryDataPnr, mnjxPnr.getPnrId());
        if (CollUtil.isNotEmpty(pnrEiDtos)) {
            memoryDataPnr.getPnrEiDtos().addAll(pnrEiDtos);
        }
    }

    /**
     * PNR中的姓名项
     *
     * @param memoryDataPnr pnr内存大对象
     * @param pnrId         pnrid
     * @return pnr对象
     */
    private List<PnrNmDto> loadPnrNmDtos(MemoryDataPnr memoryDataPnr, String pnrId) {
        return iMnjxPnrNmService.lambdaQuery().eq(MnjxPnrNm::getPnrId, pnrId).orderByAsc(MnjxPnrNm::getPnrIndex).list().stream().map(mnjxPnrNm -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("NM:" + mnjxPnrNm.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("NM:" + mnjxPnrNm.getInputValue());
            }
            PnrNmDto dto = new PnrNmDto();
            dto.setMnjxPnrNm(mnjxPnrNm);
            //设置ssr
            List<PnrSsrDto> pnrSsrDto = this.loadPnrSsrDtos(memoryDataPnr, mnjxPnrNm.getPnrNmId());
            if (CollUtil.isNotEmpty(pnrSsrDto)) {
                dto.getPnrSsrDtos().addAll(pnrSsrDto);
            }
            //设置旅客CT项
            List<PnrNmCtDto> pnrNmCtDtos = this.loadPnrNmCtDtos(memoryDataPnr, mnjxPnrNm.getPnrNmId());
            if (CollUtil.isNotEmpty(pnrNmCtDtos)) {
                dto.getPnrNmCtDtos().addAll(pnrNmCtDtos);
            }
            //设置旅客OSI项
            List<PnrNmOsiDto> pnrNmOsiDtos = this.loadPnrNmOsiDtos(memoryDataPnr, mnjxPnrNm.getPnrNmId());
            if (CollUtil.isNotEmpty(pnrNmOsiDtos)) {
                dto.getPnrNmOsiDtos().addAll(pnrNmOsiDtos);
            }
            //设置旅客RMK项
            List<PnrNmRmkDto> pnrNmRmkDtos = this.loadPnrNmRmkDtos(memoryDataPnr, mnjxPnrNm.getPnrNmId());
            if (CollUtil.isNotEmpty(pnrNmRmkDtos)) {
                dto.getPnrNmRmkDtos().addAll(pnrNmRmkDtos);
            }
            //旅客无陪儿童信息
            MnjxPnrNmUm mnjxPnrNmUm = this.loadPnrNmUm(mnjxPnrNm.getPnrNmId());
            if (ObjectUtil.isNotNull(mnjxPnrNmUm)) {
                dto.setMnjxPnrNmUm(mnjxPnrNmUm);
            }
            //旅客携带婴儿信息
            PnrXnDto pnrXnDto = this.loadPnrXnDto(memoryDataPnr, mnjxPnrNm.getPnrNmId());
            if (ObjectUtil.isNotEmpty(pnrXnDto) && ObjectUtil.isNotEmpty(pnrXnDto.getMnjxNmXn())) {
                dto.getPnrXnDtos().add(pnrXnDto);
            }
            //XN TN
            if (ObjectUtil.isNotEmpty(pnrXnDto.getMnjxNmXn())) {
                List<PnrNmTnDto> pnrNmTnDtos = this.loadPnrNmTnDtos(memoryDataPnr, MnjxPnrNmTn::getNmXnId, pnrXnDto.getMnjxNmXn().getNmXnId());
                if (CollUtil.isNotEmpty(pnrNmTnDtos)) {
                    memoryDataPnr.getPnrNmTnDtos().addAll(pnrNmTnDtos);
                }
            }
            //旅客出票记录
            List<MnjxPnrNmTn> mnjxPnrNmTns = iMnjxPnrNmTnService.lambdaQuery().eq(MnjxPnrNmTn::getPnrNmId, mnjxPnrNm.getPnrNmId()).or(ObjectUtil.isNotEmpty(pnrXnDto.getMnjxNmXn()), i -> i.eq(MnjxPnrNmTn::getNmXnId, pnrXnDto.getMnjxNmXn().getNmXnId())).orderByAsc(MnjxPnrNmTn::getPnrIndex).list();
            if (CollUtil.isNotEmpty(mnjxPnrNmTns)) {
                List<String> tnIds = mnjxPnrNmTns.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList());
                List<MnjxPnrNmTicket> mnjxPnrNmTickets = iMnjxPnrNmTicketService.lambdaQuery().in(MnjxPnrNmTicket::getPnrNmTnId, tnIds).list();
                if (CollUtil.isNotEmpty(mnjxPnrNmTickets)) {
                    dto.getMnjxPnrNmTickets().addAll(mnjxPnrNmTickets);
                }
            }
            //旅客改签、换开信息
            List<PnrOiDto> oiDtos = this.loadPnrOiDtos(memoryDataPnr, mnjxPnrNm.getPnrNmId());
            if (CollUtil.isNotEmpty(oiDtos)) {
                dto.getPnrOiDtos().addAll(oiDtos);
            }
            //EI项 签注信息组
            List<PnrNmEiDto> nmEiDtos = this.loadPnrNmEiDtos(memoryDataPnr, mnjxPnrNm.getPnrNmId());
            if (CollUtil.isNotEmpty(nmEiDtos)) {
                dto.getPnrNmEiDtos().addAll(nmEiDtos);
            }
            //FP
            List<PnrNmFpDto> pnrNmFpDtos = this.loadPnrNmFpDtos(memoryDataPnr, mnjxPnrNm.getPnrNmId());
            if (CollUtil.isNotEmpty(pnrNmFpDtos)) {
                dto.getPnrNmFpDtos().addAll(pnrNmFpDtos);
            }
            //FC
            List<PnrNmFcDto> nmFcDtos = this.loadPnrNmFcDtos(memoryDataPnr, mnjxPnrNm.getPnrNmId());
            if (CollUtil.isNotEmpty(nmFcDtos)) {
                dto.getPnrNmFcDtos().addAll(nmFcDtos);
            }
            //FN
            List<PnrNmFnDto> nmFnDtos = this.loadPnrNmFnDtos(memoryDataPnr, mnjxPnrNm.getPnrNmId());
            if (CollUtil.isNotEmpty(nmFnDtos)) {
                dto.getPnrNmFnDtos().addAll(nmFnDtos);
            }
            //TN
            List<PnrNmTnDto> nmTnDtos = this.loadPnrNmTnDtos(memoryDataPnr, MnjxPnrNmTn::getPnrNmId, mnjxPnrNm.getPnrNmId());
            if (CollUtil.isNotEmpty(nmTnDtos)) {
                memoryDataPnr.getPnrNmTnDtos().addAll(nmTnDtos);
            }
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 当前旅客携带的婴儿
     *
     * @param memoryDataPnr pnr内存大对象
     * @param pnrNmId       旅客id
     * @return 婴儿
     */
    private PnrXnDto loadPnrXnDto(MemoryDataPnr memoryDataPnr, String pnrNmId) {
        MnjxNmXn mnjxNmXn = iMnjxNmXnService.lambdaQuery().eq(MnjxNmXn::getPnrNmId, pnrNmId).orderByAsc(MnjxNmXn::getPnrIndex).one();
        if (ObjectUtil.isNotEmpty(mnjxNmXn) && !memoryDataPnr.getExistHistoryPnrItem().contains("XN:" + mnjxNmXn.getInputValue())) {
            memoryDataPnr.getExistHistoryPnrItem().add("XN:" + mnjxNmXn.getInputValue());
        }
        return new PnrXnDto(mnjxNmXn);
    }

    /**
     * 封口数据
     *
     * @param mnjxPnr pnr对象
     * @return 所有封口数据
     */
    private List<MnjxPnrAt> loadPnrAts(MnjxPnr mnjxPnr) {
        return iMnjxPnrAtService.lambdaQuery().eq(MnjxPnrAt::getPnrId, mnjxPnr.getPnrId()).orderByAsc(MnjxPnrAt::getPnrAtId).list();
    }

    /**
     * 旅客客票
     *
     * @param memoryDataPnr pnr内存对象
     * @param column        column 列
     * @param nmId          婴儿姓名ID
     * @return 旅客客票
     */
    private List<PnrNmTnDto> loadPnrNmTnDtos(MemoryDataPnr memoryDataPnr, SFunction<MnjxPnrNmTn, ?> column, String nmId) {
        return iMnjxPnrNmTnService.lambdaQuery().eq(column, nmId).list().stream().map(mnjxPnrNmTn -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("TN:" + mnjxPnrNmTn.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("TN:" + mnjxPnrNmTn.getInputValue());
            }
            return new PnrNmTnDto(mnjxPnrNmTn);
        }).collect(Collectors.toList());
    }

    /**
     * 旅客换开项
     *
     * @param memoryDataPnr PNR内存大对象
     * @param pnrNmId       旅客姓名Id
     * @return 旅客换开项
     */
    private List<PnrOiDto> loadPnrOiDtos(MemoryDataPnr memoryDataPnr, String pnrNmId) {
        return iMnjxNmOiService.lambdaQuery().eq(MnjxNmOi::getPnrNmId, pnrNmId).orderByAsc(MnjxNmOi::getPnrIndex).list().stream().map(mnjxNmOi -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("OI:" + mnjxNmOi.getOiInfo())) {
                memoryDataPnr.getExistHistoryPnrItem().add("OI:" + mnjxNmOi.getOiInfo());
            }
            return new PnrOiDto(mnjxNmOi);
        }).collect(Collectors.toList());
    }

    /**
     * 旅客票价组
     *
     * @param memoryDataPnr pnr内存大对象
     * @param pnrNmId       PNR旅客姓名项，某个旅客的id
     * @return 旅客票价组
     */
    private List<PnrNmFnDto> loadPnrNmFnDtos(MemoryDataPnr memoryDataPnr, String pnrNmId) {
        return iMnjxNmFnService.lambdaQuery().eq(MnjxNmFn::getPnrNmId, pnrNmId).orderByAsc(MnjxNmFn::getPnrIndex).list().stream().map(mnjxNmFn -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("NMFN:" + mnjxNmFn.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("NMFN:" + mnjxNmFn.getInputValue());
            }
            return new PnrNmFnDto(mnjxNmFn);
        }).collect(Collectors.toList());
    }

    /**
     * 旅客票价计算组
     *
     * @param memoryDataPnr pnr内存大对象
     * @param pnrNmId       旅客姓名Id
     * @return 旅客票价计算组
     */
    private List<PnrNmFcDto> loadPnrNmFcDtos(MemoryDataPnr memoryDataPnr, String pnrNmId) {
        return iMnjxNmFcService.lambdaQuery().eq(MnjxNmFc::getPnrNmId, pnrNmId).orderByAsc(MnjxNmFc::getPnrIndex).list().stream().map(mnjxNmFc -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("NMFC:" + mnjxNmFc.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("NMFC:" + mnjxNmFc.getInputValue());
            }
            return new PnrNmFcDto(mnjxNmFc);
        }).collect(Collectors.toList());
    }

    /**
     * 旅客票价支付组
     *
     * @param memoryDataPnr PNR内存大对象
     * @param pnrNmId       旅客姓名Id
     * @return 旅客支付组
     */
    private List<PnrNmFpDto> loadPnrNmFpDtos(MemoryDataPnr memoryDataPnr, String pnrNmId) {
        return iMnjxNmFpService.lambdaQuery().eq(MnjxNmFp::getPnrNmId, pnrNmId).orderByAsc(MnjxNmFp::getPnrIndex).list().stream().map(mnjxNmFp -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("NMFP:" + mnjxNmFp.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("NMFP:" + mnjxNmFp.getInputValue());
            }
            return new PnrNmFpDto(mnjxNmFp);
        }).collect(Collectors.toList());
    }

    /**
     * EI项 签注信息组
     *
     * @param memoryDataPnr pnr的内存大对象
     * @param pnrNmId       旅客ID
     * @return 旅客的签注信息组
     */
    private List<PnrNmEiDto> loadPnrNmEiDtos(MemoryDataPnr memoryDataPnr, String pnrNmId) {
        return iMnjxNmEiService.lambdaQuery().eq(MnjxNmEi::getPnrNmId, pnrNmId).orderByAsc(MnjxNmEi::getPnrIndex).list().stream().map(mnjxNmEi -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("NMEI:" + mnjxNmEi.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("NMEI:" + mnjxNmEi.getInputValue());
            }
            return new PnrNmEiDto(mnjxNmEi);
        }).collect(Collectors.toList());
    }

    /**
     * 获取旅客的无陪儿童
     *
     * @param pnrNmId 旅客姓名的id
     * @return 无陪儿童
     */
    private MnjxPnrNmUm loadPnrNmUm(String pnrNmId) {
        return iMnjxPnrNmUmService.lambdaQuery().eq(MnjxPnrNmUm::getPnrNmId, pnrNmId).one();
    }

    /**
     * 设置旅客备注项
     *
     * @param memoryDataPnr pnr内存大对象
     * @param pnrNmId       旅客姓名项Id
     * @return 旅客备注项
     */
    private List<PnrNmRmkDto> loadPnrNmRmkDtos(MemoryDataPnr memoryDataPnr, String pnrNmId) {
        return iMnjxNmRmkService.lambdaQuery().eq(MnjxNmRmk::getPnrNmId, pnrNmId).orderByAsc(MnjxNmRmk::getPnrIndex).list().stream().map(mnjxNmRmk -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("NMRMK:" + mnjxNmRmk.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("NMRMK:" + mnjxNmRmk.getInputValue());
            }
            return new PnrNmRmkDto(mnjxNmRmk);
        }).collect(Collectors.toList());
    }

    /**
     * 设置旅客其他服务项
     *
     * @param memoryDataPnr pnr内存大对象
     * @param pnrNmId       旅客姓名项ID
     * @return 旅客其他服务项列表
     */
    private List<PnrNmOsiDto> loadPnrNmOsiDtos(MemoryDataPnr memoryDataPnr, String pnrNmId) {
        return iMnjxNmOsiService.lambdaQuery().eq(MnjxNmOsi::getPnrNmId, pnrNmId).orderByAsc(MnjxNmOsi::getPnrIndex).list().stream().map(mnjxNmOsi -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("NMOSI:" + mnjxNmOsi.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("NMOSI:" + mnjxNmOsi.getInputValue());
            }
            return new PnrNmOsiDto(mnjxNmOsi);
        }).collect(Collectors.toList());
    }

    /**
     * 旅客联系项
     *
     * @param memoryDataPnr pnr大对象
     * @param pnrNmId       pnr姓名项Id
     * @return 旅客联系项
     */
    private List<PnrNmCtDto> loadPnrNmCtDtos(MemoryDataPnr memoryDataPnr, String pnrNmId) {
        return iMnjxNmCtService.lambdaQuery().eq(MnjxNmCt::getPnrNmId, pnrNmId).orderByAsc(MnjxNmCt::getPnrIndex).list().stream().map(mnjxNmCt -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("NMCT:" + mnjxNmCt.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("NMCT:" + mnjxNmCt.getInputValue());
            }
            return new PnrNmCtDto(mnjxNmCt);
        }).collect(Collectors.toList());
    }


    /**
     * 签注信息组
     *
     * @param memoryDataPnr PNR大对象
     * @param pnrId         pnrId
     * @return 签注信息组
     */
    private List<PnrEiDto> loadPnrEiDtos(MemoryDataPnr memoryDataPnr, String pnrId) {
        return iMnjxPnrEiService.lambdaQuery().eq(MnjxPnrEi::getPnrId, pnrId).orderByAsc(MnjxPnrEi::getPnrIndex).list().stream().map(mnjxPnrEi -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("EI:" + mnjxPnrEi.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("EI:" + mnjxPnrEi.getInputValue());
            }
            return new PnrEiDto(mnjxPnrEi);
        }).collect(Collectors.toList());
    }

    /**
     * 旅游代码组
     *
     * @param memoryDataPnr pnr内存大对象
     * @param pnrId         pnrId
     * @return 旅游代码组
     */
    private List<PnrTcDto> loadPnrTcDtos(MemoryDataPnr memoryDataPnr, String pnrId) {
        return iMnjxPnrTcService.lambdaQuery().eq(MnjxPnrTc::getPnrId, pnrId).orderByAsc(MnjxPnrTc::getPnrIndex).list().stream().map(mnjxPnrTc -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("TC:" + mnjxPnrTc.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("TC:" + mnjxPnrTc.getInputValue());
            }
            return new PnrTcDto(mnjxPnrTc);
        }).collect(Collectors.toList());
    }

    /**
     * 获取PNR记录组
     *
     * @param pnrId pnrId
     * @return 记录组
     */
    private List<MnjxPnrRecord> loadPnrRecord(String pnrId) {
        return iMnjxPnrRecordService.lambdaQuery().eq(MnjxPnrRecord::getPnrId, pnrId).orderByAsc(MnjxPnrRecord::getPnrIndex).list();
    }

    /**
     * 备注组
     *
     * @param memoryDataPnr pnr的内存大对象
     * @param pnrId         pnrId
     * @return 备注组
     */
    private List<PnrRmkDto> loadPnrRmkDtos(MemoryDataPnr memoryDataPnr, String pnrId) {
        return iMnjxPnrRmkService.lambdaQuery().eq(MnjxPnrRmk::getPnrId, pnrId).orderByAsc(MnjxPnrRmk::getPnrIndex).list().stream().map(mnjxPnrRmk -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("RMK:" + mnjxPnrRmk.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("RMK:" + mnjxPnrRmk.getInputValue());
            }
            return new PnrRmkDto(mnjxPnrRmk);
        }).collect(Collectors.toList());
    }

    /**
     * 团队组
     *
     * @param memoryDataPnr pnr大对象
     * @param pnrId         pnrId
     * @return 团队组
     */
    private List<PnrGnDto> loadPnrGnDtos(MemoryDataPnr memoryDataPnr, String pnrId) {
        return iMnjxPnrGnService.lambdaQuery().eq(MnjxPnrGn::getPnrId, pnrId).orderByAsc(MnjxPnrGn::getPnrIndex).list().stream().map(mnjxPnrGn -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("GN:" + mnjxPnrGn.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("GN:" + mnjxPnrGn.getInputValue());
            }
            return new PnrGnDto(mnjxPnrGn);
        }).collect(Collectors.toList());
    }

    /**
     * 票价支付组
     *
     * @param memoryDataPnr pnr内存大对象
     * @param pnrId         pnrId
     * @return 票价支付组
     */
    private List<PnrFpDto> loadPnrFpDtos(MemoryDataPnr memoryDataPnr, String pnrId) {
        return iMnjxPnrFpService.lambdaQuery().eq(MnjxPnrFp::getPnrId, pnrId).orderByAsc(MnjxPnrFp::getPnrIndex).list().stream().map(mnjxPnrFp -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("FP:" + mnjxPnrFp.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("FP:" + mnjxPnrFp.getInputValue());
            }
            return new PnrFpDto(mnjxPnrFp);
        }).collect(Collectors.toList());
    }

    /**
     * 返回票价组数据
     *
     * @param memoryDataPnr 内存大对象
     * @param pnrId         pnrId
     * @return 票价组
     */
    private List<PnrFnDto> loadPnrFnDtos(MemoryDataPnr memoryDataPnr, String pnrId) {
        return iMnjxPnrFnService.lambdaQuery().eq(MnjxPnrFn::getPnrId, pnrId).orderByAsc(MnjxPnrFn::getPnrIndex).list().stream().map(mnjxPnrFn -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("FN:" + mnjxPnrFn.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("FN:" + mnjxPnrFn.getInputValue());
            }
            return new PnrFnDto(mnjxPnrFn);
        }).collect(Collectors.toList());
    }

    /**
     * 特殊服务组
     *
     * @param memoryDataPnr pnr内存大对象
     * @param pnrNmId       旅客姓名ID
     * @return 特殊服务组
     */
    private List<PnrSsrDto> loadPnrSsrDtos(MemoryDataPnr memoryDataPnr, String pnrNmId) {
        return iMnjxNmSsrService.lambdaQuery().eq(MnjxNmSsr::getPnrNmId, pnrNmId).orderByAsc(MnjxNmSsr::getPnrIndex).list().stream().map(mnjxNmSsr -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("SSR:" + mnjxNmSsr.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("SSR:" + mnjxNmSsr.getInputValue());
            }
            PnrSsrDto ssrDto = new PnrSsrDto();
            ssrDto.setMnjxNmSsr(mnjxNmSsr);
            return ssrDto;
        }).collect(Collectors.toList());
    }

    /**
     * @param memoryDataPnr pnr内存大对象
     * @param pnrId         pnrId
     * @return osi项
     */
    private List<PnrOsiDto> loadPnrOsiDtos(MemoryDataPnr memoryDataPnr, String pnrId) {
        return iMnjxPnrOsiService.lambdaQuery().eq(MnjxPnrOsi::getPnrId, pnrId).orderByAsc(MnjxPnrOsi::getPnrIndex).list().stream().map(mnjxPnrOsi -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("OSI:" + mnjxPnrOsi.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("OSI:" + mnjxPnrOsi.getInputValue());
            }
            return new PnrOsiDto(mnjxPnrOsi);
        }).collect(Collectors.toList());
    }

    /**
     * 获得航段数据
     *
     * @param memoryDataPnr pnr大对象
     * @param pnrId         pnrId
     * @return 所有航段数据
     */
    private List<PnrSegDto> loadPnrSegDtos(MemoryDataPnr memoryDataPnr, String pnrId) {
        return iMnjxPnrSegService.lambdaQuery().eq(MnjxPnrSeg::getPnrId, pnrId).orderByAsc(MnjxPnrSeg::getPnrIndex).list().stream().map(mnjxPnrSeg -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("SEG:" + mnjxPnrSeg.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("SEG:" + mnjxPnrSeg.getInputValue());
            }
            return new PnrSegDto(mnjxPnrSeg);
        }).collect(Collectors.toList());
    }

    /**
     * 出票时间设置
     *
     * @param memoryDataPnr pnr内存大对象
     * @param pnrId         pnrId
     * @return 出票时间设置
     */
    private List<PnrTkDto> loadPnrTkDtos(MemoryDataPnr memoryDataPnr, String pnrId) {
        return iMnjxPnrTkService.lambdaQuery().eq(MnjxPnrTk::getPnrId, pnrId).orderByAsc(MnjxPnrTk::getPnrIndex).list().stream().map(mnjxPnrTk -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("TK:" + mnjxPnrTk.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("TK:" + mnjxPnrTk.getInputValue());
            }
            return new PnrTkDto(mnjxPnrTk);
        }).collect(Collectors.toList());
    }

    /**
     * 通过PNRID装载票价计算组
     *
     * @param memoryDataPnr pnr的内存大对象
     * @param pnrId         pnrid
     * @return 票价计算组
     */
    private List<PnrFcDto> loadPnrFcDtos(MemoryDataPnr memoryDataPnr, String pnrId) {
        return iMnjxPnrFcService.lambdaQuery().eq(MnjxPnrFc::getPnrId, pnrId).orderByAsc(MnjxPnrFc::getPnrIndex).list().stream().map(mnjxPnrFc -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("FC:" + mnjxPnrFc.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("FC:" + mnjxPnrFc.getInputValue());
            }
            return new PnrFcDto(mnjxPnrFc);
        }).collect(Collectors.toList());
    }

    /**
     * 通过PNR的id获取当前PNR的所有联系方式
     *
     * @param memoryDataPnr 内存中的大对象
     * @param pnrId         PNRID
     * @return 当前PNR的联系方式
     */
    private List<PnrCtDto> loadPnrCtDtos(MemoryDataPnr memoryDataPnr, String pnrId) {
        return iMnjxPnrCtService.lambdaQuery().eq(MnjxPnrCt::getPnrId, pnrId).orderByAsc(MnjxPnrCt::getPnrIndex).list().stream().map(mnjxPnrCt -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains("CT:" + mnjxPnrCt.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add("CT:" + mnjxPnrCt.getInputValue());
            }
            return new PnrCtDto(mnjxPnrCt);
        }).collect(Collectors.toList());
    }

    public void nm(MemoryDataPnr memoryDataPnr, List<NmDto> nmDtos, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        pnrCommandServicePartNm.nm(memoryDataPnr, nmDtos, mnjxOffice, mnjxSi);
    }


    public void sd(MemoryDataPnr memoryDataPnr, SdDto sdDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        pnrCommandServicePartSd.sd(memoryDataPnr, sdDto, mnjxOffice, mnjxSi);
    }


    public void tk(MemoryDataPnr memoryDataPnr, TkDto tkDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        // 如果内存PNR对象中的PNR为空，则此处新建PNR
        MnjxPnr mnjxPnr;
        if (ObjectUtil.isNotEmpty(memoryDataPnr.getMnjxPnr().getPnrId())) {
            mnjxPnr = memoryDataPnr.getMnjxPnr();
        } else {
            // 新建PNR
            mnjxPnr = iPnrOperationService.createNewPnr(memoryDataPnr, mnjxOffice, mnjxSi);
        }
        List<PnrTkDto> pnrTkDtos = memoryDataPnr.getPnrTkDtos();
        if (CollUtil.isNotEmpty(pnrTkDtos) && pnrTkDtos.stream().anyMatch(t -> !t.isXe())) {
            throw new UnifiedResultException(Constant.DUP_TK);
        }
        PnrTkDto pnrTkDto = new PnrTkDto();
        MnjxPnrTk mnjxPnrTk = new MnjxPnrTk();
        mnjxPnrTk.setPnrId(mnjxPnr.getPnrId());
        BeanUtils.copyProperties(tkDto, mnjxPnrTk);
        mnjxPnrTk.setInputValue(StrUtil.format("{}/{}/{}/{}", tkDto.getPnrTkType(), tkDto.getPlanEtdzTime(), DateUtils.ymd2Com(tkDto.getPlanEtdzDate()), tkDto.getEtdzOffice()));
        pnrTkDto.setMnjxPnrTk(mnjxPnrTk);
        pnrTkDtos.add(pnrTkDto);
    }

    public void ct(MemoryDataPnr memoryDataPnr, CtDto ctDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        if (ObjectUtil.isEmpty(ctDto.getPNo())) {
            this.handlePnrCt(memoryDataPnr, ctDto, mnjxOffice, mnjxSi);
        } else {
            this.handleNmCt(memoryDataPnr, ctDto);
        }
    }

    private void handlePnrCt(MemoryDataPnr memoryDataPnr, CtDto ctDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        if (ObjectUtil.isEmpty(mnjxPnr.getPnrId())) {
            mnjxPnr = iPnrOperationService.createNewPnr(memoryDataPnr, mnjxOffice, mnjxSi);
        }
        String inputValue = StrUtil.format("{}/T{}", ctDto.getCityCode(), ctDto.getCtText());
        MnjxPnrCt pnrCt = new MnjxPnrCt();
        if (StrUtil.isNotEmpty(ctDto.getCityCode())) {
            MnjxCity city = iMnjxCityService.lambdaQuery().eq(MnjxCity::getCityCode, ctDto.getCityCode()).one();
            if (ObjectUtil.isNotEmpty(city)) {
                pnrCt.setCityCode(ctDto.getCityCode());
                pnrCt.setCtText(ctDto.getCtText());
            }
        } else {
            pnrCt.setCtText(inputValue);
        }
        pnrCt.setPnrId(mnjxPnr.getPnrId());
        pnrCt.setInputValue(inputValue);
        // 新建pnr
        PnrCtDto pnrCtDto = new PnrCtDto();
        pnrCtDto.setMnjxPnrCt(pnrCt);
        memoryDataPnr.getPnrCtDtos().add(pnrCtDto);
    }

    private void handleNmCt(MemoryDataPnr memoryDataPnr, CtDto ctDto) throws UnifiedResultException {
        if (ObjectUtil.isEmpty(memoryDataPnr.getMnjxPnr().getPnrId())) {
            throw new UnifiedResultException(Constant.PSGR_ID);
        }
        Integer psgId = Integer.parseInt(ctDto.getPNo());
        // 姓名组
        List<PnrNmDto> pnrNmList = memoryDataPnr.getPnrNmDtos().stream().filter(k -> !k.isXe()).filter(k -> k.getMnjxPnrNm().getPsgIndex().equals(psgId)).collect(Collectors.toList());
        if (CollUtil.isEmpty(pnrNmList)) {
            throw new UnifiedResultException(Constant.PSGR_ID);
        }
        String inputValue = StrUtil.format("{}/{}/P{}", ctDto.getCityCode(), ctDto.getCtText(), ctDto.getPNo());
        MnjxNmCt nmCt = new MnjxNmCt();
        if (StrUtil.isNotEmpty(ctDto.getCityCode())) {
            MnjxCity city = iMnjxCityService.lambdaQuery().eq(MnjxCity::getCityCode, ctDto.getCityCode()).one();
            if (ObjectUtil.isNotEmpty(city)) {
                nmCt.setCityCode(ctDto.getCityCode());
                nmCt.setCtText(String.format("%s/P%s", ctDto.getCtText(), ctDto.getPNo()));
            }
        } else {
            nmCt.setCtText(inputValue);
        }
        nmCt.setInputValue(inputValue);
        PnrNmDto pnrNmDto = pnrNmList.get(0);
        nmCt.setPnrNmId(pnrNmDto.getMnjxPnrNm().getPnrNmId());
        PnrNmCtDto pnrNmCtDto = new PnrNmCtDto();
        pnrNmCtDto.setMnjxNmCt(nmCt);
        pnrNmDto.getPnrNmCtDtos().add(pnrNmCtDto);
    }

    public void ssr(MemoryDataPnr memoryDataPnr, SsrDto ssrDto) throws UnifiedResultException {
        pnrCommandServicePartSsr.ssr(memoryDataPnr, ssrDto);
    }

    public void pat(MemoryDataPnr memoryDataPnr, PatDto patDto) throws UnifiedResultException {
        pnrCommandServicePartPat.pat(memoryDataPnr, patDto);
    }

    public List<AvVo> av(MemoryDataPnr memoryDataPnr, AvDto avDto) throws UnifiedResultException {
        return pnrCommandServicePartAv.av(memoryDataPnr, avDto);
    }

    /**
     * 清除cki信息
     *
     * @param memoryDataPnr memoryDataPnr
     */
    private void removeCki(MemoryDataPnr memoryDataPnr) {
        // 如果有改期标识才清除cki信息
        TriVo triVo = memoryDataPnr.getTriVo();
        if (ObjectUtil.isNotEmpty(triVo)) {
            log.info("封口清除cki信息：[{}]", triVo);
            String pNo = triVo.getPNo();
            List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos().stream()
                    .filter(k -> k.getPnrIndex() == Integer.parseInt(pNo))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrNmDtos)) {
                PnrNmDto pnrNmDto = pnrNmDtos.get(0);
                // 在（OI）付费的情况下删除cki信息
                if (CollUtil.isNotEmpty(pnrNmDto.getPnrOiDtos())) {
                    // 删除cki信息
                    MnjxPnrNm mnjxPnrNm = pnrNmDto.getMnjxPnrNm();
                    List<MnjxPsgCki> psgCkis = iMnjxPsgCkiService.lambdaQuery().eq(MnjxPsgCki::getPnrNmId, mnjxPnrNm.getPnrNmId()).list();
                    if (CollUtil.isNotEmpty(psgCkis)) {
                        // 根据cki信息清除旅客登机信息
                        List<String> psgCikIds = psgCkis.stream().map(MnjxPsgCki::getPsgCkiId).collect(Collectors.toList());
                        iMnjxPsgCkiOptionService.lambdaUpdate().in(MnjxPsgCkiOption::getPsgCkiId, psgCikIds).remove();
                        iMnjxPsgOperateRecordService.lambdaUpdate().in(MnjxPsgOperateRecord::getPsgCkiId, psgCikIds).remove();
                        iMnjxPsgSeatService.lambdaUpdate().in(MnjxPsgSeat::getPsgCkiId, psgCikIds).remove();
                        iMnjxPsgCkiService.removeByIds(psgCikIds);
                    }
                }
            }
        }
    }

    /**
     * 免费改期释放座位
     */
    private void freeChangeReleaseSeat(MemoryDataPnr memoryDataPnr, List<MnjxPnrSeg> lastDbSegList, List<MnjxPnrSeg> thisDbSegList) throws UnifiedResultException {
        List<PnrRmkDto> pnrRmkDtos = memoryDataPnr.getPnrRmkDtos();
        // 占座或释放座位、更新ticket对应的航段id、hbnbNo
        if (CollUtil.isNotEmpty(pnrRmkDtos)) {
            List<MnjxPnrRmk> pnrRmkList = pnrRmkDtos.stream()
                    .map(PnrRmkDto::getMnjxPnrRmk)
                    .filter(k -> StrUtil.isNotEmpty(k.getRmkName()))
                    .filter(k -> k.getRmkName().equals(Constant.AUTOMATIC))
                    .collect(Collectors.toList());
            // 旅客人数
            int nmSize = memoryDataPnr.getPnrNmDtos().size();
            // 免费改签修改开舱可用座位数、票号表航段组id、hbnb号
            if (CollUtil.isNotEmpty(pnrRmkList)) {
                this.changeFreeTriSeats(lastDbSegList, thisDbSegList, nmSize);
                thisDbSegList = thisDbSegList.stream().sorted(Comparator.comparing(MnjxPnrSeg::getPnrSegNo)).collect(Collectors.toList());
                this.changeFreeHbnb(memoryDataPnr, thisDbSegList);
            }
            // 付费改签修改开舱可用座位数
            else if (memoryDataPnr.getPnrSegDtos().stream().anyMatch(PnrSegDto::isXe)
                    && memoryDataPnr.getPnrNmDtos().stream().anyMatch(n -> CollUtil.isNotEmpty(n.getPnrOiDtos()))) {
                this.changePaidTriSeats(lastDbSegList, thisDbSegList, nmSize);
            }
        }
    }

    /**
     * Title: changeFreeHbnb
     * Description: 免费改签的HBNB号修改
     *
     * @param memoryDataPnr memoryDataPnr
     * @param pnrSegs       pnrSegs
     * <AUTHOR>
     * @date 2022/12/1 10:30
     */
    private void changeFreeHbnb(MemoryDataPnr memoryDataPnr, List<MnjxPnrSeg> pnrSegs) {
        // 更新ticket对应的航段id、hbnbNo
        List<String> pnrNmIds = memoryDataPnr.getPnrNmDtos().stream().map(PnrNmDto::getMnjxPnrNm).map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList());
        // 获取所有的婴儿
        List<MnjxNmXn> xnList = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId, pnrNmIds)
                .list();
        // 获取所有的TN数据
        List<MnjxPnrNmTn> allTnList = new ArrayList<>();
        if (CollUtil.isNotEmpty(xnList)) {
            List<MnjxPnrNmTn> tnList = iMnjxPnrNmTnService.lambdaQuery()
                    .in(MnjxPnrNmTn::getNmXnId, xnList.stream()
                            .map(MnjxNmXn::getNmXnId)
                            .collect(Collectors.toList())
                    )
                    .list();
            allTnList.addAll(tnList);
        }
        allTnList.addAll(iMnjxPnrNmTnService.lambdaQuery()
                .in(MnjxPnrNmTn::getPnrNmId, pnrNmIds)
                .list());
        List<String> pnrTnIds = allTnList.stream()
                .map(MnjxPnrNmTn::getTnId)
                .collect(Collectors.toList());
        // 获取所有的票号数据
        List<MnjxPnrNmTicket> pnrNmTickets = iMnjxPnrNmTicketService.lambdaQuery()
                .in(MnjxPnrNmTicket::getPnrNmTnId, pnrTnIds)
                .orderByAsc(MnjxPnrNmTicket::getTicketNo)
                .list();
        // 查到新的航段对应的最大hbnbNo
        List<String> queryParams = new ArrayList<>();
        pnrSegs.stream()
                .filter(s -> !Constant.SA.equals(s.getPnrSegType()))
                .forEach(s -> queryParams.add(StrUtil.format("{}:{}", StrUtil.isNotEmpty(s.getCarrierFlight()) ? s.getCarrierFlight() : s.getFlightNo(), s.getFlightDate())));
        List<HbnbNoDto> hbnbNoDtoList = pnrCommandMapper.retrieveHbnb(queryParams);
        // 存储最大的hbnb号，键是对应的航班号+日期，值为该航段组对应航班的最大hbnb号
        Map<String, Integer> maxHbnbMap = new HashMap<>();
        if (CollUtil.isNotEmpty(hbnbNoDtoList)) {
            Map<String, List<HbnbNoDto>> collect = hbnbNoDtoList.stream()
                    .collect(Collectors.groupingBy(s -> StrUtil.format("{}:{}", StrUtil.isNotEmpty(s.getCarrierFlight()) ? s.getCarrierFlight() : s.getFlightNo(), s.getFlightDate())));
            Set<Map.Entry<String, List<HbnbNoDto>>> entries = collect.entrySet();
            for (Map.Entry<String, List<HbnbNoDto>> entry : entries) {
                String key = entry.getKey();
                List<HbnbNoDto> value = entry.getValue();
                int maxHbnb1 = 1;
                OptionalInt max1 = value.stream()
                        .filter(s -> s.getPnrSegId().equals(s.getS1Id()) && StrUtil.isNotEmpty(s.getHbnb1()))
                        .mapToInt(s -> Integer.parseInt(s.getHbnb1()))
                        .max();
                if (max1.isPresent()) {
                    maxHbnb1 = max1.getAsInt();
                }
                int maxHbnb2 = 1;
                OptionalInt max2 = value.stream()
                        .filter(s -> s.getPnrSegId().equals(s.getS2Id()) && StrUtil.isNotEmpty(s.getHbnb2()))
                        .mapToInt(s -> Integer.parseInt(s.getHbnb2()))
                        .max();
                if (max2.isPresent()) {
                    maxHbnb2 = max2.getAsInt();
                }
                maxHbnbMap.put(key, Math.max(maxHbnb1, maxHbnb2));
            }
        }
        // 按旅客进行处理票号
        List<String> ticketNoList = new ArrayList<>();
        for (String pnrNmId : pnrNmIds) {
            // 对携带的婴儿票号处理
            if (CollUtil.isNotEmpty(xnList)) {
                List<MnjxNmXn> xns = xnList.stream()
                        .filter(x -> pnrNmId.equals(x.getPnrNmId()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(xns)) {
                    List<MnjxPnrNmTn> tnList = allTnList.stream()
                            .filter(t -> xns.get(0).getNmXnId().equals(t.getNmXnId()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(tnList)) {
                        this.setNmTicketSegId(pnrNmTickets, pnrSegs, tnList.get(0), maxHbnbMap);
                    }
                }
            }
            MnjxPnrNmTn tn = allTnList.stream()
                    .filter(t -> pnrNmId.equals(t.getPnrNmId()))
                    .collect(Collectors.toList())
                    .get(0);
            this.setNmTicketSegId(pnrNmTickets, pnrSegs, tn, maxHbnbMap);
            ticketNoList.addAll(pnrNmTickets.stream()
                    .map(MnjxPnrNmTicket::getTicketNo)
                    .collect(Collectors.toList()));
        }
        // 更新数据
        iMnjxPnrNmTicketService.updateBatchById(pnrNmTickets);
        // 更新票号票价表
        List<MnjxTicketPrice> ticketPriceList = iMnjxTicketPriceService.lambdaQuery()
                .in(MnjxTicketPrice::getTicketNo, ticketNoList)
                .list();
        StringBuilder segInfoBuilder = new StringBuilder();
        for (int i = 0; i < pnrSegs.size(); i++) {
            MnjxPnrSeg pnrSeg = pnrSegs.get(i);
            if (StrUtil.isNotEmpty(pnrSeg.getFlightNo())) {
                segInfoBuilder.append(StrUtil.format("{} {} {}", pnrSeg.getFlightNo(), pnrSeg.getFlightDate(), pnrSeg.getSellCabin()));
            } else {
                segInfoBuilder.append(StrUtil.format("SA {}{}", pnrSeg.getOrg(), pnrSeg.getDst()));
            }
            if (i < pnrSegs.size() - 1) {
                segInfoBuilder.append("/");
            }
        }
        ticketPriceList.forEach(t -> t.setSegInfo(segInfoBuilder.toString()));
        iMnjxTicketPriceService.updateBatchById(ticketPriceList);
    }

    /**
     * Title: changePaidTriSeats
     * Description: 付费改签座位占用修改
     *
     * @param lastDbSegList lastDbSegList
     * @param thisDbSegList thisDbSegList
     * @param nmSize        nmSize
     * <AUTHOR>
     * @date 2022/12/1 10:30
     */
    private void changePaidTriSeats(List<MnjxPnrSeg> lastDbSegList, List<MnjxPnrSeg> thisDbSegList, int nmSize) throws UnifiedResultException {
        // 释放旧的座位
        for (MnjxPnrSeg lastSeg : lastDbSegList) {
            if (Constant.SA.equals(lastSeg.getPnrSegType())) {
                continue;
            }
            String flightNo = StrUtil.isNotEmpty(lastSeg.getCarrierFlight()) ? lastSeg.getCarrierFlight() : lastSeg.getFlightNo();
            String flightDate = lastSeg.getFlightDate();
            String sellCabin = lastSeg.getSellCabin();
            List<MnjxOpenCabin> openCabinList = pnrCommandMapper.retrieveOpenCabinList(flightNo, flightDate);
            openCabinList = this.setMultiSegOpenCabin(lastSeg.getOrg(), lastSeg.getDst(), openCabinList);
            openCabinList.stream().filter(o -> o.getSellCabin().equals(sellCabin)).forEach(k -> {
                int availableNumber = k.getSeatAvailable() + nmSize;
                k.setSeatAvailable(availableNumber);
            });
            iMnjxOpenCabinService.updateBatchById(openCabinList);
        }
        // 占座
        for (MnjxPnrSeg thisSeg : thisDbSegList) {
            if (Constant.SA.equals(thisSeg.getPnrSegType())) {
                continue;
            }
            String flightNo = StrUtil.isNotEmpty(thisSeg.getCarrierFlight()) ? thisSeg.getCarrierFlight() : thisSeg.getFlightNo();
            String flightDate = thisSeg.getFlightDate();
            String sellCabin = thisSeg.getSellCabin();
            List<MnjxOpenCabin> openCabinList = pnrCommandMapper.retrieveOpenCabinList(flightNo, flightDate);
            openCabinList = this.setMultiSegOpenCabin(thisSeg.getOrg(), thisSeg.getDst(), openCabinList);
            for (MnjxOpenCabin o : openCabinList) {
                if (o.getSellCabin().equals(sellCabin)) {
                    int availableNumber = o.getSeatAvailable() - nmSize;
                    if (availableNumber < 0) {
                        throw new UnifiedResultException(Constant.OVER_SEATS);
                    }
                    o.setSeatAvailable(availableNumber);
                }
            }
            iMnjxOpenCabinService.updateBatchById(openCabinList);
        }
    }

    /**
     * Title: changeFreeTriSeats
     * Description: 免费改签座位占用修改
     *
     * @param lastDbSegList lastDbSegList
     * @param thisDbSegList thisDbSegList
     * @param nmSize        nmSize
     * <AUTHOR>
     * @date 2022/12/1 10:30
     */
    private void changeFreeTriSeats(List<MnjxPnrSeg> lastDbSegList, List<MnjxPnrSeg> thisDbSegList, int nmSize) throws UnifiedResultException {
        // 释放旧的座位
        for (MnjxPnrSeg lastSeg : lastDbSegList) {
            if (Constant.SA.equals(lastSeg.getPnrSegType())) {
                continue;
            }
            String flightNo = StrUtil.isNotEmpty(lastSeg.getCarrierFlight()) ? lastSeg.getCarrierFlight() : lastSeg.getFlightNo();
            String flightDate = lastSeg.getFlightDate();
            String sellCabin = lastSeg.getSellCabin();
            List<MnjxOpenCabin> openCabinList = pnrCommandMapper.retrieveOpenCabinList(flightNo, flightDate);
            openCabinList = this.setMultiSegOpenCabin(lastSeg.getOrg(), lastSeg.getDst(), openCabinList);
            openCabinList.stream().filter(o -> o.getSellCabin().equals(sellCabin)).forEach(k -> {
                int availableNumber = k.getSeatAvailable() + nmSize;
                k.setSeatAvailable(availableNumber);
            });
            iMnjxOpenCabinService.updateBatchById(openCabinList);
        }
        // 占座
        for (MnjxPnrSeg thisSeg : thisDbSegList) {
            if (Constant.SA.equals(thisSeg.getPnrSegType())) {
                continue;
            }
            String flightNo = StrUtil.isNotEmpty(thisSeg.getCarrierFlight()) ? thisSeg.getCarrierFlight() : thisSeg.getFlightNo();
            String flightDate = thisSeg.getFlightDate();
            String sellCabin = thisSeg.getSellCabin();
            List<MnjxOpenCabin> openCabinList = pnrCommandMapper.retrieveOpenCabinList(flightNo, flightDate);
            openCabinList = this.setMultiSegOpenCabin(thisSeg.getOrg(), thisSeg.getDst(), openCabinList);
            for (MnjxOpenCabin o : openCabinList) {
                if (o.getSellCabin().equals(sellCabin)) {
                    int availableNumber = o.getSeatAvailable() - nmSize;
                    if (availableNumber < 0) {
                        throw new UnifiedResultException(Constant.OVER_SEATS);
                    }
                    o.setSeatAvailable(availableNumber);
                    break;
                }
            }
            iMnjxOpenCabinService.updateBatchById(openCabinList);
        }
    }

    /**
     * Title: setNmTicketSegId
     * Description: 免费改签对票号中对应的航段组ID更新
     *
     * @param pnrNmTickets pnrNmTickets
     * @param thisDbSegs   thisDbSegs
     * @param tn           tn
     * <AUTHOR>
     * @date 2022/11/17 9:59
     */
    private void setNmTicketSegId(List<MnjxPnrNmTicket> pnrNmTickets, List<MnjxPnrSeg> thisDbSegs, MnjxPnrNmTn tn, Map<String, Integer> maxHbnbMap) {
        List<MnjxPnrNmTicket> ticketList = pnrNmTickets.stream()
                .filter(t -> tn.getTnId().equals(t.getPnrNmTnId()))
                .collect(Collectors.toList());
        for (int i = 0; i < ticketList.size(); i++) {
            MnjxPnrNmTicket mnjxPnrNmTicket = ticketList.get(i);
            if (i == 0) {
                MnjxPnrSeg mnjxPnrSeg = thisDbSegs.get(0);
                this.setMaxHbnb(mnjxPnrSeg, maxHbnbMap, mnjxPnrNmTicket);
                mnjxPnrNmTicket.setS1Id(mnjxPnrSeg.getPnrSegId());

                if (thisDbSegs.size() > 1) {
                    MnjxPnrSeg mnjxPnrSeg1 = thisDbSegs.get(1);
                    this.setMaxHbnb(mnjxPnrSeg1, maxHbnbMap, mnjxPnrNmTicket);
                    mnjxPnrNmTicket.setS2Id(mnjxPnrSeg1.getPnrSegId());
                }
            } else if (i == 1) {
                MnjxPnrSeg mnjxPnrSeg = thisDbSegs.get(2);
                this.setMaxHbnb(mnjxPnrSeg, maxHbnbMap, mnjxPnrNmTicket);
                mnjxPnrNmTicket.setS1Id(mnjxPnrSeg.getPnrSegId());

                if (thisDbSegs.size() > 3) {
                    MnjxPnrSeg mnjxPnrSeg1 = thisDbSegs.get(3);
                    this.setMaxHbnb(mnjxPnrSeg1, maxHbnbMap, mnjxPnrNmTicket);
                    mnjxPnrNmTicket.setS2Id(mnjxPnrSeg1.getPnrSegId());
                }
            } else if (i == 2) {
                MnjxPnrSeg mnjxPnrSeg = thisDbSegs.get(4);
                this.setMaxHbnb(mnjxPnrSeg, maxHbnbMap, mnjxPnrNmTicket);
                mnjxPnrNmTicket.setS1Id(mnjxPnrSeg.getPnrSegId());
            }
        }
    }

    /**
     * Title: setMaxHbnb
     * Description: 设置最大HBNB号
     *
     * @param mnjxPnrSeg      mnjxPnrSeg
     * @param maxHbnbMap      maxHbnbMap
     * @param mnjxPnrNmTicket mnjxPnrNmTicket
     * <AUTHOR>
     * @date 2022/12/1 10:31
     */
    private void setMaxHbnb(MnjxPnrSeg mnjxPnrSeg, Map<String, Integer> maxHbnbMap, MnjxPnrNmTicket mnjxPnrNmTicket) {
        String key = StrUtil.format("{}:{}", StrUtil.isNotEmpty(mnjxPnrSeg.getCarrierFlight()) ? mnjxPnrSeg.getCarrierFlight() : mnjxPnrSeg.getFlightNo(), mnjxPnrSeg.getFlightDate());
        if (maxHbnbMap.containsKey(key)) {
            Integer maxHbnb = maxHbnbMap.get(key);
            mnjxPnrNmTicket.setHbnb1(StrUtil.fill(StrUtil.toString(maxHbnb + 1), '0', 4, true));
            maxHbnbMap.put(key, maxHbnb + 1);
        }
    }
}
