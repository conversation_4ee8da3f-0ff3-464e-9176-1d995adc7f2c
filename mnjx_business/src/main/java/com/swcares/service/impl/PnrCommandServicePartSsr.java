package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.*;
import com.swcares.service.IMnjxFrequenterService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class PnrCommandServicePartSsr {

    @Resource
    private PnrCommandServicePartXe pnrCommandServicePartXe;

    @Resource
    private IMnjxFrequenterService iMnjxFrequenterService;

    public void ssr(MemoryDataPnr memoryDataPnr, SsrDto ssrDto) throws UnifiedResultException {
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        MnjxNmSsr mnjxNmSsr = new MnjxNmSsr();
        PnrSsrDto pnrSsrDto = new PnrSsrDto();
        PnrNmDto pnrNmDto;
        int psgIndex = Integer.parseInt(ssrDto.getPsgIndex());

        if (CollUtil.isNotEmpty(pnrNmDtos)) {
            // 验证旅客序号是否错误
            List<PnrNmDto> filterNmDto = pnrNmDtos.stream()
                    .filter(n -> n.getPnrIndex() == psgIndex)
                    .filter(n -> !n.isXe())
                    .filter(n -> StrUtil.isEmpty(n.getMnjxPnrNm().getChangeType()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(filterNmDto)) {
                throw new UnifiedResultException(Constant.PSGR_ID);
            }
            if (!Constant.SSR_MAP.containsKey(ssrDto.getSsrType())) {
                throw new UnifiedResultException(Constant.SERVICE_TYPE);
            }
            // 获取到该旅客
            pnrNmDto = filterNmDto.get(0);
            // 旅客的所有SSR
            List<PnrSsrDto> pnrSsrDtos = pnrNmDto.getPnrSsrDtos();
            this.validateSegmentMatch(ssrDto, ssrDto.getFlightNo(), memoryDataPnr);
            switch (ssrDto.getSsrType()) {
                case Constant.SSR_TYPE_FOID:
                    this.handleFoid(pnrSsrDtos, ssrDto, mnjxNmSsr, pnrNmDto, memoryDataPnr);
                    break;
                case Constant.SSR_TYPE_INFT:
                    this.handleInft(pnrSsrDtos, ssrDto, mnjxNmSsr, pnrNmDto, memoryDataPnr);
                    break;
                case Constant.SSR_TYPE_CHLD:
                    this.handleChld(pnrSsrDtos, ssrDto, mnjxNmSsr, pnrNmDto, memoryDataPnr);
                    break;
                case Constant.SSR_TYPE_FQTV:
                    this.handleFqtv(pnrSsrDtos, ssrDto, mnjxNmSsr, pnrNmDto, memoryDataPnr);
                    break;
                case Constant.SSR_TYPE_MEDA:
                    this.handleMeda(pnrSsrDtos, ssrDto, mnjxNmSsr, pnrNmDto, memoryDataPnr);
                    break;
                default:
                    // 其他特服项统一处理
                    this.handleOther(pnrSsrDtos, ssrDto, mnjxNmSsr, pnrNmDto, memoryDataPnr);
                    break;
            }
        } else {
            throw new UnifiedResultException(Constant.PSGR_ID);
        }
        pnrSsrDto.setMnjxNmSsr(mnjxNmSsr);
        pnrNmDto.getPnrSsrDtos().add(pnrSsrDto);
    }

    /**
     * Description: 处理 INFT 类型
     *
     * @param pnrSsrDtos    pnrSsrDtos
     * @param ssrDto        ssrDto
     * @param mnjxNmSsr     mnjxPnrSsr
     * @param pnrNmDto      pnrNmDto
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/5/31 15:29
     */
    private void handleInft(List<PnrSsrDto> pnrSsrDtos, SsrDto ssrDto, MnjxNmSsr mnjxNmSsr, PnrNmDto pnrNmDto, MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        // 验证航段组是否存在
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos().stream()
                .filter(p -> !p.isXe())
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(pnrSegDtos)) {
            throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
        }
        // 验证唯一INFT类型
        if (CollUtil.isNotEmpty(pnrSsrDtos)) {
            PnrSegDto segDto = pnrSegDtos.stream()
                    .filter(p -> p.getPnrIndex() == Integer.parseInt(ssrDto.getSegIndex()))
                    .collect(Collectors.toList())
                    .get(0);
            long existCount = pnrSsrDtos.stream()
                    .filter(s -> !s.isXe() && s.getMnjxNmSsr().getSsrType().equals(ssrDto.getSsrType()))
                    .filter(s -> s.getMnjxNmSsr().getPnrSegNo().intValue() == segDto.getMnjxPnrSeg().getPnrSegNo().intValue())
                    .count();
            if (existCount > 0) {
                throw new UnifiedResultException(Constant.DUP_ID);
            }
        }
        // 验证适合和航段组匹配
        String flightDate = DateUtils.com2ymd(ssrDto.getFlightDate());
        int segNo = 0;
        for (PnrSegDto pnrSegDto : pnrSegDtos) {
            boolean isSegRight = true;
            MnjxPnrSeg mnjxPnrSeg = pnrSegDto.getMnjxPnrSeg();
            // 验证航班号正确
            if (!ssrDto.getFlightNo().equals(mnjxPnrSeg.getFlightNo())) {
                isSegRight = false;
            }
            // 验证城市对正确
            if (!ssrDto.getCityPair().equals(StrUtil.format("{}{}", mnjxPnrSeg.getOrg(), mnjxPnrSeg.getDst()))) {
                isSegRight = false;
            }
            // 验证舱等正确
            if (!ssrDto.getSellCabin().equals(mnjxPnrSeg.getSellCabin())) {
                isSegRight = false;
            }
            // 验证日期正确
            if (!flightDate.equals(mnjxPnrSeg.getFlightDate())) {
                isSegRight = false;
            }
            if (isSegRight) {
                segNo = mnjxPnrSeg.getPnrSegNo();
                break;
            }
        }
        if (segNo == 0) {
            throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
        }
        // SSR INFT需要有XN项
        List<PnrXnDto> pnrXnDtos = pnrNmDto.getPnrXnDtos();
        if (CollUtil.isEmpty(pnrXnDtos) || pnrXnDtos.stream().allMatch(PnrXnDto::isXe)) {
            throw new UnifiedResultException(Constant.NO_XN);
        }
        String inputValue = StrUtil.format("SSR INFT {} {}1 {} {} {}{} {} {}/P{}", ssrDto.getAirlineCode(), ssrDto.getActionCode(), ssrDto.getCityPair(), ssrDto.getFlightNo().substring(2), ssrDto.getSellCabin(), ssrDto.getFlightDate(), ssrDto.getInfName(), ssrDto.getBirthDay(), ssrDto.getPsgIndex());
        mnjxNmSsr.setOrgDst(ssrDto.getCityPair());
        mnjxNmSsr.setFltDate(flightDate);
        mnjxNmSsr.setPnrSegNo(segNo);
        mnjxNmSsr.setSsrType(ssrDto.getSsrType());
        mnjxNmSsr.setActionCode(ssrDto.getActionCode());
        mnjxNmSsr.setInputValue(inputValue);
        mnjxNmSsr.setPnrNmId(pnrNmDto.getMnjxPnrNm().getPnrNmId());
        mnjxNmSsr.setSsrInfo(inputValue);
        mnjxNmSsr.setAirlineCode(ssrDto.getAirlineCode());
    }

    /**
     * Title: handleFoid
     * Description: 处理 FOID 类型
     *
     * @param pnrSsrDtos    pnrSsrDtos
     * @param ssrDto        ssrDto
     * @param mnjxNmSsr     mnjxPnrSsr
     * @param pnrNmDto      pnrNmDto
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/5/31 15:24
     */
    private void handleFoid(List<PnrSsrDto> pnrSsrDtos, SsrDto ssrDto, MnjxNmSsr mnjxNmSsr, PnrNmDto pnrNmDto, MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        // 验证唯一FOID类型
        this.validateExistSsr(ssrDto.getSsrType(), pnrSsrDtos, null);
        // 验证身份证号上的年龄，如果1-12岁，且当前有航段信息，旅客没有CHLD时，自动添加一条CHLD
        boolean hasChld = false;
        if (CollUtil.isNotEmpty(pnrSsrDtos)) {
            long existCount = pnrSsrDtos.stream()
                    .filter(s -> !s.isXe())
                    .filter(s -> s.getMnjxNmSsr().getSsrType().equals(Constant.SSR_TYPE_CHLD))
                    .count();
            hasChld = existCount > 0;
        }
        MnjxPnrNm mnjxPnrNm = pnrNmDto.getMnjxPnrNm();
        // 根据年龄判断是否自动添加儿童项
        if (CollUtil.isNotEmpty(memoryDataPnr.getPnrSegDtos()) && !hasChld && Constant.CERTIFICATE_TYPE_NI.equals(ssrDto.getCertificateType())) {
            String foidNumber = ssrDto.getFoidNumber();
            String ymd = foidNumber.substring(6, 14);
            ymd = StrUtil.format("{}-{}-{}", ymd.substring(0, 4), ymd.substring(4, 6), ymd.substring(6));
            int yearDiff = this.getYearDiff(ymd);
            String psgType = mnjxPnrNm.getPsgType();
            if (yearDiff > 1 && yearDiff < 12) {
                boolean autoAddChld = true;
                if (psgType.equals(Constant.UM_TYPE)) {
                    MnjxPnrNmUm mnjxPnrNmUm = pnrNmDto.getMnjxPnrNmUm();
                    if (mnjxPnrNmUm.getUmAge() > 12) {
                        autoAddChld = false;
                    }
                }
                if (autoAddChld) {
                    PnrSegDto pnrSegDto = memoryDataPnr.getPnrSegDtos().get(0);
                    MnjxPnrSeg mnjxPnrSeg = pnrSegDto.getMnjxPnrSeg();
                    PnrSsrDto pnrSsrDto = new PnrSsrDto();
                    MnjxNmSsr chldSsr = new MnjxNmSsr();
                    chldSsr.setSsrType(Constant.SSR_TYPE_CHLD);
                    chldSsr.setActionCode(Constant.ACTION_CODE_HK);
                    chldSsr.setAirlineCode(mnjxPnrSeg.getFlightNo().substring(0, 2));
                    chldSsr.setPnrNmId(mnjxPnrNm.getPnrNmId());
                    chldSsr.setInputValue(StrUtil.format("SSR CHLD {} {}1 {}/P{}", chldSsr.getAirlineCode(), chldSsr.getActionCode(), DateUtils.ymd2Com(ymd), ssrDto.getPsgIndex()));
                    chldSsr.setSsrInfo(chldSsr.getInputValue());
                    pnrSsrDto.setMnjxNmSsr(chldSsr);
                    pnrNmDto.getPnrSsrDtos().add(pnrSsrDto);
                    mnjxPnrNm.setPsgType(Constant.CHILD_TYPE);
                }
            }
        }
        // 根据证件号码、证件号和航司查询常客表判断是否自动添加FQTV
        // 多航段组，如果航司都和卡航司一样，需要添加多个FQTV
        // 如果是共享航班，按照承运航司添加FQTV
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos().stream()
                .filter(p -> !p.isXe() && !Constant.SA.equals(p.getMnjxPnrSeg().getPnrSegType()))
                .collect(Collectors.toList());
        List<String> airlineCodeList = pnrSegDtos.stream()
                .map(p -> StrUtil.isNotEmpty(p.getCarrierFlight()) ? p.getCarrierFlight().substring(0, 2) : p.getMnjxPnrSeg().getFlightNo().substring(0, 2))
                .distinct()
                .collect(Collectors.toList());
        List<MnjxFrequenter> frequenterList = iMnjxFrequenterService.lambdaQuery()
                .in(MnjxFrequenter::getAirlineCode, airlineCodeList)
                .eq(MnjxFrequenter::getFrequenterCertificateType, ssrDto.getCertificateType())
                .eq(MnjxFrequenter::getFrequenterCertificateNo, ssrDto.getFoidNumber())
                .list();
        for (MnjxFrequenter frequenter : frequenterList) {
            for (PnrSegDto pnrSegDto : pnrSegDtos) {
                MnjxPnrSeg pnrSeg = pnrSegDto.getMnjxPnrSeg();
                String airlineCode;
                if (StrUtil.isNotEmpty(pnrSegDto.getCarrierFlight())) {
                    airlineCode = pnrSegDto.getCarrierFlight().substring(0, 2);
                } else {
                    airlineCode = pnrSeg.getFlightNo().substring(0, 2);
                }
                // 如果在输入FOID之前已经输入了FQTV，则不自动添加FQTV
                if (pnrNmDto.getPnrSsrDtos().stream().anyMatch(s -> !s.isXe() && airlineCode.equals(s.getMnjxNmSsr().getAirlineCode()) && Constant.SSR_TYPE_FQTV.equals(s.getMnjxNmSsr().getSsrType()) && (pnrSeg.getOrg() + pnrSeg.getDst()).equals(s.getMnjxNmSsr().getOrgDst()))) {
                    continue;
                }
                if (frequenter.getAirlineCode().equals(airlineCode)) {
                    PnrSsrDto pnrSsrDto = new PnrSsrDto();
                    MnjxNmSsr fqtvSsr = new MnjxNmSsr();
                    fqtvSsr.setSsrType(Constant.SSR_TYPE_FQTV);
                    fqtvSsr.setActionCode(Constant.ACTION_CODE_HK);
                    fqtvSsr.setPnrSegNo(pnrSeg.getPnrSegNo());
                    fqtvSsr.setOrgDst(pnrSeg.getOrg() + pnrSeg.getDst());
                    fqtvSsr.setFltDate(pnrSeg.getFlightDate());
                    fqtvSsr.setAirlineCode(frequenter.getAirlineCode());
                    fqtvSsr.setPnrNmId(mnjxPnrNm.getPnrNmId());
                    fqtvSsr.setInputValue(StrUtil.format("SSR FQTV {} HK1 {}{} {} {} {} {}{}/P{}", frequenter.getAirlineCode(), pnrSeg.getOrg(), pnrSeg.getDst(), pnrSeg.getSellCabin(), pnrSeg.getFlightNo().substring(2), DateUtils.ymd2Com(pnrSeg.getFlightDate()), frequenter.getAirlineCode(), frequenter.getFrequenterCard(), ssrDto.getPsgIndex()));
                    fqtvSsr.setSsrInfo(fqtvSsr.getInputValue());
                    pnrSsrDto.setMnjxNmSsr(fqtvSsr);
                    pnrNmDto.getPnrSsrDtos().add(pnrSsrDto);
                }
            }
        }

        String inputValue = StrUtil.format("SSR FOID {} {}1 {}{}/P{}", ssrDto.getAirlineCode(), ssrDto.getActionCode(), ssrDto.getCertificateType(), ssrDto.getFoidNumber(), ssrDto.getPsgIndex());
        mnjxNmSsr.setSsrType(ssrDto.getSsrType());
        mnjxNmSsr.setActionCode(ssrDto.getActionCode());
        mnjxNmSsr.setInputValue(inputValue);
        mnjxNmSsr.setPnrNmId(mnjxPnrNm.getPnrNmId());
        mnjxNmSsr.setSsrInfo(inputValue);
        mnjxNmSsr.setAirlineCode(ssrDto.getAirlineCode());
    }

    /**
     * Title: handleChld
     * Description: 处理 CHLD
     *
     * @param pnrSsrDtos    pnrSsrDtos
     * @param ssrDto        ssrDto
     * @param mnjxNmSsr     mnjxPnrSsr
     * @param pnrNmDto      pnrNmDto
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/1 15:59
     */
    private void handleChld(List<PnrSsrDto> pnrSsrDtos, SsrDto ssrDto, MnjxNmSsr mnjxNmSsr, PnrNmDto pnrNmDto, MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        MnjxPnrNm mnjxPnrNm = pnrNmDto.getMnjxPnrNm();
        // 是否有航段组
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        if (CollUtil.isEmpty(pnrSegDtos)) {
            throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
        }
        // 验证和航段组的航班航司是否匹配
        boolean matchAirlineCode = false;
        for (PnrSegDto pnrSegDto : pnrSegDtos) {
            MnjxPnrSeg mnjxPnrSeg = pnrSegDto.getMnjxPnrSeg();
            String carrierFlight = pnrSegDto.getCarrierFlight();
            if (mnjxPnrSeg.getFlightNo().substring(0, 2).equals(ssrDto.getAirlineCode()) || (StrUtil.isNotEmpty(carrierFlight) && carrierFlight.substring(0, 2).equals(ssrDto.getAirlineCode()))) {
                matchAirlineCode = true;
                break;
            }
        }
        if (!matchAirlineCode) {
            throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
        }
        if (mnjxPnrNm.getPsgType().equals(Constant.UM_TYPE)) {
            MnjxPnrNmUm mnjxPnrNmUm = pnrNmDto.getMnjxPnrNmUm();
            Integer umAge = mnjxPnrNmUm.getUmAge();
            // 如果不是无陪儿童
            if (umAge > 12) {
                throw new UnifiedResultException(Constant.NOT_CHD);
            }
        }

        for (PnrSsrDto pnrSsrDto : pnrSsrDtos) {
            MnjxNmSsr existSsr = pnrSsrDto.getMnjxNmSsr();
            // 如果已存在了CHLD，则将已有SSR CHLD记录标记为删除，然后将当前SSR CHLD记录增加上去
            if (existSsr.getSsrType().equals(ssrDto.getSsrType())) {
                List<Integer> xeList = new ArrayList<>();
                xeList.add(pnrSsrDto.getPnrIndex());
                XeDto xeDto = new XeDto();
                xeDto.setXeIndexs(xeList);
                pnrCommandServicePartXe.xe(memoryDataPnr, xeDto);
            }
        }
        String inputValue = StrUtil.format("SSR CHLD {} {}1 {}/P{}", ssrDto.getAirlineCode(), ssrDto.getActionCode(), ssrDto.getBirthDay(), ssrDto.getPsgIndex());
        // 旅客变更为儿童
        mnjxPnrNm.setPsgType(Constant.CHILD_TYPE);
        mnjxNmSsr.setSsrType(ssrDto.getSsrType());
        mnjxNmSsr.setActionCode(ssrDto.getActionCode());
        mnjxNmSsr.setInputValue(inputValue);
        mnjxNmSsr.setPnrNmId(pnrNmDto.getMnjxPnrNm().getPnrNmId());
        mnjxNmSsr.setSsrInfo(inputValue);
        mnjxNmSsr.setAirlineCode(ssrDto.getAirlineCode());
    }

    /**
     * Title: handleFqtv
     * Description: 处理FQTV
     *
     * @param pnrSsrDtos
     * @param ssrDto
     * @param mnjxNmSsr
     * @param pnrNmDto
     * @param memoryDataPnr
     * @return
     * <AUTHOR>
     * @date 2023/3/17 10:31
     */
    private void handleFqtv(List<PnrSsrDto> pnrSsrDtos, SsrDto ssrDto, MnjxNmSsr mnjxNmSsr, PnrNmDto pnrNmDto, MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        // 是否有航段组
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        if (CollUtil.isEmpty(pnrSegDtos)) {
            throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
        }
        PnrSegDto pnrSegDto;
        String segIndexStr = ssrDto.getSegIndex();
        if (StrUtil.isNotEmpty(segIndexStr)) {
            // 验证输入的航段组序号是否能找到航段组
            int segIndex = Integer.parseInt(segIndexStr);
            if (pnrSegDtos.stream().noneMatch(p -> !p.isXe() && segIndex == p.getPnrIndex())) {
                throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
            }
            pnrSegDto = pnrSegDtos.stream()
                    .filter(p -> !p.isXe() && segIndex == p.getPnrIndex())
                    .collect(Collectors.toList())
                    .get(0);
        } else {
            // 没有输入航段组序号，如果航段组数量大于1，报错
            List<PnrSegDto> collect = pnrSegDtos.stream()
                    .filter(p -> !p.isXe())
                    .collect(Collectors.toList());
            if (collect.size() > 1) {
                throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
            }
            pnrSegDto = collect.get(0);
        }
        MnjxPnrSeg pnrSeg = pnrSegDto.getMnjxPnrSeg();
        String cityPair = StrUtil.format("{}{}", pnrSeg.getOrg(), pnrSeg.getDst());
        // 验证输入的航段组序号对应的航段组航司是否匹配
        if (!ssrDto.getAirlineCode().equals(pnrSeg.getFlightNo().substring(0, 2))) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }
        // 验证是否已存在特服项
        if (pnrSsrDtos.stream().anyMatch(p -> !p.isXe() && Constant.SSR_TYPE_FQTV.equals(p.getMnjxNmSsr().getSsrType()) && pnrSeg.getPnrSegNo().equals(p.getMnjxNmSsr().getPnrSegNo()))) {
            throw new UnifiedResultException(Constant.EXIST_FQTV);
        }
        String inputValue;
        if (StrUtil.isNotEmpty(segIndexStr)) {
            inputValue = StrUtil.format("SSR FQTV {} HK1 {}{} {} {} {} {}{}/P{}", ssrDto.getAirlineCode(), pnrSeg.getOrg(), pnrSeg.getDst(), pnrSeg.getSellCabin(), pnrSeg.getFlightNo().substring(2), DateUtils.ymd2Com(pnrSeg.getFlightDate()), ssrDto.getAirlineCode(), ssrDto.getFrequentCard(), ssrDto.getPsgIndex());
        } else {
            inputValue = StrUtil.format("SSR FQTV {} HK/ {}{}/{}/P{}", ssrDto.getAirlineCode(), ssrDto.getAirlineCode(), ssrDto.getFrequentCard(), ssrDto.getFrequentCardLevel(), ssrDto.getPsgIndex());
        }
        MnjxFrequenter dbFrequenter = iMnjxFrequenterService.lambdaQuery()
                .eq(MnjxFrequenter::getAirlineCode, ssrDto.getAirlineCode())
                .eq(MnjxFrequenter::getFrequenterCard, ssrDto.getFrequentCard())
                .one();
        if (ObjectUtil.isNotEmpty(dbFrequenter)) {
            //todo 暂时保留这个判断
        }
        MnjxFrequenter mnjxFrequenter = new MnjxFrequenter();
        mnjxFrequenter.setFrequenterCard(ssrDto.getFrequentCard());
        mnjxFrequenter.setFrequenterLevel(ssrDto.getFrequentCardLevel());
        mnjxFrequenter.setAirlineCode(ssrDto.getAirlineCode());
        pnrNmDto.getMnjxFrequenterList().add(mnjxFrequenter);
        mnjxNmSsr.setPnrSegNo(pnrSeg.getPnrSegNo());
        mnjxNmSsr.setOrgDst(cityPair);
        mnjxNmSsr.setFltDate(pnrSeg.getFlightDate());
        mnjxNmSsr.setSsrType(Constant.SSR_TYPE_FQTV);
        mnjxNmSsr.setActionCode(ssrDto.getActionCode());
        mnjxNmSsr.setInputValue(inputValue);
        mnjxNmSsr.setPnrNmId(pnrNmDto.getMnjxPnrNm().getPnrNmId());
        mnjxNmSsr.setSsrInfo(inputValue);
        mnjxNmSsr.setAirlineCode(ssrDto.getAirlineCode());
    }

    private void handleMeda(List<PnrSsrDto> pnrSsrDtos, SsrDto ssrDto, MnjxNmSsr mnjxNmSsr, PnrNmDto pnrNmDto, MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        // 是否有航段组
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        if (CollUtil.isEmpty(pnrSegDtos)) {
            throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
        }
        PnrSegDto pnrSegDto;
        String segIndexStr = ssrDto.getSegIndex();
        if (StrUtil.isNotEmpty(segIndexStr)) {
            // 验证输入的航段组序号是否能找到航段组
            int segIndex = Integer.parseInt(ssrDto.getSegIndex());
            // 航司与输入所选航段组匹配
            List<PnrSegDto> filterSegs = pnrSegDtos.stream()
                    .filter(s -> !s.isXe() && s.getPnrIndex() == segIndex)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(filterSegs)) {
                throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
            }
            pnrSegDto = filterSegs.get(0);
            MnjxPnrSeg mnjxPnrSeg = pnrSegDto.getMnjxPnrSeg();
            if (!ssrDto.getAirlineCode().equals(mnjxPnrSeg.getFlightNo().substring(0, 2))) {
                throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
            }
        } else {
            String flightNo = ssrDto.getFlightNo();
            String flightDate = this.getHandleYearDate(ssrDto.getFlightDate());
            String cityPair = ssrDto.getCityPair();
            // 验证输入的航段信息与内存中的航段信息是否至少有一条匹配
            List<PnrSegDto> filterSegs = pnrSegDtos.stream()
                    .filter(s -> !s.isXe())
                    .filter(s -> s.getMnjxPnrSeg().getFlightNo().equals(flightNo))
                    .filter(s -> s.getMnjxPnrSeg().getFlightDate().equals(DateUtils.com2ymd(flightDate)))
                    .filter(s -> s.getMnjxPnrSeg().getOrg().equals(cityPair.substring(0, 3)))
                    .filter(s -> s.getMnjxPnrSeg().getDst().equals(cityPair.substring(3)))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(filterSegs)) {
                throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
            }
            pnrSegDto = filterSegs.get(0);
        }
        MnjxPnrSeg pnrSeg = pnrSegDto.getMnjxPnrSeg();
        String cityPair = StrUtil.format("{}{}", pnrSeg.getOrg(), pnrSeg.getDst());
        // 验证输入的航段组序号对应的航段组航司是否匹配
        if (!ssrDto.getAirlineCode().equals(pnrSeg.getFlightNo().substring(0, 2))) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }
        // 判断重复
        this.validateExistSsr(Constant.SSR_TYPE_MEDA, pnrSsrDtos, cityPair);
        String inputValue;
        String inputFlightNo = ssrDto.isInputAllFlightNo() ? ssrDto.getFlightNo() : pnrSeg.getFlightNo().substring(2);
        String comDate = DateUtils.ymd2Com(pnrSeg.getFlightDate()).substring(0, 5);
        // 没有输入旅客序号，输入航段序号
        if (ssrDto.isNoPsgIndexInput() && StrUtil.isNotEmpty(segIndexStr)) {
            if (StrUtil.isEmpty(ssrDto.getFreeText())) {
                inputValue = StrUtil.format("SSR MEDA {} NN1 {} {} {}{}", ssrDto.getAirlineCode(), cityPair, pnrSeg.getFlightNo().substring(2), pnrSeg.getCabinClass(), comDate);
            } else {
                inputValue = StrUtil.format("SSR MEDA {} NN1 {} {} {}{} {}", ssrDto.getAirlineCode(), cityPair, pnrSeg.getFlightNo().substring(2), pnrSeg.getCabinClass(), comDate, ssrDto.getFreeText().trim());
            }
        }
        // 没有输入旅客序号，没有输入航段序号
        else if (ssrDto.isNoPsgIndexInput() && StrUtil.isEmpty(segIndexStr)) {
            if (StrUtil.isEmpty(ssrDto.getFreeText())) {
                inputValue = StrUtil.format("SSR MEDA {} NN1 {} {} {}", ssrDto.getAirlineCode(), cityPair, inputFlightNo, comDate);
            } else {
                inputValue = StrUtil.format("SSR MEDA {} NN1 {} {} {} {}", ssrDto.getAirlineCode(), cityPair, inputFlightNo, comDate, ssrDto.getFreeText().trim());
            }
        }
        // 输入旅客序号，输入航段序号
        else if (!ssrDto.isNoPsgIndexInput() && StrUtil.isNotEmpty(segIndexStr)) {
            if (StrUtil.isEmpty(ssrDto.getFreeText())) {
                inputValue = StrUtil.format("SSR MEDA {} NN1 {} {} {}{}/P{}", ssrDto.getAirlineCode(), cityPair, pnrSeg.getFlightNo().substring(2), pnrSeg.getCabinClass(), comDate, ssrDto.getPsgIndex());
            } else {
                inputValue = StrUtil.format("SSR MEDA {} NN1 {} {} {}{} {}/P{}", ssrDto.getAirlineCode(), cityPair, pnrSeg.getFlightNo().substring(2), pnrSeg.getCabinClass(), comDate, ssrDto.getFreeText().trim(), ssrDto.getPsgIndex());
            }
        }
        // 输入旅客序号，没有输入航段序号
        else {
            if (StrUtil.isEmpty(ssrDto.getFreeText())) {
                inputValue = StrUtil.format("SSR MEDA {} NN1 {} {} {}/P{}", ssrDto.getAirlineCode(), cityPair, inputFlightNo, comDate, ssrDto.getPsgIndex());
            } else {
                inputValue = StrUtil.format("SSR MEDA {} NN1 {} {} {} {}/P{}", ssrDto.getAirlineCode(), cityPair, inputFlightNo, comDate, ssrDto.getFreeText().trim(), ssrDto.getPsgIndex());
            }
        }
        mnjxNmSsr.setPnrSegNo(pnrSeg.getPnrSegNo());
        mnjxNmSsr.setOrgDst(cityPair);
        mnjxNmSsr.setFltDate(pnrSeg.getFlightDate());
        mnjxNmSsr.setSsrType(Constant.SSR_TYPE_MEDA);
        mnjxNmSsr.setActionCode(ssrDto.getActionCode());
        mnjxNmSsr.setInputValue(inputValue);
        mnjxNmSsr.setPnrNmId(pnrNmDto.getMnjxPnrNm().getPnrNmId());
        mnjxNmSsr.setSsrInfo(inputValue);
        mnjxNmSsr.setAirlineCode(ssrDto.getAirlineCode());
    }

    /**
     * Title: handleOther
     * Description: 其他特服项
     *
     * @param pnrSsrDtos    pnrSsrDtos
     * @param ssrDto        ssrDto
     * @param mnjxNmSsr     mnjxPnrSsr
     * @param pnrNmDto      pnrNmDto
     * @param memoryDataPnr memoryDataPnr
     * <AUTHOR>
     * @date 2022/6/6 14:57
     */
    private void handleOther(List<PnrSsrDto> pnrSsrDtos, SsrDto ssrDto, MnjxNmSsr mnjxNmSsr, PnrNmDto pnrNmDto, MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        // 是否有航段组
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        if (CollUtil.isEmpty(pnrSegDtos)) {
            throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
        }
        String ssrType = ssrDto.getSsrType();
        String inputValue;
        String flightNo;
        String sellCabin;
        String flightDate;
        String cityPair;
        int segNo = 0;
        // 如果输入了航段组序号
        if (StrUtil.isNotEmpty(ssrDto.getSegIndex())) {
            int segIndex = Integer.parseInt(ssrDto.getSegIndex());
            // 航司与输入所选航段组匹配
            List<PnrSegDto> filterSegs = pnrSegDtos.stream()
                    .filter(s -> s.getPnrIndex() == segIndex)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(filterSegs)) {
                throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
            }
            PnrSegDto pnrSegDto = filterSegs.get(0);
            MnjxPnrSeg mnjxPnrSeg = pnrSegDto.getMnjxPnrSeg();
            if (!ssrDto.getAirlineCode().equals(mnjxPnrSeg.getFlightNo().substring(0, 2))) {
                throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
            }
            segNo = mnjxPnrSeg.getPnrSegNo();
            flightNo = mnjxPnrSeg.getFlightNo();
            flightDate = this.getHandleYearDate(DateUtils.ymd2Com(mnjxPnrSeg.getFlightDate()));
            sellCabin = mnjxPnrSeg.getSellCabin();
            cityPair = StrUtil.format("{}{}", mnjxPnrSeg.getOrg(), mnjxPnrSeg.getDst());
        } else {
            flightNo = ssrDto.getFlightNo();
            flightDate = this.getHandleYearDate(ssrDto.getFlightDate());
            sellCabin = ssrDto.getSellCabin();
            cityPair = ssrDto.getCityPair();
            // 验证输入的航段信息与内存中的航段信息是否至少有一条匹配
            long count = pnrSegDtos.stream()
                    .filter(s -> s.getMnjxPnrSeg().getFlightNo().equals(flightNo))
                    .filter(s -> s.getMnjxPnrSeg().getFlightDate().equals(DateUtils.com2ymd(flightDate)))
                    .filter(s -> s.getMnjxPnrSeg().getOrg().equals(cityPair.substring(0, 3)))
                    .filter(s -> s.getMnjxPnrSeg().getDst().equals(cityPair.substring(3)))
                    .filter(s -> s.getMnjxPnrSeg().getSellCabin().equals(sellCabin))
                    .count();
            if (count == 0) {
                throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
            }
        }
        if (CollUtil.isNotEmpty(pnrSsrDtos)) {
            // 最多只能存在一种餐食
            long mlCount = pnrSsrDtos.stream()
                    .filter(s -> !s.isXe() && cityPair.equals(s.getMnjxNmSsr().getOrgDst()))
                    .filter(s -> s.getMnjxNmSsr().getSsrType().matches("[A-Z]{2}ML"))
                    .count();
            if (mlCount > 0 && ssrType.matches("[A-Z]{2}ML")) {
                throw new UnifiedResultException(Constant.ONLY_ONE_MEAL);
            }
            // 其他项只能存在一项
            this.validateExistSsr(ssrType, pnrSsrDtos, cityPair);
        }
        // 当餐食时，除了SPML其他的没有自由文本
        if (ssrType.matches("[A-Z]{2}ML")) {
            if (!"SPML".equals(ssrType) || StrUtil.isEmpty(ssrDto.getFreeText())) {
                inputValue = StrUtil.format("SSR {} {} {}1 {} {} {}{}/P{}", ssrType, ssrDto.getAirlineCode(), ssrDto.getActionCode(), cityPair,
                        flightNo.substring(2), sellCabin, flightDate, ssrDto.getPsgIndex());
            } else {
                inputValue = StrUtil.format("SSR {} {} {}1 {} {} {}{} {}/P{}", ssrType, ssrDto.getAirlineCode(), ssrDto.getActionCode(), cityPair,
                        flightNo.substring(2), sellCabin, flightDate, ssrDto.getFreeText(), ssrDto.getPsgIndex());
            }
        } else {
            inputValue = StrUtil.format("SSR {} {} {}1 {} {} {}{} {}/P{}", ssrType, ssrDto.getAirlineCode(), ssrDto.getActionCode(), cityPair,
                    flightNo.substring(2), sellCabin, flightDate, ssrDto.getFreeText(), ssrDto.getPsgIndex());
        }
        mnjxNmSsr.setSsrType(ssrType);
        mnjxNmSsr.setActionCode(ssrDto.getActionCode());
        mnjxNmSsr.setInputValue(inputValue);
        mnjxNmSsr.setPnrNmId(pnrNmDto.getMnjxPnrNm().getPnrNmId());
        mnjxNmSsr.setSsrInfo(inputValue);
        mnjxNmSsr.setAirlineCode(ssrDto.getAirlineCode());
        mnjxNmSsr.setOrgDst(cityPair);
        mnjxNmSsr.setPnrSegNo(StrUtil.isNotEmpty(ssrDto.getSegIndex()) ? segNo : null);
    }

    /**
     * Title: validateExistSsr
     * Description: 验证某个SSR类型只能存在唯一一项
     *
     * @param ssrType    ssrType
     * @param pnrSsrDtos pnrSsrDtos
     * @param cityPair   cityPair
     * <AUTHOR>
     * @date 2022/5/31 16:48
     */
    private void validateExistSsr(String ssrType, List<PnrSsrDto> pnrSsrDtos, String cityPair) throws UnifiedResultException {
        if (CollUtil.isNotEmpty(pnrSsrDtos)) {
            long existCount;
            if (StrUtil.isNotEmpty(cityPair)) {
                existCount = pnrSsrDtos.stream()
                        .filter(pnrSsrDto -> !pnrSsrDto.isXe() && cityPair.equals(pnrSsrDto.getMnjxNmSsr().getOrgDst()))
                        .filter(pnrSsrDto -> pnrSsrDto.getMnjxNmSsr().getSsrType().trim().equalsIgnoreCase(ssrType.trim()))
                        .count();
            } else {
                existCount = pnrSsrDtos.stream()
                        .filter(pnrSsrDto -> !pnrSsrDto.isXe() && !Constant.ACTION_CODE_XX.equals(pnrSsrDto.getMnjxNmSsr().getActionCode()))
                        .filter(pnrSsrDto -> pnrSsrDto.getMnjxNmSsr().getSsrType().trim().equalsIgnoreCase(ssrType.trim()))
                        .count();
            }
            if (existCount > 0) {
                throw new UnifiedResultException(Constant.DUP_ID);
            }
            // 轮椅类型只能有一种
            long wcCount;
            if (StrUtil.isNotEmpty(cityPair)) {
                wcCount = pnrSsrDtos.stream()
                        .filter(pnrSsrDto -> !pnrSsrDto.isXe() && cityPair.equals(pnrSsrDto.getMnjxNmSsr().getOrgDst()))
                        .filter(pnrSsrDto -> pnrSsrDto.getMnjxNmSsr().getSsrType().startsWith("WC") && ssrType.startsWith("WC"))
                        .count();
            } else {
                wcCount = pnrSsrDtos.stream()
                        .filter(pnrSsrDto -> pnrSsrDto.getMnjxNmSsr().getSsrType().startsWith("WC") && ssrType.startsWith("WC"))
                        .count();
            }
            if (wcCount > 0) {
                throw new UnifiedResultException(Constant.DUP_ID);
            }
        }
    }

    /**
     * Title: getYearDiff
     * Description: 获取年份差
     *
     * @param ymd ymd
     * @return 获取年份差
     * <AUTHOR>
     * @date 2022/6/1 16:38
     */
    private int getYearDiff(String ymd) {
        Date compareDate = DateUtils.ymd2Date(ymd);
        Calendar compareCal = Calendar.getInstance();
        Calendar nowCal = Calendar.getInstance();
        compareCal.setTime(compareDate);
        nowCal.setTime(new Date());
        return nowCal.get(Calendar.YEAR) - compareCal.get(Calendar.YEAR);
    }

    /**
     * Title: getHandleYearDate
     * Description: 获取处理了年之后的航信日期，如果跨年才显示年
     *
     * @param flightDate 航班日期
     * @return {@link String}
     * <AUTHOR>
     * @date 2022/7/14 10:24
     */
    private String getHandleYearDate(String flightDate) {
        int subYear = this.getYearDiff(DateUtils.com2ymd(flightDate));
        if (subYear == 0) {
            flightDate = flightDate.substring(0, 5);
        }
        return flightDate;
    }

    /**
     * Title: validateSegmentMatch
     * Description: 验证SSR航司与航段组航司匹配，共享航班额外验证承运航班航司至少有一个匹配
     *
     * @param ssrDto
     * @param flightNo
     * @param memoryDataPnr
     * @return
     * <AUTHOR>
     * @date 2023/3/9 10:03
     */
    private void validateSegmentMatch(SsrDto ssrDto, String flightNo, MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        String airlineCode = ssrDto.getAirlineCode();
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        if (CollUtil.isEmpty(pnrSegDtos)) {
            throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
        }
        pnrSegDtos = pnrSegDtos.stream()
                .filter(p -> !p.isXe() && !Constant.SA.equals(p.getMnjxPnrSeg().getPnrSegType()))
                .filter(p -> airlineCode.equals(p.getMnjxPnrSeg().getFlightNo().substring(0, 2)))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(pnrSegDtos)) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }
        if (StrUtil.isNotEmpty(ssrDto.getSegIndex())) {
            pnrSegDtos = pnrSegDtos.stream()
                    .filter(p -> ssrDto.getSegIndex().equals(StrUtil.toString(p.getPnrIndex())))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(pnrSegDtos)) {
                throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
            }
        }
        PnrSegDto pnrSegDto = pnrSegDtos.get(0);
        MnjxPnrSeg mnjxPnrSeg = pnrSegDto.getMnjxPnrSeg();
        if (StrUtil.isEmpty(flightNo)) {
            flightNo = mnjxPnrSeg.getFlightNo();
        }
        // 如果输入的航司既不是承运航司2字码也不是共享航司2字码，报错
        if (!airlineCode.equals(flightNo.substring(0, 2)) && (StrUtil.isEmpty(pnrSegDto.getCarrierFlight()) || !airlineCode.equals(pnrSegDto.getCarrierFlight().substring(0, 2)))) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }
    }
}
