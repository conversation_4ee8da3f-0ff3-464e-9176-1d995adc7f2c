package com.swcares.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.*;
import com.swcares.entity.*;
import com.swcares.mapper.MnjxSeatMapper;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/24 11:05
 */
@Slf4j
@Service
public class EffectFlightServiceImpl implements IEffectFlightService {

    @Resource
    private MnjxSeatMapper mnjxSeatMapper;

    @Resource
    private IMnjxPlaneService iMnjxPlaneService;

    @Resource
    private IMnjxCndService iMnjxCndService;

    @Resource
    private IMnjxTcardSectionService iMnjxTcardSectionService;

    @Resource
    private IMnjxSeatModelService iMnjxSeatModelService;

    @Resource
    private IMnjxGateService iMnjxGateService;

    @Resource
    private IMnjxFlightGateService iMnjxFlightGateService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxConfigService iMnjxConfigService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxCityService iMnjxCityService;

    @Resource
    private IMnjxStandardPatService iMnjxStandardPatService;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;

    @Resource
    private IMnjxSeatService iMnjxSeatService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    /**
     * 生效数据
     *
     * @param mnjxFlight 航班数据
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void createEffect(MnjxFlight mnjxFlight) throws UnifiedResultException {
        MnjxConfig configFlightInitial = iMnjxConfigService.lambdaQuery()
                .eq(MnjxConfig::getType, "FLIGHT_INITIAL")
                .one();
        String isFlightInitial = StrUtil.EMPTY;
        //初始化航班
        if (ObjectUtil.isNotEmpty(configFlightInitial) && Constant.STR_ONE.equals(configFlightInitial.getAvailable())) {
            isFlightInitial = Constant.Y;
        }
        // 生效航班，初始化航班数据
        log.info("创建计划航班");
        List<MnjxPlanFlight> mnjxPlanFlights = this.createPlanFlights(mnjxFlight, isFlightInitial);
        log.info("写入计划航班");
        iMnjxPlanFlightService.saveBatch(mnjxPlanFlights);
        // 生成计划航节
        log.info("创建计划航节");
        List<MnjxPlanSection> mnjxPlanSections = this.createPlanSections(mnjxPlanFlights, mnjxFlight);
        log.info("写入计划航节");
        iMnjxPlanSectionService.saveBatch(mnjxPlanSections);
        // 开舱数据
        log.info("创建开舱");
        List<MnjxOpenCabin> mnjxOpenCabins = this.createOpenCabins(mnjxPlanSections, mnjxFlight);
        log.info("写入开舱");
        iMnjxOpenCabinService.saveBatch(mnjxOpenCabins);
        // 舱位座位数据
        log.info("创建座位");
        List<MnjxSeat> mnjxSeats = this.createSeats(mnjxOpenCabins, mnjxFlight);
        log.info("写入座位");
        iMnjxSeatService.saveBatch(mnjxSeats);
        // 更新t-card数据
        log.info("更新t-card");
        this.updateTcard(mnjxFlight);
    }

    /**
     * 生成计划航班数据
     *
     * @param mnjxFlight 航班数据
     * @return 生成计划航班数据
     */
    private List<MnjxPlanFlight> createPlanFlights(MnjxFlight mnjxFlight, String isFlightInitial) throws UnifiedResultException {
        // 航班的时间点
        List<String> planFlightDates = this.getPlanFlightDates(mnjxFlight);
        // 按照规定日期都要生成航班
        List<MnjxPlanFlight> mnjxPlanFlights = new ArrayList<>();
        planFlightDates.forEach(planFlightDate -> {
            MnjxPlanFlight mnjxPlanFlight = this.createPlanFlight(mnjxFlight, planFlightDate, isFlightInitial);
            mnjxPlanFlights.add(mnjxPlanFlight);
        });
        return mnjxPlanFlights;
    }

    /**
     * 更新t-card数据
     *
     * @param mnjxFlight 航班数据
     */
    private void updateTcard(MnjxFlight mnjxFlight) {
        // 获取当前航班得tcard数据
        MnjxTcard mnjxTcard = getMnjxTcard(mnjxFlight);
        mnjxTcard.setBecomeEffective(Constant.COMMON_Y);
        mnjxTcard.updateById();
    }

    /**
     * 获得航班周期对应的所有时间点
     *
     * @param mnjxFlight 航班数据
     * @return 航班周期对应的所有时间点
     * @throws UnifiedResultException 统一异常
     */
    private List<String> getPlanFlightDates(MnjxFlight mnjxFlight) throws UnifiedResultException {
        // 获取TCard数据
        MnjxTcard mnjxTcard = getMnjxTcard(mnjxFlight);
        // 班期
        String cycle = mnjxTcard.getCycle();
        // 班期
        if (StrUtil.isBlank(cycle) || StrUtils.length(cycle) > Constant.FOUR) {
            throw new UnifiedResultException(StrUtils.format("航班{}周期{}错误：指定必须在4位以内。例如：123/X12/D得格式", mnjxFlight.getFlightNo(), cycle));
        }
        // 获取时间流
        List<String> planFlightDates = DateUtils.rangeToList(mnjxTcard.getStartDate(), mnjxTcard.getEndDate());
        // 如果设置的是D，就代表每天都生成
        if (!StrUtils.equalsIgnoreCase(cycle, Constant.TCARD_CYCLE_D)) {
            List<String> weeks = CollUtils.toList(StrUtils.cut(cycle, NumberUtils.parseInt(Constant.STR_ONE)));
            // 如果不是设置成D的，已X开始的
            if (StrUtils.startWithIgnoreCase(cycle, Constant.TCARD_CYCLE_X)) {
                // 从时间流中排除星期几不生成航班的日期
                planFlightDates = planFlightDates.stream().filter(planFlightDate -> {
                    boolean isFilter = true;
                    // 获得星期几
                    int dayOfWeek = DateUtil.dayOfWeek(DateUtils.ymd2Date(planFlightDate)) - 1;
                    if (weeks.contains(StrUtils.toString(dayOfWeek))) {
                        isFilter = false;
                    }
                    return isFilter;
                }).collect(Collectors.toList());
            } else {
                // 从时间流中获取星期几要生成航班的日期
                planFlightDates = planFlightDates.stream().filter(planFlightDate -> {
                    boolean isFilter = false;
                    // 获得星期几
                    int dayOfWeek = DateUtil.dayOfWeek(DateUtils.ymd2Date(planFlightDate)) - 1;
                    if (weeks.contains(StrUtils.toString(dayOfWeek))) {
                        isFilter = true;
                    }
                    return isFilter;
                }).collect(Collectors.toList());
            }
        }
        // 剔除已有旅客的时间点
        List<String> existPassengerDateList = this.getExistPassengerDateList(mnjxFlight.getFlightNo());
        if (CollUtil.isNotEmpty(existPassengerDateList)) {
            planFlightDates = planFlightDates.stream()
                    .filter(d -> !existPassengerDateList.contains(d))
                    .collect(Collectors.toList());
        }
        return planFlightDates;
    }

    /**
     * 计划航节
     *
     * @param mnjxPlanFlights 计划航班
     * @param mnjxFlight      航班
     * @return 计划航节
     */
    private List<MnjxPlanSection> createPlanSections(List<MnjxPlanFlight> mnjxPlanFlights, MnjxFlight mnjxFlight) throws UnifiedResultException {
        // 获取TCard数据
        MnjxTcard mnjxTcard = this.getMnjxTcard(mnjxFlight);
        // 获得所有得t2-card数据，就是航段数据
        List<MnjxTcardSection> mnjxTcardSections = iMnjxTcardSectionService.lambdaQuery()
                .eq(MnjxTcardSection::getTcardId, mnjxTcard.getTcardId())
                .orderByAsc(MnjxTcardSection::getSectionNo)
                .list();
        // 飞机ID(具体用那么飞机执飞，生产是由运控管理系统来指定的。我们是模拟的，所以就随机找一个飞机就可以了)
        MnjxPlane mnjxPlane = this.randomGetPlane(mnjxFlight);
        if (ObjectUtil.isEmpty(mnjxPlane)) {
            throw new UnifiedResultException(StrUtils.format("航班{}未找到此飞机", mnjxFlight.getFlightNo()));
        }
        // 是否初始化和分配登机口
        MnjxConfig configGate = iMnjxConfigService.lambdaQuery()
                .eq(MnjxConfig::getType, "GATE")
                .one();
        MnjxFlightGate flightGate = iMnjxFlightGateService.lambdaQuery()
                .eq(MnjxFlightGate::getFlightNo, mnjxFlight.getFlightNo())
                .one();
        Random rd = new Random();
        // 保存集合
        List<MnjxPlanSection> mnjxPlanSections = new ArrayList<>();
        for (MnjxPlanFlight mnjxPlanFlight : mnjxPlanFlights) {
            // 航节数目（减1得原因就是航节数量）
            int tcardSectionSize = mnjxTcardSections.size() - 1;
            // 根据t2-card得数据生成航节数据
            for (int i = 0; i < tcardSectionSize; i++) {
                MnjxPlanSection mnjxPlanSection = new MnjxPlanSection();
                // id
                mnjxPlanSection.setPlanSectionId(IdUtils.getId());
                // 计划航班
                mnjxPlanSection.setPlanFlightId(mnjxPlanFlight.getPlanFlightId());
                // 是否最后一个航节,当序号+1等于航节数时，说明时最后一个航节（这个地方没得业务逻辑关系，只是用数字来表示最后得航节时在那个编号）
                if (i + 1 == tcardSectionSize) {
                    mnjxPlanSection.setIsLastSection(Integer.parseInt(Constant.STR_ONE));
                } else {
                    mnjxPlanSection.setIsLastSection(Integer.parseInt(Constant.STR_ZERO));
                }
                // 出港
                MnjxTcardSection mnjxTcardSectionDep = mnjxTcardSections.get(i);
                // 进港
                MnjxTcardSection mnjxTcardSectionArr = mnjxTcardSections.get(i + 1);
                // 出发机场的id
                mnjxPlanSection.setDepAptId(mnjxTcardSectionDep.getAirportId());
                // 到达机场的id
                mnjxPlanSection.setArrAptId(mnjxTcardSectionArr.getAirportId());
                // 预计离港时间
                mnjxPlanSection.setEstimateOff(mnjxTcardSectionDep.getDepTime());
                // 预计离港日期变更
                mnjxPlanSection.setEstimateOffChange(mnjxTcardSectionDep.getOffDateChange());
                // 预计到达时间
                mnjxPlanSection.setEstimateArr(mnjxTcardSectionArr.getArrTime());
                // 预计到达日期变更
                mnjxPlanSection.setEstimateArrChange(mnjxTcardSectionArr.getArrDateChange());
                // 预计登机时间
                mnjxPlanSection.setEstimateBoarding(mnjxTcardSectionDep.getBrdTime());
                // 预计登机变更
                mnjxPlanSection.setEstimateBoardingChange(mnjxTcardSectionDep.getBrdDateChange());
                // 实际离港时间
                if (ObjectUtil.isNotEmpty(flightGate) && flightGate.getDelayTime() >= 30 && flightGate.getDelayTime() < 1000) {
                    String tmp = StrUtil.format("{} {}:{}:00", DateUtil.today(), mnjxPlanSection.getEstimateOff().substring(0, 2), mnjxPlanSection.getEstimateOff().substring(2));
                    DateTime estimateOffTime = DateUtil.parseDateTime(tmp);
                    DateTime actualOffTime = DateUtil.offsetMinute(estimateOffTime, flightGate.getDelayTime());
                    String actualOff = DateUtil.format(actualOffTime, "HHmm");
                    mnjxPlanSection.setActualOff(actualOff);
                } else {
                    mnjxPlanSection.setActualOff(mnjxPlanSection.getEstimateOff());
                }
                // 实际离港跨天
                mnjxPlanSection.setActualOffChange(mnjxPlanSection.getActualOffChange());
                // 登机状态
                mnjxPlanSection.setBoardStatus(Constant.BOARD_STATUS_BCL);
                // 获取CND数据
                MnjxCnd mnjxCnd = iMnjxCndService.getById(mnjxTcard.getCndId());
                // 登机限额
                mnjxPlanSection.setHlLimit(mnjxCnd.getLayout());
                // 进港机位(出发机场的登机口，随机获取)
                MnjxGate mnjxGateArr = this.randomGetGate(mnjxTcardSectionArr);
                mnjxPlanSection.setArrPosition(ObjectUtils.isNotNull(mnjxGateArr) ? mnjxGateArr.getGateNo() : StrUtils.EMPTY);
                // 离港机位(出发机场的登机口，随机获取)
                MnjxGate mnjxGateOff = this.randomGetGate(mnjxTcardSectionDep);
                mnjxPlanSection.setOffPosition(ObjectUtils.isNotNull(mnjxGateOff) ? mnjxGateOff.getGateNo() : StrUtils.EMPTY);
                // 航空公司飞机id
                mnjxPlanSection.setPlaneId(mnjxPlane.getPlaneId());
                // 修改前的飞机ID
                mnjxPlanSection.setPrePlaneId(mnjxPlane.getPlaneId());
                // 修改前的预计离港时间
                mnjxPlanSection.setPreEstimateOff(mnjxTcardSectionDep.getDepTime());
                // 修改前的预计到港时间
                mnjxPlanSection.setPreEstimateArr(mnjxTcardSectionArr.getArrTime());
                // 各等级的候补旅客人数，形如SB000/000/000
                String gradeStandbyNumber = this.getGradeStandbyNumber(mnjxCnd);
                mnjxPlanSection.setGradeStandbyNumber(gradeStandbyNumber);
                // 各等级订座人数，形如F018/C021/Y279
                String gradeBookNumber = this.getGradeBookNumber(mnjxCnd);
                mnjxPlanSection.setGradeBookNumber(gradeBookNumber);
                // 各等级已办理值机的旅客人数:F06/C020/Y254
                String gradeCkNumber = this.getGradeCkNumber(mnjxCnd);
                mnjxPlanSection.setGradeCkNumber(gradeCkNumber);
                // 是否自动分配登机口
                if (ObjectUtil.isNotEmpty(configGate) && Constant.STR_ONE.equals(configGate.getAvailable())) {
                    String gateNum;
                    // 分配登机口
                    if (ObjectUtil.isNotEmpty(flightGate)) {
                        // 特定的mnjx_flight_gate表设置的登机口(1-7)
                        gateNum = flightGate.getGateNo();
                    } else {
                        // 其余在8-100以内随机分配
                        gateNum = String.valueOf(rd.nextInt(93) + 8);
                    }
                    mnjxPlanSection.setGate(gateNum);
                }
                // 设置默认最大值机可利用业载 200000
                mnjxPlanSection.setUaw(200000);
                // 设置默认最大航班可利用业载 200000
                mnjxPlanSection.setUwt(200000);
                // 往保存集合中添加数据
                mnjxPlanSections.add(mnjxPlanSection);
            }
        }
        return mnjxPlanSections;
    }

    /**
     * 创建开舱数据
     *
     * @param mnjxPlanSections 计划航节
     * @param mnjxFlight       航班数据
     * @return 创建开舱数据
     */
    private List<MnjxOpenCabin> createOpenCabins(List<MnjxPlanSection> mnjxPlanSections, MnjxFlight mnjxFlight) throws UnifiedResultException {
        // 获取TCard数据
        MnjxTcard mnjxTcard = this.getMnjxTcard(mnjxFlight);
        // 获得cnd数据
        MnjxCnd mnjxCnd = iMnjxCndService.getById(mnjxTcard.getCndId());
        // 每个航节的舱位数据
        List<List<MnjxOpenCabin>> openCabins = new ArrayList<>();
        List<BigDecimal> discountList = new ArrayList<>();
        for (MnjxPlanSection mnjxPlanSection : mnjxPlanSections) {
            // 基准运价
            BigDecimal standardPat = this.getStandardPat(mnjxPlanSection);
            // 第一舱等
            if (StrUtils.isAllNotEmpty(mnjxCnd.getFirstCabinClass(), mnjxCnd.getFirstSellCabin())) {
                BigDecimal discount = mnjxCnd.getFirstDiscount();
                List<MnjxOpenCabin> mnjxOpenCabinsFirst = this.getOpenCabin(mnjxCnd.getFirstCabinClass(), mnjxCnd.getFirstSellCabin(), mnjxCnd.getFirstSeats(), mnjxPlanSection, standardPat);
                openCabins.add(mnjxOpenCabinsFirst);
                discountList.add(discount);
            }
            // 第二舱等
            if (StrUtils.isAllNotEmpty(mnjxCnd.getSecondCabinClass(), mnjxCnd.getSecondSellCabin())) {
                BigDecimal discount = mnjxCnd.getSecondDiscount();
                List<MnjxOpenCabin> mnjxOpenCabinsSecond = this.getOpenCabin(mnjxCnd.getSecondCabinClass(), mnjxCnd.getSecondSellCabin(), mnjxCnd.getSecondSeats(), mnjxPlanSection, standardPat);
                openCabins.add(mnjxOpenCabinsSecond);
                discountList.add(discount);
            }
            // 第三舱等
            if (StrUtils.isAllNotEmpty(mnjxCnd.getThirdCabinClass(), mnjxCnd.getThirdSellCabin())) {
                BigDecimal discount = mnjxCnd.getThirdDiscount();
                List<MnjxOpenCabin> mnjxOpenCabinsThird = this.getOpenCabin(mnjxCnd.getThirdCabinClass(), mnjxCnd.getThirdSellCabin(), mnjxCnd.getThirdSeats(), mnjxPlanSection, standardPat);
                openCabins.add(mnjxOpenCabinsThird);
                discountList.add(discount);
            }
            // 第四舱等
            if (StrUtils.isAllNotEmpty(mnjxCnd.getFourthCabinClass(), mnjxCnd.getFourthSellCabin())) {
                BigDecimal discount = mnjxCnd.getFourthDiscount();
                List<MnjxOpenCabin> mnjxOpenCabinsFourth = this.getOpenCabin(mnjxCnd.getFourthCabinClass(), mnjxCnd.getFourthSellCabin(), mnjxCnd.getFourthSeats(), mnjxPlanSection, standardPat);
                openCabins.add(mnjxOpenCabinsFourth);
                discountList.add(discount);
            }
            // 第五舱等
            if (StrUtils.isAllNotEmpty(mnjxCnd.getFifthCabinClass(), mnjxCnd.getFifthSellCabin())) {
                BigDecimal discount = mnjxCnd.getFifthDiscount();
                List<MnjxOpenCabin> mnjxOpenCabinsFifth = this.getOpenCabin(mnjxCnd.getFifthCabinClass(), mnjxCnd.getFifthSellCabin(), mnjxCnd.getFifthSeats(), mnjxPlanSection, standardPat);
                openCabins.add(mnjxOpenCabinsFifth);
                discountList.add(discount);
            }
        }
        // 计算具体销售舱位折扣后的价格
        this.calculateSellCabinPrice(openCabins, discountList);
        return openCabins.stream().flatMap(item -> item.stream().filter(ObjectUtils::isNotNull)).collect(Collectors.toList());
    }

    /**
     * Title: calculateSellCabinPrice
     * Description: 每个销售舱位价格从当前舱等价格开始折扣1%-5%。X
     * 销售舱位过多时1%-5%容易导致重算，需求变更为按销售舱位数平均递减价格
     *
     * @param openCabins 所有开舱数据
     * @param discounts  所有折扣数据
     * <AUTHOR>
     */
    private void calculateSellCabinPrice(List<List<MnjxOpenCabin>> openCabins, List<BigDecimal> discounts) {
        for (int i = 0; i < openCabins.size(); i++) {
            BigDecimal discount = discounts.get(i);
            List<MnjxOpenCabin> openCabinList = openCabins.get(i);
            BigDecimal diffDiscount;
            // 非最后个舱等的数据，计算需要判断当前最低折扣是否低于了下一个舱等的折扣
            if (i < openCabins.size() - 1) {
                BigDecimal nextDiscount = discounts.get(i + 1);
                // 下个舱等折扣比当前高，说明是下一个航段计划的开舱数据了，那当前是这个开舱的最后一个舱等
                if (nextDiscount.compareTo(discount) > 0) {
                    // 最后一个舱等的销售舱位折扣价最多折扣到一半
                    diffDiscount = discount.divide(new BigDecimal(2), RoundingMode.CEILING);
                } else {
                    diffDiscount = discount.subtract(nextDiscount);
                }
            } else {
                // 最后一个舱等的销售舱位折扣价最多折扣到一半
                diffDiscount = discount.divide(new BigDecimal(2), RoundingMode.CEILING);
            }
            BigDecimal averageDiff = diffDiscount.divide(new BigDecimal(openCabinList.size()), RoundingMode.FLOOR);
            // 折扣价计算
            for (int j = 0; j < openCabinList.size(); j++) {
                MnjxOpenCabin mnjxOpenCabin = openCabinList.get(j);
                // 第一个销售舱位是舱等，不进行折扣
                if (j > 0) {
                    discount = discount.subtract(averageDiff);
                }
                BigDecimal price = NumberUtils.numRound(NumberUtils.mul(mnjxOpenCabin.getSellCabinPrice(), discount));
                mnjxOpenCabin.setSellCabinPrice(price.intValue());
            }
        }
    }

    /**
     * 座位列表数据
     *
     * @param mnjxOpenCabins 所有舱等
     * @param mnjxFlight     航班数据
     * @return 座位列表数据
     */
    private List<MnjxSeat> createSeats(List<MnjxOpenCabin> mnjxOpenCabins, MnjxFlight mnjxFlight) {
        // 获取TCard数据
        MnjxTcard mnjxTcard = getMnjxTcard(mnjxFlight);
        // 获得cnd数据
        MnjxCnd mnjxCnd = iMnjxCndService.getById(mnjxTcard.getCndId());
        // 获得座位模型数据
        List<MnjxSeatModel> mnjxSeatModelsDb = iMnjxSeatModelService.lambdaQuery().eq(MnjxSeatModel::getCndId, mnjxCnd.getCndId()).isNotNull(MnjxSeatModel::getSeatNo).list();
        Map<String, List<MnjxOpenCabin>> openCabinGroup = mnjxOpenCabins.stream().collect(Collectors.groupingBy(MnjxOpenCabin::getPlanSectionId));
        List<MnjxSeat> seatList = new ArrayList<>();
        for (String cabinKey : openCabinGroup.keySet()) {
            List<MnjxOpenCabin> openCabins = openCabinGroup.get(cabinKey);
            List<MnjxSeatModel> mnjxSeatModels = new ArrayList<>();
            Collections.addAll(mnjxSeatModels, new MnjxSeatModel[mnjxSeatModelsDb.size()]);
            Collections.copy(mnjxSeatModels, mnjxSeatModelsDb);
            for (MnjxOpenCabin mnjxOpenCabin : openCabins) {
                int total = mnjxOpenCabin.getSeatTotal();
                String cabinClass = mnjxOpenCabin.getCabinClass();
                List<MnjxSeatModel> cabinClassSeatModelList = mnjxSeatModels.stream().filter(s -> s.getCabinClass().equals(cabinClass)).collect(Collectors.toList());
                List<MnjxSeatModel> removeList = new ArrayList<>();
                if (CollUtil.isNotEmpty(cabinClassSeatModelList)) {
                    for (int i = 0; i < total; i++) {
                        MnjxSeat mnjxSeat = new MnjxSeat();
                        MnjxSeatModel seatModel = cabinClassSeatModelList.get(i);
                        BeanUtil.copyProperties(seatModel, mnjxSeat);
                        mnjxSeat.setSeatId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        mnjxSeat.setOpenCabinId(mnjxOpenCabin.getOpenCabinId());
                        mnjxSeat.setPlanSectionId(mnjxOpenCabin.getPlanSectionId());
                        if (StrUtil.equalsAny(mnjxOpenCabin.getOpenCabinStatus(), Constant.C, Constant.X)) {
                            mnjxSeat.setSeatStatus(Constant.X);
                        }
                        seatList.add(mnjxSeat);
                        removeList.add(seatModel);
                        if (!StrUtil.equalsAny(mnjxSeat.getSeatStatus(), "*", "B", "I", "U", "/", "C")) {
                            mnjxOpenCabin.setSeatAvailable(mnjxOpenCabin.getSeatAvailable() - 1);
                        }
                    }
                }
                mnjxSeatModels.removeAll(removeList);
            }
        }
        return seatList;
    }


    /**
     * 构建航班对象
     *
     * @param mnjxFlight mnjxFlight
     */
    private MnjxPlanFlight createPlanFlight(MnjxFlight mnjxFlight, String planFlightDate, String isFlightInitial) {
        // 获取TCard数据
        MnjxTcard mnjxTcard = getMnjxTcard(mnjxFlight);
        MnjxPlanFlight mnjxPlanFlight = new MnjxPlanFlight();
        // id
        mnjxPlanFlight.setPlanFlightId(IdUtils.getId());
        // tcard_id
        mnjxPlanFlight.setTcardId(mnjxTcard.getTcardId());
        // airline_code
        mnjxPlanFlight.setAirlineCode(StrUtils.subPre(mnjxFlight.getFlightNo(), 2));
        // 获取cnd数据
        MnjxCnd mnjxCnd = iMnjxCndService.getById(mnjxTcard.getCndId());
        mnjxPlanFlight.setCndNo(mnjxCnd.getCndNo());

        // 航班执飞类型 D每天
        mnjxPlanFlight.setFlightType(mnjxTcard.getTType());
        // 航班日期
        mnjxPlanFlight.setFlightDate(planFlightDate);

        mnjxPlanFlight.setIsA(mnjxFlight.getIsA());
        mnjxPlanFlight.setIsX(mnjxFlight.getIsX());
        mnjxPlanFlight.setIsW(mnjxFlight.getIsW());
        mnjxPlanFlight.setIsE(mnjxFlight.getIsE());
        mnjxPlanFlight.setAllowAsr(mnjxFlight.getAllowAsr());
        String flightNo = mnjxFlight.getFlightNo();
        MnjxFlightGate cancelFlight = iMnjxFlightGateService.lambdaQuery()
                .eq(MnjxFlightGate::getFlightNo, flightNo)
                .eq(MnjxFlightGate::getDelayTime, 1000)
                .one();
        // 航班状态
        mnjxPlanFlight.setFlightStatus(Constant.FLIGHT_STATUS_ACTIVE);
        if (ObjectUtil.isNotEmpty(cancelFlight)) {
            // 值机状态 取消
            mnjxPlanFlight.setCkStatus(Constant.CK_STATUS_CI);
        } else {
            // 值机状态 正常
            mnjxPlanFlight.setCkStatus(Constant.CK_STATUS_OP);
        }
        // 前值机状态
        mnjxPlanFlight.setPreCkStatus(Constant.CK_STATUS_OP);
        // 初始化航班
        mnjxPlanFlight.setIsFlightInitial(isFlightInitial);
        return mnjxPlanFlight;
    }

    /**
     * 随机获取运输的飞机
     *
     * @param mnjxFlight 航班数据
     * @return 随机获取运输的飞机
     */
    private MnjxPlane randomGetPlane(MnjxFlight mnjxFlight) {
        // 获取t1-card信息
        MnjxTcard mnjxTcard = getMnjxTcard(mnjxFlight);
        // 飞机列表
        List<MnjxPlane> mnjxPlanes = iMnjxPlaneService.lambdaQuery().eq(MnjxPlane::getAirlineId, mnjxFlight.getAirlineId()).eq(MnjxPlane::getCndId, mnjxTcard.getCndId()).list();
        // 随机获取飞机列表
        return mnjxPlanes.stream().findAny().orElse(null);
    }

    /**
     * 随机获取出港、进港的登机口
     *
     * @param mnjxTcardSection 根据
     * @return 随机获取出港、进港的登机口
     */
    private MnjxGate randomGetGate(MnjxTcardSection mnjxTcardSection) {
        // 获得此机场的所有登机口
        List<MnjxGate> mnjxGates = iMnjxGateService.lambdaQuery().eq(MnjxGate::getAirportId, mnjxTcardSection.getAirportId()).list();
        // 随机获得登机口
        return mnjxGates.stream().findAny().orElse(null);
    }

    /**
     * 各等级的候补旅客人数，形如SB000/000/000
     *
     * @param mnjxCnd 航班布局
     * @return 各等级的候补旅客人数，形如SB000/000/000
     */
    private String getGradeStandbyNumber(MnjxCnd mnjxCnd) {
        List<String> layouts = StrUtils.splitTrim(mnjxCnd.getLayout(), StrUtils.SLASH);
        StringBuilder gradeStandbyNumber = new StringBuilder("SB");
        for (int i = 0, j = layouts.size(); i < j; i++) {
            gradeStandbyNumber.append(StrUtils.fillAfter(StrUtils.EMPTY, Constant.C_ZERO, Constant.THREE));
            if (i != j - 1) {
                gradeStandbyNumber.append(StrUtils.SLASH);
            }
        }
        return gradeStandbyNumber.toString();
    }

    /**
     * 各等级订座人数，形如F018/C021/Y279
     *
     * @param mnjxCnd 航班布局
     * @return 各等级订座人数，形如F018/C021/Y279
     */
    private String getGradeBookNumber(MnjxCnd mnjxCnd) {
        StringBuilder gradeBookNumber = new StringBuilder();
        List<String> layouts = StrUtils.splitTrim(mnjxCnd.getLayout(), StrUtils.SLASH);
        for (int i = 0, j = layouts.size(); i < j; i++) {
            gradeBookNumber.append(StrUtils.fillAfter(StrUtils.subPre(layouts.get(i), 1), Constant.C_ZERO, Constant.THREE));
            if (i != j - 1) {
                gradeBookNumber.append(StrUtils.SLASH);
            }
        }
        return gradeBookNumber.toString();
    }

    /**
     * 各等级已办理值机的旅客人数:F06/C020/Y254
     *
     * @param mnjxCnd 航班布局
     * @return 各等级已办理值机的旅客人数:F06/C020/Y254
     */
    private String getGradeCkNumber(MnjxCnd mnjxCnd) {
        StringBuilder gradeCkNumber = new StringBuilder();
        List<String> layouts = StrUtils.splitTrim(mnjxCnd.getLayout(), StrUtils.SLASH);
        for (int i = 0, j = layouts.size(); i < j; i++) {
            gradeCkNumber.append(StrUtils.fillAfter(StrUtils.subPre(layouts.get(i), 1), Constant.C_ZERO, Constant.THREE));
            if (i != j - 1) {
                gradeCkNumber.append(StrUtils.SLASH);
            }
        }
        return gradeCkNumber.toString();
    }

    /**
     * 构建每个舱等数据
     *
     * @param cabinClass      舱等
     * @param sellCabin       销售舱位的字符串
     * @param seats           座位数
     * @param mnjxPlanSection 计划航节
     * @return 构建每个舱等数据
     * @throws UnifiedResultException 异常
     */
    private List<MnjxOpenCabin> getOpenCabin(String cabinClass, String sellCabin, int seats, MnjxPlanSection mnjxPlanSection, BigDecimal standardPat) throws UnifiedResultException {
        // 具体航空公司
        MnjxPlanFlight mnjxPlanFlight = iMnjxPlanFlightService.getById(mnjxPlanSection.getPlanFlightId());
        MnjxTcard mnjxTcard = iMnjxTcardService.getById(mnjxPlanFlight.getTcardId());
        MnjxFlight mnjxFlight = iMnjxFlightService.getById(mnjxTcard.getFlightId());
        String flightNo = mnjxFlight.getFlightNo();
        MnjxAirline mnjxAirline = iMnjxAirlineService.getById(mnjxFlight.getAirlineId());
        MnjxConfig cabinStatusConfigC = iMnjxConfigService.lambdaQuery()
                .eq(MnjxConfig::getType, Constant.CABIN_STATUS_C_CONFIG)
                .one();
        MnjxConfig cabinStatusConfigX = iMnjxConfigService.lambdaQuery()
                .eq(MnjxConfig::getType, Constant.CABIN_STATUS_X_CONFIG)
                .one();
        List<MnjxOpenCabin> mnjxOpenCabins = new ArrayList<>();
        // 第一个舱等下的所有舱位
        if (StrUtils.isNotBlank(cabinClass)) {
            // 当前舱等下的所有舱位
            List<String> cabins = CollUtils.toList(StrUtils.cut(sellCabin, NumberUtils.parseInt(Constant.STR_ONE)));
            // 所有舱位分配座位数
            Map<String, Integer> seatRandom = this.assignSeats(cabins, seats);
            // 所有舱位分配的销售舱位状态
            MnjxConfig lqsConfig = iMnjxConfigService.lambdaQuery()
                    .eq(MnjxConfig::getType, "CABIN_STATUS_LQ")
                    .one();
            Map<String, String> sellCabinStatusRandomMap = new HashMap<>(1024);
            if (ObjectUtil.isNotEmpty(lqsConfig) && lqsConfig.getAvailable().equals(Constant.STR_ONE)) {
                sellCabinStatusRandomMap = this.assignSellCabinStatus(cabins);
            }
            for (String cabin : cabins) {
                // 销售舱位
                MnjxOpenCabin mnjxOpenCabin = new MnjxOpenCabin();
                // id
                mnjxOpenCabin.setOpenCabinId(IdUtils.getId());
                // 航节ID
                mnjxOpenCabin.setPlanSectionId(mnjxPlanSection.getPlanSectionId());
                // 票价登机
                mnjxOpenCabin.setTicketLevel(StrUtils.format("{}{}", cabinClass, cabin));
                // 舱等
                mnjxOpenCabin.setCabinClass(cabinClass);
                // 销售舱位
                mnjxOpenCabin.setSellCabin(cabin);
                // 暂时处理销售运价 = 基准运价*航空公司折扣，具体销售运价折扣在后面处理
                BigDecimal sellCabinPrice = NumberUtils.mul(standardPat, mnjxAirline.getAirlineCodeDiscount());
                mnjxOpenCabin.setSellCabinPrice(sellCabinPrice.intValue());
                // 舱位状态
                if (ObjectUtil.isNotEmpty(lqsConfig) && lqsConfig.getAvailable().equals(Constant.STR_ONE)) {
                    mnjxOpenCabin.setOpenCabinStatus(sellCabinStatusRandomMap.get(cabin));
                } else {
                    mnjxOpenCabin.setOpenCabinStatus(Constant.CABIN_STATUS_OPEN);
                }
                // 特定航班设置J舱等所有销售舱位状态为C
                if (Constant.STR_ONE.equals(cabinStatusConfigC.getAvailable()) && "J".equals(cabinClass) && Arrays.asList(Constant.X_C_FLIGHT_NO).contains(flightNo)) {
                    mnjxOpenCabin.setOpenCabinStatus(Constant.C);
                }
                // 特定航班设置G舱等所有销售舱位状态为X
                if (Constant.STR_ONE.equals(cabinStatusConfigX.getAvailable()) && "G".equals(cabinClass) && Arrays.asList(Constant.X_C_FLIGHT_NO).contains(flightNo)) {
                    mnjxOpenCabin.setOpenCabinStatus(Constant.X);
                }
                // 当前销售舱位总的座位数
                Integer seatTotal = seatRandom.get(cabin);
                mnjxOpenCabin.setSeatTotal(seatTotal);
                // 可利用的座位数
                mnjxOpenCabin.setSeatAvailable(seatTotal);
                mnjxOpenCabins.add(mnjxOpenCabin);
            }
        }
        return mnjxOpenCabins;
    }

    /**
     * 为所有销售舱位分配座位数
     *
     * @param cabins 所有的销售舱位
     * @param seats  这个舱等下能分配的座位数
     * @return 为所有销售舱位分配座位数
     */
    private Map<String, Integer> assignSeats(List<String> cabins, Integer seats) throws UnifiedResultException {
        int size = cabins.size();
        Map<String, Integer> seatRandom = new HashMap<>(size);
        MnjxConfig resetCabinYSeatNum = iMnjxConfigService.lambdaQuery()
                .eq(MnjxConfig::getType, "RESET_CABIN_Y_SEAT_NUM")
                .one();
        for (int i = 0, j = size; i < size; i++) {
            // 舱位
            String cabin = cabins.get(i);
            if (i != size - 1) {
                int seatNum;
                if (seats == 0) {
                    seatNum = 0;
                } else {
                    int top = Math.floorDiv(seats, j);
                    int end = Math.floorDiv(seats, j * 2);
                    seatNum = RandomUtils.randomInt(end, top);
                    if (Constant.Y.equals(cabin) && ObjectUtil.isNotEmpty(resetCabinYSeatNum) && !Constant.STR_ZERO.equals(resetCabinYSeatNum.getAvailable())) {
                        int configNum = Integer.parseInt(resetCabinYSeatNum.getAvailable());
                        if (configNum > seats) {
                            log.error("启用设定的Y销售舱位座位数量超过该舱等最大座位数！按默认方式设置！");
                        } else {
                            seatNum = configNum;
                        }
                    }
                }
                seatRandom.put(cabin, seatNum);
                seats -= seatNum;
                j--;
            } else {
                seatRandom.put(cabin, seats);
            }
        }
        return seatRandom;
    }

    /**
     * Title: assignSellCabinStatus
     * Description: 销售舱位装态随机获取
     *
     * @param cabins cabins
     * @return 销售舱位装态随机获取
     * <AUTHOR>
     * @date 2022/11/18 16:21
     */
    private Map<String, String> assignSellCabinStatus(List<String> cabins) {
        int size = cabins.size();
        Map<String, String> sellCabinStatusRandomMap = new HashMap<>(size);
        ArrayList<String> sellCabinStatusList = CollUtil.toList(Constant.SELL_CABIN_STATUS);
        Iterator<String> iterator = sellCabinStatusList.iterator();
        for (String cabin : cabins) {
            int randomInt = RandomUtils.randomInt(0, 5);
            if (randomInt > 2) {
                String sellCabinStatus = Constant.CABIN_STATUS_OPEN;
                if (CollUtil.isEmpty(sellCabinStatusList)) {
                    int randomSellCabinInt = RandomUtils.randomInt(0, Constant.SELL_CABIN_STATUS.length);
                    sellCabinStatus = Constant.SELL_CABIN_STATUS[randomSellCabinInt];
                }
                while (iterator.hasNext()) {
                    sellCabinStatus = iterator.next();
                    iterator.remove();
                    break;
                }
                sellCabinStatusRandomMap.put(cabin, sellCabinStatus);
            } else {
                sellCabinStatusRandomMap.put(cabin, Constant.CABIN_STATUS_OPEN);
            }
        }
        return sellCabinStatusRandomMap;
    }

    /**
     * 获得基准运价
     *
     * @param mnjxPlanSection 当前航节
     * @return 基准运价
     * @throws UnifiedResultException 异常
     */
    private BigDecimal getStandardPat(MnjxPlanSection mnjxPlanSection) throws UnifiedResultException {
        // 出发机场的ID
        String orgAptId = mnjxPlanSection.getDepAptId();
        // 出发机场所在的城市id
        MnjxCity orgCity = this.getMnjxCity(orgAptId);
        log.info("获取到的出发城市ID：{}", orgCity.getCityId());
        // 到达机场的ID
        String dstAptId = mnjxPlanSection.getArrAptId();
        // 到达机场所在的城市id
        MnjxCity dstCity = this.getMnjxCity(dstAptId);
        log.info("获取到的到达城市ID：{}", dstCity.getCityId());
        // 基准运价列表
        List<MnjxStandardPat> mnjxStandardPats = iMnjxStandardPatService.list();
        // 城市对基准运价
        List<MnjxStandardPat> filter = mnjxStandardPats.stream()
                .filter(mnjxStandardPat -> (mnjxStandardPat.getOrgCityId().equals(orgCity.getCityId()) && mnjxStandardPat.getDstCityId().equals(dstCity.getCityId())) || (mnjxStandardPat.getOrgCityId().equals(dstCity.getCityId()) && mnjxStandardPat.getDstCityId().equals(orgCity.getCityId())))
                .collect(Collectors.toList());
        log.info("筛选了{}条运价", filter.size());
        if (CollUtil.isEmpty(filter)) {
            throw new UnifiedResultException(StrUtils.format("未设置当前城市对{}({})={}({})的基础运价", orgCity.getCityCname(), orgCity.getCityCode(), dstCity.getCityCname(), dstCity.getCityCode()));
        }
        return filter.get(0).getNormalPat();
//        return mnjxStandardPats.stream()
//                .filter(mnjxStandardPat -> (mnjxStandardPat.getOrgCityId().equals(orgCity.getCityId()) && mnjxStandardPat.getDstCityId().equals(dstCity.getCityId())) || (mnjxStandardPat.getOrgCityId().equals(dstCity.getCityId()) && mnjxStandardPat.getDstCityId().equals(orgCity.getCityId())))
//                .map(MnjxStandardPat::getNormalPat)
//                .findFirst()
//                .orElseThrow(() -> new UnifiedResultException(StrUtils.format("未设置当前城市对{}({})={}({})的基础运价", orgCity.getCityCname(), orgCity.getCityCode(), dstCity.getCityCname(), dstCity.getCityCode())));
    }

    /**
     * 通过ID找到机场数据
     *
     * @param airportId 机场id
     * @return 某个机场所官员的城市数据
     * @throws UnifiedResultException 异常
     */
    private MnjxCity getMnjxCity(String airportId) throws UnifiedResultException {
        // 机场
        MnjxAirport mnjxAirport = iMnjxAirportService.getById(airportId);
        if (ObjectUtil.isEmpty(mnjxAirport)) {
            throw new UnifiedResultException(StrUtils.format("id为{}的机场不存在", airportId));
        }
        // 城市ID
        String cityId = mnjxAirport.getCityId();
        // 城市ID为空，说明当前机场没有关联城市
        if (StrUtils.isEmpty(cityId)) {
            throw new UnifiedResultException(StrUtils.format("当前机场{}({})未关联到城市", mnjxAirport.getAirportCname(), mnjxAirport.getAirportCode()));
        }
        // 城市列表
        MnjxCity mnjxCity = iMnjxCityService.getById(cityId);
        if (ObjectUtil.isEmpty(mnjxCity)) {
            throw new UnifiedResultException(StrUtils.format("id为{}的城市不存在", cityId));
        }
        return mnjxCity;
    }

    private MnjxTcard getMnjxTcard(MnjxFlight mnjxFlight) {
        return iMnjxTcardService.lambdaQuery()
                .eq(MnjxTcard::getFlightId, mnjxFlight.getFlightId())
                .one();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void doDeleteExistPlanFlight(List<String> seatsIds, List<String> openCabinsIds, List<String> planSectionsIds, List<String> planFlightsIds) {
        log.info("开始删除SEAT");
        List<List<String>> partition = Lists.partition(seatsIds, 1000);
        partition.parallelStream().forEach(p -> mnjxSeatMapper.deleteBatchIds(p));
        log.info("结束删除SEAT");
        log.info("开始删除open cabin");
        if (iMnjxOpenCabinService.removeByIds(openCabinsIds)) {
            log.info("开始删除plan section");
            if (iMnjxPlanSectionService.removeByIds(planSectionsIds)) {
                log.info("开始删除plan flight");
                iMnjxPlanFlightService.removeByIds(planFlightsIds);
            }
        }
    }

    @Override
    public List<String> getExistPassengerDateList(String flightNo) {
        List<MnjxPnrSeg> pnrSegs = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getFlightNo, flightNo)
                .list();
        List<String> existPassengerDateList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrSegs)) {
            existPassengerDateList = pnrSegs.stream()
                    .map(MnjxPnrSeg::getFlightDate)
                    .collect(Collectors.toList());
        }
        return existPassengerDateList;
    }
}
