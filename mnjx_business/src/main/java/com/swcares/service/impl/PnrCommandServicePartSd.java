package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.AvVo;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.eterm.crs.service.impl.SdServiceImpl <br>
 * description：SdServiceImpl <br>
 *
 * <AUTHOR> <br>
 * @version v1.0 <br>
 * @date 2022/01/13 <br>
 */
@Slf4j
@Service
public class PnrCommandServicePartSd {

    @Resource
    private IPnrOperationService iPnrOperationService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxCndService iMnjxCndService;

    @Resource
    private IMnjxPlaneModelService iMnjxPlaneModelService;

    @Resource
    private IPnrCommandService iPnrCommandService;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxFrequenterService iMnjxFrequenterService;

    /**
     * 航班选择
     *
     * @param memoryDataPnr PNR内存大对象
     * @param sdDto         指令参数
     * @param mnjxOffice    部门
     * @param mnjxSi        工作号
     * @throws UnifiedResultException 统一异常
     */
    public void sd(MemoryDataPnr memoryDataPnr, SdDto sdDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        // 有效航段数判断
        if (CollUtil.isNotEmpty(memoryDataPnr.getPnrSegDtos())) {
            long validSegNo = memoryDataPnr.getPnrSegDtos().stream()
                    .filter(pnrSegDto -> !pnrSegDto.isXe())
                    .count();
            if (validSegNo > Constant.THREE) {
                throw new UnifiedResultException(Constant.SEG_SIZE_ERROR);
            }
        }

        List<AvVo> avVoList = iPnrCommandService.getAvResultCache(memoryDataPnr);
        List<Integer> avVosIndex = iPnrCommandService.getAvCurrentPageCache(memoryDataPnr);
        List<AvVo> avVos1 = new ArrayList<>();
        avVosIndex.stream().sorted().forEach(index -> avVos1.add(avVoList.get(index - 1)));
        List<AvVo> avVos = ObjectUtil.isNotEmpty(avVos1) ? avVos1 : avVoList;
        if (CollUtil.isEmpty(avVos)) {
            throw new UnifiedResultException(Constant.NO_DISPLAY_SYMBOL);
        }
        // 如果输入的序号不在AV查询结果范围之内，报错：**SCH NBR**
        if (sdDto.getIndex() <= 0 || sdDto.getIndex() > avVos.size()) {
            throw new UnifiedResultException(Constant.SCH_NBR);
        }
        AvVo avVo = avVos.get(sdDto.getIndex() - 1);
        //判断是不是共享航班
        if (StrUtil.isNotBlank(avVo.getCarrierFlight())) {
            //判断共享航班是否被禁用
            MnjxFlight carrierFlight = iMnjxFlightService.lambdaQuery()
                    .eq(MnjxFlight::getFlightNo, avVo.getCarrierFlight())
                    .one();
            if (ObjectUtil.isNotNull(carrierFlight)) {
                MnjxFlight shareFlight = iMnjxFlightService.lambdaQuery()
                        .eq(MnjxFlight::getFlightNo, avVo.getFlightNo())
                        .one();
                if (Constant.STR_ZERO.equals(shareFlight.getShareState())) {
                    throw new UnifiedResultException(Constant.UNABLE_TO_SELL);
                }
            } else {
                throw new UnifiedResultException("未找到共享航班");
            }
        }
        // 如果输入的舱位不在AV查询结果相应行中存在
        List<String> sellCabinAndSeat = avVo.getSellCabinAndSeat();
        if (CollUtil.isEmpty(sellCabinAndSeat)) {
            String errMsg = this.constErrMsg(avVo, sdDto);
            throw new UnifiedResultException(errMsg);
        }
        List<String> sellCabins = sellCabinAndSeat.stream()
                .map(it -> it.substring(0, 1))
                .collect(Collectors.toList());
        boolean error = true;
        for (String avCabin : sellCabins) {
            if (avCabin.equalsIgnoreCase(sdDto.getCabin())) {
                error = false;
                break;
            }
        }
        if (error) {
            String errMsg = this.constErrMsg(avVo, sdDto);
            throw new UnifiedResultException(errMsg);
        }
        String statusAndSeats = sellCabinAndSeat.get(sellCabins.indexOf(sdDto.getCabin())).substring(1);
        if (StrUtil.equalsAnyIgnoreCase(statusAndSeats.substring(0, 1), Constant.SEAT_STATUS_C, Constant.SEAT_STATUS_X)) {
            String errMsg = this.constErrMsg(avVo, sdDto);
            throw new UnifiedResultException(errMsg);
        } else {
            if (!Constant.SEAT_STATUS_A.equalsIgnoreCase(statusAndSeats.substring(0, 1))) {
                if (!Constant.SEAT_STATUS_A.equalsIgnoreCase(statusAndSeats.substring(1))) {
                    if (Constant.STR_ZERO.equals(statusAndSeats.substring(1))) {
                        String errMsg = this.constErrMsg(avVo, sdDto);
                        throw new UnifiedResultException(errMsg);
                    }
                    String seatNum = statusAndSeats.substring(1);
                    if (Constant.SEAT_S.equals(seatNum)) {
                        seatNum = Constant.STR_ZERO;
                    }
                    int avSeat = Integer.parseInt(seatNum);
                    if (avSeat < sdDto.getSeats()) {
                        String errMsg = this.constErrMsg(avVo, sdDto);
                        throw new UnifiedResultException(errMsg);
                    }
                }
            }
        }
        this.checkData(memoryDataPnr, sdDto);
        this.buildData(sdDto, avVo, memoryDataPnr, mnjxOffice, mnjxSi);
    }

    private void checkData(MemoryDataPnr memoryDataPnr, SdDto sdDto) throws UnifiedResultException {
        // 如果输入的行动代码不是NN，报错：ACTION CODE
        if (!Constant.ACTION_CODE_NN.equalsIgnoreCase(sdDto.getActionCode())) {
            throw new UnifiedResultException(Constant.ACTION_CODE);
        }
        // 与GN中的团队人员数进行比较
        if (CollUtil.isNotEmpty(memoryDataPnr.getPnrGnDtos())) {
            List<PnrGnDto> pnrGnDtos = memoryDataPnr.getPnrGnDtos();
            PnrGnDto pnrGnDto = pnrGnDtos.stream()
                    .filter(g -> !g.isXe())
                    .collect(Collectors.toList())
                    .get(0);
            if (!sdDto.getSeats().equals(pnrGnDto.getMnjxPnrGn().getGroupNumber())) {
                throw new UnifiedResultException(Constant.SEATS);
            }
        } else {
            // 判断nm人数
            List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
            if (CollUtil.isNotEmpty(pnrNmDtos)) {
                long count = pnrNmDtos.stream()
                        .filter(p -> StrUtil.isEmpty(p.getMnjxPnrNm().getChangeType()))
                        .count();
                if (count != sdDto.getSeats()) {
                    throw new UnifiedResultException(Constant.SEATS);
                }
            }
        }
        // 判断ss人数
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        if (CollUtil.isNotEmpty(pnrSegDtos)) {
            List<MnjxPnrSeg> mnjxPnrSegs = pnrSegDtos.stream()
                    .filter(it -> !it.isXe())
                    .map(PnrSegDto::getMnjxPnrSeg)
                    .collect(Collectors.toList());
            for (MnjxPnrSeg mnjxPnrSeg : mnjxPnrSegs) {
                if ("SS".equalsIgnoreCase(mnjxPnrSeg.getPnrSegType()) || "SD".equalsIgnoreCase(mnjxPnrSeg.getPnrSegType())) {
                    if (!Objects.equals(mnjxPnrSeg.getSeatNumber(), sdDto.getSeats())) {
                        throw new UnifiedResultException(Constant.SEATS);
                    }
                }
            }
        }
        if (StrUtil.isBlank(sdDto.getActionCode())) {
            sdDto.setActionCode("NN");
        }
    }

    /**
     * 拼接 错误 提示
     *
     * @param avVo  AV结果对象
     * @param sdDto 参数对象
     * @return 错我提示
     */
    private String constErrMsg(AvVo avVo, SdDto sdDto) {
        String airlineCode = StrUtils.subPre(avVo.getFlightNo(), 2);
        String no = StrUtils.subSuf(avVo.getFlightNo(), 2);
        return StrUtils.format("{} {} {} {} {}{} NN{} {}\r\n{}",
                airlineCode, no, sdDto.getCabin(), DateUtils.ymd2Com(avVo.getFlightDate()),
                avVo.getOrg(), avVo.getDst(), sdDto.getSeats(), Constant.CLASS, Constant.SD_UNABLE_TO_SELL);
    }

    private void buildData(SdDto sdDto, AvVo avVo, MemoryDataPnr memoryDataPnr, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        // 如果当前没有活动PNR，则要自动生成一条活动PNR
        if (ObjectUtil.isNull(memoryDataPnr.getMnjxPnr().getPnrId())) {
            iPnrOperationService.createNewPnr(memoryDataPnr, mnjxOffice, mnjxSi);
        }
        PnrSegDto pnrSegDto = new PnrSegDto();
        MnjxPnrSeg mnjxPnrSeg = new MnjxPnrSeg();
        pnrSegDto.setMnjxPnrSeg(mnjxPnrSeg);
        String pnrSegId = IdUtil.getSnowflake(1, 1).nextIdStr();
        mnjxPnrSeg.setPnrSegId(pnrSegId);
        mnjxPnrSeg.setPnrId(memoryDataPnr.getMnjxPnr().getPnrId());
        mnjxPnrSeg.setPnrSegNo(memoryDataPnr.getPnrSegDtos().size() + 1);
        mnjxPnrSeg.setFlightNo(avVo.getFlightNo());
        if (StrUtil.isNotBlank(avVo.getCarrierFlight())) {
            pnrSegDto.setCarrierFlight(avVo.getCarrierFlight());
            mnjxPnrSeg.setCarrierFlight(avVo.getCarrierFlight());
        }
        mnjxPnrSeg.setFlightDate(avVo.getFlightDate());
        List<MnjxOpenCabin> allOpenCabins = new ArrayList<>();
        for (String cabinClass : avVo.getCabinClassSellCabin().keySet()) {
            allOpenCabins.addAll(avVo.getCabinClassSellCabin().get(cabinClass));
        }
        MnjxOpenCabin openCabin = null;
        for (MnjxOpenCabin mnjxOpenCabin : allOpenCabins) {
            if (mnjxOpenCabin.getSellCabin().equals(sdDto.getCabin())) {
                openCabin = mnjxOpenCabin;
                break;
            }
        }
        if (ObjectUtil.isNull(openCabin)) {
            throw new UnifiedResultException("NOT FOUND OPEN CABIN INFO");
        }
        mnjxPnrSeg.setCabinClass(openCabin.getCabinClass());
        mnjxPnrSeg.setSellCabin(sdDto.getCabin());
        mnjxPnrSeg.setOrg(avVo.getOrg());
        mnjxPnrSeg.setDst(avVo.getDst());
        mnjxPnrSeg.setActionCode(StrUtils.format("{}{}", "DK", sdDto.getSeats()));
        mnjxPnrSeg.setEstimateOff(avVo.getEstimateOff());
        mnjxPnrSeg.setEstimateArr(avVo.getEstimateArr());
        mnjxPnrSeg.setPnrSegType("SD");
        mnjxPnrSeg.setSeatNumber(sdDto.getSeats());
        MnjxPlanFlight mnjxPlanFlight = iMnjxPlanFlightService.lambdaQuery()
                .eq(MnjxPlanFlight::getPlanFlightId, avVo.getPlanFlightId())
                .one();
        MnjxCnd mnjxCnd = iMnjxCndService.lambdaQuery()
                .eq(MnjxCnd::getCndNo, mnjxPlanFlight.getCndNo())
                .one();
        MnjxPlaneModel mnjxPlaneModel = iMnjxPlaneModelService.lambdaQuery()
                .eq(MnjxPlaneModel::getPlaneModelId, mnjxCnd.getPlaneModelId())
                .one();
        mnjxPnrSeg.setPlaneVersion(mnjxPlaneModel.getPlaneModelType());
        mnjxPnrSeg.setRecreation(mnjxPlaneModel.getIsRecreation());
        mnjxPnrSeg.setMeal(avVo.getMealCode());
        String inputValue;
        if (StrUtil.isNotBlank(avVo.getCarrierFlight())) {
            inputValue = " " + "*" + avVo.getFlightNo() + " " + sdDto.getCabin() + "   "
                    + DateUtils.ymd2WeekEn(avVo.getFlightDate()).substring(0, 2)
                    + DateUtils.ymd2Com(avVo.getFlightDate()) + "  " + avVo.getOrg() + avVo.getDst()
                    + " " + "DK" + sdDto.getSeats() + "   " + avVo.getEstimateOff() + " " + avVo.getEstimateArr() + "          "
                    + mnjxPlaneModel.getPlaneModelType() + " " + StrUtil.nullToDefault(avVo.getMealCode(), " ") + " "
                    + "0  R E T1T2";
        } else {
            inputValue = "  " + avVo.getFlightNo() + " " + sdDto.getCabin() + "   "
                    + DateUtils.ymd2WeekEn(avVo.getFlightDate()).substring(0, 2)
                    + DateUtils.ymd2Com(avVo.getFlightDate()) + "  " + avVo.getOrg() + avVo.getDst()
                    + " " + "DK" + sdDto.getSeats() + "   " + avVo.getEstimateOff() + " " + avVo.getEstimateArr() + "          "
                    + mnjxPlaneModel.getPlaneModelType() + " " + StrUtil.nullToDefault(avVo.getMealCode(), " ") + " "
                    + "0  R E T1T2";
        }
        mnjxPnrSeg.setInputValue(inputValue);
        memoryDataPnr.getPnrSegDtos().add(pnrSegDto);
        sortPnr(pnrSegDto, memoryDataPnr);
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos().stream()
                .sorted(Comparator.comparing(PnrSegDto::getPnrIndex))
                .collect(Collectors.toList());
        memoryDataPnr.getPnrSegDtos().clear();
        memoryDataPnr.getPnrSegDtos().addAll(pnrSegDtos);

        // 筛选出所有seg
        List<AbstractPnrDto> pnrSegs = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrSegDtos)) {
            pnrSegs = pnrSegDtos.stream()
                    .filter(it -> !it.isXe())
                    .collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(pnrSegs)) {
            List<AbstractPnrDto> sortDtos = pnrSegs.stream()
                    .sorted(Comparator.comparing(AbstractPnrDto::getPnrIndex))
                    .collect(Collectors.toList());
            int pnrSegNo = 1;
            if (CollUtil.isNotEmpty(sortDtos)) {
                for (AbstractPnrDto abstractPnrDto : sortDtos) {
                    ((PnrSegDto) abstractPnrDto).getMnjxPnrSeg().setPnrSegNo(pnrSegNo);
                    pnrSegNo = pnrSegNo + 1;
                }
            }
        }
        this.checkAndAddFqtv(memoryDataPnr, pnrSegDto, pnrSegId);
    }

    /**
     * Title: checkAndAddFqtv
     * Description: 检查是否需要自动添加FQTV
     *
     * @param memoryDataPnr
     * @param pnrSegDto
     * @param pnrSegId
     * @return
     * <AUTHOR>
     * @date 2023/4/17 14:34
     */
    private void checkAndAddFqtv(MemoryDataPnr memoryDataPnr, PnrSegDto pnrSegDto, String pnrSegId) {
        // 检查内存中是否已有身份证信息，根据当前seg的航司和身份证信息查询是否有已录入的常客信息，如果有则自动添加一项SSR FQTV
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos().stream()
                .filter(p -> !p.isXe())
                .collect(Collectors.toList());
        MnjxPnrSeg mnjxPnrSeg = pnrSegDto.getMnjxPnrSeg();
        String airlineCode;
        if (StrUtil.isNotEmpty(pnrSegDto.getCarrierFlight())) {
            airlineCode = pnrSegDto.getCarrierFlight().substring(0, 2);
        } else {
            airlineCode = mnjxPnrSeg.getFlightNo().substring(0, 2);
        }
        if (CollUtil.isNotEmpty(pnrNmDtos)) {
            for (PnrNmDto pnrNmDto : pnrNmDtos) {
                List<PnrSsrDto> ssrDtos = pnrNmDto.getPnrSsrDtos();
                if (CollUtil.isNotEmpty(ssrDtos)) {
                    List<PnrSsrDto> foidSsrs = ssrDtos.stream()
                            .filter(s -> !s.isXe() && Constant.SSR_TYPE_FOID.equals(s.getMnjxNmSsr().getSsrType()))
                            .collect(Collectors.toList());
                    List<PnrSsrDto> fqtvSsrs = ssrDtos.stream()
                            .filter(s -> !s.isXe() && Constant.SSR_TYPE_FQTV.equals(s.getMnjxNmSsr().getSsrType()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(foidSsrs) && (CollUtil.isEmpty(fqtvSsrs) || fqtvSsrs.stream().noneMatch(s -> airlineCode.equals(s.getMnjxNmSsr().getAirlineCode()) && (mnjxPnrSeg.getOrg() + mnjxPnrSeg.getDst()).equals(s.getMnjxNmSsr().getOrgDst())))) {
                        MnjxNmSsr foidSsr = foidSsrs.get(0).getMnjxNmSsr();
                        String foidValue = foidSsr.getInputValue();
                        String foidInfo = foidValue.split(" ")[4].split("/")[0];
                        String foidType = foidInfo.substring(0, 2);
                        String foidNo = foidInfo.substring(2);
                        MnjxFrequenter frequenter = iMnjxFrequenterService.lambdaQuery()
                                .eq(MnjxFrequenter::getAirlineCode, airlineCode)
                                .eq(MnjxFrequenter::getFrequenterCertificateType, foidType)
                                .eq(MnjxFrequenter::getFrequenterCertificateNo, foidNo)
                                .one();
                        if (ObjectUtil.isNotEmpty(frequenter)) {
                            PnrSsrDto pnrSsrDto = new PnrSsrDto();
                            MnjxNmSsr mnjxNmSsr = new MnjxNmSsr();
                            PnrSegDto currentSegDto = memoryDataPnr.getPnrSegDtos().stream()
                                    .filter(p -> pnrSegId.equals(p.getMnjxPnrSeg().getPnrSegId()))
                                    .collect(Collectors.toList())
                                    .get(0);
                            mnjxNmSsr.setPnrSegNo(currentSegDto.getMnjxPnrSeg().getPnrSegNo());
                            mnjxNmSsr.setAirlineCode(airlineCode);
                            mnjxNmSsr.setOrgDst(mnjxPnrSeg.getOrg() + mnjxPnrSeg.getDst());
                            mnjxNmSsr.setFltDate(mnjxPnrSeg.getFlightDate().contains("-") ? mnjxPnrSeg.getFlightDate() : DateUtils.com2ymd(mnjxPnrSeg.getFlightDate()));
                            mnjxNmSsr.setActionCode(Constant.HK);
                            mnjxNmSsr.setSsrType(Constant.SSR_TYPE_FQTV);
                            mnjxNmSsr.setPnrNmId(pnrNmDto.getMnjxPnrNm().getPnrNmId());
                            String fqtvInputValue = StrUtil.format("SSR FQTV {} HK1 {} {} {} {} {}{}/P{}", mnjxNmSsr.getAirlineCode(), mnjxNmSsr.getOrgDst(), mnjxPnrSeg.getSellCabin(),
                                    mnjxPnrSeg.getFlightNo().substring(2), DateUtils.ymd2Com(mnjxPnrSeg.getFlightDate()), mnjxNmSsr.getAirlineCode(), frequenter.getFrequenterCard(), pnrNmDto.getPnrIndex());
                            mnjxNmSsr.setInputValue(fqtvInputValue);
                            mnjxNmSsr.setSsrInfo(fqtvInputValue);
                            pnrSsrDto.setMnjxNmSsr(mnjxNmSsr);
                            ssrDtos.add(pnrSsrDto);
                        }
                    }
                }
            }
        }
    }

    private void sortPnr(PnrSegDto pnrSegDto, MemoryDataPnr memoryDataPnr) {
        // 获取所有的pnr项
        List<AbstractPnrDto> abstractPnrDtos = memoryDataPnr.getAbstractPnrDtos().stream()
                .sorted(Comparator.comparing(AbstractPnrDto::getPnrIndex))
                .collect(Collectors.toList());
        // 筛选出所有seg
        List<AbstractPnrDto> pnrSegs = new ArrayList<>();
        if (CollUtil.isNotEmpty(abstractPnrDtos)) {
            pnrSegs = abstractPnrDtos.stream()
                    .filter(it -> !it.isXe())
                    .filter(it -> it instanceof PnrSegDto)
                    .collect(Collectors.toList());
        }
        // 缓存里面添加了seg
        if (CollUtil.isNotEmpty(pnrSegs)) {
            int indexNum = 0;
            // 找出日期相同的seg
            List<AbstractPnrDto> sameDate = pnrSegs.stream()
                    .filter(it -> {
                        PnrSegDto pnrSeg = (PnrSegDto) it;
                        return StrUtil.isNotBlank(pnrSeg.getMnjxPnrSeg().getFlightDate()) && pnrSeg.getMnjxPnrSeg().getFlightDate().equals(pnrSegDto.getMnjxPnrSeg().getFlightDate());
                    })
                    .collect(Collectors.toList());
            // 如果不含有相同日期的seg
            if (CollUtil.isEmpty(sameDate)) {
                for (AbstractPnrDto abstractPnrDto : pnrSegs) {
                    PnrSegDto pnrSeg = (PnrSegDto) abstractPnrDto;
                    if (StrUtil.isNotBlank(pnrSeg.getMnjxPnrSeg().getFlightDate()) && DateUtil.parse(pnrSegDto.getMnjxPnrSeg().getFlightDate(), DatePattern.NORM_DATE_PATTERN).before(DateUtil.parse(pnrSeg.getMnjxPnrSeg().getFlightDate(), DatePattern.NORM_DATE_PATTERN))) {
                        indexNum = pnrSeg.getPnrIndex();
                        break;
                    }
                }
                if (indexNum == 0) {
                    indexNum = pnrSegs.get(pnrSegs.size() - 1).getPnrIndex() + 1;
                }
            } else {
                // 如果含有相同日期的seg
                //判断是否有第一个航段可接续
                for (AbstractPnrDto abstractPnrDto : sameDate) {
                    PnrSegDto pnrSeg = (PnrSegDto) abstractPnrDto;
                    if (pnrSeg.getMnjxPnrSeg().getDst().equals(pnrSegDto.getMnjxPnrSeg().getOrg())) {
                        indexNum = pnrSeg.getPnrIndex() + 1;
                        break;
                    }
                }
                //未找到航段可接续，找到第一个大于等于当前航班出发时刻的航段行
                if (indexNum == 0) {
                    for (AbstractPnrDto abstractPnrDto : sameDate) {
                        PnrSegDto pnrSeg = (PnrSegDto) abstractPnrDto;
                        if (StrUtil.isNotBlank(pnrSeg.getMnjxPnrSeg().getFlightDate()) && DateUtil.parse(pnrSegDto.getMnjxPnrSeg().getFlightDate() + " " + pnrSegDto.getMnjxPnrSeg().getEstimateOff(), "yyyy-MM-dd HHmm").before(DateUtil.parse(pnrSeg.getMnjxPnrSeg().getFlightDate() + " " + pnrSeg.getMnjxPnrSeg().getEstimateOff(), "yyyy-MM-dd HHmm"))) {
                            indexNum = pnrSeg.getPnrIndex();
                            break;
                        }
                    }
                }
                //如果未找到符合条件的seg，则当前行插入同日期这一组的最后一行
                if (indexNum == 0) {
                    indexNum = pnrSegs.get(sameDate.size() - 1).getPnrIndex() + 1;
                }
            }
            for (AbstractPnrDto abstractPnrDto : abstractPnrDtos) {
                if (abstractPnrDto.getPnrIndex() >= indexNum) {
                    abstractPnrDto.setPnrIndex(abstractPnrDto.getPnrIndex() + 1);
                }
            }
            pnrSegDto.setPnrIndex(indexNum);
        }
    }
}
