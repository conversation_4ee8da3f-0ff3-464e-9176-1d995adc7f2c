package com.swcares.obj.type;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
public enum EnumPatType {
    /**
     * 警残
     */
    JC("JC"),
    /**
     * 军残
     */
    GM("GM"),
    /**
     * 婴儿
     */
    IN("IN"),
    /**
     * 儿童
     */
    CH("CH"),
    /**
     * 不知道的都是其他类型
     */
    NO("");

    @Getter
    private final String code;

    /**
     * 通过中文获取具体的枚举类型
     *
     * @param code 中文描述
     * @return 具体的枚举类型
     */
    public static EnumPatType ofCode(String code) {
        return Arrays.stream(EnumPatType.values())
                .filter(enumPatType -> enumPatType.getCode().equalsIgnoreCase(code))
                .findAny()
                .orElse(NO);
    }
}
