package com.swcares.obj.type;

import com.swcares.core.util.Constant;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
public enum EnumParamType {
    /**
     * RequestHeader(代码中接收注解
     */
    HEADER(Constant.PARAM_TYPE_HEADER),
    /**
     * RequestParam(代码中接收注解
     */
    QUERY(Constant.PARAM_TYPE_QUERY),
    /**
     * （⽤于restful接⼝）
     *
     * PathVariable(代码中接收注解
     */
    PATH(Constant.PARAM_TYPE_PATH),
    /**
     * RequestBody(代码中接收注解
     */
    BODY(Constant.PARAM_TYPE_BODY),
    /**
     *
     */
    FORM(Constant.PARAM_TYPE_FORM);

    @Getter
    private final String describe;

    /**
     * 通过中文获取具体的枚举类型
     *
     * @param describe 中文描述
     * @return 具体的枚举类型
     */
    public static EnumParamType of(String describe) {
        return Arrays.stream(EnumParamType.values()).filter(enumParamType -> enumParamType.getDescribe().equalsIgnoreCase(describe)).findAny().orElse(null);
    }
}
