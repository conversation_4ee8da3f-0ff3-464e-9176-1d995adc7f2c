<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.PnrCommandMapper">

    <select id="retrieveOpenCabinList" resultType="com.swcares.entity.MnjxOpenCabin">
        select moc.*
        from mnjx_open_cabin moc
                 left join mnjx_plan_section mps on
            moc.plan_section_id = mps.plan_section_id
                 left join mnjx_plan_flight mpf on
            mps.plan_flight_id = mpf.plan_flight_id
                 left join mnjx_tcard mt on
            mpf.tcard_id = mt.tcard_id
                 left join mnjx_flight mf on
            mt.flight_id = mf.flight_id
        where mf.flight_no = #{flightNo}
          and mpf.flight_date = #{flightDate}
    </select>
    <select id="retrievePlanSection" resultType="com.swcares.entity.MnjxPlanSection">
        SELECT mps.*
        FROM mnjx_flight mf
                 LEFT JOIN mnjx_tcard mt
                           ON mt.flight_id = mf.flight_id
                 LEFT JOIN mnjx_plan_flight mpf
                           ON mpf.tcard_id = mt.tcard_id
                 LEFT JOIN mnjx_plan_section mps
                           ON mps.plan_flight_id = mpf.plan_flight_id
        WHERE mps.plan_section_id IS NOT NULL
          AND mf.flight_no = #{flightNo}
          AND mpf.flight_date = #{flightDate}
    </select>

    <select id="retrieveOpenCabin" resultType="com.swcares.entity.MnjxOpenCabin">
        select
            moc.*
        from
            mnjx_flight mf
                left join mnjx_tcard mt on
                mf.flight_id = mt.flight_id
                left join mnjx_plan_flight mpf on
                mt.tcard_id = mpf.tcard_id
                left join mnjx_plan_section mps on
                mpf.plan_flight_id = mps.plan_flight_id
                left join mnjx_open_cabin moc on
                mps.plan_section_id = moc.plan_section_id
        where
            mf.flight_no = #{flightNo}
          and mpf.flight_date = #{flightDate}
          and moc.cabin_class = #{cabinClass}
          and moc.sell_cabin = #{sellCabin}
    </select>

    <select id="retrievePlaneModel" resultType="com.swcares.entity.MnjxPlaneModel">
        select
            mpm.*
        from
            mnjx_flight mf
                left join mnjx_tcard mt on
                mf.flight_id = mt.flight_id
                left join mnjx_plan_flight mpf on
                mt.tcard_id = mpf.tcard_id
                left join mnjx_cnd mc on
                mpf.cnd_no = mc.cnd_no
                left join mnjx_plane_model mpm on
                mc.plane_model_id = mpm.plane_model_id
        where
            mf.flight_no = #{flightNo}
          and mpf.flight_date = #{flightDate}
        group by mpm.plane_model_id
    </select>

    <select id="retrieveHbnb" resultType="com.swcares.obj.dto.HbnbNoDto">
        select
            mps.flight_no ,
            mps.carrier_flight ,
            mps.flight_date ,
            mps.pnr_seg_id ,
            mpnt.s1_id ,
            mpnt.s2_id ,
            mpnt.hbnb_1 ,
            mpnt.hbnb_2
        from
            mnjx_pnr_seg mps
        left join mnjx_pnr_nm_ticket mpnt on
            (mps.pnr_seg_id = mpnt.s1_id
                or mps.pnr_seg_id = mpnt.s2_id)
        where
            1 = 1
            and concat(mps.flight_no, ':', mps.flight_date) in
            <foreach collection="queryParams" item="param" open="(" separator="," close=")">
                #{param}
            </foreach>
        order by
        mpnt.hbnb_1 desc, mpnt.hbnb_2
    </select>

    <select id="retrieveOpenCabinListByFlightNoDateList" resultType="com.swcares.obj.dto.OpenCabinFlightDto">
        select
            mf.flight_no,
            mpf.flight_date,
            moc.*
        from
            mnjx_open_cabin moc,
            mnjx_plan_section mps,
            mnjx_plan_flight mpf,
            mnjx_tcard mt,
            mnjx_flight mf
        where
            1 = 1
            and moc.plan_section_id = mps.plan_section_id
            and mps.plan_flight_id = mpf.plan_flight_id
            and mpf.tcard_id = mt.tcard_id
            and mt.flight_id = mf.flight_id
            and concat( mf.flight_no, ':', mpf.flight_date ) in
            <foreach collection="queryParams" item="param" open="(" separator="," close=")">
                #{param}
            </foreach>
    </select>
</mapper>
