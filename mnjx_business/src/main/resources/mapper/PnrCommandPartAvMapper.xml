<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.PnrCommandPartAvMapper">
    
    <select id="retrieveTcardIdsByCityPair" resultType="com.swcares.entity.MnjxTcard">
        select
        mt.*
        from
        mnjx_tcard mt
        where
        tcard_id in (
        select distinct
        c.tcard_id
        from
        (
        select
        a.tcard_id
        from
        mnjx_tcard_section a
        where
        a.airport_id in
        <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
            #{orgId}
        </foreach>
        and a.tcard_id in (
        select
        b.tcard_id
        from
        mnjx_tcard_section b
        where
        b.airport_id in
        <foreach collection="dstIds" item="dstId" open="(" separator="," close=")">
            #{dstId}
        </foreach>)) c)
    </select>
    
    
    <select id="queryCndByPlanSecitonId" resultType="com.swcares.entity.MnjxCnd">
        select mc.* from mnjx_cnd mc
		inner join mnjx_plane mp on mp.cnd_id = mc.cnd_id
		inner join mnjx_plan_section mps on mps.plane_id = mp.plane_id
		where mps.plan_section_id = #{planSectionId}
    </select>

    <select id="retrieveCnd" resultType="com.swcares.entity.MnjxCnd">
        select
            mc.cnd_no ,
            mc.first_sell_cabin ,
            mc.second_sell_cabin ,
            mc.third_sell_cabin ,
            mc.fourth_sell_cabin ,
            mc.fifth_sell_cabin
        from
            mnjx_cnd mc
    </select>

</mapper>
