<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.PnrCommandPartSsMapper">

    <select id="retrieveSection" resultType="com.swcares.obj.vo.MnjxPlanSectionVo">
		select mpf.flight_date         as flightDate,
			   mps.plan_section_id     as planSectionId,
			   mps.plan_flight_id      as planFlightId,
			   mps.dep_apt_id          as depAptId,
			   mps.arr_apt_id          as arrAptId,
			   mps.estimate_off        as estimateOff,
			   mps.estimate_off_change as estimateOffChange,
			   mps.estimate_arr        as estimateArr,
			   mps.meal_code           as mealCode,
			   mps.plane_id            as planeId
		from mnjx_flight mf
				 left join mnjx_tcard mt on
			mf.flight_id = mt.flight_id
				 left join mnjx_plan_flight mpf on
			mpf.tcard_id = mt.tcard_id
				 left join mnjx_plan_section mps on
			mps.plan_flight_id = mpf.plan_flight_id
		where mf.flight_no = #{flightNo}
		  and mpf.flight_date = #{date}
		order by mps.is_last_section, mps.estimate_off_change, mps.estimate_off
	</select>
</mapper>
