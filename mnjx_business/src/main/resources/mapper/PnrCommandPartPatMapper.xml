<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.PnrCommandPartPatMapper">

    <select id="retrieveOpenCabin" resultType="com.swcares.entity.MnjxOpenCabin">
        SELECT
        moc.cabin_class AS cabinClass,
        sum( moc.sell_cabin_price ) AS sellCabinPrice,
        moc.sell_cabin AS sellCabin,
        mt.start_date AS startDate,
        mt.end_date AS endDate
        FROM
        mnjx_flight mf
        LEFT JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
        LEFT JOIN mnjx_plan_flight mpf ON mt.tcard_id = mpf.tcard_id
        LEFT JOIN mnjx_plan_section mps ON mpf.plan_flight_id = mps.plan_flight_id
        LEFT JOIN mnjx_open_cabin moc ON mps.plan_section_id = moc.plan_section_id
        WHERE
        mf.flight_no = #{flightNo}
        AND mpf.flight_date = #{flightDate}
        AND moc.cabin_class = #{cabinClass}
        <if test="sellCabin != null and sellCabin != ''">
            and moc.sell_cabin = #{sellCabin}
        </if>
        AND ( dep_apt_id = #{org} OR arr_apt_id = #{dst})
        GROUP BY
        moc.cabin_class, moc.sell_cabin
        ORDER BY sellCabinPrice DESC
    </select>

    <select id="retrievePlaneModel" resultType="com.swcares.entity.MnjxPlaneModel">
        select mpm.*
        from mnjx_flight mf
                 left join mnjx_tcard mt on
            mf.flight_id = mt.flight_id
                 left join mnjx_plan_flight mpf on
            mt.tcard_id = mpf.tcard_id
                 left join mnjx_cnd mc on
            mpf.cnd_no = mc.cnd_no
                 left join mnjx_plane_model mpm on
            mc.plane_model_id = mpm.plane_model_id
        where mf.flight_no = #{flightNo}
          and mpf.flight_date = #{flightDate}
        group by mpm.plane_model_id
    </select>

    <select id="retrieveCnd" resultType="com.swcares.entity.MnjxCnd">
        select mc.*
        from mnjx_flight mf
                 left join mnjx_tcard mt on
            mf.flight_id = mt.flight_id
                 left join mnjx_plan_flight mpf on
            mt.tcard_id = mpf.tcard_id
                 left join mnjx_cnd mc on
            mpf.cnd_no = mc.cnd_no
        where mf.flight_no = #{flightNo}
          and mpf.flight_date = #{flightDate}
    </select>

    <select id="retrieveTcardSection" resultType="com.swcares.entity.MnjxTcardSection">
        select
        mts.*
        from
        mnjx_flight mf
        left join mnjx_tcard mt on
        mf.flight_id = mt.flight_id
        left join mnjx_tcard_section mts on
        mt.tcard_id = mts.tcard_id
        left join mnjx_airport ma on
        mts.airport_id = ma.airport_id
        where
        1 = 1
        and mf.flight_no = #{flightNo}
        <if test="orgDstList != null and orgDstList.size>0">
            and ma.airport_code in
            <foreach collection="orgDstList" item="orgDst" separator="," open="(" close=")">
                #{orgDst}
            </foreach>
        </if>
        group by mts.section_no
    </select>
</mapper>
