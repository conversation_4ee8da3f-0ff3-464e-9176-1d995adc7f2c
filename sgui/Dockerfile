#使用文件创建镜像
FROM harbor.kaiya.com:30443/library/java:8

#作者信息
MAINTAINER "zx <EMAIL>"
ENV SGUI_PATH=/usr/local/sgui
ENV PATH=$PATH:SGUI_PATH

#修改时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

#设定工作目录
WORKDIR SGUI_PATH

#拷贝工程
COPY target/sgui.jar ./sgui.jar
COPY target/lib ./lib/

#拷贝执行脚本
COPY entrypoint.sh ./
RUN chmod 777 entrypoint.sh

#打印的日志
VOLUME SGUI_PATH/logs/

#容器暴露端口（可以暴露的，容器启动时确定具体暴露的端口）
EXPOSE 8350

#执行的命令
#CMD ["java","-jar","sgui.jar"]
ENTRYPOINT ["/bin/bash","-c","SGUI_PATH/entrypoint.sh"]
