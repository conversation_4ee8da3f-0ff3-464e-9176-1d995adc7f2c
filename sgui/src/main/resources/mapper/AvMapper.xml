<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.AvMapper">

    <select id="retrieveAv" resultType="com.swcares.obj.vo.AvVo">
        select
            ma.airline_full_name as airline,
            mpf.flight_no as flightNo,
            mpf.flight_date as flightDate,
            (select airport_code from mnjx_airport ma where ma.airport_id = mps.dep_apt_id) as org,
            (select airport_code from mnjx_airport ma where ma.airport_id = mps.arr_apt_id) as dst
        from
            mnjx_plan_section mps
            left join mnjx_plan_flight mpf on mps.plan_flight_id = mpf.plan_flight_id
            left join mnjx_airline ma on mpf.airline_code = ma.airline_code
        where
            mps.plan_section_id is not null
        order by
            mps.estimate_off
    </select>
</mapper>