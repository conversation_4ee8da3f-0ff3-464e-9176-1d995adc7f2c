# J2Cache configuration


#########################################
# Cache Broadcast Method ç¼å­å¹¿æ­æ¹æ³
# values:
# jgroups -> use jgroups's multicast ä½¿ç¨jgroupsçå¤æ­
# redis -> use redis publish/subscribe mechanism  ä½¿ç¨redisçåå¸/è®¢éæºå¶ï¼ä½¿ç¨jedisï¼
# lettuce -> use redis publish/subscribe mechanism (using lettuce, Recommend) ä½¿ç¨redisçåå¸/è®¢éæºå¶ï¼ä½¿ç¨lettuceï¼æ¨èï¼
# rabbitmq -> use RabbitMQ publisher/consumer mechanism ä½¿ç¨RabbitMQåå¸è/æ¶è´¹èæºå¶
# rocketmq -> use RocketMQ publisher/consumer mechanism ä½¿ç¨RocketMQåå¸è/æ¶è´¹èæºå¶
# none -> don't notify the other nodes in cluster ä¸è¦éç¥éç¾¤ä¸­çå¶ä»èç¹,å°±æ¯åèç¹ä½¿ç¨
# xx.xxxx.xxxx.Xxxxx your own cache broadcast policy classname that implement net.oschina.j2cache.cluster.ClusterPolicy æ¨èªå·±çå®ç°net.oschina.j2cache.cluster.ClusterPolicyçç¼å­å¹¿æ­ç­ç¥ç±»å
#########################################
# å¹¿æ­ç­ç¥
# j2cache.broadcast = net.oschina.j2cache.cache.support.redis.SpringRedisPubSubPolicy
# åèç¹ä½¿ç¨
j2cache.broadcast = none

#ç»æ­éç½®
# jgroups properties
# jgroups.channel.name = j2cache
# jgroups.configXml = /network.xml

# RabbitMQ properties
# rabbitmq.exchange = j2cache
# rabbitmq.host = localhost
# rabbitmq.port = 5672
# rabbitmq.username = guest
# rabbitmq.password = guest

# RocketMQ properties
# rocketmq.name = j2cache
# rocketmq.topic = j2cache
# use ; to split multi hosts
# rocketmq.hosts = 127.0.0.1:9876

#########################################
# Level 1&2 provider ä¸çº§ä¸äºçº§æä¾è
# values:
# none -> disable this level cache ç¦ç¨æ­¤çº§å«çç¼å­
# ehcache -> use ehcache2 as level 1 cache ä½¿ç¨ehcache2ä½ä¸º1çº§ç¼å­
# ehcache3 -> use ehcache3 as level 1 cache ä½¿ç¨ehcache3ä½ä¸º1çº§ç¼å­
# caffeine -> use caffeine as level 1 cache(only in memory) ä½¿ç¨caffeineä½ä¸º1çº§ç¼å­ï¼ä»å¨åå­ä¸­ï¼
# redis -> use redis as level 2 cache (using jedis) å°redisç¨ä½2çº§ç¼å­ï¼ä½¿ç¨jedisï¼
# lettuce -> use redis as level 2 cache (using lettuce) ä½¿ç¨redisä½ä¸º2çº§ç¼å­ï¼ä½¿ç¨ lettuce)
# readonly-redis -> use redis as level 2 cache ,but never write data to it. if use this provider, you must uncomment `j2cache.L2.config_section` to make the redis configurations available. å°redisç¨ä½2çº§ç¼å­ï¼ä½ä¸è¦åå¶åå¥æ°æ®ãå¦æä½¿ç¨æ­¤æä¾ç¨åºï¼åå¿é¡»åæ¶æ³¨éâ j2cache.L2.config_sectionâä»¥ä½¿rediséç½®å¯ç¨ã
# memcached -> use memcached as level 2 cache (xmemcached), ä½¿ç¨memcachedä½ä¸º2çº§ç¼å­ï¼xmemcachedï¼
# [classname] -> use custom provider ä½¿ç¨èªå®ä¹æä¾ç¨åº
#########################################

# ä¸çº§ç¼å­ç­ç¥ä¸ºehcache2
j2cache.L1.provider_class = ehcache

# äºçº§ç¼å­ç­ç¥ä¸ºredis->none(ç¦ç¨äºçº§ç¼å­)
j2cache.L2.provider_class = none

# When L2 provider isn't `redis`, using `L2.config_section = redis` to read redis configurations å½L2æä¾èä¸æ¯`redis`æ¶ï¼ä½¿ç¨`L2.config_section = redis`è¯»årediséç½®
#j2cache.L2.config_section = redis

# Enable/Disable ttl in redis cache data (if disabled, the object in redis will never expire, default:true) å¨redisç¼å­æ°æ®ä¸­å¯ç¨/ç¦ç¨ttlï¼å¦æç¦ç¨ï¼åredisä¸­çå¯¹è±¡å°æ°¸ä¸è¿æï¼é»è®¤å¼ï¼trueï¼
# NOTICE: redis hash mode (redis.storage = hash) do not support this feature) æ³¨æï¼redisåå¸æ¨¡å¼ï¼redis.storage =åå¸ï¼ä¸æ¯ææ­¤åè½ï¼
j2cache.sync_ttl_to_redis = true

# Whether to cache null objects by default (default false) é»è®¤æåµä¸æ¯å¦ç¼å­ç©ºå¯¹è±¡ï¼é»è®¤ä¸ºfalseï¼
j2cache.default_cache_null_object = true

#########################################
# Cache Serialization Provider ç¼å­åºååæä¾ç¨åº
# values:
# fst -> using fast-serialization (recommend) ä½¿ç¨fast-serializationåºååï¼æ¨èï¼
# kryo -> using kryo serialization kryoåºåå
# json -> using fst's json serialization (testing) ä½¿ç¨fstçjsonåºååï¼æµè¯ï¼
# fastjson -> using fastjson serialization (embed non-static class not support) ä½¿ç¨fastjsonåºååï¼ä¸æ¯æåµå¥çééæç±»ï¼
# java -> java standard Javaæ å
# fse -> using fse serialization ä½¿ç¨fseåºåå
# [classname implements Serializer]
#########################################

j2cache.serialization = fst

#########################################
# Ehcache configuration
#########################################

ehcache.configXml = ehcache2.xml
#ehcache3.configXml = ehcache3.xml
# ehcache3.defaultHeapSize = 1000

#########################################
# Caffeine configuration
# caffeine.region.[name] = size, xxxx[s|m|h|d]
#
#########################################
#caffeine.properties = /caffeine.properties
#caffeine.region.default = 1000, 1h

#########################################
# Redis connection configuration
#########################################

#########################################
# Redis Cluster Mode
#
# single -> single redis server åRedisæå¡å¨
# sentinel -> master-slaves servers ä¸»ä»æå¡å¨
# cluster -> cluster servers (æ°æ®åºéç½®æ æï¼ä½¿ç¨ database = 0ï¼
# sharded -> sharded servers  (å¯ç ãæ°æ®åºå¿é¡»å¨ hosts ä¸­æå®ï¼ä¸è¿æ¥æ± éç½®æ æ ; redis://user:password@127.0.0.1:6379/0ï¼
#
#########################################

#redis.mode = single

#redis storage mode (generic|hash)
#redis.storage = generic

#cluster name just for sharded
#redis.cluster_name = mymaster

## redis cache namespace optional, default[j2cache]
#redis.namespace = j2cache

## connection
# Separate multiple redis nodes with commas, such as ************:6379,************:6379,************:6379
# redis.hosts = *************:6379
# redis.timeout = 2000
# redis.password =
# redis.database = 0

## redis pub/sub channel name
# redis.channel = j2cache

## redis pool properties
#redis.maxTotal = -1
#redis.maxIdle = 2000
#redis.maxWaitMillis = 100
#redis.minEvictableIdleTimeMillis = 864000000
#redis.minIdle = 1000
#redis.numTestsPerEvictionRun = 10
#redis.lifo = false
#redis.softMinEvictableIdleTimeMillis = 10
#redis.testOnBorrow = true
#redis.testOnReturn = false
#redis.testWhileIdle = false
#redis.timeBetweenEvictionRunsMillis = 300000
#redis.blockWhenExhausted = true