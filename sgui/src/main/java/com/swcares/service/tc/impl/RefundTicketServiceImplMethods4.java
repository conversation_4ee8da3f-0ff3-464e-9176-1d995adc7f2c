// 这些方法需要添加到RefundTicketServiceImpl中

    /**
     * 构建查询退票单号响应对象
     */
    private QueryRefundFormVo buildQueryRefundFormVo(
            QueryRefundFormDto dto, MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn,
            MnjxRefundTicket refundTicket, MnjxPnrNm pnrNm, MnjxPnr pnr) {

        QueryRefundFormVo result = new QueryRefundFormVo();

        // 设置基本信息
        result.setOffice("LXA101");
        result.setIataNo("08323431");
        result.setAgent("49041");
        result.setOperator("49041");
        result.setAirlineCode(dto.getTicketNo().substring(0, 3));
        result.setTicketNoEnd(null);
        result.setTicketNoSecond(null);
        result.setTicketNo(dto.getTicketNo());
        result.setTicketNoView(dto.getTicketNo());
        result.setCheck(null);
        result.setCouponNo(null);
        result.setConjunction(1);
        result.setPayMethod("CASH");
        result.setCurrency("CNY");
        result.setRefundFormCurrency("CNY");
        result.setCommissionRate(BigDecimal.ZERO);
        result.setCreditCard("");
        result.setRemark(null);
        result.setQuerySuccess(true);
        result.setCmdOption(null);
        result.setTicketType(dto.getTicketType());
        result.setTicketManagementOrganizationCode(dto.getTicketManagementOrganizationCode());
        result.setSegmentInfos(null);
        result.setDeviceNum(dto.getPrinterNo());
        result.setRefundDate(null);
        result.setInternational("0");

        // 设置旅客信息
        boolean isInfant = StrUtil.isNotEmpty(pnrNmTn.getNmXnId());
        if (isInfant) {
            MnjxNmXn nmXn = iMnjxNmXnService.getById(pnrNmTn.getNmXnId());
            if (nmXn != null) {
                result.setPassengerName(nmXn.getXnCname());
                result.setPassengerType("INF");
            }
        } else {
            result.setPassengerName(pnrNm.getName());
            result.setPassengerType(this.convertPsgType(pnrNm.getPsgType()));
        }

        // 设置退票单号
        result.setCmdNo(refundTicket.getRefundNo());

        // 设置金额信息
        result.setGrossRefund(refundTicket.getCollection().add(refundTicket.getCnPrice()).add(refundTicket.getYqPrice()));
        result.setDeduction(refundTicket.getComm());
        result.setRefund(refundTicket.getNetRefund().toString());
        result.setCommission(refundTicket.getComm());
        result.setNetRefund(refundTicket.getNetRefund());

        // 设置税费信息
        List<QueryRefundFormVo.TaxInfo> taxInfos = new ArrayList<>();
        BigDecimal totalTaxs = BigDecimal.ZERO;

        if (refundTicket.getCnPrice() != null && refundTicket.getCnPrice().compareTo(BigDecimal.ZERO) > 0) {
            QueryRefundFormVo.TaxInfo cnTax = new QueryRefundFormVo.TaxInfo();
            cnTax.setTaxCode("CN");
            cnTax.setTaxAmount(refundTicket.getCnPrice());
            taxInfos.add(cnTax);
            totalTaxs = totalTaxs.add(refundTicket.getCnPrice());
        }

        if (refundTicket.getYqPrice() != null && refundTicket.getYqPrice().compareTo(BigDecimal.ZERO) > 0) {
            QueryRefundFormVo.TaxInfo yqTax = new QueryRefundFormVo.TaxInfo();
            yqTax.setTaxCode("YQ");
            yqTax.setTaxAmount(refundTicket.getYqPrice());
            taxInfos.add(yqTax);
            totalTaxs = totalTaxs.add(refundTicket.getYqPrice());
        }

        result.setTaxInfos(taxInfos);
        result.setTotalTaxs(totalTaxs);

        // 设置二次验证因素
        if (dto.getSecondFactor() != null) {
            QueryRefundFormVo.SecondFactor secondFactor = new QueryRefundFormVo.SecondFactor();
            secondFactor.setSecondFactorCode(dto.getSecondFactor().getSecondFactorCode());
            secondFactor.setSecondFactorValue(dto.getSecondFactor().getSecondFactorValue());
            result.setSecondFactor(secondFactor);
        }

        // 7. originalTickets调用buildOpenSourceText方法构建
        List<String> originalTickets = this.buildOriginalTickets(pnrNmTicket, pnr);
        result.setOriginalTickets(originalTickets);

        return result;
    }

    /**
     * 构建原始票据信息
     */
    private List<String> buildOriginalTickets(MnjxPnrNmTicket pnrNmTicket, MnjxPnr pnr) {
        List<String> originalTickets = new ArrayList<>();

        try {
            // 查询航段信息
            List<String> segmentIds = new ArrayList<>();
            if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id())) {
                segmentIds.add(pnrNmTicket.getS1Id());
            }
            if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id())) {
                segmentIds.add(pnrNmTicket.getS2Id());
            }

            if (!segmentIds.isEmpty()) {
                List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.listByIds(segmentIds);

                for (MnjxPnrSeg pnrSeg : pnrSegList) {
                    // 构建航段信息字符串
                    StringBuilder segmentInfo = new StringBuilder();
                    segmentInfo.append(pnrSeg.getFlightNo()).append(" ");
                    segmentInfo.append(pnrSeg.getSellCabin()).append(" ");
                    segmentInfo.append(pnrSeg.getFlightDate()).append(" ");
                    segmentInfo.append(pnrSeg.getOrg()).append(pnrSeg.getDst()).append(" ");
                    segmentInfo.append(pnrSeg.getActionCode());

                    originalTickets.add(segmentInfo.toString());
                }
            }

            // 如果没有航段信息，添加基本的PNR信息
            if (originalTickets.isEmpty()) {
                originalTickets.add("PNR: " + pnr.getPnrCrs());
            }

        } catch (Exception e) {
            // 异常情况下返回基本信息
            originalTickets.add("TICKET: " + pnrNmTicket.getTicketNo());
        }

        return originalTickets;
    }
