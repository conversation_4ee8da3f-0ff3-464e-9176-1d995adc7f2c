package com.swcares.service.tc.impl;

import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.entity.*;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;
import com.swcares.service.*;
import com.swcares.service.tc.IRefundTicketService;
import com.swcares.service.tc.ITcV2Service;
import com.swcares.service.tc.ITicketService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:18
 */
@Service
public class TcV2ServiceImpl implements ITcV2Service {

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxAgentAirlineService iMnjxAgentAirlineService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private ITicketService iTicketService;

    @Resource
    private IRefundTicketService iRefundTicketService;

    @Override
    public ETReceiptDictEntryVo getETReceiptDictEntry() {
        return null;
    }

    @Override
    public QueryOfficeInformationVo queryOfficeInformation(QueryOfficeInformationDto dto) {
        QueryOfficeInformationVo vo = new QueryOfficeInformationVo();
        // 查询OFFICE
        MnjxOffice office = iMnjxOfficeService.lambdaQuery()
                .eq(MnjxOffice::getOfficeNo, dto.getOffice())
                .one();
        // 创建返回office对象
        QueryOfficeInformationVo.OfficeInformationVo officeInformationVo = new QueryOfficeInformationVo.OfficeInformationVo();
        officeInformationVo.setOffice(office.getOfficeNo());
        officeInformationVo.setAllowTicket(true);
        officeInformationVo.setUseAms(false);
        // 只处理代理人类型的office
        if ("0".equals(office.getOfficeType())) {
            // 查询代理人信息
            MnjxAgent agent = iMnjxAgentService.getById(office.getOrgId());
            officeInformationVo.setIata(agent.getAgentIata());
            // 查询代理航司信息
            List<MnjxAgentAirline> agentAirlineList = iMnjxAgentAirlineService.lambdaQuery()
                    .eq(MnjxAgentAirline::getAgentId, agent.getAgentId())
                    .list();
            // 查询航司
            List<MnjxAirline> airlineList = iMnjxAirlineService.listByIds(agentAirlineList.stream()
                    .map(MnjxAgentAirline::getAirlineId)
                    .collect(Collectors.toList()));
            // 设置代理航司数据
            for (MnjxAgentAirline agentAirline : agentAirlineList) {
                QueryOfficeInformationVo.AirlineTicketVo airlineTicketVo = new QueryOfficeInformationVo.AirlineTicketVo();
                airlineTicketVo.setAirlineCode(airlineList.stream()
                        .filter(a -> agentAirline.getAirlineId().equals(a.getAirlineId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getAirlineCode());
                airlineTicketVo.setTicketPromise(true);
                vo.getAirlineTickets().add(airlineTicketVo);
            }

            // 设置代理人数据
            QueryOfficeInformationVo.AgentVo agentVo = new QueryOfficeInformationVo.AgentVo();
            agentVo.setAddress(agent.getAgentContactAddress());
            agentVo.setContact(agent.getAgentContactCname());
            agentVo.setFax("NULL");
            agentVo.setPhone(agent.getAgentContactPhone());
            vo.setAgent(agentVo);
        }

        // 查询打票机
        List<MnjxPrinter> printerList = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getOfficeId, office.getOfficeId())
                .isNotNull(MnjxPrinter::getTicketStart)
                .isNotNull(MnjxPrinter::getTicketEnd)
                .list();
        // 设置打票机数据
        for (MnjxPrinter printer : printerList) {
            QueryOfficeInformationVo.TicketMachineVo ticketMachineVo = new QueryOfficeInformationVo.TicketMachineVo();
            ticketMachineVo.setPid(printer.getPrinterPid());
            ticketMachineVo.setDevno(printer.getPrinterNo());
            ticketMachineVo.setCurrency("CNY");
            ticketMachineVo.setType("4");
            ticketMachineVo.setTkt("BSP");
            ticketMachineVo.setCtlPid(printer.getSiId());
            ticketMachineVo.setCtlAgnt("");
            String end = StrUtil.toString(printer.getTicketEnd());
            ticketMachineVo.setTnRange(StrUtil.format("{}-{}", printer.getTicketStart(), end.substring(end.length() - 5)));
            ticketMachineVo.setCurrentTicketNumber(StrUtil.toString(printer.getLastTicket()));
            ticketMachineVo.setTicketRemainder(null);
            vo.getTicketMachines().add(ticketMachineVo);
        }

        // 设置office数据
        vo.setOffice(officeInformationVo);
        return vo;
    }

    @Override
    public QueryTicketDetailVo queryTicketDetail(QueryTicketDetailDto dto) throws SguiResultException {
        return iTicketService.queryTicketDetail(dto);
    }

    @Override
    public QueryTicketByDetrVo queryTicketByDetr(QueryTicketByDetrDto dto) throws SguiResultException {
        return iTicketService.queryTicketByDetr(dto);
    }

    @Override
    public TicketByRtktVo queryTicketByRtkt(String ticketNo) throws SguiResultException {
        return iTicketService.queryTicketByRtkt(ticketNo);
    }

    @Override
    public List<QueryTicketByPnrVo> queryTicketByPnr(QueryTicketByPnrDto dto) throws SguiResultException {
        return iTicketService.queryTicketByPnr(dto);
    }

    @Override
    public List<QueryTicketByPnrVo> queryTicketDigestsByCert(QueryTicketByCertDto dto) throws SguiResultException {
        return iTicketService.queryTicketByCert(dto);
    }

    @Override
    public PreviewRefundTicketVo previewRefundTicket(PreviewRefundTicketDto dto) throws SguiResultException {
        return iRefundTicketService.previewRefundTicket(dto);
    }

    @Override
    public FindRefundTicketVo findRefundTicket(FindRefundTicketDto dto) throws SguiResultException {
        return iRefundTicketService.findRefundTicket(dto);
    }

    @Override
    public QueryPnrMessageVo queryPnrMessage(QueryPnrMessageDto dto) throws SguiResultException {
        return iRefundTicketService.queryPnrMessage(dto);
    }

    @Override
    public BatchFindRefundFeeVo batchFindRefundFee(BatchFindRefundFeeDto dto) throws SguiResultException {
        return iRefundTicketService.batchFindRefundFee(dto);
    }

    @Override
    public BatchAutoRefundVo batchAutoRefund(BatchAutoRefundDto dto) throws SguiResultException {
        return iRefundTicketService.batchAutoRefund(dto);
    }
}
