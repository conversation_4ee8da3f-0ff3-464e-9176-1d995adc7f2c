package com.swcares.service.tc.impl;

import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.util.NumberUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;
import com.swcares.service.*;
import com.swcares.service.tc.ITcV2Service;
import com.swcares.service.tc.ITicketService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:18
 */
@Service
public class TcV2ServiceImpl implements ITcV2Service {

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxAgentAirlineService iMnjxAgentAirlineService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private ITicketService iTicketService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Override
    public ETReceiptDictEntryVo getETReceiptDictEntry() {
        return null;
    }

    @Override
    public QueryOfficeInformationVo queryOfficeInformation(QueryOfficeInformationDto dto) {
        QueryOfficeInformationVo vo = new QueryOfficeInformationVo();
        // 查询OFFICE
        MnjxOffice office = iMnjxOfficeService.lambdaQuery()
                .eq(MnjxOffice::getOfficeNo, dto.getOffice())
                .one();
        // 创建返回office对象
        QueryOfficeInformationVo.OfficeInformationVo officeInformationVo = new QueryOfficeInformationVo.OfficeInformationVo();
        officeInformationVo.setOffice(office.getOfficeNo());
        officeInformationVo.setAllowTicket(true);
        officeInformationVo.setUseAms(false);
        // 只处理代理人类型的office
        if ("0".equals(office.getOfficeType())) {
            // 查询代理人信息
            MnjxAgent agent = iMnjxAgentService.getById(office.getOrgId());
            officeInformationVo.setIata(agent.getAgentIata());
            // 查询代理航司信息
            List<MnjxAgentAirline> agentAirlineList = iMnjxAgentAirlineService.lambdaQuery()
                    .eq(MnjxAgentAirline::getAgentId, agent.getAgentId())
                    .list();
            // 查询航司
            List<MnjxAirline> airlineList = iMnjxAirlineService.listByIds(agentAirlineList.stream()
                    .map(MnjxAgentAirline::getAirlineId)
                    .collect(Collectors.toList()));
            // 设置代理航司数据
            for (MnjxAgentAirline agentAirline : agentAirlineList) {
                QueryOfficeInformationVo.AirlineTicketVo airlineTicketVo = new QueryOfficeInformationVo.AirlineTicketVo();
                airlineTicketVo.setAirlineCode(airlineList.stream()
                        .filter(a -> agentAirline.getAirlineId().equals(a.getAirlineId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getAirlineCode());
                airlineTicketVo.setTicketPromise(true);
                vo.getAirlineTickets().add(airlineTicketVo);
            }

            // 设置代理人数据
            QueryOfficeInformationVo.AgentVo agentVo = new QueryOfficeInformationVo.AgentVo();
            agentVo.setAddress(agent.getAgentContactAddress());
            agentVo.setContact(agent.getAgentContactCname());
            agentVo.setFax("NULL");
            agentVo.setPhone(agent.getAgentContactPhone());
            vo.setAgent(agentVo);
        }

        // 查询打票机
        List<MnjxPrinter> printerList = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getOfficeId, office.getOfficeId())
                .isNotNull(MnjxPrinter::getTicketStart)
                .isNotNull(MnjxPrinter::getTicketEnd)
                .list();
        // 设置打票机数据
        for (MnjxPrinter printer : printerList) {
            QueryOfficeInformationVo.TicketMachineVo ticketMachineVo = new QueryOfficeInformationVo.TicketMachineVo();
            ticketMachineVo.setPid(printer.getPrinterPid());
            ticketMachineVo.setDevno(printer.getPrinterNo());
            ticketMachineVo.setCurrency("CNY");
            ticketMachineVo.setType("4");
            ticketMachineVo.setTkt("BSP");
            ticketMachineVo.setCtlPid(printer.getSiId());
            ticketMachineVo.setCtlAgnt("");
            String end = StrUtil.toString(printer.getTicketEnd());
            ticketMachineVo.setTnRange(StrUtil.format("{}-{}", printer.getTicketStart(), end.substring(end.length() - 5)));
            ticketMachineVo.setCurrentTicketNumber(StrUtil.toString(printer.getLastTicket()));
            ticketMachineVo.setTicketRemainder(null);
            vo.getTicketMachines().add(ticketMachineVo);
        }

        // 设置office数据
        vo.setOffice(officeInformationVo);
        return vo;
    }

    @Override
    public QueryTicketDetailVo queryTicketDetail(QueryTicketDetailDto dto) throws SguiResultException {
        return this.iTicketService.queryTicketDetail(dto);
    }

    @Override
    public QueryTicketByDetrVo queryTicketByDetr(QueryTicketByDetrDto dto) throws SguiResultException {
        return this.iTicketService.queryTicketByDetr(dto);
    }

    @Override
    public TicketByRtktVo queryTicketByRtkt(String ticketNo) throws SguiResultException {
        return this.iTicketService.queryTicketByRtkt(ticketNo);
    }

    @Override
    public List<QueryTicketByPnrVo> queryTicketByPnr(QueryTicketByPnrDto dto) throws SguiResultException {
        return this.iTicketService.queryTicketByPnr(dto);
    }

    @Override
    public List<QueryTicketByPnrVo> queryTicketDigestsByCert(QueryTicketByCertDto dto) throws SguiResultException {
        return iTicketService.queryTicketByCert(dto);
    }

    @Override
    public PreviewRefundTicketVo previewRefundTicket(PreviewRefundTicketDto dto) throws SguiResultException {
        // 参数验证
        if (StrUtil.isEmpty(dto.getTktNo())) {
            throw new SguiResultException("票号不能为空");
        }

        // 处理票号格式，去掉"-"
        String formattedTicketNo = dto.getTktNo().replace("-", "");

        // 1. 根据票号查询mnjx_pnr_nm_ticket表
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("票号不存在");
        }

        // 2. 根据ticket的tnId查询mnjx_pnr_nm_tn表
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
        if (pnrNmTn == null) {
            throw new SguiResultException("票号关联信息不存在");
        }

        // 判断是否为婴儿票
        boolean isInfant = StrUtil.isNotEmpty(pnrNmTn.getNmXnId());
        String pnrNmId;
        String passengerName = null;
        String psgType = null;

        if (isInfant) {
            // 4. 婴儿处理：根据nmXnId查询mnjx_nm_xn表
            MnjxNmXn nmXn = iMnjxNmXnService.getById(pnrNmTn.getNmXnId());
            if (nmXn == null) {
                throw new SguiResultException("婴儿信息不存在");
            }
            pnrNmId = nmXn.getPnrNmId();
            passengerName = nmXn.getXnCname();
            psgType = "INF";
        } else {
            // 3. 成人/儿童处理：根据pnrNmId查询mnjx_pnr_nm表
            pnrNmId = pnrNmTn.getPnrNmId();
        }

        // 查询旅客详细信息
        MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        if (pnrNm == null) {
            throw new SguiResultException("旅客信息不存在");
        }

        if (!isInfant) {
            passengerName = pnrNm.getName();
            psgType = this.convertPsgType(pnrNm.getPsgType());
        }

        // 5. 根据pnrId查询mnjx_pnr表
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        if (pnr == null) {
            throw new SguiResultException("PNR信息不存在");
        }

        // 构建返回对象
        PreviewRefundTicketVo result = new PreviewRefundTicketVo();

        // 构建退票订单信息
        PreviewRefundTicketVo.RefundTicketOrder refundTicketOrder = this.buildRefundTicketOrder(
                pnrNmTicket, pnrNmTn, pnr, pnrNm, passengerName, psgType, isInfant, dto.getSecondFactor());
        result.setRefundTicketOrder(refundTicketOrder);

        // 构建退票计算信息
        PreviewRefundTicketVo.RefundCompute refundCompute = this.buildRefundCompute(
                pnrNmTicket, pnrNmTn, pnr, pnrNm, isInfant);
        result.setRefundCompute(refundCompute);

        // 设置二次验证信息
        PreviewRefundTicketVo.SecondFactor secondFactor = new PreviewRefundTicketVo.SecondFactor();
        secondFactor.setSecondFactorCode(dto.getSecondFactor().getSecondFactorCode());
        secondFactor.setSecondFactorValue(dto.getSecondFactor().getSecondFactorValue());
        result.setSecondFactor(secondFactor);

        return result;
    }

    /**
     * 转换旅客类型
     */
    private String convertPsgType(String psgType) {
        if (StrUtil.isEmpty(psgType)) {
            return "ADT";
        }
        switch (psgType) {
            case "0":
                return "ADT"; // 成人
            case "1":
                return "CHD"; // 儿童
            case "2":
                return "UM";  // 无人陪伴旅客
            case "4":
                return "GMJC";
            default:
                return "ADT";
        }
    }

    /**
     * 构建退票订单信息
     */
    private PreviewRefundTicketVo.RefundTicketOrder buildRefundTicketOrder(
            MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm,
            String passengerName, String psgType, boolean isInfant,
            PreviewRefundTicketDto.SecondFactor secondFactorDto) {

        PreviewRefundTicketVo.RefundTicketOrder refundTicketOrder = new PreviewRefundTicketVo.RefundTicketOrder();

        // 构建票信息
        PreviewRefundTicketVo.Ticket ticket = new PreviewRefundTicketVo.Ticket();
        ticket.setTktType("D");
        ticket.setCdsTicket(false);
        ticket.setTicketNo(pnrNmTicket.getTicketNo());
        ticket.setPsgType(psgType);
        ticket.setTicketPsgType(psgType);
        ticket.setName(passengerName);
        ticket.setPassengerNameSuffix(passengerName);
        ticket.setSpecialPassengerType(psgType);
        ticket.setPayType("");
        ticket.setCurrency("CNY");
        ticket.setEtTag("1");
        ticket.setAirline(pnrNmTicket.getTicketNo().substring(0, 3)); // 航司结算码
        ticket.setPnr(pnr.getPnrIcs());
        ticket.setCrsPnrNo(pnr.getPnrCrs());
        ticket.setIsCoupon("0");
        ticket.setIsAirportCntl("0");
        ticket.setExchangeTktNo("");
        ticket.setGovernmentPurchase(false);
        ticket.setCommissionRate("0.00");

        // 构建航段信息
        List<PreviewRefundTicketVo.Segment> segments = this.buildSegments(pnrNmTicket, pnr.getPnrId());
        ticket.setSegment(segments);

        // 设置市场航空公司（取第一个航段的航空公司）
        if (!segments.isEmpty()) {
            ticket.setMarketAirline(segments.get(0).getAirline());
        }

        // 查询价格信息
        this.setPriceInfo(ticket, pnrNm.getPnrId(), pnrNm.getPnrNmId(), isInfant, segments.size());

        // 设置二次验证信息
        PreviewRefundTicketVo.SecondFactor secondFactor = new PreviewRefundTicketVo.SecondFactor();
        secondFactor.setSecondFactorCode(secondFactorDto.getSecondFactorCode());
        secondFactor.setSecondFactorValue(secondFactorDto.getSecondFactorValue());
        ticket.setSecondFactor(secondFactor);

        refundTicketOrder.setTicket(ticket);

        // 设置其他订单信息
        refundTicketOrder.setOffice("LXA101");
        refundTicketOrder.setIata("08323431");
        refundTicketOrder.setAgent("49041");
        refundTicketOrder.setOperator("49041");
        refundTicketOrder.setConjunction("1");
        refundTicketOrder.setTicketManagementOrganizationCode("BSP");
        refundTicketOrder.setPrinterNo("1");
        refundTicketOrder.setRefundPrintNumber(null);
        refundTicketOrder.setReceiptPrinted("0");

        return refundTicketOrder;
    }

    /**
     * 构建航段信息
     */
    private List<PreviewRefundTicketVo.Segment> buildSegments(MnjxPnrNmTicket pnrNmTicket, String pnrId) {
        List<PreviewRefundTicketVo.Segment> segments = new ArrayList<>();

        // 查询航段信息
        List<String> segmentIds = new ArrayList<>();
        if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id())) {
            segmentIds.add(pnrNmTicket.getS1Id());
        }
        if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id())) {
            segmentIds.add(pnrNmTicket.getS2Id());
        }

        if (!segmentIds.isEmpty()) {
            List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.listByIds(segmentIds);

            for (int i = 0; i < pnrSegList.size(); i++) {
                MnjxPnrSeg pnrSeg = pnrSegList.get(i);
                PreviewRefundTicketVo.Segment segment = new PreviewRefundTicketVo.Segment();

                segment.setAirline(pnrSeg.getFlightNo().substring(0, 2));
                segment.setIsAble("1");
                segment.setDepartureCode(pnrSeg.getOrg());
                segment.setArriveCode(pnrSeg.getDst());
                segment.setRph("1-" + (i + 1));
                segment.setIsCheck(null);
                segment.setTktTag(pnrNmTicket.getTicketNo());
                segment.setE8Rph(String.valueOf(i + 1));

                // 设置票状态
                if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id()) && pnrNmTicket.getS1Id().equals(pnrSeg.getPnrSegId())) {
                    segment.setTicketStatus(StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus1()) ? pnrNmTicket.getTicketStatus1() : "OPEN FOR USE");
                } else if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id()) && pnrNmTicket.getS2Id().equals(pnrSeg.getPnrSegId())) {
                    segment.setTicketStatus(StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus2()) ? pnrNmTicket.getTicketStatus2() : "OPEN FOR USE");
                } else {
                    segment.setTicketStatus("OPEN FOR USE");
                }

                segment.setFlightNo(pnrSeg.getFlightNo());
                segment.setCabinCode(pnrSeg.getSellCabin());
                segment.setDepartureDate(pnrSeg.getFlightDate());
                segment.setDepartureTime(pnrSeg.getEstimateOff().substring(0, 2) + ":" + pnrSeg.getEstimateOff().substring(2) + ":00");
                segment.setSegmentType("2");

                segments.add(segment);
            }
        }

        return segments;
    }

    /**
     * 设置价格信息
     */
    private void setPriceInfo(PreviewRefundTicketVo.Ticket ticket, String pnrId, String pnrNmId,
                             boolean isInfant, int segmentCount) {
        // 7. 查询FN信息
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;
        BigDecimal commission = BigDecimal.ZERO;

        if (nmFn != null) {
            totalAmount = nmFn.getFPrice() != null ? nmFn.getFPrice() : BigDecimal.ZERO;
            cnTax = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqTax = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 8. 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnrId)
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                totalAmount = pnrFn.getFPrice() != null ? pnrFn.getFPrice() : BigDecimal.ZERO;
                cnTax = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqTax = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算代理费：每个航段5元
        commission = BigDecimal.valueOf(segmentCount * 5L);

        // 设置税费信息
        List<PreviewRefundTicketVo.Tax> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax cnTaxInfo = new PreviewRefundTicketVo.Tax();
            cnTaxInfo.setName("CN");
            cnTaxInfo.setValue(cnTax.toString());
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax yqTaxInfo = new PreviewRefundTicketVo.Tax();
            yqTaxInfo.setName("YQ");
            yqTaxInfo.setValue(yqTax.toString());
            taxes.add(yqTaxInfo);
        }

        BigDecimal totalTaxes = cnTax.add(yqTax);

        ticket.setTotalAmount(totalAmount.toString());
        ticket.setTotalTaxs(totalTaxes.toString());
        ticket.setTaxs(taxes);
        ticket.setCommission(commission.toString());
    }

    /**
     * 构建退票计算信息
     */
    private PreviewRefundTicketVo.RefundCompute buildRefundCompute(
            MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm, boolean isInfant) {

        PreviewRefundTicketVo.RefundCompute refundCompute = new PreviewRefundTicketVo.RefundCompute();
        refundCompute.setRemark(null);
        refundCompute.setConjunction("1.00");
        refundCompute.setCreditCard("");

        // 构建金额信息
        PreviewRefundTicketVo.Amount amount = new PreviewRefundTicketVo.Amount();

        // 查询价格信息
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNm.getPnrNmId())
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;

        if (nmFn != null) {
            totalAmount = nmFn.getFPrice() != null ? nmFn.getFPrice() : BigDecimal.ZERO;
            cnTax = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqTax = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                totalAmount = pnrFn.getFPrice() != null ? pnrFn.getFPrice() : BigDecimal.ZERO;
                cnTax = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqTax = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算航段数量
        int segmentCount = 0;
        if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id())) segmentCount++;
        if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id())) segmentCount++;

        // 计算代理费：每个航段5元
        BigDecimal commission = BigDecimal.valueOf(segmentCount * 5);

        // 9. 计算退票手续费：票面价的10%
        BigDecimal otherDeduction = totalAmount.multiply(BigDecimal.valueOf(0.1))
                .setScale(2, RoundingMode.HALF_UP);

        // 计算总税费
        BigDecimal totalTaxes = cnTax.add(yqTax);

        // 10. 计算应退费用：票面价+总税费-代理费-退票手续费
        BigDecimal netRefund = totalAmount.add(totalTaxes).subtract(commission).subtract(otherDeduction);

        // 设置税费列表
        List<PreviewRefundTicketVo.Tax> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax cnTaxInfo = new PreviewRefundTicketVo.Tax();
            cnTaxInfo.setName("CN");
            cnTaxInfo.setValue(cnTax.toString());
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax yqTaxInfo = new PreviewRefundTicketVo.Tax();
            yqTaxInfo.setName("YQ");
            yqTaxInfo.setValue(yqTax.toString());
            taxes.add(yqTaxInfo);
        }

        amount.setCommision(NumberUtils.formatBigDecimalStr(commission.toString()));
        amount.setTotalAmount(totalAmount.toString());
        amount.setCommisionRate("0.00");
        amount.setNetRefund(netRefund.toString());
        amount.setOtherDeduction(otherDeduction.toString());
        amount.setTaxs(taxes);
        amount.setTotalTaxs(totalTaxes.toString());
        amount.setTicketNo(null);

        refundCompute.setAmount(amount);

        return refundCompute;
    }
}
