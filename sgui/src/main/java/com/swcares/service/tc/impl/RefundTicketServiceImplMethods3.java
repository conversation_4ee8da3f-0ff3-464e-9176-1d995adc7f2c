// 这些方法需要添加到RefundTicketServiceImpl中

    /**
     * 处理单个票号的退票
     */
    private BatchAutoRefundVo.RefundResult processTicketRefund(BatchAutoRefundDto.TicketInfo ticketInfo) {
        BatchAutoRefundVo.RefundResult refundResult = new BatchAutoRefundVo.RefundResult();
        
        try {
            // 处理票号格式，去掉"-"
            String formattedTicketNo = ticketInfo.getTicketNo().replace("-", "");

            // 1. 通过ticketNo查询mnjx_pnr_nm_ticket表，获取tnId
            MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                    .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                    .one();

            if (pnrNmTicket == null) {
                return this.buildFailedRefundResult(ticketInfo, "未查到该票号");
            }

            // 2. 通过printNo查询mnjx_printer表，获取printerId
            MnjxPrinter printer = iMnjxPrinterService.lambdaQuery()
                    .eq(MnjxPrinter::getPrinterNo, ticketInfo.getPrintNo())
                    .one();

            if (printer == null) {
                return this.buildFailedRefundResult(ticketInfo, "未查到打票机信息");
            }

            // 3. 通过tnId和printerId查询mnjx_pnr_nm_tn表
            MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getTnId, pnrNmTicket.getPnrNmTnId())
                    .eq(MnjxPnrNmTn::getPrinterId, printer.getPrinterId())
                    .one();

            // 4. 如果没有查到tn数据，构建refundResult，success为false
            if (pnrNmTn == null) {
                return this.buildFailedRefundResult(ticketInfo, "未查到该票号");
            }

            // 5. 通过查询到的tn数据，查询pnrNm或nmXn（再查到pnrNm），查询pnr数据
            String pnrNmId = this.getPnrNmId(pnrNmTn);
            if (StrUtil.isEmpty(pnrNmId)) {
                return this.buildFailedRefundResult(ticketInfo, "未查到旅客信息");
            }

            MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
            if (pnrNm == null) {
                return this.buildFailedRefundResult(ticketInfo, "未查到旅客信息");
            }

            MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
            if (pnr == null) {
                return this.buildFailedRefundResult(ticketInfo, "未查到PNR信息");
            }

            // 6. 如果pnr的status不是DEL，那么按照PNR状态不是DEL的response.json的数据返回
            if (!"DEL".equals(pnr.getStatus())) {
                return this.buildNonDelPnrResult(ticketInfo);
            }

            // 7. 如果pnr的status是DEL，执行退票操作
            return this.executeRefund(ticketInfo, pnrNmTicket, pnrNmTn, pnr, pnrNm);

        } catch (Exception e) {
            return this.buildFailedRefundResult(ticketInfo, "退票处理异常：" + e.getMessage());
        }
    }

    /**
     * 获取pnrNmId
     */
    private String getPnrNmId(MnjxPnrNmTn pnrNmTn) {
        if (StrUtil.isNotEmpty(pnrNmTn.getNmXnId())) {
            // 婴儿票，通过nmXnId查询pnrNmId
            MnjxNmXn nmXn = iMnjxNmXnService.getById(pnrNmTn.getNmXnId());
            return nmXn != null ? nmXn.getPnrNmId() : null;
        } else {
            // 成人/儿童票，直接使用pnrNmId
            return pnrNmTn.getPnrNmId();
        }
    }

    /**
     * 构建失败的退票结果
     */
    private BatchAutoRefundVo.RefundResult buildFailedRefundResult(BatchAutoRefundDto.TicketInfo ticketInfo, String description) {
        BatchAutoRefundVo.RefundResult refundResult = new BatchAutoRefundVo.RefundResult();
        refundResult.setTicketNo(this.formatTicketNo(ticketInfo.getTicketNo()));
        refundResult.setSuccess(false);
        refundResult.setAmount(null);
        refundResult.setTrfdno("");
        refundResult.setPrintNo(ticketInfo.getPrintNo());
        refundResult.setErrorCode(null);
        refundResult.setDescription(description);
        refundResult.setTransactionNo(null);
        refundResult.setSatTransactionNo(null);
        return refundResult;
    }

    /**
     * 构建PNR状态不是DEL的结果
     */
    private BatchAutoRefundVo.RefundResult buildNonDelPnrResult(BatchAutoRefundDto.TicketInfo ticketInfo) {
        BatchAutoRefundVo.RefundResult refundResult = new BatchAutoRefundVo.RefundResult();
        refundResult.setTicketNo(this.formatTicketNo(ticketInfo.getTicketNo()));
        refundResult.setSuccess(false);
        refundResult.setAmount(null);
        refundResult.setTrfdno("");
        refundResult.setPrintNo(ticketInfo.getPrintNo());
        refundResult.setErrorCode(null);
        refundResult.setDescription("PNR状态不允许退票");
        refundResult.setTransactionNo(null);
        refundResult.setSatTransactionNo(null);
        return refundResult;
    }

    /**
     * 执行退票操作
     */
    private BatchAutoRefundVo.RefundResult executeRefund(
            BatchAutoRefundDto.TicketInfo ticketInfo, MnjxPnrNmTicket pnrNmTicket, 
            MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm) {

        try {
            // 1. 将对应的pnrNmTicket的ticketStatus1和ticketStatus2都设置为REFUNDED
            this.updateTicketStatus(pnrNmTicket);

            // 2. 创建退票单号
            String refundNo = this.generateRefundNo(ticketInfo.getTicketNo());

            // 3. 退票单数据存储
            this.saveRefundTicket(refundNo, ticketInfo.getTicketNo(), pnrNmTn, pnr, pnrNm);

            // 4. 退票成功后，构建refundResult
            BatchAutoRefundVo.RefundResult refundResult = new BatchAutoRefundVo.RefundResult();
            refundResult.setTicketNo(this.formatTicketNo(ticketInfo.getTicketNo()));
            refundResult.setSuccess(true);
            refundResult.setAmount(null);
            refundResult.setTrfdno(refundNo);
            refundResult.setPrintNo(ticketInfo.getPrintNo());
            refundResult.setErrorCode(null);
            refundResult.setDescription(null);
            refundResult.setTransactionNo(null);
            refundResult.setSatTransactionNo(null);

            return refundResult;

        } catch (Exception e) {
            return this.buildFailedRefundResult(ticketInfo, "退票执行失败：" + e.getMessage());
        }
    }

    /**
     * 更新票状态为REFUNDED
     */
    private void updateTicketStatus(MnjxPnrNmTicket pnrNmTicket) {
        boolean needUpdate = false;
        
        if (StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus1())) {
            pnrNmTicket.setTicketStatus1("REFUNDED");
            needUpdate = true;
        }
        
        if (StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus2())) {
            pnrNmTicket.setTicketStatus2("REFUNDED");
            needUpdate = true;
        }
        
        if (needUpdate) {
            iMnjxPnrNmTicketService.updateById(pnrNmTicket);
        }
    }

    /**
     * 生成退票单号
     */
    private String generateRefundNo(String ticketNo) {
        // 前三位为航司结算码（和当前票号前三位一样）
        String airlineCode = ticketNo.substring(0, 3);
        
        // 查询表中是否已有退票单数据
        String maxRefundNo = iMnjxRefundTicketService.lambdaQuery()
                .select(MnjxRefundTicket::getRefundNo)
                .like(MnjxRefundTicket::getRefundNo, airlineCode + "%")
                .orderByDesc(MnjxRefundTicket::getRefundNo)
                .last("LIMIT 1")
                .one()
                .getRefundNo();

        long nextNumber;
        if (StrUtil.isEmpty(maxRefundNo)) {
            // 如果没有，则从000000000开始
            nextNumber = 0L;
        } else {
            // 获取表中移除前三位的最大refundNo，开始自增
            String numberPart = maxRefundNo.substring(3);
            nextNumber = Long.parseLong(numberPart) + 1;
        }

        // 格式化为9位数字
        return airlineCode + String.format("%09d", nextNumber);
    }

    /**
     * 保存退票单数据
     */
    private void saveRefundTicket(String refundNo, String ticketNo, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm) {
        // 查询价格信息
        boolean isInfant = StrUtil.isNotEmpty(pnrNmTn.getNmXnId());
        
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNm.getPnrNmId())
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal collection = BigDecimal.ZERO; // 票价（不含税）
        BigDecimal cnPrice = BigDecimal.ZERO;    // CN基建费价格
        BigDecimal yqPrice = BigDecimal.ZERO;    // 燃油附加费价格

        if (nmFn != null) {
            collection = nmFn.getSPrice() != null ? nmFn.getSPrice() : BigDecimal.ZERO;
            cnPrice = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqPrice = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                collection = pnrFn.getSPrice() != null ? pnrFn.getSPrice() : BigDecimal.ZERO;
                cnPrice = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqPrice = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算退票手续费：票面价的10%
        BigDecimal comm = collection.multiply(BigDecimal.valueOf(0.1)).setScale(2, RoundingMode.HALF_UP);

        // 计算实际退款金额：票价+税费-退票手续费
        BigDecimal netRefund = collection.add(cnPrice).add(yqPrice).subtract(comm);

        // 创建退票单记录
        MnjxRefundTicket refundTicket = new MnjxRefundTicket();
        refundTicket.setRefundId(IdUtils.getSnowflake(1, 1).nextIdStr());
        refundTicket.setRefundNo(refundNo);
        refundTicket.setTicketNo(ticketNo);
        refundTicket.setCollection(collection);
        refundTicket.setCnPrice(cnPrice);
        refundTicket.setYqPrice(yqPrice);
        refundTicket.setComm(comm);
        refundTicket.setNetRefund(netRefund);

        iMnjxRefundTicketService.save(refundTicket);
    }
