// 这些方法需要添加到RefundTicketServiceImpl中

    /**
     * 格式化票号
     */
    private String formatTicketNo(String ticketNo) {
        if (StrUtil.isEmpty(ticketNo)) {
            return ticketNo;
        }
        
        // 如果已经包含"-"，直接返回
        if (ticketNo.contains("-")) {
            return ticketNo;
        }
        
        // 如果长度足够，添加"-"
        if (ticketNo.length() >= 10) {
            String prefix = ticketNo.substring(0, 3);
            String suffix = ticketNo.substring(3);
            return prefix + "-" + suffix;
        }
        
        return ticketNo;
    }

    /**
     * 构建退票费用响应DTO
     */
    private BatchFindRefundFeeVo.QueryRefundFeeAggregateRespDTO buildRefundFeeRespDTO(
            MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm, 
            boolean isInfant, String originalTicketNo) {

        BatchFindRefundFeeVo.QueryRefundFeeAggregateRespDTO respDTO = 
            new BatchFindRefundFeeVo.QueryRefundFeeAggregateRespDTO();

        // 设置基本信息
        respDTO.setConjunction("1.00");
        respDTO.setRemark(null);
        respDTO.setCreditCard("");
        respDTO.setPayType("CASH");
        respDTO.setCurrency("");
        respDTO.setSegList(new ArrayList<>());
        respDTO.setMsg(null);

        // 5. amount参考退前预览计算退票中各价格的处理方式
        BatchFindRefundFeeVo.Amount amount = this.buildRefundAmount(
            pnrNmTicket, pnrNmTn, pnr, pnrNm, isInfant, originalTicketNo);
        respDTO.setAmount(amount);

        return respDTO;
    }

    /**
     * 构建退票金额信息
     */
    private BatchFindRefundFeeVo.Amount buildRefundAmount(
            MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm, 
            boolean isInfant, String originalTicketNo) {

        BatchFindRefundFeeVo.Amount amount = new BatchFindRefundFeeVo.Amount();

        // 3. 查询旅客运价，如果没有则通过pnrId查询pnr级别的运价
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNm.getPnrNmId())
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;

        if (nmFn != null) {
            totalAmount = nmFn.getFPrice() != null ? nmFn.getFPrice() : BigDecimal.ZERO;
            cnTax = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqTax = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                totalAmount = pnrFn.getFPrice() != null ? pnrFn.getFPrice() : BigDecimal.ZERO;
                cnTax = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqTax = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算航段数量
        int segmentCount = 0;
        if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id())) segmentCount++;
        if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id())) segmentCount++;

        // 计算代理费：每个航段5元
        BigDecimal commission = BigDecimal.valueOf(segmentCount * 5);

        // 计算退票手续费：票面价的10%
        BigDecimal otherDeduction = totalAmount.multiply(BigDecimal.valueOf(0.1))
                .setScale(2, RoundingMode.HALF_UP);

        // 计算总税费
        BigDecimal totalTaxes = cnTax.add(yqTax);

        // 计算应退费用：票面价+总税费-代理费-退票手续费
        BigDecimal netRefund = totalAmount.add(totalTaxes).subtract(commission).subtract(otherDeduction);

        // 设置税费列表
        List<BatchFindRefundFeeVo.Tax> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            BatchFindRefundFeeVo.Tax cnTaxInfo = new BatchFindRefundFeeVo.Tax();
            cnTaxInfo.setName("CN");
            cnTaxInfo.setValue(cnTax.toString());
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            BatchFindRefundFeeVo.Tax yqTaxInfo = new BatchFindRefundFeeVo.Tax();
            yqTaxInfo.setName("YQ");
            yqTaxInfo.setValue(yqTax.toString());
            taxes.add(yqTaxInfo);
        }

        // 设置金额信息
        amount.setCommision(NumberUtils.formatBigDecimalStr(commission.toString()));
        amount.setTotalAmount(totalAmount.toString());
        amount.setCommisionRate("0.00");
        amount.setNetRefund(netRefund.toString());
        amount.setOtherDeduction(otherDeduction.toString());
        amount.setTaxs(taxes);
        amount.setTotalTaxs(totalTaxes.toString());
        amount.setTicketNo(this.formatTicketNo(originalTicketNo));

        return amount;
    }
