package com.swcares.service.tc;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:18
 */
public interface ITcV2Service {

    ETReceiptDictEntryVo getETReceiptDictEntry();

    QueryOfficeInformationVo queryOfficeInformation(QueryOfficeInformationDto dto);

    /**
     * 按票号查询客票详情
     *
     * @param dto 查询参数
     * @return 客票详情
     * @throws SguiResultException 异常
     */
    QueryTicketDetailVo queryTicketDetail(QueryTicketDetailDto dto) throws SguiResultException;

    /**
     * 通过DETR查询票面信息
     *
     * @param dto 查询参数
     * @return 票面信息
     * @throws SguiResultException 异常
     */
    QueryTicketByDetrVo queryTicketByDetr(QueryTicketByDetrDto dto) throws SguiResultException;

    /**
     * 通过RTKT查询票面信息
     *
     * @param ticketNo 票号
     * @return 票面信息
     * @throws SguiResultException 异常
     */
    TicketByRtktVo queryTicketByRtkt(String ticketNo) throws SguiResultException;

    /**
     * 按PNR查询客票信息
     *
     * @param dto 查询参数
     * @return 客票信息列表
     * @throws SguiResultException 异常
     */
    List<QueryTicketByPnrVo> queryTicketByPnr(QueryTicketByPnrDto dto) throws SguiResultException;

    /**
     * 按证件号查询客票信息
     *
     * @param dto 查询参数
     * @return 客票信息列表
     * @throws SguiResultException 异常
     */
    List<QueryTicketByPnrVo> queryTicketDigestsByCert(QueryTicketByCertDto dto) throws SguiResultException;

    /**
     * 退前预览
     *
     * @param dto 查询参数
     * @return 退前预览信息
     * @throws SguiResultException 异常
     */
    PreviewRefundTicketVo previewRefundTicket(PreviewRefundTicketDto dto) throws SguiResultException;

    /**
     * 初始加载退票信息
     *
     * @param dto 查询参数
     * @return 退票信息
     * @throws SguiResultException 异常
     */
    FindRefundTicketVo findRefundTicket(FindRefundTicketDto dto) throws SguiResultException;

    /**
     * 退票PNR信息查询
     *
     * @param dto 查询参数
     * @return PNR信息
     * @throws SguiResultException 异常
     */
    QueryPnrMessageVo queryPnrMessage(QueryPnrMessageDto dto) throws SguiResultException;

    /**
     * 计算退票价格
     *
     * @param dto 查询参数
     * @return 退票价格信息
     * @throws SguiResultException 异常
     */
    BatchFindRefundFeeVo batchFindRefundFee(BatchFindRefundFeeDto dto) throws SguiResultException;

    /**
     * 自动退票
     *
     * @param dto 查询参数
     * @return 退票结果
     * @throws SguiResultException 异常
     */
    BatchAutoRefundVo batchAutoRefund(BatchAutoRefundDto dto) throws SguiResultException;
}
