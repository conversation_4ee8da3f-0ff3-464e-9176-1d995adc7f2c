package com.swcares.service.tc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.QueryTicketByCertDto;
import com.swcares.obj.dto.QueryTicketByDetrDto;
import com.swcares.obj.dto.QueryTicketByPnrDto;
import com.swcares.obj.dto.QueryTicketDetailDto;
import com.swcares.obj.vo.QueryTicketByDetrVo;
import com.swcares.obj.vo.QueryTicketByPnrVo;
import com.swcares.obj.vo.QueryTicketDetailVo;
import com.swcares.obj.vo.TicketByRtktVo;
import com.swcares.service.*;
import com.swcares.service.tc.ITicketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客票服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/30 14:00
 */
@Slf4j
@Service
public class TicketServiceImpl implements ITicketService {

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Override
    public QueryTicketDetailVo queryTicketDetail(QueryTicketDetailDto dto) throws SguiResultException {
        if (StrUtil.isEmpty(dto.getTicketNo())) {
            throw new SguiResultException("票号不能为空");
        }
        if (ObjectUtil.isEmpty(dto.getSecondFactor()) || StrUtil.isEmpty(dto.getSecondFactor().getSecondFactorCode())) {
            throw new SguiResultException("缺少查询条件");
        }

        // 通过票号查询mnjx_pnr_nm_ticket表
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, dto.getTicketNo().replace("-", ""))
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("没有找到票面信息");
        }

        // 获取票与航段的关系信息
        String pnrNmTnId = pnrNmTicket.getPnrNmTnId();
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTnId);
        if (pnrNmTn == null) {
            throw new SguiResultException("没有找到票面信息");
        }

        // 获取旅客信息
        MnjxPnrNm pnrNm = null;
        MnjxNmXn nmXn = null;
        MnjxPnr pnr = null;
        String pnrId = null;
        String pnrCrs = null;

        if (StrUtil.isNotEmpty(pnrNmTn.getPnrNmId())) {
            // 非婴儿票
            pnrNm = iMnjxPnrNmService.getById(pnrNmTn.getPnrNmId());
            if (pnrNm == null) {
                throw new SguiResultException("没有找到票面信息");
            }
            pnrId = pnrNm.getPnrId();
        } else if (StrUtil.isNotEmpty(pnrNmTn.getNmXnId())) {
            // 婴儿票
            nmXn = iMnjxNmXnService.getById(pnrNmTn.getNmXnId());
            if (nmXn == null) {
                throw new SguiResultException("没有找到票面信息");
            }
            pnrNm = iMnjxPnrNmService.getById(nmXn.getPnrNmId());
            if (pnrNm == null) {
                throw new SguiResultException("没有找到票面信息");
            }
            pnrId = pnrNm.getPnrId();
        } else {
            throw new SguiResultException("没有找到票面信息");
        }

        // 获取PNR信息
        pnr = iMnjxPnrService.getById(pnrId);
        if (pnr == null) {
            throw new SguiResultException("没有找到票面信息");
        }
        pnrCrs = pnr.getPnrCrs();

        // 根据secondFactorCode和secondFactorValue验证
        if (dto.getSecondFactor() != null) {
            String secondFactorCode = dto.getSecondFactor().getSecondFactorCode();
            String secondFactorValue = dto.getSecondFactor().getSecondFactorValue();

            if (StrUtil.isNotEmpty(secondFactorCode) && StrUtil.isNotEmpty(secondFactorValue)) {
                if ("NI".equals(secondFactorCode)) {
                    // 通过身份证号验证
                    List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                            .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                            .eq(MnjxNmSsr::getSsrType, "FOID")
                            .list();

                    boolean found = false;
                    if (CollUtil.isNotEmpty(ssrList)) {
                        for (MnjxNmSsr ssr : ssrList) {
                            if (StrUtil.isNotEmpty(ssr.getSsrInfo()) &&
                                    ssr.getSsrInfo().contains(" NI") &&
                                    secondFactorValue.equals(ssr.getSsrInfo().split(" NI")[1].split("/")[0])) {
                                found = true;
                                break;
                            }
                        }
                    }

                    if (!found) {
                        throw new SguiResultException("没有找到票面信息");
                    }
                } else if ("PP".equals(secondFactorCode)) {
                    // 通过护照号验证
                    List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                            .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                            .eq(MnjxNmSsr::getSsrType, "DOCS")
                            .list();

                    boolean found = false;
                    if (CollUtil.isNotEmpty(ssrList)) {
                        for (MnjxNmSsr ssr : ssrList) {
                            if (StrUtil.isNotEmpty(ssr.getSsrInfo())) {
                                // 解析DOCS格式：SSR DOCS CA HK1 P/CN/32322110/CN/10MAY20/M/22MAY22/ZHANG/SAN/H/P1
                                // 需要提取护照号码部分（第三个斜杠后的内容）
                                String ssrInfo = ssr.getSsrInfo();
                                String[] parts = ssrInfo.split("/");
                                if (parts.length >= 3) {
                                    // 护照号在第三部分
                                    String passportNumber = parts[2];
                                    if (passportNumber.equals(secondFactorValue)) {
                                        found = true;
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    if (!found) {
                        throw new SguiResultException("没有找到票面信息");
                    }
                } else if ("UU".equals(secondFactorCode)) {
                    // 通过特殊身份证查询
                    List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                            .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                            .eq(MnjxNmSsr::getSsrType, "FOID")
                            .list();

                    boolean found = false;
                    if (CollUtil.isNotEmpty(ssrList)) {
                        for (MnjxNmSsr ssr : ssrList) {
                            if (StrUtil.isNotEmpty(ssr.getSsrInfo()) &&
                                    ssr.getSsrInfo().contains(" UU") &&
                                    secondFactorValue.equals(ssr.getSsrInfo().split(" UU")[1].split("/")[0])) {
                                found = true;
                                break;
                            }
                        }
                    }

                    if (!found) {
                        throw new SguiResultException("没有找到票面信息");
                    }
                } else if ("NM".equals(secondFactorCode)) {
                    // 通过旅客姓名验证
                    String name = nmXn != null ? nmXn.getXnCname() : pnrNm.getName();
                    if (!secondFactorValue.equals(name)) {
                        throw new SguiResultException("没有找到票面信息");
                    }
                } else if ("CN".equals(secondFactorCode)) {
                    // 通过PNR验证
                    if (!secondFactorValue.equals(pnrCrs)) {
                        throw new SguiResultException("没有找到票面信息");
                    }
                }
            }
        }

        // 构建返回数据
        QueryTicketDetailVo vo = new QueryTicketDetailVo();
        vo.setEtNumber(pnrNmTicket.getTicketNo().substring(0, 3) + "-" + pnrNmTicket.getTicketNo().substring(3));

        // 设置旅客信息
        if (nmXn != null) {
            // 婴儿票
            vo.setPassengerName(nmXn.getXnCname());
            vo.setPassengerNameSuffix(nmXn.getXnCname());
            vo.setFullName(nmXn.getXnCname());
            vo.setPassengerType("INF");
            vo.setTicketPsgType("INF");
            vo.setSpecialPassengerType("INF");
        } else {
            // 成人或儿童票
            vo.setPassengerName(pnrNm.getName());
            vo.setPassengerNameSuffix(pnrNm.getName());
            vo.setFullName(pnrNm.getName());

            if ("1".equals(pnrNm.getPsgType())) {
                vo.setPassengerType("CHD");
                vo.setTicketPsgType("CHD");
                vo.setSpecialPassengerType("CHD");
            } else {
                vo.setPassengerType("ADT");
                vo.setTicketPsgType("ADT");
                vo.setSpecialPassengerType("ADT");
            }
        }

        vo.setNameSuffix("");
        vo.setTicketTypeCode("D");
        vo.setCdsTicket(false);
        vo.setEtType("BSP");
        vo.setGovernmentPurchase(false);
        vo.setReceiptPrinted("1".equals(pnrNmTicket.getReceiptPrint()));
        vo.setConjunctiveTicket("");

        // 设置出票日期和时间
        String currentDate = DateUtil.today();
        vo.setIssueTicketDate(currentDate);
        vo.setIssueTime(DateUtil.format(DateUtil.date(), "HH:mm"));

        // 设置二次验证因素
        if (dto.getSecondFactor() != null) {
            QueryTicketDetailVo.SecondFactorVo secondFactorVo = new QueryTicketDetailVo.SecondFactorVo();
            secondFactorVo.setSecondFactorCode(dto.getSecondFactor().getSecondFactorCode());
            secondFactorVo.setSecondFactorValue(dto.getSecondFactor().getSecondFactorValue());
            vo.setSecondFactor(secondFactorVo);
        }

        // 设置航段信息
        List<QueryTicketDetailVo.AirSegVo> airSegList = new ArrayList<>();

        // 获取航段信息
        if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id())) {
            MnjxPnrSeg seg1 = iMnjxPnrSegService.getById(pnrNmTicket.getS1Id());
            if (seg1 != null) {
                QueryTicketDetailVo.AirSegVo airSegVo = buildAirSegVo(seg1, pnr, 1);
                airSegVo.setTicketStatus(pnrNmTicket.getTicketStatus1());
                airSegList.add(airSegVo);
            }
        }

        if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id())) {
            MnjxPnrSeg seg2 = iMnjxPnrSegService.getById(pnrNmTicket.getS2Id());
            if (seg2 != null) {
                QueryTicketDetailVo.AirSegVo airSegVo = buildAirSegVo(seg2, pnr, 2);
                airSegVo.setTicketStatus(pnrNmTicket.getTicketStatus2());
                airSegList.add(airSegVo);
            }
        }

        vo.setAirSeg(airSegList);
        vo.setAirSegCrsPnr(pnrCrs);

        return vo;
    }

    /**
     * 构建航段信息VO
     *
     * @param seg          航段实体
     * @param pnr          PNR
     * @param segmentIndex 航段序号
     * @return 航段信息VO
     */
    private QueryTicketDetailVo.AirSegVo buildAirSegVo(MnjxPnrSeg seg, MnjxPnr pnr, int segmentIndex) {
        QueryTicketDetailVo.AirSegVo airSegVo = new QueryTicketDetailVo.AirSegVo();

        airSegVo.setDepAirportCode(seg.getOrg());
        airSegVo.setCrsPnrNo(pnr.getPnrCrs());
        airSegVo.setCabin(seg.getSellCabin());
        airSegVo.setFlightNo(seg.getFlightNo());

        // 设置出发时间
        String depDate = seg.getFlightDate();
        String depTime = seg.getEstimateOff();
        if (StrUtil.isNotEmpty(depDate) && StrUtil.isNotEmpty(depTime)) {
            airSegVo.setDepTime(depDate + " " + depTime.substring(0, 2) + ":" + depTime.substring(2) + ":00");
        }

        // 设置到达时间
        String arrTime = seg.getEstimateArr();
        if (StrUtil.isNotEmpty(depDate) && StrUtil.isNotEmpty(arrTime)) {
            airSegVo.setArrTime(depDate + " " + arrTime.substring(0, 2) + ":" + arrTime.substring(2) + ":00");
        }

        airSegVo.setArrAirportCode(seg.getDst());

        airSegVo.setPnrNo(pnr.getPnrIcs());
        airSegVo.setCrsType("1E");

        // 设置航司
        String flightNo = seg.getFlightNo();
        if (StrUtil.isNotEmpty(flightNo) && flightNo.length() >= 2) {
            airSegVo.setAirline(flightNo.substring(0, 2));
        }

        airSegVo.setDepartureDate(seg.getFlightDate());
        airSegVo.setChangeReason("");
        airSegVo.setSegmentIndex(segmentIndex);

        // 设置ARNK和OPEN标志
        if ("SA".equals(seg.getPnrSegType())) {
            airSegVo.setSegANRK(true);
            airSegVo.setSegOPEN(false);
        } else if ("SS".equals(seg.getPnrSegType())) {
            airSegVo.setSegANRK(false);
            airSegVo.setSegOPEN(true);
        } else {
            airSegVo.setSegANRK(false);
            airSegVo.setSegOPEN(false);
        }

        return airSegVo;
    }

    @Override
    public QueryTicketByDetrVo queryTicketByDetr(QueryTicketByDetrDto dto) throws SguiResultException {
        // 参数验证
        if (StrUtil.isEmpty(dto.getTicketNo())) {
            throw new SguiResultException("票号不能为空");
        }

        // 验证detrType
        if (StrUtil.isEmpty(dto.getDetrType()) || !Arrays.asList("D", "H", "X", "F").contains(dto.getDetrType())) {
            throw new SguiResultException("选项参数错误");
        }

        // 去掉票号中的"-"
        String ticketNo = dto.getTicketNo().replace("-", "");

        // 通过票号查询mnjx_pnr_nm_ticket表
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, ticketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("无票面信息");
        }

        // 获取票与航段的关系信息
        String pnrNmTnId = pnrNmTicket.getPnrNmTnId();
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTnId);
        if (pnrNmTn == null) {
            throw new SguiResultException("无票面信息");
        }

        // 获取旅客信息
        String pnrNmId = pnrNmTn.getPnrNmId();
        String nmXnId = pnrNmTn.getNmXnId();

        // 旅客姓名和类型
        String passengerName;
        String passengerType = ""; // 空为成人，CHD为儿童，INF为婴儿
        String xnBirthDate = null;
        MnjxPnrNm pnrNm;
        // 获取旅客信息
        if (StrUtil.isNotEmpty(pnrNmId)) {
            // 非婴儿旅客
            pnrNm = iMnjxPnrNmService.getById(pnrNmId);
            if (pnrNm == null) {
                throw new SguiResultException("无票面信息");
            }
            passengerName = pnrNm.getName();

            // 判断是否为儿童
            if ("1".equals(pnrNm.getPsgType())) {
                passengerType = "CHD";
            }
        } else if (StrUtil.isNotEmpty(nmXnId)) {
            // 婴儿旅客
            MnjxNmXn nmXn = iMnjxNmXnService.getById(nmXnId);
            if (nmXn == null) {
                throw new SguiResultException("无票面信息");
            }
            passengerName = nmXn.getXnCname();
            passengerType = "INF";
            xnBirthDate = nmXn.getXnBirthday();
            pnrNmId = nmXn.getPnrNmId();
            pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        } else {
            throw new SguiResultException("无票面信息");
        }
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        MnjxNmSsr foid = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                .eq(MnjxNmSsr::getSsrType, "FOID")
                .one();

        // 二次筛选条件验证
        if (ObjectUtil.isNotEmpty(dto.getSecondFactor()) && StrUtil.isNotEmpty(dto.getSecondFactor().getSecondFactorCode())) {
            // 如果是通过身份证筛选
            if ("NI".equals(dto.getSecondFactor().getSecondFactorCode())) {
                if (!dto.getSecondFactor().getSecondFactorValue().equals(foid.getInputValue().split("NI")[1].split("/")[0])) {
                    throw new SguiResultException("无票面信息");
                }
            } else if ("CN".equals(dto.getSecondFactor().getSecondFactorCode())) {
                if (!dto.getSecondFactor().getSecondFactorValue().equals(pnr.getPnrCrs())) {
                    throw new SguiResultException("无票面信息");
                }
            }
        }


        // 构建返回对象
        QueryTicketByDetrVo vo = new QueryTicketByDetrVo();

        // 根据detrType类型处理不同的返回数据
        switch (dto.getDetrType()) {
            case "D":
                // 获取航段信息
                String s1Id = pnrNmTicket.getS1Id();
                String s2Id = pnrNmTicket.getS2Id();

                MnjxPnrSeg firstSeg = null;
                MnjxPnrSeg lastSeg = null;

                if (StrUtil.isNotEmpty(s1Id)) {
                    firstSeg = iMnjxPnrSegService.getById(s1Id);
                }

                if (StrUtil.isNotEmpty(s2Id)) {
                    lastSeg = iMnjxPnrSegService.getById(s2Id);
                }

                if (firstSeg == null && lastSeg == null) {
                    throw new SguiResultException("无票面信息");
                }
                // 票面信息
                String openSourceText = this.buildOpenSourceText(dto.getTicketNo(), passengerName, passengerType, xnBirthDate, firstSeg, lastSeg, pnrNmTn.getIssuedTime(), pnrNmTicket, pnr);
                vo.setOpenSourceText(openSourceText);

                // 设置二次筛选条件
                QueryTicketByDetrVo.SecondFactor secondFactor = new QueryTicketByDetrVo.SecondFactor();
                secondFactor.setSecondFactorCode("NM");
                secondFactor.setSecondFactorValue(passengerName);
                vo.setSecondFactor(secondFactor);
                break;

            case "H":
                // 客票历史信息
                String tktHistoryText = this.buildTktHistoryText(dto.getTicketNo(), passengerName, pnrNmTn);
                vo.setTktHistoryText(tktHistoryText);
                break;

            case "F":
                // 证件及其他信息
                QueryTicketByDetrVo.Credential credential = this.buildCredential(dto.getTicketNo(), passengerName, passengerType, foid.getInputValue(), xnBirthDate);
                vo.setCredential(credential);
                break;

            case "X":
                // 税项明细，暂时返回空数据
                vo.setCredential(null);
                vo.setTicketFareInfoText("");
                vo.setOpenSourceText(null);
                vo.setSecondFactor(null);
                vo.setTktHistoryText(null);
                break;

            default:
                throw new SguiResultException("无票面信息");
        }

        return vo;
    }

    @Override
    public TicketByRtktVo queryTicketByRtkt(String ticketNo) throws SguiResultException {
        // 参数验证
        if (StrUtil.isEmpty(ticketNo)) {
            throw new SguiResultException("票号不能为空");
        }

        // 处理票号格式，去掉"-"
        String formattedTicketNo = ticketNo.replace("-", "");

        // 通过票号查询mnjx_pnr_nm_ticket表
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("无票面信息");
        }

        // 获取票与航段的关系信息
        String pnrNmTnId = pnrNmTicket.getPnrNmTnId();
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTnId);
        if (pnrNmTn == null) {
            throw new SguiResultException("无票面信息");
        }

        // 获取旅客信息
        String pnrNmId = pnrNmTn.getPnrNmId();
        MnjxPnrNm pnrNm = null;
        MnjxNmXn nmXn = null;
        if (StrUtil.isNotEmpty(pnrNmId)) {
            pnrNm = iMnjxPnrNmService.getById(pnrNmId);
            if (pnrNm == null) {
                throw new SguiResultException("无票面信息");
            }
        } else {
            nmXn = iMnjxNmXnService.getById(pnrNmTn.getNmXnId());
            pnrNmId = nmXn.getPnrNmId();
            pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        }
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());

        // 获取航段信息
        String s1Id = pnrNmTicket.getS1Id();
        String s2Id = pnrNmTicket.getS2Id();

        MnjxPnrSeg firstSeg = null;
        MnjxPnrSeg lastSeg = null;

        if (StrUtil.isNotEmpty(s1Id)) {
            firstSeg = iMnjxPnrSegService.getById(s1Id);
            if (firstSeg == null) {
                throw new SguiResultException("无票面信息");
            }
        }

        if (StrUtil.isNotEmpty(s2Id)) {
            lastSeg = iMnjxPnrSegService.getById(s2Id);
        } else if (firstSeg != null) {
            lastSeg = firstSeg;
        }

        if (firstSeg == null || lastSeg == null) {
            throw new SguiResultException("无票面信息");
        }

        // 构建返回对象
        return this.buildTicketByRtktVo(pnr, pnrNmTicket, pnrNmTn, pnrNm, nmXn, firstSeg, lastSeg);
    }

    /**
     * 构建openSourceText
     *
     * @param ticketNo      票号
     * @param passengerName 旅客姓名
     * @param passengerType 旅客类型
     * @param xnBirthDate   出生日期（婴儿）
     * @param firstSeg      第一航段
     * @param lastSeg       最后航段
     * @param issuedTime    出票时间
     * @param pnrNmTicket
     * @return Base64编码后的openSourceText
     */
    private String buildOpenSourceText(String ticketNo, String passengerName, String passengerType, String xnBirthDate,
                                       MnjxPnrSeg firstSeg, MnjxPnrSeg lastSeg, String issuedTime, MnjxPnrNmTicket pnrNmTicket, MnjxPnr pnr) {
        StringBuilder sb = new StringBuilder();
        // 第一行：ISSUED BY: ORG/DST: #{第一段出发城市三字码}/#{最后一段到达城市三字码} BSP-D
        sb.append("ISSUED BY:                           ORG/DST: ");

        MnjxCity orgCity = iSguiCommonService.getCityByAirportCode(firstSeg.getOrg());
        String orgCityCode = orgCity.getCityCode();
        String dstCityCode;
        if (ObjectUtil.isNotEmpty(lastSeg)) {
            MnjxCity dstCity = iSguiCommonService.getCityByAirportCode(lastSeg.getDst());
            dstCityCode = dstCity.getCityCode();
        } else {
            MnjxCity dstCity = iSguiCommonService.getCityByAirportCode(firstSeg.getDst());
            dstCityCode = dstCity.getCityCode();
        }
        sb.append(orgCityCode).append("/").append(dstCityCode);
        sb.append("                 BSP-D\n");

        // 第二行：E/R: Q/改退收费
        sb.append("E/R: Q/改退收费\n");

        // 第三行：TOUR CODE:
        sb.append("TOUR CODE:\n");

        // 第四行：PASSENGER: #{旅客姓名} #{如果是儿童，设置为CHD (CHILD)；如果是婴儿，设置为INF(婴儿出生月年，格式如DEC24)}
        sb.append("PASSENGER: ").append(passengerName);
        if ("CHD".equals(passengerType)) {
            sb.append(" CHD (CHILD)");
        } else if ("INF".equals(passengerType) && StrUtil.isNotEmpty(xnBirthDate)) {
            sb.append(" INF(").append(DateUtils.ym2Com(xnBirthDate)).append(")");
        }
        sb.append("\n");

        // 第五行：EXCH: CONJ TKT:
        sb.append("EXCH:                               CONJ TKT:\n");

        // 第六行：O FM:1#{第一段出发机场三字码} #{第一段航司} #{第一段无航司的航班号} #{第一段销售舱位} #{第一段航班日期} #{第一段起飞时间} OK #{第一段销售舱位}#{如果是儿童，CH50；如果是婴儿，INF90} /#{出票日期} 20K #{票面状态}
        sb.append("O FM:1").append(firstSeg.getOrg()).append(" ")
                .append(firstSeg.getFlightNo().substring(0, 2)).append("    ")
                .append(firstSeg.getFlightNo().substring(2)).append("  ")
                .append(firstSeg.getSellCabin()).append(" ")
                .append(DateUtils.ymd2Com(firstSeg.getFlightDate())).append(" ")
                .append(firstSeg.getEstimateOff()).append(" OK ")
                .append(firstSeg.getSellCabin());

        if ("CHD".equals(passengerType)) {
            sb.append("CH50");
        } else if ("INF".equals(passengerType)) {
            sb.append("INF90");
        }

        DateTime dateTime = DateUtils.parse(issuedTime);
        String format = DateUtils.format(dateTime, "yyyy-MM-dd");
        String comIssuedDate = DateUtils.ymd2Com(format);
        sb.append("            /").append(comIssuedDate).append(" 20K ").append(pnrNmTicket.getTicketStatus1()).append("\n");

        // 第七行：RL:#{PNR CRS编码} /#{PNR ICS编码}1E
        if (pnr != null) {
            sb.append("          RL:").append(pnr.getPnrCrs()).append("  /").append(pnr.getPnrIcs()).append("1E\n");
        } else {
            sb.append("          RL:  /1E\n");
        }

        // 8行前可能有第二段的信息
        if (ObjectUtil.isNotEmpty(lastSeg)) {
            // 第六行：O FM:2#{第二段出发机场三字码} #{第二段航司} #{第二段无航司的航班号} #{第二段销售舱位} #{第二段航班日期} #{第二段起飞时间} OK #{第二段销售舱位}#{如果是儿童，CH50；如果是婴儿，INF90} /#{出票日期} 20K #{票面状态}
            sb.append("O FM:2").append(lastSeg.getOrg()).append(" ")
                    .append(lastSeg.getFlightNo().substring(0, 2)).append("    ")
                    .append(lastSeg.getFlightNo().substring(2)).append("  ")
                    .append(lastSeg.getSellCabin()).append(" ")
                    .append(DateUtils.ymd2Com(lastSeg.getFlightDate())).append(" ")
                    .append(lastSeg.getEstimateOff()).append(" OK ")
                    .append(lastSeg.getSellCabin());

            if ("CHD".equals(passengerType)) {
                sb.append("CH50");
            } else if ("INF".equals(passengerType)) {
                sb.append("INF90");
            }

            sb.append("            /").append(comIssuedDate).append(" 20K ").append(pnrNmTicket.getTicketStatus2()).append("\n");

            // 第七行：RL:#{PNR CRS编码} /#{PNR ICS编码}1E
            if (pnr != null) {
                sb.append("          RL:").append(pnr.getPnrCrs()).append("  /").append(pnr.getPnrIcs()).append("1E\n");
            } else {
                sb.append("          RL:  /1E\n");
            }

            // 第八行：TO: #{最后一段的到达机场三字码}
            sb.append("  TO: ").append(lastSeg.getDst()).append("\n");
        } else {
            // 第八行：TO: #{最后一段的到达机场三字码}
            sb.append("  TO: ").append(firstSeg.getDst()).append("\n");
        }

        // 第九行：FC:
        sb.append("FC:\n");

        // 第十行：FARE: |FOP:
        sb.append("FARE:                      |FOP:\n");

        // 第十一行：TAX: |OI:
        sb.append("TAX:                       |OI:\n");

        // 第十二行：TOTAL: |TKTN: #{票号}
        sb.append("TOTAL:                     |TKTN: ").append(ticketNo).append("\n");

        // Base64编码
        return Base64.getEncoder().encodeToString(sb.toString().getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 构建客票历史信息
     *
     * @param ticketNo      票号
     * @param passengerName 旅客姓名
     * @param pnrNmTn       票与航段的关系信息
     * @return 客票历史信息
     */
    private String buildTktHistoryText(String ticketNo, String passengerName, MnjxPnrNmTn pnrNmTn) {
        StringBuilder sb = new StringBuilder();

        // 格式化票号，添加"-"
        String formattedTicketNo = ticketNo;
        if (!ticketNo.contains("-") && ticketNo.length() == 13) {
            formattedTicketNo = ticketNo.substring(0, 3) + "-" + ticketNo.substring(3);
        }

        // 第一行：NAME: #{旅客姓名} TKTN:#{票号}
        sb.append("NAME: ").append(passengerName).append("  TKTN:").append(formattedTicketNo).append("\r\n");

        // 第二行：IATA OFFC: #{IATA办公室} ISSUED: #{出票日期} RVAL: 00 EXP: #{过期日期}
        DateTime issuedDateTime = DateUtils.parse(pnrNmTn.getIssuedTime());
        String issuedDate = DateUtils.format(issuedDateTime, "yyyy-MM-dd");
        issuedDate = DateUtils.ymd2Com(issuedDate);

        // 计算过期日期（出票日期加一年）
        DateTime expDateTime = DateUtils.offsetMonth(issuedDateTime, 12);
        String expDate = DateUtils.format(expDateTime, "yyyy-MM-dd");
        expDate = DateUtils.ymd2Com(expDate);

        MnjxSi mnjxSi = iMnjxSiService.getById(pnrNmTn.getIssuedSiId());
        MnjxOffice mnjxOffice = iMnjxOfficeService.getById(mnjxSi.getOfficeId());
        MnjxAgent mnjxAgent = iMnjxAgentService.getById(mnjxOffice.getOrgId());
        sb.append("IATA OFFC: ").append(mnjxAgent.getAgentIata()).append(" ISSUED: ").append(issuedDate).append(" RVAL: 00 EXP: ").append(expDate).append("\r\n");

        // 第三行：#{序号} #{出票时间}/#{终端ID} TRMK #{备注}
        MnjxPrinter printer = iMnjxPrinterService.getById(pnrNmTn.getPrinterId());
        sb.append("  ")
                .append(printer.getPrinterNo())
                .append("    ")
                .append(issuedDate.substring(0, 5))
                .append("/")
                .append(DateUtils.format(issuedDateTime, "HHmm"))
                .append("/")
                .append(mnjxSi.getSiNo())
                .append("    TRMK XIZANG JIASHENG+")
                .append(mnjxOffice.getOfficeNo())
                .append("+DEV-1\r\n\r\n");

        return sb.toString();
    }

    /**
     * 构建证件信息
     *
     * @param ticketNo       票号
     * @param passengerName  旅客姓名
     * @param passengerType  旅客类型
     * @param foidInputValue FOID输入值
     * @param xnBirthDate    出生日期（婴儿）
     * @return 证件信息
     */
    private QueryTicketByDetrVo.Credential buildCredential(String ticketNo, String passengerName, String passengerType, String foidInputValue, String xnBirthDate) {
        QueryTicketByDetrVo.Credential credential = new QueryTicketByDetrVo.Credential();

        // 构建证件文本
        StringBuilder sb = new StringBuilder();

        // 格式化票号，添加"-"
        String formattedTicketNo = ticketNo;
        if (!ticketNo.contains("-") && ticketNo.length() == 13) {
            formattedTicketNo = ticketNo.substring(0, 3) + "-" + ticketNo.substring(3);
        }

        // 第一行：票号： #{票号}
        sb.append("票号： ").append(formattedTicketNo).append("\n");

        // 第二行：乘机人姓名： #{旅客姓名}
        sb.append("乘机人姓名： ");
        if ("CHD".equals(passengerType)) {
            sb.append(passengerName).append(" CHD");
        } else if ("INF".equals(passengerType)) {
            sb.append(passengerName).append(" INF(").append(DateUtils.ymd2Com(xnBirthDate).substring(2)).append(")");
        } else {
            sb.append(passengerName);
        }
        sb.append("\n");

        // 第三行：证件号码
        String niNo = foidInputValue.split(" NI")[1].split("/")[0];
        sb.append("NI").append(niNo);

        // Base64编码
        credential.setCertificatesText(Base64.getEncoder().encodeToString(sb.toString().getBytes(StandardCharsets.UTF_8)));

        // 构建证件列表
        List<QueryTicketByDetrVo.Certificate> certificatesList = new ArrayList<>();
        QueryTicketByDetrVo.Certificate certificate = new QueryTicketByDetrVo.Certificate();
        certificate.setCertType("NI");
        certificate.setCertNumber(niNo.substring(0, 6) + "************");
        certificate.setEncryptCertNumber("7741FEA60721501A318B6522524D51470C72C5129D6E58A8C069713104031984CDC5C2BDB679129DB20142522DEC54674EE1DB62A8652C957A89FB651AC37DC1C2012FD8544E47E5560711C3355C888AB4FA7A1073C3E70546E084434E63B011660D84FD39ACEE0CEE9C4B1FF269A35F207E");

        if ("INF".equals(passengerType)) {
            certificate.setBirthday(xnBirthDate.replace("-", ""));
        } else {
            String birthDateStr = niNo.substring(6, 14);
            String birthYear = birthDateStr.substring(0, 4);
            String birthMonth = birthDateStr.substring(4, 6);
            String birthDay = birthDateStr.substring(6, 8);
            certificate.setBirthday(birthYear + birthMonth + birthDay);
        }
        certificatesList.add(certificate);

        credential.setCertificatesList(certificatesList);

        return credential;
    }

    /**
     * 构建TicketByRtktVo对象
     *
     * @param pnrNmTicket 票务信息
     * @param pnrNmTn     票与航段的关系信息
     * @param pnrNm       旅客信息
     * @param firstSeg    第一航段
     * @param lastSeg     最后航段
     * @return TicketByRtktVo
     */
    private TicketByRtktVo buildTicketByRtktVo(MnjxPnr pnr, MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnrNm pnrNm, MnjxNmXn nmXn, MnjxPnrSeg firstSeg, MnjxPnrSeg lastSeg) {
        TicketByRtktVo vo = new TicketByRtktVo();

        MnjxSi mnjxSi = iMnjxSiService.getById(pnrNmTn.getIssuedSiId());
        MnjxOffice mnjxOffice = iMnjxOfficeService.getById(mnjxSi.getOfficeId());
        MnjxAgent mnjxAgent = iMnjxAgentService.getById(mnjxOffice.getOrgId());
        MnjxPrinter printer = iMnjxPrinterService.getById(pnrNmTn.getPrinterId());
        // 构建票务信息
        TicketByRtktVo.Ticket ticket = new TicketByRtktVo.Ticket();
        ticket.setIssueType("    XIZANG JIASHENG " + mnjxOffice.getOfficeNo() + "                  DEV-" + printer.getPrinterNo() + "         ");
        ticket.setIataCode(mnjxAgent.getAgentIata());
        ticket.setOffice(mnjxOffice.getOfficeNo());
        ticket.setIssueAirline(pnrNmTn.getIssuedAirline());
        ticket.setTicketState("issue");
        ticket.setCode(pnr.getPnrCrs());
        ticket.setIssueDate(pnrNmTn.getIssuedTime());
        ticket.setAccountNumber(mnjxSi.getSiNo());
        ticket.setTicketNumber(pnrNmTicket.getTicketNo());
        ticket.setEi("Q/变更退票收费");
        ticket.setIssueSignCode("CN");
        ticket.setPrintNumber(printer.getPrinterNo());
        ticket.setInternationalIndicator("D");
        ticket.setIssueAirlineCode(pnrNmTicket.getTicketNo().substring(0, 3));
        ticket.setFp("CASH,CNY");
        ticket.setTicketManagementOrganizationCode("BSP");
        ticket.setTicketType("NONE");
        ticket.setOriginalTicket("");
        ticket.setTicketManagementOrganizationCodeByRtkt("BSP");
        if (Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus1())) {
            if (StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus2())) {
                if (Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus2())) {
                    ticket.setRefundPrintNumber(printer.getPrinterNo());
                }
            } else {
                ticket.setRefundPrintNumber(printer.getPrinterNo());
            }
        }
        ticket.setTicketTypeByRtkt("BSP");
        vo.setTicket(ticket);

        // 构建旅客信息
        TicketByRtktVo.Passenger passenger = new TicketByRtktVo.Passenger();
        passenger.setPassengerName("/");

        // 判断旅客类型
        String passengerType = "ADT";
        if (pnrNm != null) {
            if ("1".equals(pnrNm.getPsgType())) {
                passengerType = "CHD";
            } else if ("2".equals(pnrNm.getPsgType())) {
                passengerType = "INF";
            }
        }
        passenger.setPassengerType(passengerType);

        // 构建航段信息
        List<TicketByRtktVo.Segment> segments = new ArrayList<>();
        segments.add(this.constructRtktPassengerSegment(firstSeg));
        if (ObjectUtil.isNotEmpty(lastSeg)) {
            segments.add(this.constructRtktPassengerSegment(lastSeg));
        }
        passenger.setSegments(segments);

        // 构建连接航段信息
        List<TicketByRtktVo.Segment> conjunctionSegments = new ArrayList<>();
        TicketByRtktVo.Segment firstConjSegment = this.constructRtktPassengerSegment(firstSeg);
        firstConjSegment.setSegNumber(firstSeg.getPnrSegNo());
        firstConjSegment.setTicketNo(ticket.getTicketNumber());
        conjunctionSegments.add(firstConjSegment);
        if (ObjectUtil.isNotEmpty(lastSeg)) {
            TicketByRtktVo.Segment lastConjSegment = this.constructRtktPassengerSegment(lastSeg);
            lastConjSegment.setSegNumber(firstSeg.getPnrSegNo());
            lastConjSegment.setTicketNo(ticket.getTicketNumber());
            conjunctionSegments.add(lastConjSegment);
        }
        passenger.setConjunctionSegments(conjunctionSegments);

        vo.setPassenger(passenger);

        // 构建价格信息
        BigDecimal scny;
        BigDecimal fareAmount;
        BigDecimal taxAmount;
        BigDecimal taxCn;
        BigDecimal taxYq;
        MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                .one();
        if (ObjectUtil.isNotEmpty(pnrFn)) {
            scny = pnrFn.getSPrice();
            fareAmount = pnrFn.getAPrice();
            taxCn = pnrFn.getTCnPrice();
            taxYq = pnrFn.getTYqPrice();
            taxAmount = taxCn.add(taxYq);
        } else {
            MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                    .eq(MnjxNmFn::getPnrNmId, pnrNm.getPnrNmId())
                    .one();
            scny = nmFn.getSPrice();
            fareAmount = nmFn.getAPrice();
            taxCn = nmFn.getTCnPrice();
            taxYq = nmFn.getTYqPrice();
            taxAmount = taxCn.add(taxYq);
        }

        List<MnjxPnrRecord> fcRecordList = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .like(MnjxPnrRecord::getPnrType, "FC")
                .eq(MnjxPnrRecord::getChangeMark, "X")
                .list();
        String fc = fcRecordList.stream()
                .sorted(Comparator.comparing(MnjxPnrRecord::getChangeAtNo).reversed())
                .collect(Collectors.toList())
                .get(0)
                .getInputValue();
        TicketByRtktVo.Price price = new TicketByRtktVo.Price();
        price.setNegotiatedFareCode(null);
        price.setQueryExclusiveNegotiatedFare(false);
        price.setFc(fc.replace("FC/", "").replace("A/", ""));
        price.setTaxAmount("CNY" + taxAmount);
        price.setTaxDetail("CN：CNY" + taxCn + " / YQ：CNY" + taxYq);

        // 构建税费列表
        List<TicketByRtktVo.Tax> taxes = new ArrayList<>();
        TicketByRtktVo.Tax tax1 = new TicketByRtktVo.Tax();
        tax1.setTaxCode("CN");
        tax1.setTaxAmount(taxCn.toString());
        tax1.setCurrencyCode("CNY");
        tax1.setNewOldRefundTax("T");
        taxes.add(tax1);

        TicketByRtktVo.Tax tax2 = new TicketByRtktVo.Tax();
        tax2.setTaxCode("YQ");
        tax2.setTaxAmount(taxYq.toString());
        tax2.setCurrencyCode("CNY");
        tax2.setNewOldRefundTax("T");
        taxes.add(tax2);

        price.setTaxes(taxes);
        price.setTotalTaxAmount(taxAmount.toString());
        price.setFsnAmount(scny.toString());
        price.setTicketAmount("");
        price.setCommissionFare("CNY0.00");
        price.setCommissionRate("0.00");
        price.setCommissionMode(true);
        price.setGpSign("");

        // 计算总价
        price.setFareAmount("CNY" + fareAmount.toString());

        price.setCreditCardDetail("");
        price.setCurrency("CNY");
        price.setPaymentTypeCode("CASH");
        price.setCreditCard(null);
        price.setScny("CNY" + scny);
        price.setTc("");
        price.setTicketAmountFOrR("CNY" + scny);
        price.setAutoFareType(true);
        price.setTicketAmountOri("0");
        vo.setPrice(price);

        return vo;
    }

    /**
     * Title: constructRtktPassengerSegment
     * Description: 构建passenger中的segments和conjunctionSegments<br>
     *
     * @param pnrSeg
     * @return {@link TicketByRtktVo.Segment}
     * <AUTHOR>
     * @date 2025/5/23 15:22
     */
    private TicketByRtktVo.Segment constructRtktPassengerSegment(MnjxPnrSeg pnrSeg) {
        TicketByRtktVo.Segment segment = new TicketByRtktVo.Segment();
        segment.setDepartureCity(pnrSeg.getOrg());
        segment.setArrivalCity(pnrSeg.getDst());
        segment.setDepartureDateTime(pnrSeg.getFlightDate() + " " + pnrSeg.getEstimateOff().substring(0, 2) + ":" + pnrSeg.getEstimateOff().substring(2));
        segment.setArrivalDateTime(pnrSeg.getFlightDate() + " " + pnrSeg.getEstimateArr().substring(0, 2) + ":" + pnrSeg.getEstimateArr().substring(2));
        segment.setCabin(pnrSeg.getSellCabin());
        segment.setFlightNo(pnrSeg.getFlightNo());
        segment.setFareBasis(pnrSeg.getSellCabin());
        segment.setBaggage("20K");
        return segment;
    }

    @Override
    public List<QueryTicketByPnrVo> queryTicketByPnr(QueryTicketByPnrDto dto) throws SguiResultException {
        // 参数验证
        if (StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();

        if (pnr == null) {
            return Collections.emptyList();
        }
        if ("DEL".equals(pnr.getPnrStatus())) {
            throw new SguiResultException("查询PNR被取消");
        }

        // 查询PNR下的所有旅客
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();

        if (CollUtil.isEmpty(pnrNmList)) {
            return Collections.emptyList();
        }

        // 查询PNR下的所有航段
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        if (CollUtil.isEmpty(pnrSegList)) {
            return Collections.emptyList();
        }

        List<String> pnrNmIdList = pnrNmList.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());
        List<MnjxNmOsi> allNmOsiList = iMnjxNmOsiService.lambdaQuery()
                .in(MnjxNmOsi::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxNmRmk> allNmRmkList = iMnjxNmRmkService.lambdaQuery()
                .in(MnjxNmRmk::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxPnrNmTn> allNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                .in(MnjxPnrNmTn::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxNmFn> allNmFnList = iMnjxNmFnService.lambdaQuery()
                .in(MnjxNmFn::getPnrNmId, pnrNmIdList)
                .list();
        MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                .one();
        // 构建返回结果
        List<QueryTicketByPnrVo> resultList = new ArrayList<>();

        // 处理成人和儿童旅客
        for (MnjxPnrNm pnrNm : pnrNmList) {
            String pnrNmId = pnrNm.getPnrNmId();
            // 查询旅客的票务信息
            List<MnjxPnrNmTn> nmTnList = allNmTnList.stream()
                    .filter(t -> pnrNmId.equals(t.getPnrNmId()))
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(nmTnList)) {
                continue;
            }

            List<MnjxNmRmk> nmRmkList = allNmRmkList.stream()
                    .filter(r -> pnrNmId.equals(r.getPnrNmId()))
                    .collect(Collectors.toList());
            List<MnjxNmFn> nmFnList = allNmFnList.stream()
                    .filter(f -> pnrNmId.equals(f.getPnrNmId()))
                    .collect(Collectors.toList());
            List<MnjxNmOsi> nmOsiList = allNmOsiList.stream()
                    .filter(o -> pnrNmId.equals(o.getPnrNmId()))
                    .collect(Collectors.toList());
            for (MnjxPnrNmTn pnrNmTn : nmTnList) {
                List<MnjxPnrNmTicket> pnrNmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                        .eq(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTn.getTnId())
                        .list();

                if (CollUtil.isEmpty(pnrNmTicketList)) {
                    continue;
                }

                for (MnjxPnrNmTicket pnrNmTicket : pnrNmTicketList) {
                    QueryTicketByPnrVo vo = this.buildTicketByPnrVo(pnrNmTicket, pnrNm, null, pnr, nmOsiList, nmRmkList, nmFnList, pnrFn, pnrNmTn);
                    // 设置二次筛选条件
                    QueryTicketByPnrVo.SecondFactor secondFactor = new QueryTicketByPnrVo.SecondFactor();
                    secondFactor.setSecondFactorCode("CN");
                    secondFactor.setSecondFactorValue(pnr.getPnrCrs());
                    vo.setSecondFactor(secondFactor);
                    resultList.add(vo);
                }
            }
        }

        // 处理婴儿旅客
        List<MnjxNmXn> nmXnList = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId, pnrNmIdList)
                .list();

        if (CollUtil.isNotEmpty(nmXnList)) {
            List<MnjxPnrNmTn> allPnrNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                    .in(MnjxPnrNmTn::getNmXnId, nmXnList.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList()))
                    .list();
            for (MnjxNmXn nmXn : nmXnList) {
                // 查询婴儿的票务信息
                List<MnjxPnrNmTn> pnrNmTnList = allPnrNmTnList.stream()
                        .filter(t -> nmXn.getNmXnId().equals(t.getNmXnId()))
                        .collect(Collectors.toList());

                if (CollUtil.isEmpty(pnrNmTnList)) {
                    continue;
                }

                for (MnjxPnrNmTn pnrNmTn : pnrNmTnList) {
                    List<MnjxPnrNmTicket> pnrNmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                            .eq(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTn.getTnId())
                            .list();

                    if (CollUtil.isEmpty(pnrNmTicketList)) {
                        continue;
                    }

                    for (MnjxPnrNmTicket pnrNmTicket : pnrNmTicketList) {
                        MnjxPnrNm adultPnrNm = iMnjxPnrNmService.getById(nmXn.getPnrNmId());
                        QueryTicketByPnrVo vo = this.buildTicketByPnrVo(pnrNmTicket, adultPnrNm, nmXn, pnr, null, null, null, null, pnrNmTn);
                        // 设置二次筛选条件
                        QueryTicketByPnrVo.SecondFactor secondFactor = new QueryTicketByPnrVo.SecondFactor();
                        secondFactor.setSecondFactorCode("CN");
                        secondFactor.setSecondFactorValue(pnr.getPnrCrs());
                        vo.setSecondFactor(secondFactor);
                        resultList.add(vo);
                    }
                }
            }
        }

        return resultList;
    }

    @Override
    public List<QueryTicketByPnrVo> queryTicketByCert(QueryTicketByCertDto dto) throws SguiResultException {
        // 参数验证
        if (!StrUtil.isAllNotEmpty(dto.getCertNo(), dto.getCertCode())) {
            throw new SguiResultException("证件信息不能为空");
        }

        // 根据证件类型不同查询ssr表证件数据
        List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getSsrType, "PP".equals(dto.getCertCode()) ? "DOCS" : "FOID")
                .like(MnjxNmSsr::getSsrInfo, dto.getCertNo())
                .list();

        if (CollUtil.isEmpty(ssrList)) {
            return Collections.emptyList();
        }
        // 再进行详细的筛选
        if ("PP".equals(dto.getCertCode())) {
            ssrList = ssrList.stream()
                    .filter(s -> s.getSsrInfo().split("/")[2].equals(dto.getCertNo()))
                    .collect(Collectors.toList());
        } else if ("NI".equals(dto.getCertCode())) {
            ssrList = ssrList.stream()
                    .filter(s -> s.getSsrInfo().split("NI")[1].split("/P")[0].equals(dto.getCertNo()))
                    .collect(Collectors.toList());
        } else {
            ssrList = ssrList.stream()
                    .filter(s -> s.getSsrInfo().split("UU")[1].split("/P")[0].equals(dto.getCertNo()))
                    .collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(ssrList)) {
            return Collections.emptyList();
        }

        List<String> pnrNmIdList = ssrList.stream()
                .map(MnjxNmSsr::getPnrNmId)
                .collect(Collectors.toList());
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.listByIds(pnrNmIdList);
        List<String> pnrIdList = pnrNmList.stream()
                .map(MnjxPnrNm::getPnrId)
                .collect(Collectors.toList());
        List<MnjxPnr> pnrList = iMnjxPnrService.listByIds(pnrIdList);
        List<MnjxNmOsi> allNmOsiList = iMnjxNmOsiService.lambdaQuery()
                .in(MnjxNmOsi::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxNmRmk> allNmRmkList = iMnjxNmRmkService.lambdaQuery()
                .in(MnjxNmRmk::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxPnrNmTn> allNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                .in(MnjxPnrNmTn::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxNmFn> allNmFnList = iMnjxNmFnService.lambdaQuery()
                .in(MnjxNmFn::getPnrNmId, pnrNmIdList)
                .list();
        List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnrIdList)
                .list();

        // 构建返回结果
        List<QueryTicketByPnrVo> resultList = new ArrayList<>();
        // 处理旅客
        for (MnjxPnrNm pnrNm : pnrNmList) {
            String pnrNmId = pnrNm.getPnrNmId();
            String pnrId = pnrNm.getPnrId();
            // 查询旅客的票务信息
            List<MnjxPnrNmTn> nmTnList = allNmTnList.stream()
                    .filter(t -> pnrNmId.equals(t.getPnrNmId()))
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(nmTnList)) {
                continue;
            }

            MnjxPnr pnr = pnrList.stream()
                    .filter(p -> pnrId.equals(p.getPnrId()))
                    .collect(Collectors.toList())
                    .get(0);
            MnjxPnrFn pnrFn = null;
            if (CollUtil.isNotEmpty(pnrFnList) && pnrFnList.stream().anyMatch(p -> pnrId.equals(p.getPnrId()))) {
                pnrFn = pnrFnList.stream()
                        .filter(p -> pnrId.equals(p.getPnrId()))
                        .collect(Collectors.toList())
                        .get(0);
            }
            List<MnjxNmRmk> nmRmkList = allNmRmkList.stream()
                    .filter(r -> pnrNmId.equals(r.getPnrNmId()))
                    .collect(Collectors.toList());
            List<MnjxNmFn> nmFnList = allNmFnList.stream()
                    .filter(f -> pnrNmId.equals(f.getPnrNmId()))
                    .collect(Collectors.toList());
            List<MnjxNmOsi> nmOsiList = allNmOsiList.stream()
                    .filter(o -> pnrNmId.equals(o.getPnrNmId()))
                    .collect(Collectors.toList());
            for (MnjxPnrNmTn pnrNmTn : nmTnList) {
                List<MnjxPnrNmTicket> pnrNmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                        .eq(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTn.getTnId())
                        .list();

                if (CollUtil.isEmpty(pnrNmTicketList)) {
                    continue;
                }

                for (MnjxPnrNmTicket pnrNmTicket : pnrNmTicketList) {
                    QueryTicketByPnrVo vo = this.buildTicketByPnrVo(pnrNmTicket, pnrNm, null, pnr, nmOsiList, nmRmkList, nmFnList, pnrFn, pnrNmTn);
                    // 设置二次筛选条件
                    QueryTicketByPnrVo.SecondFactor secondFactor = new QueryTicketByPnrVo.SecondFactor();
                    secondFactor.setSecondFactorCode(dto.getCertCode());
                    secondFactor.setSecondFactorValue(dto.getCertNo());
                    vo.setSecondFactor(secondFactor);
                    resultList.add(vo);
                }
            }
        }
        return resultList;
    }

    /**
     * 构建按PNR查询客票信息VO
     *
     * @param pnrNmTicket 票务信息
     * @param pnrNm       旅客信息
     * @param nmXn        婴儿信息
     * @param pnr         PNR信息
     * @param nmOsiList   旅客OSI信息
     * @param nmRmkList   旅客RMK信息
     * @param nmFnList    旅客FN信息
     * @param pnrFn       PNR FN信息
     * @param pnrNmTn     客票信息
     * @return 按PNR查询客票信息VO
     */
    private QueryTicketByPnrVo buildTicketByPnrVo(MnjxPnrNmTicket pnrNmTicket, MnjxPnrNm pnrNm, MnjxNmXn nmXn,
                                                  MnjxPnr pnr, List<MnjxNmOsi> nmOsiList, List<MnjxNmRmk> nmRmkList,
                                                  List<MnjxNmFn> nmFnList, MnjxPnrFn pnrFn, MnjxPnrNmTn pnrNmTn) {
        QueryTicketByPnrVo vo = new QueryTicketByPnrVo();

        // 设置票号
        vo.setEtNumber(pnrNmTicket.getTicketNo().substring(0, 3) + "-" + pnrNmTicket.getTicketNo().substring(3));

        // 设置旅客信息
        if (nmXn != null) {
            // 婴儿票
            vo.setPassengerName(nmXn.getXnCname() + " INF(" + DateUtils.ym2Com(nmXn.getXnBirthday()) + ")(INFANT)");
            vo.setPassengerNameSuffix(nmXn.getXnCname() + " INF(" + DateUtils.ym2Com(nmXn.getXnBirthday()) + ")(INFANT)");
            vo.setPassengerType("3");
            vo.setFullName(pnrNm.getName() + " INF");
            vo.setSpecialPassengerType("INF");
            vo.setTicketPsgType("INF");
        } else {
            // 成人或儿童票
            if ("1".equals(pnrNm.getPsgType())) {
                // 儿童
                vo.setPassengerName(pnrNm.getName());
                vo.setPassengerNameSuffix(pnrNm.getName() + " CHD(CHILD)");
                vo.setPassengerType("1");
                vo.setSpecialPassengerType("CHD");
                vo.setTicketPsgType("CHD");
                vo.setFullName(pnrNm.getName() + " CHD");
            } else {
                // 成人
                vo.setPassengerNameSuffix(pnrNm.getName());
                vo.setPassengerType("0");
                vo.setSpecialPassengerType("ADT");
                vo.setTicketPsgType("ADT");
                // 判断GMJC
                if (CollUtil.isNotEmpty(nmRmkList) && nmRmkList.stream().anyMatch(r -> Constant.RMK_TYPE_GMJC.equals(r.getRmkName()))) {
                    if (ObjectUtil.isNotEmpty(pnrFn)) {
                        if ("GM".equals(pnrFn.getPatType())) {
                            vo.setPassengerName(pnrNm.getName() + " GM(GMJC)");
                            vo.setPassengerNameSuffix(pnrNm.getName() + " GM(GMJC)");
                            vo.setSpecialPassengerType("GM");
                            vo.setFullName(pnrNm.getName() + " GM");
                            vo.setTicketPsgType("GM");
                            vo.setPassengerType("4");
                        } else if ("JC".equals(pnrFn.getPatType())) {
                            vo.setPassengerName(pnrNm.getName() + " JC(GMJC)");
                            vo.setPassengerNameSuffix(pnrNm.getName() + " JC(GMJC)");
                            vo.setSpecialPassengerType("JC");
                            vo.setFullName(pnrNm.getName() + " JC");
                            vo.setTicketPsgType("JC");
                            vo.setPassengerType("4");
                        }
                    } else if (CollUtil.isNotEmpty(nmFnList)) {
                        if (nmFnList.stream().anyMatch(f -> "GM".equals(f.getPatType()))) {
                            vo.setPassengerName(pnrNm.getName() + " GM(GMJC)");
                            vo.setPassengerNameSuffix(pnrNm.getName() + " GM(GMJC)");
                            vo.setSpecialPassengerType("GM");
                            vo.setFullName(pnrNm.getName() + " GM");
                            vo.setTicketPsgType("GM");
                            vo.setPassengerType("4");
                        } else if (nmFnList.stream().anyMatch(f -> "JC".equals(f.getPatType()))) {
                            vo.setPassengerName(pnrNm.getName() + " JC(GMJC)");
                            vo.setPassengerNameSuffix(pnrNm.getName() + " JC(GMJC)");
                            vo.setSpecialPassengerType("JC");
                            vo.setFullName(pnrNm.getName() + " JC");
                            vo.setTicketPsgType("JC");
                            vo.setPassengerType("4");
                        }
                    }
                }
                // 判断VIP
                if (CollUtil.isNotEmpty(nmOsiList) && nmOsiList.stream().anyMatch(o -> Constant.VIP_TYPE.equals(o.getPnrOsiType()))) {
                    vo.setPassengerName(pnrNm.getName() + " VIP");
                    vo.setPassengerNameSuffix(pnrNm.getName() + " VIP");
                    vo.setFullName(pnrNm.getName() + " VIP");
                    vo.setTicketPsgType("VIP");
                    vo.setSpecialPassengerType("VIP");
                }
            }
        }

        vo.setNameSuffix(null);
        vo.setTicketTypeCode("D");
        vo.setCdsTicket(false);
        vo.setEtType("BSP");
        vo.setGovernmentPurchase(false);
        vo.setReceiptPrinted(false);
        vo.setConjunctiveTicket("");
        String issuedTime = pnrNmTn.getIssuedTime();
        if (StrUtil.isNotEmpty(issuedTime)) {
            DateTime dateTime = DateUtils.parse(issuedTime);
            String format = DateUtils.format(dateTime, "yyyy-MM-dd");
            vo.setIssueTicketDate(format);
            vo.setIssueTime(DateUtils.format(dateTime, "HH:mm"));
        }

        // 设置航段信息
        List<QueryTicketByPnrVo.AirSeg> airSegList = new ArrayList<>();

        // 获取航段信息
        MnjxPnrSeg firstSeg = null;
        MnjxPnrSeg lastSeg = null;

        if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id())) {
            firstSeg = iMnjxPnrSegService.getById(pnrNmTicket.getS1Id());
        }

        if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id())) {
            lastSeg = iMnjxPnrSegService.getById(pnrNmTicket.getS2Id());
        }

        airSegList.add(this.constructPnrAirSeg(pnr, firstSeg, nmXn, pnrNm, pnrNmTicket.getTicketStatus1(), nmFnList, pnrFn));
        if (lastSeg != null) {
            airSegList.add(this.constructPnrAirSeg(pnr, lastSeg, nmXn, pnrNm, pnrNmTicket.getTicketStatus2(), nmFnList, pnrFn));
        }

        vo.setAirSeg(airSegList);
        vo.setAirSegCrsPnr(pnr.getPnrCrs());

        return vo;
    }

    private QueryTicketByPnrVo.AirSeg constructPnrAirSeg(MnjxPnr pnr, MnjxPnrSeg pnrSeg, MnjxNmXn nmXn, MnjxPnrNm pnrNm, String ticketStatus, List<MnjxNmFn> nmFnList, MnjxPnrFn pnrFn) {
        QueryTicketByPnrVo.AirSeg airSeg = new QueryTicketByPnrVo.AirSeg();
        airSeg.setDepAirportCode(pnrSeg.getOrg());
        airSeg.setCrsPnrNo(pnr.getPnrCrs());
        airSeg.setCabin(pnrSeg.getSellCabin());
        airSeg.setSegmentStatus("OK");
        airSeg.setFlightNo(pnrSeg.getFlightNo());

        // 设置出发时间
        String depDate = pnrSeg.getFlightDate();
        String depTime = pnrSeg.getEstimateOff();
        if (StrUtil.isNotEmpty(depDate) && StrUtil.isNotEmpty(depTime)) {
            airSeg.setDepTime(depDate + " " + depTime.substring(0, 2) + ":" + depTime.substring(2) + ":00");
        }

        airSeg.setDepAirportTerminal("");

        // 设置到达时间
        String arrTime = pnrSeg.getEstimateArr();
        if (StrUtil.isNotEmpty(depDate) && StrUtil.isNotEmpty(arrTime)) {
            airSeg.setArrTime(depDate + " " + arrTime.substring(0, 2) + ":" + arrTime.substring(2) + ":00");
        }
        airSeg.setArrAirportCode(pnrSeg.getDst());

        airSeg.setTicketStatus(ticketStatus);
        airSeg.setArrAirportTerminal("");
        airSeg.setPnrNo(pnr.getPnrIcs());

        // 设置费率
        String rate = pnrSeg.getSellCabin();
        if (nmXn != null) {
            rate += "IN90";
        } else if ("1".equals(pnrNm.getPsgType())) {
            rate += "CH50";
        } else {
            if ((ObjectUtil.isNotEmpty(pnrFn) && "JC".equals(pnrFn.getPatType())) || (CollUtil.isNotEmpty(nmFnList) && nmFnList.stream().anyMatch(f -> "JC".equals(f.getPatType())))) {
                rate += "JC";
            } else if ((ObjectUtil.isNotEmpty(pnrFn) && "GM".equals(pnrFn.getPatType())) || (CollUtil.isNotEmpty(nmFnList) && nmFnList.stream().anyMatch(f -> "GM".equals(f.getPatType())))) {
                rate += "GM";
            }
        }
        airSeg.setRate(rate);

        airSeg.setCrsType("1E");
        airSeg.setStopType(null);
        airSeg.setOperationAirline("");

        // 设置航司
        String flightNo = pnrSeg.getFlightNo();
        if (StrUtil.isNotEmpty(flightNo) && flightNo.length() >= 2) {
            airSeg.setAirline(flightNo.substring(0, 2));
        }

        airSeg.setUniqueIndex(null);
        airSeg.setDepartureDate(pnrSeg.getFlightDate());
        airSeg.setChangeReason("");
        airSeg.setSegmentIndex(pnrSeg.getPnrSegNo());
        airSeg.setSegANRK(false);
        airSeg.setSegOPEN(false);

        return airSeg;
    }
}
