// 这是RefundTicketServiceImpl的第二部分方法，需要合并到主文件中

    /**
     * 构建退票订单信息
     */
    private PreviewRefundTicketVo.RefundTicketOrder buildRefundTicketOrder(
            MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm,
            String passengerName, String psgType, boolean isInfant,
            PreviewRefundTicketDto.SecondFactor secondFactorDto) {

        PreviewRefundTicketVo.RefundTicketOrder refundTicketOrder = new PreviewRefundTicketVo.RefundTicketOrder();

        // 构建票信息
        PreviewRefundTicketVo.Ticket ticket = new PreviewRefundTicketVo.Ticket();
        ticket.setTktType("D");
        ticket.setCdsTicket(false);
        ticket.setTicketNo(pnrNmTicket.getTicketNo());
        ticket.setPsgType(psgType);
        ticket.setTicketPsgType(psgType);
        ticket.setName(passengerName);
        ticket.setPassengerNameSuffix(passengerName);
        ticket.setSpecialPassengerType(psgType);
        ticket.setPayType("");
        ticket.setCurrency("CNY");
        ticket.setEtTag("1");
        ticket.setAirline(pnrNmTicket.getTicketNo().substring(0, 3)); // 航司结算码
        ticket.setPnr(pnr.getPnrIcs());
        ticket.setCrsPnrNo(pnr.getPnrCrs());
        ticket.setIsCoupon("0");
        ticket.setIsAirportCntl("0");
        ticket.setExchangeTktNo("");
        ticket.setGovernmentPurchase(false);
        ticket.setCommissionRate("0.00");

        // 构建航段信息
        List<PreviewRefundTicketVo.Segment> segments = this.buildSegments(pnrNmTicket, pnr.getPnrId());
        ticket.setSegment(segments);

        // 设置市场航空公司（取第一个航段的航空公司）
        if (!segments.isEmpty()) {
            ticket.setMarketAirline(segments.get(0).getAirline());
        }

        // 查询价格信息
        this.setPriceInfo(ticket, pnrNm.getPnrId(), pnrNm.getPnrNmId(), isInfant, segments.size());

        // 设置二次验证信息
        PreviewRefundTicketVo.SecondFactor secondFactor = new PreviewRefundTicketVo.SecondFactor();
        secondFactor.setSecondFactorCode(secondFactorDto.getSecondFactorCode());
        secondFactor.setSecondFactorValue(secondFactorDto.getSecondFactorValue());
        ticket.setSecondFactor(secondFactor);

        refundTicketOrder.setTicket(ticket);

        // 设置其他订单信息
        refundTicketOrder.setOffice("LXA101");
        refundTicketOrder.setIata("08323431");
        refundTicketOrder.setAgent("49041");
        refundTicketOrder.setOperator("49041");
        refundTicketOrder.setConjunction("1");
        refundTicketOrder.setTicketManagementOrganizationCode("BSP");
        refundTicketOrder.setPrinterNo("1");
        refundTicketOrder.setRefundPrintNumber(null);
        refundTicketOrder.setReceiptPrinted("0");

        return refundTicketOrder;
    }

    /**
     * 构建航段信息
     */
    private List<PreviewRefundTicketVo.Segment> buildSegments(MnjxPnrNmTicket pnrNmTicket, String pnrId) {
        List<PreviewRefundTicketVo.Segment> segments = new ArrayList<>();

        // 查询航段信息
        List<String> segmentIds = new ArrayList<>();
        if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id())) {
            segmentIds.add(pnrNmTicket.getS1Id());
        }
        if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id())) {
            segmentIds.add(pnrNmTicket.getS2Id());
        }

        if (!segmentIds.isEmpty()) {
            List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.listByIds(segmentIds);

            for (int i = 0; i < pnrSegList.size(); i++) {
                MnjxPnrSeg pnrSeg = pnrSegList.get(i);
                PreviewRefundTicketVo.Segment segment = new PreviewRefundTicketVo.Segment();

                segment.setAirline(pnrSeg.getFlightNo().substring(0, 2));
                segment.setIsAble("1");
                segment.setDepartureCode(pnrSeg.getOrg());
                segment.setArriveCode(pnrSeg.getDst());
                segment.setRph("1-" + (i + 1));
                segment.setIsCheck(null);
                segment.setTktTag(pnrNmTicket.getTicketNo());
                segment.setE8Rph(String.valueOf(i + 1));

                // 设置票状态
                if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id()) && pnrNmTicket.getS1Id().equals(pnrSeg.getPnrSegId())) {
                    segment.setTicketStatus(StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus1()) ? pnrNmTicket.getTicketStatus1() : "OPEN FOR USE");
                } else if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id()) && pnrNmTicket.getS2Id().equals(pnrSeg.getPnrSegId())) {
                    segment.setTicketStatus(StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus2()) ? pnrNmTicket.getTicketStatus2() : "OPEN FOR USE");
                } else {
                    segment.setTicketStatus("OPEN FOR USE");
                }

                segment.setFlightNo(pnrSeg.getFlightNo());
                segment.setCabinCode(pnrSeg.getSellCabin());
                segment.setDepartureDate(pnrSeg.getFlightDate());
                segment.setDepartureTime(pnrSeg.getEstimateOff().substring(0, 2) + ":" + pnrSeg.getEstimateOff().substring(2) + ":00");
                segment.setSegmentType("2");

                segments.add(segment);
            }
        }

        return segments;
    }

    /**
     * 设置价格信息
     */
    private void setPriceInfo(PreviewRefundTicketVo.Ticket ticket, String pnrId, String pnrNmId,
                             boolean isInfant, int segmentCount) {
        // 7. 查询FN信息
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;
        BigDecimal commission = BigDecimal.ZERO;

        if (nmFn != null) {
            totalAmount = nmFn.getFPrice() != null ? nmFn.getFPrice() : BigDecimal.ZERO;
            cnTax = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqTax = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 8. 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnrId)
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                totalAmount = pnrFn.getFPrice() != null ? pnrFn.getFPrice() : BigDecimal.ZERO;
                cnTax = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqTax = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算代理费：每个航段5元
        commission = BigDecimal.valueOf(segmentCount * 5L);

        // 设置税费信息
        List<PreviewRefundTicketVo.Tax> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax cnTaxInfo = new PreviewRefundTicketVo.Tax();
            cnTaxInfo.setName("CN");
            cnTaxInfo.setValue(cnTax.toString());
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax yqTaxInfo = new PreviewRefundTicketVo.Tax();
            yqTaxInfo.setName("YQ");
            yqTaxInfo.setValue(yqTax.toString());
            taxes.add(yqTaxInfo);
        }

        BigDecimal totalTaxes = cnTax.add(yqTax);

        ticket.setTotalAmount(totalAmount.toString());
        ticket.setTotalTaxs(totalTaxes.toString());
        ticket.setTaxs(taxes);
        ticket.setCommission(commission.toString());
    }

    /**
     * 构建退票计算信息
     */
    private PreviewRefundTicketVo.RefundCompute buildRefundCompute(
            MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm, boolean isInfant) {

        PreviewRefundTicketVo.RefundCompute refundCompute = new PreviewRefundTicketVo.RefundCompute();
        refundCompute.setRemark(null);
        refundCompute.setConjunction("1.00");
        refundCompute.setCreditCard("");

        // 构建金额信息
        PreviewRefundTicketVo.Amount amount = new PreviewRefundTicketVo.Amount();

        // 查询价格信息
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNm.getPnrNmId())
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;

        if (nmFn != null) {
            totalAmount = nmFn.getFPrice() != null ? nmFn.getFPrice() : BigDecimal.ZERO;
            cnTax = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqTax = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                totalAmount = pnrFn.getFPrice() != null ? pnrFn.getFPrice() : BigDecimal.ZERO;
                cnTax = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqTax = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算航段数量
        int segmentCount = 0;
        if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id())) segmentCount++;
        if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id())) segmentCount++;

        // 计算代理费：每个航段5元
        BigDecimal commission = BigDecimal.valueOf(segmentCount * 5);

        // 9. 计算退票手续费：票面价的10%
        BigDecimal otherDeduction = totalAmount.multiply(BigDecimal.valueOf(0.1))
                .setScale(2, RoundingMode.HALF_UP);

        // 计算总税费
        BigDecimal totalTaxes = cnTax.add(yqTax);

        // 10. 计算应退费用：票面价+总税费-代理费-退票手续费
        BigDecimal netRefund = totalAmount.add(totalTaxes).subtract(commission).subtract(otherDeduction);

        // 设置税费列表
        List<PreviewRefundTicketVo.Tax> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax cnTaxInfo = new PreviewRefundTicketVo.Tax();
            cnTaxInfo.setName("CN");
            cnTaxInfo.setValue(cnTax.toString());
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax yqTaxInfo = new PreviewRefundTicketVo.Tax();
            yqTaxInfo.setName("YQ");
            yqTaxInfo.setValue(yqTax.toString());
            taxes.add(yqTaxInfo);
        }

        amount.setCommision(NumberUtils.formatBigDecimalStr(commission.toString()));
        amount.setTotalAmount(totalAmount.toString());
        amount.setCommisionRate("0.00");
        amount.setNetRefund(netRefund.toString());
        amount.setOtherDeduction(otherDeduction.toString());
        amount.setTaxs(taxes);
        amount.setTotalTaxs(totalTaxes.toString());
        amount.setTicketNo(null);

        refundCompute.setAmount(amount);

        return refundCompute;
    }
