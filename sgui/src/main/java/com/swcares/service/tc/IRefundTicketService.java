package com.swcares.service.tc;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.BatchAutoRefundDto;
import com.swcares.obj.dto.BatchFindRefundFeeDto;
import com.swcares.obj.dto.DeletePnrAndDeleteInfantInfoDto;
import com.swcares.obj.dto.FindRefundTicketDto;
import com.swcares.obj.dto.PreviewRefundTicketDto;
import com.swcares.obj.dto.QueryPnrMessageDto;
import com.swcares.obj.dto.QueryRefundFormDto;
import com.swcares.obj.vo.BatchAutoRefundVo;
import com.swcares.obj.vo.BatchFindRefundFeeVo;
import com.swcares.obj.vo.DeletePnrAndDeleteInfantInfoVo;
import com.swcares.obj.vo.FindRefundTicketVo;
import com.swcares.obj.vo.PreviewRefundTicketVo;
import com.swcares.obj.vo.QueryPnrMessageVo;
import com.swcares.obj.vo.QueryRefundFormVo;

/**
 * 退票服务接口
 *
 * <AUTHOR>
 * @date 2025/1/2 13:00
 */
public interface IRefundTicketService {

    /**
     * 初始加载退票信息
     *
     * @param dto 查询参数
     * @return 退票信息
     * @throws SguiResultException 异常
     */
    FindRefundTicketVo findRefundTicket(FindRefundTicketDto dto) throws SguiResultException;

    /**
     * 退前预览
     *
     * @param dto 查询参数
     * @return 退前预览信息
     * @throws SguiResultException 异常
     */
    PreviewRefundTicketVo previewRefundTicket(PreviewRefundTicketDto dto) throws SguiResultException;

    /**
     * 退票PNR信息查询
     *
     * @param dto 查询参数
     * @return PNR信息
     * @throws SguiResultException 异常
     */
    QueryPnrMessageVo queryPnrMessage(QueryPnrMessageDto dto) throws SguiResultException;

    /**
     * 计算退票价格
     *
     * @param dto 查询参数
     * @return 退票价格信息
     * @throws SguiResultException 异常
     */
    BatchFindRefundFeeVo batchFindRefundFee(BatchFindRefundFeeDto dto) throws SguiResultException;

    /**
     * 自动退票
     *
     * @param dto 查询参数
     * @return 退票结果
     * @throws SguiResultException 异常
     */
    BatchAutoRefundVo batchAutoRefund(BatchAutoRefundDto dto) throws SguiResultException;

    /**
     * 退票取消PNR
     *
     * @param dto 查询参数
     * @return 取消结果
     * @throws SguiResultException 异常
     */
    DeletePnrAndDeleteInfantInfoVo deletePnrAndDeleteInfantInfo(DeletePnrAndDeleteInfantInfoDto dto) throws SguiResultException;

    /**
     * 查询客票管理机构
     *
     * @param ticketNo 票号
     * @return 客票管理机构名称
     * @throws SguiResultException 异常
     */
    String queryTicketManagementOrganization(String ticketNo) throws SguiResultException;

    /**
     * 查询退票单号
     *
     * @param dto 查询参数
     * @return 退票单号信息
     * @throws SguiResultException 异常
     */
    QueryRefundFormVo queryRefundForm(QueryRefundFormDto dto) throws SguiResultException;
}
