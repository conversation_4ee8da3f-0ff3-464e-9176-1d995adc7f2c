package com.swcares.service.tc;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.FindRefundTicketDto;
import com.swcares.obj.dto.PreviewRefundTicketDto;
import com.swcares.obj.dto.QueryPnrMessageDto;
import com.swcares.obj.vo.FindRefundTicketVo;
import com.swcares.obj.vo.PreviewRefundTicketVo;
import com.swcares.obj.vo.QueryPnrMessageVo;

/**
 * 退票服务接口
 *
 * <AUTHOR>
 * @date 2025/1/2 13:00
 */
public interface IRefundTicketService {

    /**
     * 初始加载退票信息
     *
     * @param dto 查询参数
     * @return 退票信息
     * @throws SguiResultException 异常
     */
    FindRefundTicketVo findRefundTicket(FindRefundTicketDto dto) throws SguiResultException;

    /**
     * 退前预览
     *
     * @param dto 查询参数
     * @return 退前预览信息
     * @throws SguiResultException 异常
     */
    PreviewRefundTicketVo previewRefundTicket(PreviewRefundTicketDto dto) throws SguiResultException;

    /**
     * 退票PNR信息查询
     *
     * @param dto 查询参数
     * @return PNR信息
     * @throws SguiResultException 异常
     */
    QueryPnrMessageVo queryPnrMessage(QueryPnrMessageDto dto) throws SguiResultException;
}
