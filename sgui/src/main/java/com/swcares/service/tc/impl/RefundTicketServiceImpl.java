package com.swcares.service.tc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.core.util.NumberUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.BatchFindRefundFeeDto;
import com.swcares.obj.dto.FindRefundTicketDto;
import com.swcares.obj.dto.PreviewRefundTicketDto;
import com.swcares.obj.dto.QueryPnrMessageDto;
import com.swcares.obj.vo.BatchFindRefundFeeVo;
import com.swcares.obj.vo.FindRefundTicketVo;
import com.swcares.obj.vo.PreviewRefundTicketVo;
import com.swcares.obj.vo.QueryPnrMessageVo;
import com.swcares.service.*;
import com.swcares.service.tc.IRefundTicketService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 退票服务实现类
 *
 * <AUTHOR>
 * @date 2025/1/2 13:00
 */
@Service
public class RefundTicketServiceImpl implements IRefundTicketService {

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Override
    public FindRefundTicketVo findRefundTicket(FindRefundTicketDto dto) throws SguiResultException {
        // 参数验证
        if (StrUtil.isEmpty(dto.getTktNo())) {
            throw new SguiResultException("票号不能为空");
        }

        // 处理票号格式，去掉"-"
        String formattedTicketNo = dto.getTktNo().replace("-", "");

        // 1. 根据票号查询mnjx_pnr_nm_ticket表
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("票号不存在");
        }

        // 2. 根据ticket的tnId查询mnjx_pnr_nm_tn表
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
        if (pnrNmTn == null) {
            throw new SguiResultException("票号关联信息不存在");
        }

        // 判断是否为婴儿票
        boolean isInfant = StrUtil.isNotEmpty(pnrNmTn.getNmXnId());
        String pnrNmId;
        String passengerName = null;
        String psgType = null;

        if (isInfant) {
            // 4. 婴儿处理：根据nmXnId查询mnjx_nm_xn表
            MnjxNmXn nmXn = iMnjxNmXnService.getById(pnrNmTn.getNmXnId());
            if (nmXn == null) {
                throw new SguiResultException("婴儿信息不存在");
            }
            pnrNmId = nmXn.getPnrNmId();
            passengerName = nmXn.getXnCname();
            psgType = "INF";
        } else {
            // 3. 成人/儿童处理：根据pnrNmId查询mnjx_pnr_nm表
            pnrNmId = pnrNmTn.getPnrNmId();
        }

        // 查询旅客详细信息
        MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        if (pnrNm == null) {
            throw new SguiResultException("旅客信息不存在");
        }

        if (!isInfant) {
            passengerName = pnrNm.getName();
            psgType = this.convertPsgType(pnrNm.getPsgType());
        }

        // 5. 根据pnrId查询mnjx_pnr表
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        if (pnr == null) {
            throw new SguiResultException("PNR信息不存在");
        }

        // 构建返回对象
        FindRefundTicketVo result = new FindRefundTicketVo();

        // 构建票务信息
        FindRefundTicketVo.TicketInfo ticketInfo = this.buildTicketInfo(
                pnrNmTicket, pnrNmTn, pnr, pnrNm, passengerName, psgType, isInfant, dto.getSecondFactor());
        result.setTicket(ticketInfo);

        // 设置其他信息
        MnjxSi issuedSi = iMnjxSiService.getById(pnrNmTn.getIssuedSiId());
        MnjxOffice issuedOffice = iMnjxOfficeService.getById(issuedSi.getOfficeId());
        MnjxAgent issuedAgent = iMnjxAgentService.getById(issuedOffice.getOrgId());
        result.setOffice(issuedOffice.getOfficeNo());
        result.setIata(issuedAgent.getAgentIata());
        result.setAgent(issuedSi.getSiNo());

        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        result.setOperator(userInfo.getSiNo());

        result.setConjunction("1");

        result.setTicketManagementOrganizationCode("BSP");

        MnjxPrinter printer = iMnjxPrinterService.getById(pnrNmTn.getPrinterId());
        result.setPrinterNo(printer.getPrinterNo());

        result.setRefundPrintNumber(null);

        result.setReceiptPrinted("0");

        return result;
    }

    @Override
    public PreviewRefundTicketVo previewRefundTicket(PreviewRefundTicketDto dto) throws SguiResultException {
        // 参数验证
        if (StrUtil.isEmpty(dto.getTktNo())) {
            throw new SguiResultException("票号不能为空");
        }

        // 处理票号格式，去掉"-"
        String formattedTicketNo = dto.getTktNo().replace("-", "");

        // 1. 根据票号查询mnjx_pnr_nm_ticket表
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("票号不存在");
        }

        // 2. 根据ticket的tnId查询mnjx_pnr_nm_tn表
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
        if (pnrNmTn == null) {
            throw new SguiResultException("票号关联信息不存在");
        }

        // 判断是否为婴儿票
        boolean isInfant = StrUtil.isNotEmpty(pnrNmTn.getNmXnId());
        String pnrNmId;
        String passengerName = null;
        String psgType = null;

        if (isInfant) {
            // 4. 婴儿处理：根据nmXnId查询mnjx_nm_xn表
            MnjxNmXn nmXn = iMnjxNmXnService.getById(pnrNmTn.getNmXnId());
            if (nmXn == null) {
                throw new SguiResultException("婴儿信息不存在");
            }
            pnrNmId = nmXn.getPnrNmId();
            passengerName = nmXn.getXnCname();
            psgType = "INF";
        } else {
            // 3. 成人/儿童处理：根据pnrNmId查询mnjx_pnr_nm表
            pnrNmId = pnrNmTn.getPnrNmId();
        }

        // 查询旅客详细信息
        MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        if (pnrNm == null) {
            throw new SguiResultException("旅客信息不存在");
        }

        if (!isInfant) {
            passengerName = pnrNm.getName();
            psgType = this.convertPsgType(pnrNm.getPsgType());
        }

        // 5. 根据pnrId查询mnjx_pnr表
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        if (pnr == null) {
            throw new SguiResultException("PNR信息不存在");
        }

        // 构建返回对象
        PreviewRefundTicketVo result = new PreviewRefundTicketVo();

        // 构建退票订单信息
        PreviewRefundTicketVo.RefundTicketOrder refundTicketOrder = this.buildRefundTicketOrder(
                pnrNmTicket, pnrNmTn, pnr, pnrNm, passengerName, psgType, isInfant, dto.getSecondFactor());
        result.setRefundTicketOrder(refundTicketOrder);

        // 构建退票计算信息
        PreviewRefundTicketVo.RefundCompute refundCompute = this.buildRefundCompute(
                pnrNmTicket, pnrNmTn, pnr, pnrNm, isInfant);
        result.setRefundCompute(refundCompute);

        // 设置二次验证信息
        PreviewRefundTicketVo.SecondFactor secondFactor = new PreviewRefundTicketVo.SecondFactor();
        secondFactor.setSecondFactorCode(dto.getSecondFactor().getSecondFactorCode());
        secondFactor.setSecondFactorValue(dto.getSecondFactor().getSecondFactorValue());
        result.setSecondFactor(secondFactor);

        return result;
    }

    /**
     * 转换旅客类型
     */
    private String convertPsgType(String psgType) {
        if (StrUtil.isEmpty(psgType)) {
            return "ADT";
        }
        switch (psgType) {
            case "0":
                return "ADT"; // 成人
            case "1":
                return "CHD"; // 儿童
            case "2":
                return "UM";  // 无人陪伴旅客
            case "4":
                return "GMJC";
            default:
                return "ADT";
        }
    }

    /**
     * 构建票务信息
     */
    private FindRefundTicketVo.TicketInfo buildTicketInfo(
            MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm,
            String passengerName, String psgType, boolean isInfant,
            FindRefundTicketDto.SecondFactor secondFactorDto) {

        FindRefundTicketVo.TicketInfo ticketInfo = new FindRefundTicketVo.TicketInfo();

        // 设置基本信息
        ticketInfo.setTktType("D");
        ticketInfo.setCdsTicket(false);
        ticketInfo.setTicketNo(pnrNmTicket.getTicketNo());
        ticketInfo.setPsgType(psgType);
        ticketInfo.setTicketPsgType(psgType);
        ticketInfo.setName(passengerName);
        ticketInfo.setPassengerNameSuffix(passengerName);
        ticketInfo.setSpecialPassengerType(psgType);
        ticketInfo.setPayType("");
        ticketInfo.setCurrency("CNY");
        ticketInfo.setEtTag("1");
        ticketInfo.setAirline(pnrNmTicket.getTicketNo().substring(0, 3)); // 航司结算码
        ticketInfo.setPnr(pnr.getPnrIcs());
        ticketInfo.setCrsPnrNo(pnr.getPnrCrs());
        ticketInfo.setIsCoupon("0");
        ticketInfo.setIsAirportCntl("0");
        ticketInfo.setExchangeTktNo("");
        ticketInfo.setGovernmentPurchase(false);
        ticketInfo.setCommissionRate("0.00");

        // 构建航段信息
        List<FindRefundTicketVo.SegmentInfo> segments = this.buildSegmentInfos(pnrNmTicket, pnr.getPnrId());
        ticketInfo.setSegment(segments);

        // 设置市场航空公司
        ticketInfo.setMarketAirline(pnrNmTn.getIssuedAirline());

        // 查询价格信息
        this.setTicketPriceInfo(ticketInfo, pnrNm.getPnrId(), pnrNm.getPnrNmId(), isInfant, segments.size());

        // 设置二次验证信息
        if (secondFactorDto != null) {
            FindRefundTicketVo.SecondFactor secondFactor = new FindRefundTicketVo.SecondFactor();
            secondFactor.setSecondFactorCode(secondFactorDto.getSecondFactorCode());
            secondFactor.setSecondFactorValue(secondFactorDto.getSecondFactorValue());
            ticketInfo.setSecondFactor(secondFactor);
        }

        return ticketInfo;
    }

    /**
     * 构建航段信息
     */
    private List<FindRefundTicketVo.SegmentInfo> buildSegmentInfos(MnjxPnrNmTicket pnrNmTicket, String pnrId) {
        List<FindRefundTicketVo.SegmentInfo> segments = new ArrayList<>();

        // 查询航段信息
        List<String> segmentIds = new ArrayList<>();
        if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id())) {
            segmentIds.add(pnrNmTicket.getS1Id());
        }
        if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id())) {
            segmentIds.add(pnrNmTicket.getS2Id());
        }

        if (!segmentIds.isEmpty()) {
            List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.listByIds(segmentIds);

            for (int i = 0; i < pnrSegList.size(); i++) {
                MnjxPnrSeg pnrSeg = pnrSegList.get(i);
                FindRefundTicketVo.SegmentInfo segment = new FindRefundTicketVo.SegmentInfo();

                segment.setAirline(pnrSeg.getFlightNo().substring(0, 2));
                segment.setIsAble("1");
                segment.setDepartureCode(pnrSeg.getOrg());
                segment.setArriveCode(pnrSeg.getDst());
                segment.setRph("1-" + (i + 1));
                segment.setIsCheck(null);
                segment.setTktTag(pnrNmTicket.getTicketNo());
                segment.setE8Rph(String.valueOf(i + 1));

                // 设置票状态
                if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id()) && pnrNmTicket.getS1Id().equals(pnrSeg.getPnrSegId())) {
                    segment.setTicketStatus(StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus1()) ? pnrNmTicket.getTicketStatus1() : "OPEN FOR USE");
                } else if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id()) && pnrNmTicket.getS2Id().equals(pnrSeg.getPnrSegId())) {
                    segment.setTicketStatus(StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus2()) ? pnrNmTicket.getTicketStatus2() : "OPEN FOR USE");
                } else {
                    segment.setTicketStatus("OPEN FOR USE");
                }

                segment.setFlightNo(pnrSeg.getFlightNo());
                segment.setCabinCode(pnrSeg.getSellCabin());
                segment.setDepartureDate(pnrSeg.getFlightDate());
                segment.setDepartureTime(pnrSeg.getEstimateOff().substring(0, 2) + ":" + pnrSeg.getEstimateOff().substring(2) + ":00");
                segment.setSegmentType("2");

                segments.add(segment);
            }
        }

        return segments;
    }

    /**
     * 设置票务价格信息
     */
    private void setTicketPriceInfo(FindRefundTicketVo.TicketInfo ticketInfo, String pnrId, String pnrNmId,
                                    boolean isInfant, int segmentCount) {
        // 查询FN信息
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;
        BigDecimal commission = BigDecimal.ZERO;

        if (nmFn != null) {
            totalAmount = nmFn.getFPrice() != null ? nmFn.getFPrice() : BigDecimal.ZERO;
            cnTax = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqTax = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnrId)
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                totalAmount = pnrFn.getFPrice() != null ? pnrFn.getFPrice() : BigDecimal.ZERO;
                cnTax = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqTax = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算代理费：每个航段5元
        commission = BigDecimal.valueOf(segmentCount * 5L);

        // 设置税费信息
        List<FindRefundTicketVo.TaxInfo> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            FindRefundTicketVo.TaxInfo cnTaxInfo = new FindRefundTicketVo.TaxInfo();
            cnTaxInfo.setName("CN");
            cnTaxInfo.setValue(cnTax.toString());
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            FindRefundTicketVo.TaxInfo yqTaxInfo = new FindRefundTicketVo.TaxInfo();
            yqTaxInfo.setName("YQ");
            yqTaxInfo.setValue(yqTax.toString());
            taxes.add(yqTaxInfo);
        }

        BigDecimal totalTaxes = cnTax.add(yqTax);

        ticketInfo.setTotalAmount(totalAmount.toString());
        ticketInfo.setTotalTaxs(totalTaxes.toString());
        ticketInfo.setTaxs(taxes);
        ticketInfo.setCommission(commission.toString());
    }

    /**
     * 构建退票订单信息
     */
    private PreviewRefundTicketVo.RefundTicketOrder buildRefundTicketOrder(
            MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm,
            String passengerName, String psgType, boolean isInfant,
            PreviewRefundTicketDto.SecondFactor secondFactorDto) {

        PreviewRefundTicketVo.RefundTicketOrder refundTicketOrder = new PreviewRefundTicketVo.RefundTicketOrder();

        // 构建票信息
        PreviewRefundTicketVo.Ticket ticket = new PreviewRefundTicketVo.Ticket();
        ticket.setTktType("D");
        ticket.setCdsTicket(false);
        ticket.setTicketNo(pnrNmTicket.getTicketNo());
        ticket.setPsgType(psgType);
        ticket.setTicketPsgType(psgType);
        ticket.setName(passengerName);
        ticket.setPassengerNameSuffix(passengerName);
        ticket.setSpecialPassengerType(psgType);
        ticket.setPayType("");
        ticket.setCurrency("CNY");
        ticket.setEtTag("1");
        ticket.setAirline(pnrNmTicket.getTicketNo().substring(0, 3)); // 航司结算码
        ticket.setPnr(pnr.getPnrIcs());
        ticket.setCrsPnrNo(pnr.getPnrCrs());
        ticket.setIsCoupon("0");
        ticket.setIsAirportCntl("0");
        ticket.setExchangeTktNo("");
        ticket.setGovernmentPurchase(false);
        ticket.setCommissionRate("0.00");

        // 构建航段信息
        List<PreviewRefundTicketVo.Segment> segments = this.buildSegments(pnrNmTicket, pnr.getPnrId());
        ticket.setSegment(segments);

        // 设置市场航空公司（取第一个航段的航空公司）
        if (!segments.isEmpty()) {
            ticket.setMarketAirline(segments.get(0).getAirline());
        }

        // 查询价格信息
        this.setPriceInfo(ticket, pnrNm.getPnrId(), pnrNm.getPnrNmId(), isInfant, segments.size());

        // 设置二次验证信息
        PreviewRefundTicketVo.SecondFactor secondFactor = new PreviewRefundTicketVo.SecondFactor();
        secondFactor.setSecondFactorCode(secondFactorDto.getSecondFactorCode());
        secondFactor.setSecondFactorValue(secondFactorDto.getSecondFactorValue());
        ticket.setSecondFactor(secondFactor);

        refundTicketOrder.setTicket(ticket);

        // 设置其他订单信息
        refundTicketOrder.setOffice("LXA101");
        refundTicketOrder.setIata("08323431");
        refundTicketOrder.setAgent("49041");
        refundTicketOrder.setOperator("49041");
        refundTicketOrder.setConjunction("1");
        refundTicketOrder.setTicketManagementOrganizationCode("BSP");
        refundTicketOrder.setPrinterNo("1");
        refundTicketOrder.setRefundPrintNumber(null);
        refundTicketOrder.setReceiptPrinted("0");

        return refundTicketOrder;
    }

    /**
     * 构建航段信息（用于PreviewRefundTicket）
     */
    private List<PreviewRefundTicketVo.Segment> buildSegments(MnjxPnrNmTicket pnrNmTicket, String pnrId) {
        List<PreviewRefundTicketVo.Segment> segments = new ArrayList<>();

        // 查询航段信息
        List<String> segmentIds = new ArrayList<>();
        if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id())) {
            segmentIds.add(pnrNmTicket.getS1Id());
        }
        if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id())) {
            segmentIds.add(pnrNmTicket.getS2Id());
        }

        if (!segmentIds.isEmpty()) {
            List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.listByIds(segmentIds);

            for (int i = 0; i < pnrSegList.size(); i++) {
                MnjxPnrSeg pnrSeg = pnrSegList.get(i);
                PreviewRefundTicketVo.Segment segment = new PreviewRefundTicketVo.Segment();

                segment.setAirline(pnrSeg.getFlightNo().substring(0, 2));
                segment.setIsAble("1");
                segment.setDepartureCode(pnrSeg.getOrg());
                segment.setArriveCode(pnrSeg.getDst());
                segment.setRph("1-" + (i + 1));
                segment.setIsCheck(null);
                segment.setTktTag(pnrNmTicket.getTicketNo());
                segment.setE8Rph(String.valueOf(i + 1));

                // 设置票状态
                if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id()) && pnrNmTicket.getS1Id().equals(pnrSeg.getPnrSegId())) {
                    segment.setTicketStatus(StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus1()) ? pnrNmTicket.getTicketStatus1() : "OPEN FOR USE");
                } else if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id()) && pnrNmTicket.getS2Id().equals(pnrSeg.getPnrSegId())) {
                    segment.setTicketStatus(StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus2()) ? pnrNmTicket.getTicketStatus2() : "OPEN FOR USE");
                } else {
                    segment.setTicketStatus("OPEN FOR USE");
                }

                segment.setFlightNo(pnrSeg.getFlightNo());
                segment.setCabinCode(pnrSeg.getSellCabin());
                segment.setDepartureDate(pnrSeg.getFlightDate());
                segment.setDepartureTime(pnrSeg.getEstimateOff().substring(0, 2) + ":" + pnrSeg.getEstimateOff().substring(2) + ":00");
                segment.setSegmentType("2");

                segments.add(segment);
            }
        }

        return segments;
    }

    /**
     * 设置价格信息（用于PreviewRefundTicket）
     */
    private void setPriceInfo(PreviewRefundTicketVo.Ticket ticket, String pnrId, String pnrNmId,
                              boolean isInfant, int segmentCount) {
        // 查询FN信息
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;
        BigDecimal commission = BigDecimal.ZERO;

        if (nmFn != null) {
            totalAmount = nmFn.getFPrice() != null ? nmFn.getFPrice() : BigDecimal.ZERO;
            cnTax = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqTax = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnrId)
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                totalAmount = pnrFn.getFPrice() != null ? pnrFn.getFPrice() : BigDecimal.ZERO;
                cnTax = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqTax = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算代理费：每个航段5元
        commission = BigDecimal.valueOf(segmentCount * 5L);

        // 设置税费信息
        List<PreviewRefundTicketVo.Tax> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax cnTaxInfo = new PreviewRefundTicketVo.Tax();
            cnTaxInfo.setName("CN");
            cnTaxInfo.setValue(cnTax.toString());
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax yqTaxInfo = new PreviewRefundTicketVo.Tax();
            yqTaxInfo.setName("YQ");
            yqTaxInfo.setValue(yqTax.toString());
            taxes.add(yqTaxInfo);
        }

        BigDecimal totalTaxes = cnTax.add(yqTax);

        ticket.setTotalAmount(totalAmount.toString());
        ticket.setTotalTaxs(totalTaxes.toString());
        ticket.setTaxs(taxes);
        ticket.setCommission(commission.toString());
    }

    /**
     * 构建退票计算信息
     */
    private PreviewRefundTicketVo.RefundCompute buildRefundCompute(
            MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm, boolean isInfant) {

        PreviewRefundTicketVo.RefundCompute refundCompute = new PreviewRefundTicketVo.RefundCompute();
        refundCompute.setRemark(null);
        refundCompute.setConjunction("1.00");
        refundCompute.setCreditCard("");

        // 构建金额信息
        PreviewRefundTicketVo.Amount amount = new PreviewRefundTicketVo.Amount();

        // 查询价格信息
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNm.getPnrNmId())
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;

        if (nmFn != null) {
            totalAmount = nmFn.getFPrice() != null ? nmFn.getFPrice() : BigDecimal.ZERO;
            cnTax = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqTax = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                totalAmount = pnrFn.getFPrice() != null ? pnrFn.getFPrice() : BigDecimal.ZERO;
                cnTax = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqTax = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算航段数量
        int segmentCount = 0;
        if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id())) segmentCount++;
        if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id())) segmentCount++;

        // 计算代理费：每个航段5元
        BigDecimal commission = BigDecimal.valueOf(segmentCount * 5);

        // 计算退票手续费：票面价的10%
        BigDecimal otherDeduction = totalAmount.multiply(BigDecimal.valueOf(0.1))
                .setScale(2, RoundingMode.HALF_UP);

        // 计算总税费
        BigDecimal totalTaxes = cnTax.add(yqTax);

        // 计算应退费用：票面价+总税费-代理费-退票手续费
        BigDecimal netRefund = totalAmount.add(totalTaxes).subtract(commission).subtract(otherDeduction);

        // 设置税费列表
        List<PreviewRefundTicketVo.Tax> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax cnTaxInfo = new PreviewRefundTicketVo.Tax();
            cnTaxInfo.setName("CN");
            cnTaxInfo.setValue(cnTax.toString());
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax yqTaxInfo = new PreviewRefundTicketVo.Tax();
            yqTaxInfo.setName("YQ");
            yqTaxInfo.setValue(yqTax.toString());
            taxes.add(yqTaxInfo);
        }

        amount.setCommision(NumberUtils.formatBigDecimalStr(commission.toString()));
        amount.setTotalAmount(totalAmount.toString());
        amount.setCommisionRate("0.00");
        amount.setNetRefund(netRefund.toString());
        amount.setOtherDeduction(otherDeduction.toString());
        amount.setTaxs(taxes);
        amount.setTotalTaxs(totalTaxes.toString());
        amount.setTicketNo(null);

        refundCompute.setAmount(amount);

        return refundCompute;
    }

    @Override
    public QueryPnrMessageVo queryPnrMessage(QueryPnrMessageDto dto) throws SguiResultException {
        // 参数验证
        if (StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 1. 通过pnrNo查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();

        if (pnr == null) {
            throw new SguiResultException("PNR不存在");
        }

        QueryPnrMessageVo result = new QueryPnrMessageVo();

        // 2. 查询PNR中所有旅客信息，包括婴儿，构建passengers数据
        List<QueryPnrMessageVo.Passenger> passengers = this.buildPassengers(pnr, dto.getPnrNo());
        result.setPassengers(passengers);

        // 5. 查询PNR中所有的航班构建flights
        List<QueryPnrMessageVo.Flight> flights = this.buildFlights(pnr);
        result.setFlights(flights);

        // 设置其他信息
        result.setTicketTypeCode(null);
        result.setPnrNo(pnr.getPnrIcs());
        result.setCsrPnrNo(pnr.getPnrCrs());

        return result;
    }

    /**
     * 构建旅客信息
     */
    private List<QueryPnrMessageVo.Passenger> buildPassengers(MnjxPnr pnr, String pnrNo) {
        List<QueryPnrMessageVo.Passenger> passengers = new ArrayList<>();

        // 查询PNR中所有旅客信息
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrNm::getPsgIndex)
                .list();

        for (MnjxPnrNm pnrNm : pnrNmList) {
            QueryPnrMessageVo.Passenger passenger = new QueryPnrMessageVo.Passenger();

            // 设置基本信息
            passenger.setPassengerName(pnrNm.getName());
            passenger.setPassengerNameSuffix(pnrNm.getName());
            String psgType = this.convertPsgType(pnrNm.getPsgType());
            passenger.setPassengerType(psgType);
            passenger.setPnrPsgType(psgType);
            passenger.setSpecialPassengerType(psgType);
            passenger.setIndex("P" + pnrNm.getPsgIndex());

            // 3. secondFactorCode设置CN，secondFactorValue设置dto的PNRNo
            QueryPnrMessageVo.SecondFactor secondFactor = new QueryPnrMessageVo.SecondFactor();
            secondFactor.setSecondFactorCode("CN");
            secondFactor.setSecondFactorValue(pnrNo);
            passenger.setSecondFactor(secondFactor);

            // 4. 通过旅客查询tn，获取票号
            List<String> ticketNos = this.getTicketNosByPnrNm(pnrNm.getPnrNmId());
            passenger.setTicketNos(ticketNos);

            // 查询婴儿信息
            QueryPnrMessageVo.Infant infant = this.buildInfant(pnrNm.getPnrNmId(), pnrNo, "P" + pnrNm.getPsgIndex());
            passenger.setInfants(infant);

            passengers.add(passenger);
        }

        return passengers;
    }

    /**
     * 构建婴儿信息
     */
    private List<QueryPnrMessageVo.Infant> buildInfants(String pnrNmId, String pnrNo) {
        List<QueryPnrMessageVo.Infant> infants = new ArrayList<>();

        // 查询该旅客的婴儿信息
        List<MnjxNmXn> nmXnList = iMnjxNmXnService.lambdaQuery()
                .eq(MnjxNmXn::getPnrNmId, pnrNmId)
                .list();

        for (MnjxNmXn nmXn : nmXnList) {
            QueryPnrMessageVo.Infant infant = new QueryPnrMessageVo.Infant();

            infant.setInfantName(nmXn.getXnCname());
            infant.setInfantNameSuffix(nmXn.getXnCname());
            infant.setInfantType("INF");

            // 设置二次验证信息
            QueryPnrMessageVo.SecondFactor secondFactor = new QueryPnrMessageVo.SecondFactor();
            secondFactor.setSecondFactorCode("CN");
            secondFactor.setSecondFactorValue(pnrNo);
            infant.setSecondFactor(secondFactor);

            // 查询婴儿的票号
            List<String> infantTicketNos = this.getTicketNosByNmXn(nmXn.getNmXnId());
            infant.setTicketNos(infantTicketNos);

            infants.add(infant);
        }

        return infants;
    }

    /**
     * 通过pnrNmId获取票号列表
     */
    private List<String> getTicketNosByPnrNm(String pnrNmId) {
        List<String> ticketNos = new ArrayList<>();

        // 查询该旅客的tn记录
        List<MnjxPnrNmTn> tnList = iMnjxPnrNmTnService.lambdaQuery()
                .eq(MnjxPnrNmTn::getPnrNmId, pnrNmId)
                .isNull(MnjxPnrNmTn::getNmXnId) // 排除婴儿票
                .list();

        for (MnjxPnrNmTn tn : tnList) {
            // 查询该tn的票号
            List<MnjxPnrNmTicket> tickets = iMnjxPnrNmTicketService.lambdaQuery()
                    .eq(MnjxPnrNmTicket::getPnrNmTnId, tn.getTnId())
                    .list();

            for (MnjxPnrNmTicket ticket : tickets) {
                // 格式化票号
                String ticketNo = ticket.getTicketNo();
                if (StrUtil.isNotEmpty(ticketNo) && ticketNo.length() >= 10) {
                    String prefix = ticketNo.substring(0, 3);
                    String suffix = ticketNo.substring(3);
                    ticketNos.add(prefix + "-" + suffix);
                } else {
                    ticketNos.add(ticketNo);
                }
            }
        }

        return ticketNos;
    }

    /**
     * 通过nmXnId获取婴儿票号列表
     */
    private List<String> getTicketNosByNmXn(String nmXnId) {
        List<String> ticketNos = new ArrayList<>();

        // 查询婴儿的tn记录
        List<MnjxPnrNmTn> tnList = iMnjxPnrNmTnService.lambdaQuery()
                .eq(MnjxPnrNmTn::getNmXnId, nmXnId)
                .list();

        for (MnjxPnrNmTn tn : tnList) {
            // 查询该tn的票号
            List<MnjxPnrNmTicket> tickets = iMnjxPnrNmTicketService.lambdaQuery()
                    .eq(MnjxPnrNmTicket::getPnrNmTnId, tn.getTnId())
                    .list();

            for (MnjxPnrNmTicket ticket : tickets) {
                // 格式化票号
                String ticketNo = ticket.getTicketNo();
                if (StrUtil.isNotEmpty(ticketNo) && ticketNo.length() >= 10) {
                    String prefix = ticketNo.substring(0, 3);
                    String suffix = ticketNo.substring(3);
                    ticketNos.add(prefix + "-" + suffix);
                } else {
                    ticketNos.add(ticketNo);
                }
            }
        }

        return ticketNos;
    }

    /**
     * 构建航班信息
     */
    private List<QueryPnrMessageVo.Flight> buildFlights(MnjxPnr pnr) {
        List<QueryPnrMessageVo.Flight> flights = new ArrayList<>();

        // 查询PNR中所有航段信息
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .ne(MnjxPnrSeg::getPnrSegType, "SA") // 排除地面段
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        for (MnjxPnrSeg pnrSeg : pnrSegList) {
            QueryPnrMessageVo.Flight flight = new QueryPnrMessageVo.Flight();

            // 设置基本航班信息
            flight.setFlightNo(pnrSeg.getFlightNo());
            flight.setAirline(pnrSeg.getFlightNo().substring(0, 2));
            flight.setCabin(pnrSeg.getSellCabin());
            flight.setOperationAirline(null);
            flight.setDepartureCode(pnrSeg.getOrg());
            flight.setArrivalCode(pnrSeg.getDst());
            flight.setActionCode(pnrSeg.getActionCode());
            flight.setTktNum(null);
            flight.setLineIndex(pnrSeg.getPnrIndex().toString());
            flight.setCodeShare(false);
            flight.setArrDays(null);

            // 设置时间信息
            if (StrUtil.isNotEmpty(pnrSeg.getFlightDate()) && StrUtil.isNotEmpty(pnrSeg.getEstimateOff())) {
                String departureTime = pnrSeg.getFlightDate() + " " +
                        pnrSeg.getEstimateOff().substring(0, 2) + ":" + pnrSeg.getEstimateOff().substring(2);
                flight.setDepartureTime(departureTime);
            }

            if (StrUtil.isNotEmpty(pnrSeg.getFlightDate()) && StrUtil.isNotEmpty(pnrSeg.getEstimateArr())) {
                String arrivalTime = pnrSeg.getFlightDate() + " " +
                        pnrSeg.getEstimateArr().substring(0, 2) + ":" + pnrSeg.getEstimateArr().substring(2);
                flight.setArrivalTime(arrivalTime);
            }

            // 设置航站楼信息
            flight.setDepartureTerminal("T2");
            flight.setArrivalTerminal("T2");

            // 6. 通过tnId和航班的segId查询ticket，设置flights中每个航班对应的票号ticketNos
            List<QueryPnrMessageVo.TicketNoInfo> ticketNoInfos = this.getTicketNosBySegment(pnrSeg.getPnrSegId());
            flight.setTicketNos(ticketNoInfos);

            flights.add(flight);
        }

        return flights;
    }

    /**
     * 通过航段ID获取票号信息
     */
    private List<QueryPnrMessageVo.TicketNoInfo> getTicketNosBySegment(String segmentId) {
        List<QueryPnrMessageVo.TicketNoInfo> ticketNoInfos = new ArrayList<>();

        // 查询包含该航段的票务信息
        List<MnjxPnrNmTicket> tickets = iMnjxPnrNmTicketService.lambdaQuery()
                .and(wrapper -> wrapper.eq(MnjxPnrNmTicket::getS1Id, segmentId)
                        .or()
                        .eq(MnjxPnrNmTicket::getS2Id, segmentId))
                .list();

        int number = 1;
        for (MnjxPnrNmTicket ticket : tickets) {
            QueryPnrMessageVo.TicketNoInfo ticketNoInfo = new QueryPnrMessageVo.TicketNoInfo();
            ticketNoInfo.setNumber(String.valueOf(number++));
            ticketNoInfo.setTicketNo(ticket.getTicketNo()); // 不格式化，保持原格式
            ticketNoInfos.add(ticketNoInfo);
        }

        return ticketNoInfos;
    }

    @Override
    public BatchFindRefundFeeVo batchFindRefundFee(BatchFindRefundFeeDto dto) throws SguiResultException {
        // 参数验证
        if (CollUtil.isEmpty(dto.getTktNos())) {
            throw new SguiResultException("票号列表不能为空");
        }

        BatchFindRefundFeeVo result = new BatchFindRefundFeeVo();
        List<BatchFindRefundFeeVo.QueryRefundFeeAggregateRespDTO> respList = new ArrayList<>();
        List<String> successTicketNos = new ArrayList<>();
        List<String> failedTicketNos = new ArrayList<>();

        // 处理每个票号
        for (String tktNo : dto.getTktNos()) {
            try {
                // 处理票号格式，去掉"-"
                String formattedTicketNo = tktNo.replace("-", "");

                // 1. 通过票号查询ticket，获取tnId
                MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                        .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                        .one();

                if (pnrNmTicket == null) {
                    failedTicketNos.add(this.formatTicketNo(tktNo));
                    continue;
                }

                // 2. 通过tnId查询tn，获取pnrNmId或nmXnId
                MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
                if (pnrNmTn == null) {
                    failedTicketNos.add(this.formatTicketNo(tktNo));
                    continue;
                }

                // 判断是否为婴儿票
                boolean isInfant = StrUtil.isNotEmpty(pnrNmTn.getNmXnId());
                String pnrNmId = isInfant ?
                    iMnjxNmXnService.getById(pnrNmTn.getNmXnId()).getPnrNmId() :
                    pnrNmTn.getPnrNmId();

                // 查询旅客信息
                MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
                if (pnrNm == null) {
                    failedTicketNos.add(this.formatTicketNo(tktNo));
                    continue;
                }

                // 查询PNR信息
                MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
                if (pnr == null) {
                    failedTicketNos.add(this.formatTicketNo(tktNo));
                    continue;
                }

                // 4. 构建退票费用数据
                BatchFindRefundFeeVo.QueryRefundFeeAggregateRespDTO respDTO =
                    this.buildRefundFeeRespDTO(pnrNmTicket, pnrNmTn, pnr, pnrNm, isInfant, tktNo);
                respList.add(respDTO);

                successTicketNos.add(this.formatTicketNo(tktNo));

            } catch (Exception e) {
                failedTicketNos.add(this.formatTicketNo(tktNo));
            }
        }

        result.setQueryRefundFeeAggregateRespDTOList(respList);
        result.setStatus("SUCCESS");
        result.setMsg(null);
        result.setSuccessTicketNos(successTicketNos);
        result.setFailedTicketNos(failedTicketNos);

        return result;
    }

    /**
     * 格式化票号
     */
    private String formatTicketNo(String ticketNo) {
        if (StrUtil.isEmpty(ticketNo)) {
            return ticketNo;
        }

        // 如果已经包含"-"，直接返回
        if (ticketNo.contains("-")) {
            return ticketNo;
        }

        // 如果长度足够，添加"-"
        if (ticketNo.length() >= 10) {
            String prefix = ticketNo.substring(0, 3);
            String suffix = ticketNo.substring(3);
            return prefix + "-" + suffix;
        }

        return ticketNo;
    }

    /**
     * 构建退票费用响应DTO
     */
    private BatchFindRefundFeeVo.QueryRefundFeeAggregateRespDTO buildRefundFeeRespDTO(
            MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm,
            boolean isInfant, String originalTicketNo) {

        BatchFindRefundFeeVo.QueryRefundFeeAggregateRespDTO respDTO =
            new BatchFindRefundFeeVo.QueryRefundFeeAggregateRespDTO();

        // 设置基本信息
        respDTO.setConjunction("1.00");
        respDTO.setRemark(null);
        respDTO.setCreditCard("");
        respDTO.setPayType("CASH");
        respDTO.setCurrency("");
        respDTO.setSegList(new ArrayList<>());
        respDTO.setMsg(null);

        // 5. amount参考退前预览计算退票中各价格的处理方式
        BatchFindRefundFeeVo.Amount amount = this.buildRefundAmount(
            pnrNmTicket, pnrNmTn, pnr, pnrNm, isInfant, originalTicketNo);
        respDTO.setAmount(amount);

        return respDTO;
    }

    /**
     * 构建退票金额信息
     */
    private BatchFindRefundFeeVo.Amount buildRefundAmount(
            MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm,
            boolean isInfant, String originalTicketNo) {

        BatchFindRefundFeeVo.Amount amount = new BatchFindRefundFeeVo.Amount();

        // 3. 查询旅客运价，如果没有则通过pnrId查询pnr级别的运价
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNm.getPnrNmId())
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;

        if (nmFn != null) {
            totalAmount = nmFn.getFPrice() != null ? nmFn.getFPrice() : BigDecimal.ZERO;
            cnTax = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqTax = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                totalAmount = pnrFn.getFPrice() != null ? pnrFn.getFPrice() : BigDecimal.ZERO;
                cnTax = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqTax = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算航段数量
        int segmentCount = 0;
        if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id())) segmentCount++;
        if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id())) segmentCount++;

        // 计算代理费：每个航段5元
        BigDecimal commission = BigDecimal.valueOf(segmentCount * 5);

        // 计算退票手续费：票面价的10%
        BigDecimal otherDeduction = totalAmount.multiply(BigDecimal.valueOf(0.1))
                .setScale(2, RoundingMode.HALF_UP);

        // 计算总税费
        BigDecimal totalTaxes = cnTax.add(yqTax);

        // 计算应退费用：票面价+总税费-代理费-退票手续费
        BigDecimal netRefund = totalAmount.add(totalTaxes).subtract(commission).subtract(otherDeduction);

        // 设置税费列表
        List<BatchFindRefundFeeVo.Tax> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            BatchFindRefundFeeVo.Tax cnTaxInfo = new BatchFindRefundFeeVo.Tax();
            cnTaxInfo.setName("CN");
            cnTaxInfo.setValue(cnTax.toString());
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            BatchFindRefundFeeVo.Tax yqTaxInfo = new BatchFindRefundFeeVo.Tax();
            yqTaxInfo.setName("YQ");
            yqTaxInfo.setValue(yqTax.toString());
            taxes.add(yqTaxInfo);
        }

        // 设置金额信息
        amount.setCommision(NumberUtils.formatBigDecimalStr(commission.toString()));
        amount.setTotalAmount(totalAmount.toString());
        amount.setCommisionRate("0.00");
        amount.setNetRefund(netRefund.toString());
        amount.setOtherDeduction(otherDeduction.toString());
        amount.setTaxs(taxes);
        amount.setTotalTaxs(totalTaxes.toString());
        amount.setTicketNo(this.formatTicketNo(originalTicketNo));

        return amount;
    }
}
