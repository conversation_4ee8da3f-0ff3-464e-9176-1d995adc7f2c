package com.swcares.service.et;

import com.swcares.core.exception.SguiResultException;
import com.swcares.entity.*;
import com.swcares.obj.dto.BookPnrDto;
import com.swcares.obj.vo.BookPnrVo;

import java.util.List;

/**
 * 生成PNR服务接口
 *
 * <AUTHOR>
 * @date 2025/5/19 14:00
 */
public interface IBookPnrService {

    /**
     * 生成PNR
     *
     * @param dto 请求参数
     * @return PNR信息
     * @throws SguiResultException 异常
     */
    BookPnrVo bookPnr(BookPnrDto dto) throws SguiResultException;

    void reorderPnrIndexes(MnjxPnr pnr, List<MnjxPnrNm> pnrNmList, List<MnjxPnrSeg> pnrSegList,
                           List<MnjxPnrCt> pnrCtList, List<MnjxNmCt> nmCtList, List<MnjxPnrTk> pnrTkList,
                           List<MnjxPnrFc> pnrFcList, List<MnjxNmFc> nmFcList, List<MnjxNmSsr> nmSsrList,
                           List<MnjxPnrOsi> pnrOsiList, List<MnjxNmOsi> nmOsiList, List<MnjxPnrRmk> pnrRmkList,
                           List<MnjxNmRmk> nmRmkList, List<MnjxPnrTc> pnrTcList, List<MnjxNmTc> nmTcList, List<MnjxPnrFn> pnrFnList,
                           List<MnjxNmFn> nmFnList, List<MnjxPnrEi> pnrEiList, List<MnjxNmEi> nmEiList,
                           List<MnjxNmOi> nmOiList, List<MnjxPnrNmTn> pnrTnList, List<MnjxNmXn> nmXnList,
                           List<MnjxPnrFp> pnrFpList, List<MnjxNmFp> nmFpList, List<MnjxPnrRecord> pnrRecordList);
}
