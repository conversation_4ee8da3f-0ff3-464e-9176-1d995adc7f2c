package com.swcares.service.et;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.QueryFlightsDto;
import com.swcares.obj.dto.QueryMultiInfosDto;
import com.swcares.obj.vo.QueryFlightsVo;
import com.swcares.obj.vo.QueryMultiInfosVo;

/**
 * AV航班查询服务接口
 *
 * <AUTHOR>
 * @date 2025/5/8 13:51
 */
public interface IEtAvService {

    /**
     * 查询航班
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    QueryFlightsVo queryFlights(QueryFlightsDto dto) throws SguiResultException;

    /**
     * 多日期航班查询
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    QueryMultiInfosVo queryMultiInfos(QueryMultiInfosDto dto) throws SguiResultException;
}