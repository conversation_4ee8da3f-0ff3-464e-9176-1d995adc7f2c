package com.swcares.service.et.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.core.cache.FlightCacheConstants;
import com.swcares.core.exception.SguiResultException;
import com.swcares.entity.*;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;
import com.swcares.service.*;
import com.swcares.service.et.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/25 14:04
 */
@Slf4j
@Service
public class EtV2ServiceImpl implements IEtV2Service {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IEtAvService iEtAvService;

    @Resource
    private IEtSkService iEtSkService;

    @Resource
    private IBookPnrService iBookPnrService;

    @Resource
    private IEtCommonService iEtCommonService;

    @Resource
    private ISguiDataService iSguiDataService;

    @Resource
    private IMnjxCountryService iMnjxCountryService;

    @Resource
    private IMnjxContinentService iMnjxContinentService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxCityService iMnjxCityService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IMnjxStandardPatService iMnjxStandardPatService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Resource
    private com.swcares.mapper.EtV2Mapper etV2Mapper;

    @Resource
    private IModifySegStatusService iModifySegStatusService;

    @Override
    public SkVo querySk(QuerySkDto skDto) throws SguiResultException {
        return iEtSkService.querySk(skDto);
    }

    @Override
    public QueryFlightsVo queryFlights(QueryFlightsDto dto) throws SguiResultException {
        return iEtAvService.queryFlights(dto);
    }

    @Override
    public QueryMultiInfosVo queryMultiInfos(QueryMultiInfosDto dto) throws SguiResultException {
        // 调用EtAvService中的queryMultiInfos方法
        return iEtAvService.queryMultiInfos(dto);
    }

    @Override
    public List<QueryQuotaVo> quota(QueryQuotaDto dto) throws SguiResultException {
        // 参数校验
        if (StrUtil.isEmpty(dto.getFlightNo())) {
            throw new SguiResultException("航班号不能为空");
        }
        if (StrUtil.isEmpty(dto.getOption())) {
            throw new SguiResultException("特殊服务选项不能为空");
        }
        if (StrUtil.isEmpty(dto.getDepartureDate())) {
            throw new SguiResultException("出发日期不能为空");
        }
        if (StrUtil.isEmpty(dto.getDepartureTime())) {
            throw new SguiResultException("出发时间不能为空");
        }

        // 解析特殊服务选项
        String[] options = dto.getOption().split(",");

        // 创建返回列表
        List<QueryQuotaVo> voList = new ArrayList<>();

        // 处理每个特殊服务选项
        for (String option : options) {
            QueryQuotaVo vo = new QueryQuotaVo();
            vo.setOption(option);
            vo.setQuotaNumber(null); // 实际应用中，这里应该查询数据库获取配额数量
            voList.add(vo);
        }

        return voList;
    }

    @Override
    public List<PublicFaresVo> queryPublicFares(QueryPublicFaresDto dto) throws SguiResultException {
        // 参数校验
        if (StrUtil.isEmpty(dto.getDepartureDate())) {
            throw new SguiResultException("旅行日期参数错误");
        }
        if (StrUtil.isNotEmpty(dto.getIssueDate()) && DateUtil.compare(DateUtil.parseDate(dto.getIssueDate()), DateUtil.parseDate(dto.getDepartureDate())) > 0) {
            throw new SguiResultException("销售日期应早于或等于旅行日期");
        }
        if (StrUtil.isEmpty(dto.getDepartureCity())) {
            throw new SguiResultException("始发地参数错误");
        }
        if (StrUtil.isEmpty(dto.getArriveCity())) {
            throw new SguiResultException("目的地参数错误");
        }
        if (StrUtil.isNotEmpty(dto.getAirCode()) && !ReUtil.isMatch("[A-Z0-9]{2}", dto.getAirCode())) {
            throw new SguiResultException("不是合法的航空公司二字码");
        }

        List<PublicFaresVo> voList = new ArrayList<>();

        try {
            // 1. 获取出发城市和到达城市的机场ID列表
            List<String> departureAirportIdList = iSguiCommonService.getAirportIdList(dto.getDepartureCity());
            List<String> arriveAirportIdList = iSguiCommonService.getAirportIdList(dto.getArriveCity());

            if (CollUtil.isEmpty(departureAirportIdList)) {
                throw new SguiResultException("未找到出发城市的机场信息");
            }
            if (CollUtil.isEmpty(arriveAirportIdList)) {
                throw new SguiResultException("未找到到达城市的机场信息");
            }

            // 2. 查询符合条件的航节ID
            List<String> tcardIds = etV2Mapper.retrieveTcardIdsByCityPair(departureAirportIdList, arriveAirportIdList);
            if (CollUtil.isEmpty(tcardIds)) {
                return voList; // 没有找到符合条件的航节，返回空列表
            }

            // 3. 查询航节信息
            List<MnjxTcard> tcardList = iMnjxTcardService.listByIds(tcardIds);
            if (CollUtil.isEmpty(tcardList)) {
                return voList;
            }

            // 4. 查询航班计划
            List<MnjxPlanFlight> planFlightList = iEtCommonService.retrievePlanFlight(dto.getDepartureDate(), tcardIds);
            if (CollUtil.isEmpty(planFlightList)) {
                return voList;
            }

            // 5. 查询航班信息
            List<String> flightIdList = tcardList.stream()
                    .map(MnjxTcard::getFlightId)
                    .distinct()
                    .collect(Collectors.toList());
            List<MnjxFlight> flightList = iMnjxFlightService.listByIds(flightIdList);

            // 如果指定了航空公司，我们不在这里过滤，而是在后面的开舱数据处理时过滤
            // 这样可以确保所有航空公司的数据都被正确处理

            if (CollUtil.isEmpty(flightList) || CollUtil.isEmpty(tcardList) || CollUtil.isEmpty(planFlightList)) {
                return voList;
            }

            // 6. 查询航段计划
            List<String> planFlightIdList = planFlightList.stream()
                    .map(MnjxPlanFlight::getPlanFlightId)
                    .collect(Collectors.toList());
            List<MnjxPlanSection> planSectionList = iEtCommonService.retrievePlanSection(null, planFlightIdList);
            if (CollUtil.isEmpty(planSectionList)) {
                return voList;
            }

            // 7. 查询开舱数据
            List<String> planSectionIdList = planSectionList.stream()
                    .map(MnjxPlanSection::getPlanSectionId)
                    .collect(Collectors.toList());
            List<MnjxOpenCabin> allOpenCabinList = iMnjxOpenCabinService.lambdaQuery()
                    .in(MnjxOpenCabin::getPlanSectionId, planSectionIdList)
                    .list();

            // 如果指定了舱位，过滤开舱数据
            if (StrUtil.isNotEmpty(dto.getCabin())) {
                allOpenCabinList = allOpenCabinList.stream()
                        .filter(openCabin -> dto.getCabin().equals(openCabin.getSellCabin()))
                        .collect(Collectors.toList());
            }

            if (CollUtil.isEmpty(allOpenCabinList)) {
                return voList;
            }

            // 8. 按航班计划分组处理开舱数据
            List<MnjxOpenCabin> openCabinList = new ArrayList<>();

            // 按航班计划ID分组航段
            Map<String, List<MnjxPlanSection>> planFlightSectionsMap = planSectionList.stream()
                    .collect(Collectors.groupingBy(MnjxPlanSection::getPlanFlightId));

            // 处理每个航班计划的航段
            for (Map.Entry<String, List<MnjxPlanSection>> entry : planFlightSectionsMap.entrySet()) {
                List<MnjxPlanSection> flightSections = entry.getValue();

                // 获取当前航班计划的开舱数据
                List<String> sectionIds = flightSections.stream()
                        .map(MnjxPlanSection::getPlanSectionId)
                        .collect(Collectors.toList());

                List<MnjxOpenCabin> flightOpenCabins = allOpenCabinList.stream()
                        .filter(cabin -> sectionIds.contains(cabin.getPlanSectionId()))
                        .collect(Collectors.toList());

                // 调用getOpenCabins处理当前航班的开舱数据
                List<MnjxOpenCabin> flightCabins = iEtCommonService.getOpenCabins(flightSections, departureAirportIdList, arriveAirportIdList, flightOpenCabins);

                // 添加到总结果中
                openCabinList.addAll(flightCabins);
            }

            if (CollUtil.isEmpty(openCabinList)) {
                return voList;
            }

            // 9. 构建航空公司ID到航空公司对象的映射
            Map<String, MnjxAirline> airlineMap = new HashMap<>();
            for (MnjxFlight flight : flightList) {
                if (!airlineMap.containsKey(flight.getAirlineId())) {
                    MnjxAirline airline = iMnjxAirlineService.getById(flight.getAirlineId());
                    if (airline != null) {
                        airlineMap.put(flight.getAirlineId(), airline);
                    }
                }
            }

            // 10. 构建航段ID到航段对象的映射
            Map<String, MnjxPlanSection> planSectionMap = planSectionList.stream()
                    .collect(Collectors.toMap(MnjxPlanSection::getPlanSectionId, section -> section));

            // 11. 构建航班计划ID到航班计划对象的映射
            Map<String, MnjxPlanFlight> planFlightMap = planFlightList.stream()
                    .collect(Collectors.toMap(MnjxPlanFlight::getPlanFlightId, planFlight -> planFlight));

            // 12. 构建航节ID到航节对象的映射
            Map<String, MnjxTcard> tcardMap = tcardList.stream()
                    .collect(Collectors.toMap(MnjxTcard::getTcardId, tcard -> tcard));

            // 13. 构建航班ID到航班对象的映射
            Map<String, MnjxFlight> flightMap = flightList.stream()
                    .collect(Collectors.toMap(MnjxFlight::getFlightId, flight -> flight));

            // 14. 按航空公司和销售舱位分组开舱数据
            Map<String, Map<String, List<MnjxOpenCabin>>> airlineCabinMap = new HashMap<>();

            for (MnjxOpenCabin openCabin : openCabinList) {
                MnjxPlanSection planSection = planSectionMap.get(openCabin.getPlanSectionId());
                if (planSection == null) {
                    continue;
                }

                MnjxPlanFlight planFlight = planFlightMap.get(planSection.getPlanFlightId());
                if (planFlight == null) {
                    continue;
                }

                MnjxTcard tcard = tcardMap.get(planFlight.getTcardId());
                if (tcard == null) {
                    continue;
                }

                MnjxFlight flight = flightMap.get(tcard.getFlightId());
                if (flight == null) {
                    continue;
                }

                MnjxAirline airline = airlineMap.get(flight.getAirlineId());
                if (airline == null) {
                    continue;
                }

                String airlineCode = airline.getAirlineCode();
                String sellCabin = openCabin.getSellCabin();

                if (!airlineCabinMap.containsKey(airlineCode)) {
                    airlineCabinMap.put(airlineCode, new HashMap<>());
                }

                Map<String, List<MnjxOpenCabin>> cabinMap = airlineCabinMap.get(airlineCode);
                if (!cabinMap.containsKey(sellCabin)) {
                    cabinMap.put(sellCabin, new ArrayList<>());
                }

                cabinMap.get(sellCabin).add(openCabin);
            }

            // 15. 当前日期时间
            Date now = new Date();
            String saleDate = DateUtil.format(now, "yyyy-MM-dd");
            String saleTime = DateUtil.format(now, "HH:mm:ss");

            // 16. 创建一个列表来存储所有的运价数据
            List<PublicFaresVo> tempVoList = new ArrayList<>();

            // 17. 处理每个航空公司的销售舱位
            for (Map.Entry<String, Map<String, List<MnjxOpenCabin>>> airlineEntry : airlineCabinMap.entrySet()) {
                String airlineCode = airlineEntry.getKey();

                // 如果指定了航空公司，只处理指定的航空公司
                if (StrUtil.isNotEmpty(dto.getAirCode()) && !dto.getAirCode().equals(airlineCode)) {
                    continue;
                }

                Map<String, List<MnjxOpenCabin>> cabinMap = airlineEntry.getValue();

                for (Map.Entry<String, List<MnjxOpenCabin>> cabinEntry : cabinMap.entrySet()) {
                    String sellCabin = cabinEntry.getKey();
                    List<MnjxOpenCabin> cabinList = cabinEntry.getValue();

                    // 处理每个航空公司的每个销售舱位
                    Map<String, Integer> cabinCountMap = new HashMap<>(); // 用于跟踪每个舱位的计数
                    Map<String, List<Integer>> cabinPriceMap = new HashMap<>(); // 用于跟踪每个舱位的价格

                    for (MnjxOpenCabin openCabin : cabinList) {
                        MnjxPlanSection planSection = planSectionMap.get(openCabin.getPlanSectionId());
                        if (planSection == null) {
                            continue;
                        }

                        MnjxPlanFlight planFlight = planFlightMap.get(planSection.getPlanFlightId());
                        if (planFlight == null) {
                            continue;
                        }

                        MnjxTcard tcard = tcardMap.get(planFlight.getTcardId());
                        if (tcard == null) {
                            continue;
                        }

                        PublicFaresVo vo = new PublicFaresVo();
                        vo.setCarrier(airlineCode);

                        // 相同运价跳过
                        if (cabinPriceMap.containsKey(openCabin.getSellCabin())) {
                            List<Integer> priceList = cabinPriceMap.get(openCabin.getSellCabin());
                            if (priceList.contains(openCabin.getSellCabinPrice())) {
                                continue;
                            } else {
                                priceList.add(openCabin.getSellCabinPrice());
                                cabinPriceMap.put(openCabin.getSellCabin(), priceList);
                            }
                        } else {
                            List<Integer> priceList = new ArrayList<>();
                            priceList.add(openCabin.getSellCabinPrice());
                            cabinPriceMap.put(openCabin.getSellCabin(), priceList);
                        }
                        // 设置单程价格和往返价格
                        vo.setFarePriceOW(String.valueOf(openCabin.getSellCabinPrice()));
                        vo.setFarePriceRT(String.valueOf(openCabin.getSellCabinPrice() * 2));

                        // 设置fbc，对于同一个舱位，第一个出现的设置为sellCabin，后续的设置为sellCabin1、sellCabin2等
                        int count = cabinCountMap.getOrDefault(sellCabin, 0);
                        if (count == 0) {
                            vo.setFbc(sellCabin);
                        } else {
                            vo.setFbc(sellCabin + count);
                        }
                        cabinCountMap.put(sellCabin, count + 1);

                        // 设置rbdCode为sellCabin
                        vo.setRbdCode(sellCabin);

                        // 设置cabin
                        String cabinClass = openCabin.getCabinClass();
                        if ("J".equals(cabinClass)) {
                            vo.setCabin("FIRST");
                        } else if ("G".equals(cabinClass)) {
                            vo.setCabin("BUSINESS");
                        } else if ("Y".equals(cabinClass)) {
                            vo.setCabin("ECONOMY");
                        } else {
                            vo.setCabin(cabinClass);
                        }

                        // 设置travelTo和travelFrom
                        // 将Date类型转换为String类型，使用yyyy-MM-dd格式
                        if (tcard.getStartDate() != null) {
                            vo.setTravelFrom(DateUtil.format(tcard.getStartDate(), "yyyy-MM-dd"));
                        }

                        if (tcard.getEndDate() != null) {
                            vo.setTravelTo(DateUtil.format(tcard.getEndDate(), "yyyy-MM-dd"));
                        }

                        // 设置saleDate和saleTime
                        vo.setSaleDate(saleDate);
                        vo.setSaleTime(saleTime);

                        // 设置fareRule为PFN
                        vo.setFareRule("PFN");

                        // 查询基准运价表获取距离
                        try {
                            MnjxCity orgCity = iSguiCommonService.getCityByCityCode(dto.getDepartureCity());
                            MnjxCity dstCity = iSguiCommonService.getCityByCityCode(dto.getArriveCity());

                            if (orgCity != null && dstCity != null) {
                                MnjxStandardPat standardPat = iMnjxStandardPatService.lambdaQuery()
                                        .eq(MnjxStandardPat::getOrgCityId, orgCity.getCityId())
                                        .eq(MnjxStandardPat::getDstCityId, dstCity.getCityId())
                                        .one();

                                if (standardPat == null) {
                                    // 尝试反向查询
                                    standardPat = iMnjxStandardPatService.lambdaQuery()
                                            .eq(MnjxStandardPat::getOrgCityId, dstCity.getCityId())
                                            .eq(MnjxStandardPat::getDstCityId, orgCity.getCityId())
                                            .one();
                                }

                                if (standardPat != null) {
                                    vo.setDistance(String.valueOf(standardPat.getDistance()));
                                }
                            }
                        } catch (Exception e) {
                            // 忽略距离查询异常
                        }

                        tempVoList.add(vo);
                    }
                }
            }

            // 18. 将所有运价数据按价格从低到高排序
            tempVoList.sort((vo1, vo2) -> {
                try {
                    int price1 = Integer.parseInt(vo1.getFarePriceOW());
                    int price2 = Integer.parseInt(vo2.getFarePriceOW());
                    return Integer.compare(price1, price2);
                } catch (NumberFormatException e) {
                    return 0;
                }
            });

            // 19. 将排序后的结果添加到返回列表中
            voList.addAll(tempVoList);

        } catch (Exception e) {
            throw new SguiResultException("查询运价失败: " + e.getMessage());
        }

        return voList;
    }

    @Override
    public FaresRuleVo queryRuleDisplay(QueryRuleDisplayDto dto) throws SguiResultException {
        if (StrUtil.isNotEmpty(dto.getCarrier()) && !ReUtil.isMatch("[A-Z0-9]{2}", dto.getCarrier())) {
            throw new SguiResultException("航司输入错误");
        }
        if (StrUtil.isNotEmpty(dto.getLanguageCode()) && !ReUtil.isMatch("[A-Za-z]{2}", dto.getLanguageCode())) {
            throw new SguiResultException("语言代码输入错误");
        }
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "rules_AV1H")
                .one();
        if (data != null) {
            return JSONUtil.parseObj(data.getValue()).toBean(FaresRuleVo.class);
        }
        return null;
    }

    @Override
    public ScmVo scm(ScmDto dto) {
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "scm")
                .one();
        if (data != null) {
            return JSONUtil.parseObj(data.getValue()).toBean(ScmVo.class);
        }
        return null;
    }

    @Override
    public List<InternationalPublicVo> internationalPublic(InternationalPublicDto dto) {
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "international_public")
                .one();
        if (data != null) {
            return JSONUtil.toList(JSONUtil.parseArray(data.getValue()), InternationalPublicVo.class);
        }
        return Collections.emptyList();
    }

    @Override
    public AssistQueryVo assistQuery(AssistQueryDto dto) {
        AssistQueryVo vo = new AssistQueryVo();
        if (dto.getQueryList().contains("FSM")) {
            SguiData data = iSguiDataService.lambdaQuery()
                    .eq(SguiData::getName, "assist_fsm")
                    .one();
            if (data != null) {
                return JSONUtil.parseObj(data.getValue()).toBean(AssistQueryVo.class);
            }
        } else {
            vo.setIet(true);
        }
        return vo;
    }

    @Override
    public InternationalPublicVo queryInternationalTax(QueryInternationalTaxDto dto) {
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "international_tax")
                .one();
        if (data != null) {
            return JSONUtil.parseObj(data.getValue()).toBean(InternationalPublicVo.class);
        }
        return null;
    }

    @Override
    public CurrencyOfLocationVo currencyOfLocation(CurrencyOfLocationDto dto) {
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "currency_of_location")
                .one();
        if (data != null) {
            return JSONUtil.parseObj(data.getValue()).toBean(CurrencyOfLocationVo.class);
        }
        return null;
    }

    @Override
    public List<QueryCountryVo> country(QueryCountryDto dto) throws SguiResultException {
        if (StrUtil.isAllEmpty(dto.getCountryCode(), dto.getCountryName())) {
            throw new SguiResultException("参数错误");
        }
        List<QueryCountryVo> voList = new ArrayList<>();
        List<MnjxCountry> countryList = new ArrayList<>();
        if (StrUtil.isNotEmpty(dto.getCountryName())) {
            countryList = iMnjxCountryService.lambdaQuery()
                    .like(MnjxCountry::getCountryCname, dto.getCountryName())
                    .list();
        } else {
            MnjxCountry country = iMnjxCountryService.lambdaQuery()
                    .eq(MnjxCountry::getCountryIso, dto.getCountryCode())
                    .or()
                    .eq(MnjxCountry::getCountryThreeCode, dto.getCountryCode())
                    .one();
            if (ObjectUtil.isNotEmpty(country)) {
                countryList.add(country);
            }
        }
        if (CollUtil.isNotEmpty(countryList)) {
            List<MnjxContinent> continentList = iMnjxContinentService.listByIds(
                    countryList.stream()
                            .map(MnjxCountry::getContinentId)
                            .collect(Collectors.toList())
            );
            for (MnjxCountry country : countryList) {
                QueryCountryVo vo = new QueryCountryVo();
                vo.setCountryCode(country.getCountryIso());
                vo.setCountryIsoCode(country.getCountryThreeCode());
                vo.setCountryCnName(country.getCountryCname());
                vo.setCountryEnName(country.getCountryEname());
                vo.setCapitalCode(country.getCapitalCode());
                continentList.stream()
                        .filter(c -> c.getContinentId().equals(country.getContinentId()))
                        .findFirst()
                        .ifPresent(c -> vo.setContinent(c.getContinentCname()));
                voList.add(vo);
            }
        }
        return voList;
    }

    @Override
    public List<QueryAirlineVo> airline(QueryAirlineDto dto) throws SguiResultException {
        if (StrUtil.isAllEmpty(dto.getAirlineCode(), dto.getAirlineName())) {
            throw new SguiResultException("参数错误");
        }
        List<QueryAirlineVo> voList = new ArrayList<>();
        List<MnjxAirline> airlineList = new ArrayList<>();
        if (StrUtil.isNotEmpty(dto.getAirlineCode())) {
            MnjxAirline airline = iSguiCommonService.getAirlineByCode(dto.getAirlineCode());
            if (ObjectUtil.isNotEmpty(airline)) {
                airlineList.add(airline);
            }
        } else {
            airlineList = iMnjxAirlineService.lambdaQuery()
                    .like(MnjxAirline::getAirlineFullName, dto.getAirlineName())
                    .list();
        }
        if (CollUtil.isNotEmpty(airlineList)) {
            List<MnjxCountry> countryList = iMnjxCountryService.listByIds(
                    airlineList.stream()
                            .map(MnjxAirline::getCountryId)
                            .collect(Collectors.toList())
            );
            for (MnjxAirline airline : airlineList) {
                QueryAirlineVo vo = new QueryAirlineVo();
                QueryAirlineVo.AirlineBaseInfo airlineBaseInfo = new QueryAirlineVo.AirlineBaseInfo();
                airlineBaseInfo.setAirlineCode(airline.getAirlineCode());
                airlineBaseInfo.setAirlineCnName(airline.getAirlineFullName());
                airlineBaseInfo.setAirlineSimpleCnName(airline.getAirlineShortName());
                airlineBaseInfo.setAirlineEnName(airline.getAirlineEname());
                airlineBaseInfo.setAirlineThreeCode(airline.getAirlineThreeCode());
                airlineBaseInfo.setAirlineNbr(airline.getAirlineSettlementCode());
                countryList.stream()
                        .filter(c -> c.getCountryId().equals(airline.getCountryId()))
                        .findFirst()
                        .ifPresent(c -> airlineBaseInfo.setCountryCode((c.getCountryIso())));
                airlineBaseInfo.setAirlineType("I");
                airlineBaseInfo.setAirlineContact(airline.getAirlineContactPhone());
                vo.setAirlineBaseInfo(airlineBaseInfo);
                voList.add(vo);
            }
        }
        return voList;
    }

    @Override
    public List<QueryAirportVo> airport(QueryAirportDecodeDto dto) throws SguiResultException {
        if (StrUtil.isEmpty(dto.getReq())) {
            throw new SguiResultException("参数错误");
        }
        Base64.Decoder decoder = Base64.getDecoder();
        String decode = new String(decoder.decode(dto.getReq()));
        QueryAirportDto airportDto = JSONUtil.toBean(decode, QueryAirportDto.class);
        if (StrUtil.isAllEmpty(airportDto.getCityCode(), airportDto.getCityName(), airportDto.getAirportCode(), airportDto.getAirportName())) {
            throw new SguiResultException("参数错误");
        }
        List<QueryAirportVo> voList = new ArrayList<>();

        // 通过城市查询，构建cityBaseInfo和对应城市下的所有机场airportInfoList
        if (StrUtil.isNotEmpty(airportDto.getCityCode()) || StrUtil.isNotEmpty(airportDto.getCityName())) {
            // 通过城市码精确查询
            if (StrUtil.isNotEmpty(airportDto.getCityCode())) {
                // 先查询城市信息
                MnjxCity city = iSguiCommonService.getCityByCityCode(airportDto.getCityCode());

                if (ObjectUtil.isNotEmpty(city)) {
                    // 查询该城市下的所有机场
                    List<MnjxAirport> airportList = iMnjxAirportService.lambdaQuery()
                            .eq(MnjxAirport::getCityId, city.getCityId())
                            .list();

                    if (CollUtil.isNotEmpty(airportList)) {
                        // 获取国家信息
                        MnjxCountry country = iMnjxCountryService.getById(city.getCountryId());

                        // 构建返回对象
                        QueryAirportVo vo = new QueryAirportVo();
                        vo.setCityBaseInfo(this.buildCityBaseInfo(city, country));

                        // 构建机场信息列表
                        List<QueryAirportVo.AirportInfo> airportInfoList = new ArrayList<>();
                        for (MnjxAirport airport : airportList) {
                            QueryAirportVo.AirportInfo airportInfo = this.buildAirportInfo(airport, city, country);
                            if (airportInfo != null) {
                                airportInfoList.add(airportInfo);
                            }
                        }
                        vo.setAirportInfoList(airportInfoList);

                        voList.add(vo);
                    }
                }
            }
            // 通过城市名称模糊查询
            else {
                List<MnjxCity> cityList = iMnjxCityService.lambdaQuery()
                        .like(MnjxCity::getCityCname, airportDto.getCityName())
                        .or()
                        .like(MnjxCity::getCityEname, airportDto.getCityName())
                        .list();

                if (CollUtil.isNotEmpty(cityList)) {
                    // 获取所有城市ID
                    List<String> cityIds = cityList.stream()
                            .map(MnjxCity::getCityId)
                            .collect(Collectors.toList());
                    // 查询这些城市下的所有机场
                    List<MnjxAirport> airportList = iMnjxAirportService.lambdaQuery()
                            .in(MnjxAirport::getCityId, cityIds)
                            .list();

                    // 获取所有国家ID
                    List<String> countryIds = cityList.stream()
                            .map(MnjxCity::getCountryId)
                            .collect(Collectors.toList());
                    // 查询这些国家信息
                    List<MnjxCountry> countryList = iMnjxCountryService.listByIds(countryIds);

                    // 构建城市ID到城市对象的映射
                    Map<String, MnjxCity> cityMap = cityList.stream()
                            .collect(Collectors.toMap(MnjxCity::getCityId, city -> city));
                    // 构建国家ID到国家对象的映射
                    Map<String, MnjxCountry> countryMap = countryList.stream()
                            .collect(Collectors.toMap(MnjxCountry::getCountryId, country -> country));

                    // 按城市分组构建返回对象
                    Map<String, List<MnjxAirport>> cityAirportMap = airportList.stream()
                            .collect(Collectors.groupingBy(MnjxAirport::getCityId));

                    for (Map.Entry<String, List<MnjxAirport>> entry : cityAirportMap.entrySet()) {
                        String cityId = entry.getKey();
                        List<MnjxAirport> airports = entry.getValue();
                        MnjxCity city = cityMap.get(cityId);

                        if (city != null && CollUtil.isNotEmpty(airports)) {
                            MnjxCountry country = countryMap.get(city.getCountryId());

                            // 构建返回对象
                            QueryAirportVo vo = new QueryAirportVo();
                            vo.setCityBaseInfo(this.buildCityBaseInfo(city, country));

                            // 构建机场信息列表
                            List<QueryAirportVo.AirportInfo> airportInfoList = new ArrayList<>();
                            for (MnjxAirport airport : airports) {
                                QueryAirportVo.AirportInfo airportInfo = this.buildAirportInfo(airport, city, country);
                                if (airportInfo != null) {
                                    airportInfoList.add(airportInfo);
                                }
                            }
                            vo.setAirportInfoList(airportInfoList);

                            voList.add(vo);
                        }
                    }
                }
            }
        }
        // 通过机场查询，构建airportBaseInfo
        else {
            // 通过机场三字码精确查询
            if (StrUtil.isNotEmpty(airportDto.getAirportCode())) {
                // 先查询机场信息
                MnjxAirport airport = iSguiCommonService.getAirportByCode(airportDto.getAirportCode());

                if (ObjectUtil.isNotEmpty(airport)) {
                    // 查询城市信息
                    MnjxCity city = iMnjxCityService.getById(airport.getCityId());
                    if (ObjectUtil.isNotEmpty(city)) {
                        // 获取国家信息
                        MnjxCountry country = iMnjxCountryService.getById(city.getCountryId());

                        // 构建返回对象
                        QueryAirportVo vo = new QueryAirportVo();
                        vo.setAirportBaseInfo(this.buildAirportBaseInfo(airport, city, country));
                        voList.add(vo);
                    }
                }
            }
            // 通过机场名称模糊查询
            else {
                List<MnjxAirport> airportList = iMnjxAirportService.lambdaQuery()
                        .like(MnjxAirport::getAirportCname, airportDto.getAirportName())
                        .or()
                        .like(MnjxAirport::getAirportEname, airportDto.getAirportName())
                        .list();

                if (CollUtil.isNotEmpty(airportList)) {
                    // 获取所有城市ID
                    List<String> cityIds = airportList.stream()
                            .map(MnjxAirport::getCityId)
                            .collect(Collectors.toList());
                    // 查询这些城市信息
                    List<MnjxCity> cityList = iMnjxCityService.listByIds(cityIds);

                    // 获取所有国家ID
                    List<String> countryIds = cityList.stream()
                            .map(MnjxCity::getCountryId)
                            .collect(Collectors.toList());
                    // 查询这些国家信息
                    List<MnjxCountry> countryList = iMnjxCountryService.listByIds(countryIds);

                    // 构建城市ID到城市对象的映射
                    Map<String, MnjxCity> cityMap = cityList.stream()
                            .collect(Collectors.toMap(MnjxCity::getCityId, city -> city));
                    // 构建国家ID到国家对象的映射
                    Map<String, MnjxCountry> countryMap = countryList.stream()
                            .collect(Collectors.toMap(MnjxCountry::getCountryId, country -> country));

                    for (MnjxAirport airport : airportList) {
                        MnjxCity city = cityMap.get(airport.getCityId());
                        if (city != null) {
                            MnjxCountry country = countryMap.get(city.getCountryId());

                            // 构建返回对象
                            QueryAirportVo vo = new QueryAirportVo();
                            vo.setAirportBaseInfo(this.buildAirportBaseInfo(airport, city, country));
                            voList.add(vo);
                        }
                    }
                }
            }
        }

        return voList;
    }

    @Override
    public QueryAirlineAgencyVo airlineAgency(String airlineCode, String cityCode) throws SguiResultException {
        if (StrUtil.isEmpty(airlineCode)) {
            throw new SguiResultException("航司二字码格式错误");
        }
        if (!ReUtil.isMatch("[A-Z0-9]{2}", airlineCode)) {
            throw new SguiResultException("航司二字码输入格式错误");
        }
        if (StrUtil.isNotEmpty(cityCode) && !ReUtil.isMatch("[A-Z]{3}", cityCode)) {
            throw new SguiResultException("城市三字码输入格式错误");
        }
        QueryAirlineAgencyVo vo = new QueryAirlineAgencyVo();
        return vo;
    }

    @Override
    public List<QueryTimeDiffVo> timeDiff(QueryTimeDiffDto dto) throws SguiResultException {
        if (StrUtil.isEmpty(dto.getOrigin()) || StrUtil.isEmpty(dto.getDestination()) || StrUtil.isEmpty(dto.getDateTime())) {
            throw new SguiResultException("参数错误");
        }
        if (!ReUtil.isMatch("[A-Z]{3}", dto.getOrigin()) || !ReUtil.isMatch("[A-Z]{3}(/[A-Z]{3})*", dto.getDestination())) {
            throw new SguiResultException("城市或机场三字码输入格式错误");
        }

        List<QueryTimeDiffVo> voList = new ArrayList<>();

        // 解析日期时间
        DateTime dateTime;
        try {
            dateTime = DateUtil.parse(dto.getDateTime(), "yyyy-MM-dd HH:mm");
        } catch (Exception e) {
            throw new SguiResultException("日期时间格式错误，应为 yyyy-MM-dd HH:mm");
        }

        // 查询出发地信息（可能是城市或机场）
        MnjxCity originCity = this.getCityByCode(dto.getOrigin());
        String originLocationCode = dto.getOrigin(); // 保留原始输入的代码

        // 如果不是城市，则尝试查询机场
        if (originCity == null) {
            MnjxAirport originAirport = this.getAirportByCode(dto.getOrigin());
            if (originAirport == null) {
                throw new SguiResultException("出发地不存在");
            }
            // 找到机场对应的城市
            originCity = iMnjxCityService.getById(originAirport.getCityId());
            if (originCity == null) {
                throw new SguiResultException("出发地机场对应的城市不存在");
            }
        }

        // 构建出发地时差信息
        QueryTimeDiffVo originVo = new QueryTimeDiffVo();
        originVo.setLocation(originLocationCode); // 使用原始输入的代码
        originVo.setLocationCnName(originCity.getCityCname()); // 始终使用城市中文名

        // 设置出发地时间
        originVo.setDate(DateUtil.format(dateTime, "yyyy-MM-dd"));
        originVo.setTime24Hr(DateUtil.format(dateTime, "HH:mm"));
        // 设置12小时制时间，使用AM/PM
        String time12Hr = DateUtil.format(dateTime, "hh:mm");
        String amPm = dateTime.getField(DateField.AM_PM) == 0 ? "AM" : "PM";
        originVo.setTime12Hr(time12Hr + amPm);

        // 设置GMT时差，默认为国内城市，时差为+8
        String originGmtTimeDiff = "8.0";
        originVo.setGmtTimeDiff(originGmtTimeDiff);

        // 计算GMT时间（将当前时间转换为GMT时间）
        DateTime gmtDateTime = DateUtil.offset(dateTime, DateField.HOUR, -8); // 国内时间比GMT时间快8小时
        originVo.setGmt(DateUtil.format(gmtDateTime, "yyyy-MM-dd HH:mm"));

        // 设置时差为0（相对于自身）
        originVo.setTimeDifference("0.0");

        // 添加出发地信息
        voList.add(originVo);

        // 处理目的地（可能有多个，用/分隔）
        String[] destinations = dto.getDestination().split("/");

        // 处理每个目的地
        for (String destination : destinations) {
            // 跳过空目的地
            if (StrUtil.isEmpty(destination)) {
                continue;
            }

            // 如果目的地与出发地相同，则跳过
            if (destination.equals(dto.getOrigin())) {
                continue;
            }

            // 查询目的地信息（可能是城市或机场）
            MnjxCity destinationCity = this.getCityByCode(destination);
            String destinationLocationCode = destination; // 保留原始输入的代码

            // 如果不是城市，则尝试查询机场
            if (destinationCity == null) {
                MnjxAirport destinationAirport = this.getAirportByCode(destination);
                if (destinationAirport == null) {
                    throw new SguiResultException("目的地" + destination + "不存在");
                }
                // 找到机场对应的城市
                destinationCity = iMnjxCityService.getById(destinationAirport.getCityId());
                if (destinationCity == null) {
                    throw new SguiResultException("目的地" + destination + "机场对应的城市不存在");
                }
            }

            // 构建目的地时差信息
            QueryTimeDiffVo destinationVo = new QueryTimeDiffVo();
            destinationVo.setLocation(destinationLocationCode); // 使用原始输入的代码
            destinationVo.setLocationCnName(destinationCity.getCityCname()); // 始终使用城市中文名

            // 设置GMT时差，默认为国内城市，时差为+8
            String destinationGmtTimeDiff = "8.0";
            destinationVo.setGmtTimeDiff(destinationGmtTimeDiff);

            // 计算目的地时间（与出发地时间相同，因为都是国内城市）
            destinationVo.setDate(DateUtil.format(dateTime, "yyyy-MM-dd"));
            destinationVo.setTime24Hr(DateUtil.format(dateTime, "HH:mm"));
            // 设置12小时制时间，使用AM/PM
            String destTime12Hr = DateUtil.format(dateTime, "hh:mm");
            String destAmPm = dateTime.getField(DateField.AM_PM) == 0 ? "AM" : "PM";
            destinationVo.setTime12Hr(destTime12Hr + destAmPm);
            destinationVo.setGmt(DateUtil.format(gmtDateTime, "yyyy-MM-dd HH:mm"));

            // 计算目的地与出发地的时差（国内城市时差为0）
            destinationVo.setTimeDifference("0.0");

            voList.add(destinationVo);
        }

        return voList;
    }

    /**
     * 根据机场代码从缓存或数据库中获取机场信息
     *
     * @param airportCode 机场代码
     * @return 机场信息
     */
    private MnjxAirport getAirportByCode(String airportCode) {
        if (StrUtil.isEmpty(airportCode)) {
            return null;
        }
        String airportKey = FlightCacheConstants.AIRPORT_PREFIX + airportCode;
        String airportJsonValue = stringRedisTemplate.opsForValue().get(airportKey);
        if (StrUtil.isNotEmpty(airportJsonValue)) {
            return JSONUtil.toBean(airportJsonValue, MnjxAirport.class);
        } else {
            return iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, airportCode)
                    .one();
        }
    }

    /**
     * 根据城市代码从缓存或数据库中获取城市信息
     *
     * @param cityCode 城市代码
     * @return 城市信息
     */
    private MnjxCity getCityByCode(String cityCode) {
        if (StrUtil.isEmpty(cityCode)) {
            return null;
        }
        String cityKey = FlightCacheConstants.CITY_PREFIX + cityCode;
        String cityJsonValue = stringRedisTemplate.opsForValue().get(cityKey);
        if (StrUtil.isNotEmpty(cityJsonValue)) {
            return JSONUtil.toBean(cityJsonValue, MnjxCity.class);
        } else {
            return iMnjxCityService.lambdaQuery()
                    .eq(MnjxCity::getCityCode, cityCode)
                    .one();
        }
    }

    /**
     * 构建城市基本信息
     *
     * @param city    城市对象
     * @param country 国家对象
     * @return 城市基本信息
     */
    private QueryAirportVo.CityBaseInfo buildCityBaseInfo(MnjxCity city, MnjxCountry country) {
        if (city == null) {
            return null;
        }
        QueryAirportVo.CityBaseInfo cityBaseInfo = new QueryAirportVo.CityBaseInfo();
        cityBaseInfo.setCityCode(city.getCityCode());
        cityBaseInfo.setCityType(city.getCityType());
        cityBaseInfo.setCityCnName(city.getCityCname());
        cityBaseInfo.setCityEnName(city.getCityEname());
        // 目前都是国内城市，这三个值设定为固定的
        cityBaseInfo.setGmtTimeDiff("800");
        cityBaseInfo.setTimeZone("1");
        cityBaseInfo.setSummerTime("0");

        if (country != null) {
            cityBaseInfo.setCountryCode(country.getCountryIso());
            MnjxContinent continent = iMnjxContinentService.getById(country.getContinentId());
            cityBaseInfo.setContinent(continent.getContinentCode());
        }

        return cityBaseInfo;
    }

    /**
     * 构建机场信息
     *
     * @param airport 机场对象
     * @param city    城市对象
     * @param country 国家对象
     * @return 机场信息
     */
    private QueryAirportVo.AirportInfo buildAirportInfo(MnjxAirport airport, MnjxCity city, MnjxCountry country) {
        if (airport == null || city == null) {
            return null;
        }
        QueryAirportVo.AirportInfo airportInfo = new QueryAirportVo.AirportInfo();
        airportInfo.setAirportCode(airport.getAirportCode());
        airportInfo.setAirportType("A"); // 默认为A类型
        airportInfo.setAirportCnName(airport.getAirportCname());
        airportInfo.setAirportEnName(airport.getAirportEname());
        airportInfo.setCityCode(city.getCityCode());
        airportInfo.setCityCnName(city.getCityCname());
        airportInfo.setCityEnName(city.getCityEname());
        airportInfo.setLatitude(airport.getLatitude());
        airportInfo.setLongitude(airport.getLongitude());

        if (country != null) {
            airportInfo.setCountryCode(country.getCountryIso());
        }

        return airportInfo;
    }

    /**
     * 构建机场基本信息
     *
     * @param airport 机场对象
     * @param city    城市对象
     * @param country 国家对象
     * @return 机场基本信息
     */
    private QueryAirportVo.AirportBaseInfo buildAirportBaseInfo(MnjxAirport airport, MnjxCity city, MnjxCountry country) {
        if (airport == null || city == null) {
            return null;
        }
        QueryAirportVo.AirportBaseInfo airportBaseInfo = new QueryAirportVo.AirportBaseInfo();
        airportBaseInfo.setCode(airport.getAirportCode());
        airportBaseInfo.setType("A"); // 默认为A类型
        airportBaseInfo.setAirportCnName(airport.getAirportCname());
        airportBaseInfo.setAirportEnName(airport.getAirportEname());
        airportBaseInfo.setCityCode(city.getCityCode());
        airportBaseInfo.setCityCnName(city.getCityCname());
        airportBaseInfo.setCityEnName(city.getCityEname());
        airportBaseInfo.setLatitude(airport.getLatitude());
        airportBaseInfo.setLongitude(airport.getLongitude());

        if (country != null) {
            airportBaseInfo.setCountryCode(country.getCountryIso());
        }

        return airportBaseInfo;
    }

    @Override
    public AirPreOccupyVo airPreOccupy(AirPreOccupyDto dto) throws SguiResultException {
        // 参数校验
        if (CollUtil.isEmpty(dto.getBookAirSegs())) {
            throw new SguiResultException("航段信息不能为空");
        }

        // 构建返回数据
        AirPreOccupyVo vo = new AirPreOccupyVo();

        // 获取当前用户名
        String username = iSguiCommonService.getCurrentUsername();
        if (StrUtil.isEmpty(username)) {
            throw new SguiResultException("未登录或会话已过期");
        }

        // 从缓存中获取占位信息，如果有的话在此基础上新增flight数据
        String cacheKey = "POR:" + username;
        String porJson = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isNotEmpty(porJson)) {
            // 将缓存的 AirPreOccupyVo 转换为 AirRetrievePorVo
            vo = JSONUtil.toBean(porJson, AirPreOccupyVo.class);
        }

        if (StrUtil.isEmpty(vo.getPorID())) {
            vo.setPorID(IdUtil.getSnowflake(1, 1).nextIdStr());
        }

        for (AirPreOccupyDto.BookAirSeg bookAirSeg : dto.getBookAirSegs()) {
            // actionCode不为NN报错
            if (!"NN".equals(bookAirSeg.getActionCode())) {
                throw new SguiResultException("ACTION CODE");
            }

            // 解析航班号和日期
            String airCode = bookAirSeg.getAirCode(); // 完整航班号，包含航司二字码

            // 提取日期部分，处理可能为空的情况
            String departureDate;
            if (StrUtil.isNotEmpty(bookAirSeg.getDepartureTime()) && bookAirSeg.getDepartureTime().length() >= 10) {
                departureDate = bookAirSeg.getDepartureTime().substring(0, 10);
            } else {
                // 如果出发时间为空，使用当前日期
                departureDate = DateUtil.today();
            }

            // 销售舱位可能为空
            String fltClass = StrUtil.isNotEmpty(bookAirSeg.getFltClass()) ? bookAirSeg.getFltClass() : "Y"; // 默认使用Y舱

            // 占位数量可能为空
            int tktNum = 1; // 默认为1
            if (StrUtil.isNotEmpty(bookAirSeg.getTktNum())) {
                try {
                    tktNum = Integer.parseInt(bookAirSeg.getTktNum());
                } catch (NumberFormatException e) {
                    // 如果解析失败，使用默认值1
                }
            }

            // 查询航班信息 - 使用完整航班号查询（包含航司二字码）
            MnjxFlight flight = iMnjxFlightService.lambdaQuery()
                    .eq(MnjxFlight::getFlightNo, airCode) // 在数据库中，flightNo字段存储的是包含航司二字码的完整航班号
                    .one();

            if (flight == null) {
                throw new SguiResultException("未找到对应航班");
            }

            // 查询tcard
            MnjxTcard tcard = iMnjxTcardService.lambdaQuery()
                    .eq(MnjxTcard::getFlightId, flight.getFlightId())
                    .one();

            if (tcard == null) {
                throw new SguiResultException("未找到对应航节");
            }

            // 查询航班计划
            MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery()
                    .eq(MnjxPlanFlight::getTcardId, tcard.getTcardId())
                    .eq(MnjxPlanFlight::getFlightDate, departureDate)
                    .one();

            if (planFlight == null) {
                throw new SguiResultException("未找到对应航班计划");
            }

            // 查询出发到达机场的id
            MnjxAirport depAirport = iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, bookAirSeg.getOrgCity())
                    .one();
            MnjxAirport arrAirport = iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, bookAirSeg.getDesCity())
                    .one();

            // 查询航节计划
            List<MnjxPlanSection> planSectionList = iMnjxPlanSectionService.lambdaQuery()
                    .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                    .eq(MnjxPlanSection::getDepAptId, depAirport.getAirportId())
                    .eq(MnjxPlanSection::getArrAptId, arrAirport.getAirportId())
                    .orderByAsc(MnjxPlanSection::getIsLastSection)
                    .list();

            if (CollUtil.isEmpty(planSectionList)) {
                throw new SguiResultException("未找到对应航节计划");
            }

            // 获取航节ID列表
            List<String> planSectionIdList = planSectionList.stream()
                    .map(MnjxPlanSection::getPlanSectionId)
                    .collect(Collectors.toList());

            // 查询开舱数据
            List<MnjxOpenCabin> openCabinList = iMnjxOpenCabinService.lambdaQuery()
                    .in(MnjxOpenCabin::getPlanSectionId, planSectionIdList)
                    .eq(MnjxOpenCabin::getSellCabin, fltClass)
                    .list();

            if (CollUtil.isEmpty(openCabinList)) {
                throw new SguiResultException("未找到对应舱位");
            }

            // 检查座位是否足够
            for (MnjxOpenCabin openCabin : openCabinList) {
                if (openCabin.getSeatAvailable() < tktNum) {
                    throw new SguiResultException("可用座位数量不足");
                }
            }

            List<AirPreOccupyVo.Flight> flights = new ArrayList<>();
            if (CollUtil.isNotEmpty(vo.getFlights())) {
                flights = vo.getFlights();
            }
            AirPreOccupyVo.Flight flightInfo = new AirPreOccupyVo.Flight();

            // 设置出发城市
            AirPreOccupyVo.City departureCity = new AirPreOccupyVo.City();
            departureCity.setCode(depAirport.getAirportCode());
            departureCity.setName(depAirport.getAirportCname());
            flightInfo.setDepartureCity(departureCity);

            // 设置到达城市
            AirPreOccupyVo.City arriveCity = new AirPreOccupyVo.City();
            arriveCity.setCode(arrAirport.getAirportCode());
            arriveCity.setName(arrAirport.getAirportCname());
            flightInfo.setArriveCity(arriveCity);

            // 设置日期
            AirPreOccupyVo.DateInfo dateInfo = new AirPreOccupyVo.DateInfo();
            dateInfo.setValue(departureDate);
            flightInfo.setDate(dateInfo);

            // 设置航段信息
            List<AirPreOccupyVo.Segment> segments = new ArrayList<>();
            AirPreOccupyVo.Segment segment = new AirPreOccupyVo.Segment();

            segment.setDepartureAirportCode(depAirport.getAirportCode());
            segment.setArrivalAirportCode(arrAirport.getAirportCode());
            segment.setDepartureAirportCN(depAirport.getAirportCname());
            segment.setArrivalAirportCN(arrAirport.getAirportCname());

            segment.setDepartureDate(departureDate);

            // 出发时间可能为空，需要判断
            if (StrUtil.isNotEmpty(bookAirSeg.getDepartureTime())) {
                segment.setDepartureTime(bookAirSeg.getDepartureTime().substring(11, 16)); // 提取时间部分
            }

            segment.setArrivalDate(departureDate); // 假设同一天到达

            // 到达时间可能为空，需要判断
            if (StrUtil.isNotEmpty(bookAirSeg.getArrivalTime())) {
                segment.setArrivalTime(bookAirSeg.getArrivalTime().substring(11, 16)); // 提取时间部分
            }

            // 处理航站楼可能为空的情况
            segment.setDepartureTerminal(StrUtil.isEmpty(bookAirSeg.getDepartureTerminal()) ? "--" : bookAirSeg.getDepartureTerminal());
            segment.setArrivalTerminal(StrUtil.isEmpty(bookAirSeg.getArrivalTerminal()) ? "--" : bookAirSeg.getArrivalTerminal());

            segment.setSeatTag("DK"); // 占位标记
            segment.setElementNumber(StrUtil.toString(tktNum));

            // 设置舱位信息
            List<AirPreOccupyVo.Cabin> cabins = new ArrayList<>();
            AirPreOccupyVo.Cabin cabin = new AirPreOccupyVo.Cabin();
            cabin.setCabinName(fltClass);
            cabin.setOn(true);
            cabins.add(cabin);
            segment.setCabins(cabins);

            // 设置航空公司信息
            AirPreOccupyVo.Airline airline = new AirPreOccupyVo.Airline();
            airline.setAirCode(airCode.substring(0, 2)); // 航司二字码
            airline.setFlightNo(airCode.substring(2)); // 去掉航司二字码的航班号
            airline.setPlaneType(bookAirSeg.getEquipmentCode());

            // 设置航空部门通用信息
            AirPreOccupyVo.AviationDepartmentGeneral aviationDepartmentGeneral = new AirPreOccupyVo.AviationDepartmentGeneral();
            aviationDepartmentGeneral.setAirlineCode(airCode.substring(0, 2));
            // 不设置contentUrl，数据库中没有维护该字段信息
            airline.setAviationDepartmentGeneral(aviationDepartmentGeneral);

            segment.setAirlines(airline);
            segments.add(segment);
            flightInfo.setSegments(segments);

            // 设置票务类型
            flightInfo.setTktType("D"); // 国内航班

            flights.add(flightInfo);
            vo.setFlights(flights);

        }

        // 将 AirPreOccupyVo 对象转换为 JSON 字符串
        porJson = JSONUtil.toJsonStr(vo);
        // 存入 Redis，过期时间设置为 1 小时
        stringRedisTemplate.opsForValue().set(
                cacheKey,
                porJson,
                1,
                java.util.concurrent.TimeUnit.HOURS
        );

        return vo;
    }

    @Override
    public AirRetrievePorVo airRetrievePor() throws SguiResultException {
        // 获取当前用户名
        String username = iSguiCommonService.getCurrentUsername();
        if (StrUtil.isEmpty(username)) {
            throw new SguiResultException("未登录或会话已过期");
        }

        // 从缓存中获取占位信息
        String cacheKey = "POR:" + username;
        String porJson = stringRedisTemplate.opsForValue().get(cacheKey);

        if (StrUtil.isEmpty(porJson)) {
            throw new SguiResultException("未找到占位信息或占位信息已过期");
        }

        // 将缓存的 AirPreOccupyVo 转换为 AirRetrievePorVo
        try {
            AirPreOccupyVo preOccupyVo = JSONUtil.toBean(porJson, AirPreOccupyVo.class);
            return convertToAirRetrievePorVo(preOccupyVo);
        } catch (Exception e) {
            log.error("转换占位信息失败", e);
            throw new SguiResultException("获取占位信息失败");
        }
    }

    /**
     * 将 AirPreOccupyVo 转换为 AirRetrievePorVo
     *
     * @param preOccupyVo 占位信息
     * @return 检索结果
     */
    private AirRetrievePorVo convertToAirRetrievePorVo(AirPreOccupyVo preOccupyVo) {
        AirRetrievePorVo vo = new AirRetrievePorVo();

        // 设置POR ID
        vo.setPorID(preOccupyVo.getPorID());

        // 创建航班列表
        List<AirRetrievePorVo.Flight> flights = new ArrayList<>();

        // 遍历原占位信息中的航班
        if (CollUtil.isNotEmpty(preOccupyVo.getFlights())) {
            int lineIndex = 1;
            for (AirPreOccupyVo.Flight preOccupyFlight : preOccupyVo.getFlights()) {
                AirRetrievePorVo.Flight flight = new AirRetrievePorVo.Flight();

                // 设置出发城市
                AirRetrievePorVo.City departureCity = new AirRetrievePorVo.City();
                departureCity.setCode(preOccupyFlight.getDepartureCity().getCode());
                departureCity.setName(preOccupyFlight.getDepartureCity().getName());
                flight.setDepartureCity(departureCity);

                // 设置到达城市
                AirRetrievePorVo.City arriveCity = new AirRetrievePorVo.City();
                arriveCity.setCode(preOccupyFlight.getArriveCity().getCode());
                arriveCity.setName(preOccupyFlight.getArriveCity().getName());
                flight.setArriveCity(arriveCity);

                // 设置日期
                AirRetrievePorVo.Date date = new AirRetrievePorVo.Date();
                date.setValue(preOccupyFlight.getDate().getValue());
                flight.setDate(date);

                // 设置航段列表
                List<AirRetrievePorVo.Segment> segments = new ArrayList<>();

                // 遍历原占位信息中的航段
                if (CollUtil.isNotEmpty(preOccupyFlight.getSegments())) {
                    for (AirPreOccupyVo.Segment preOccupySegment : preOccupyFlight.getSegments()) {
                        AirRetrievePorVo.Segment segment = new AirRetrievePorVo.Segment();

                        segment.setDepartureTerminal(preOccupySegment.getDepartureTerminal());
                        segment.setArrivalAirportCN(preOccupySegment.getArrivalAirportCN());
                        segment.setStopCity(preOccupySegment.getStopCity());
                        segment.setDepartureAirportCN(preOccupySegment.getDepartureAirportCN());
                        segment.setDepartureDate(preOccupySegment.getDepartureDate());
                        segment.setArrivalAirportCode(preOccupySegment.getArrivalAirportCode());
                        segment.setArrivalTerminal(preOccupySegment.getArrivalTerminal());
                        segment.setDepartureAirportCode(preOccupySegment.getDepartureAirportCode());
                        segment.setFlightTime(preOccupySegment.getFlightTime());
                        segment.setArrivalTime(preOccupySegment.getArrivalTime());
                        segment.setArrivalDate(preOccupySegment.getArrivalDate());
                        segment.setDepartureTime(preOccupySegment.getDepartureTime());
                        segment.setConnectLevel(preOccupySegment.getConnectLevel());
                        segment.setAsr(preOccupySegment.getAsr());
                        segment.setArrDays(preOccupySegment.getArrDays());
                        segment.setFlightDistance(preOccupySegment.getFlightDistance() != null ? preOccupySegment.getFlightDistance().toString() : null);
                        segment.setTktNum(preOccupySegment.getElementNumber());
                        segment.setLineIndex(StrUtil.toString(lineIndex++));
                        segment.setSegmentType("2");
                        segment.setMarriedSegmentNumber(0);
                        segment.setSeatTag(preOccupySegment.getSeatTag()); // 占位标记

                        // 设置舱位列表
                        List<AirRetrievePorVo.Cabin> cabins = new ArrayList<>();
                        if (CollUtil.isNotEmpty(preOccupySegment.getCabins())) {
                            for (AirPreOccupyVo.Cabin preOccupyCabin : preOccupySegment.getCabins()) {
                                AirRetrievePorVo.Cabin cabin = new AirRetrievePorVo.Cabin();
                                cabin.setState(preOccupyCabin.getState());
                                cabin.setOn(preOccupyCabin.getOn());
                                cabin.setCabinName(preOccupyCabin.getCabinName());
                                cabin.setSubCabinName(null);
                                cabins.add(cabin);
                            }
                        }
                        segment.setCabins(cabins);

                        // 设置航空公司信息
                        AirRetrievePorVo.Airlines airlines = new AirRetrievePorVo.Airlines();
                        if (preOccupySegment.getAirlines() != null) {
                            airlines.setAirCode(preOccupySegment.getAirlines().getAirCode());
                            airlines.setAirCN(preOccupySegment.getAirlines().getAirCN());
                            airlines.setFlightNo(preOccupySegment.getAirlines().getFlightNo());
                            airlines.setPlaneType(preOccupySegment.getAirlines().getPlaneType());
                            if (ObjectUtil.isNotEmpty(preOccupySegment.getAirlines().getIsShared()) && preOccupySegment.getAirlines().getIsShared()) {
                                MnjxFlight mnjxFlight = iMnjxFlightService.lambdaQuery()
                                        .eq(MnjxFlight::getFlightNo, preOccupySegment.getAirlines().getFlightNo())
                                        .one();
                                airlines.setIsShared(mnjxFlight.getCarrierFlight());
                            }
                            airlines.setAlliance(preOccupySegment.getAirlines().getAlliance());

                            // 设置航空部门通用信息
                            if (preOccupySegment.getAirlines().getAviationDepartmentGeneral() != null) {
                                AirRetrievePorVo.AviationDepartmentGeneral aviationDepartmentGeneral = new AirRetrievePorVo.AviationDepartmentGeneral();
                                aviationDepartmentGeneral.setAirlineCode(preOccupySegment.getAirlines().getAviationDepartmentGeneral().getAirlineCode());
                                aviationDepartmentGeneral.setContentUrl(preOccupySegment.getAirlines().getAviationDepartmentGeneral().getContentUrl());
                                airlines.setAviationDepartmentGeneral(aviationDepartmentGeneral);
                            }
                        }
                        segment.setAirlines(airlines);
                        segments.add(segment);
                    }
                }
                flight.setSegments(segments);

                // 设置票务类型
                flight.setTktType(preOccupyFlight.getTktType());

                flights.add(flight);
            }
        }
        vo.setFlights(flights);

        return vo;
    }

    @Override
    public String airIgnorePor() throws SguiResultException {
        // 获取当前用户名
        String username = iSguiCommonService.getCurrentUsername();
        if (StrUtil.isEmpty(username)) {
            throw new SguiResultException("未登录或会话已过期");
        }

        // 从缓存中获取占位信息
        String cacheKey = "POR:" + username;
        String porJson = stringRedisTemplate.opsForValue().get(cacheKey);

        if (StrUtil.isEmpty(porJson)) {
            throw new SguiResultException("未找到占位信息或占位信息已过期");
        }

        // 删除缓存即取消占位
        stringRedisTemplate.delete(cacheKey);

        return "取消占位成功";
    }

    @Override
    public AirCancelPorVo airCancelPor(AirCancelPorDto dto) throws SguiResultException {
        // 1. 获取当前用户名
        String username = iSguiCommonService.getCurrentUsername();
        if (StrUtil.isEmpty(username)) {
            throw new SguiResultException("未登录或会话已过期");
        }

        // 2. 获取por缓存
        String cacheKey = "POR:" + username;
        String porJson = stringRedisTemplate.opsForValue().get(cacheKey);

        if (StrUtil.isEmpty(porJson)) {
            throw new SguiResultException("未找到占位信息或占位信息已过期");
        }

        // 3. 将缓存的JSON字符串转换为AirPreOccupyVo对象
        AirPreOccupyVo preOccupyVo = JSONUtil.toBean(porJson, AirPreOccupyVo.class);
        if (preOccupyVo == null || CollUtil.isEmpty(preOccupyVo.getFlights())) {
            throw new SguiResultException("占位信息不完整");
        }

        // 4. 对比dto传入参数和缓存数据，匹配并移除对应的航段
        boolean foundSegment = false;
        boolean emptySegment = true;

        if (CollUtil.isNotEmpty(dto.getSegs())) {
            // 创建一个新的航航班列表，用于存储保留的航班
            List<AirPreOccupyVo.Flight> remainingFlights = new ArrayList<>();
            for (AirCancelPorDto.Segment cancelSegment : dto.getSegs()) {
                // 遍历所有航班
                for (AirPreOccupyVo.Flight flight : preOccupyVo.getFlights()) {
                    if (CollUtil.isNotEmpty(flight.getSegments())) {
                        // 遍历所有航段
                        for (AirPreOccupyVo.Segment segment : flight.getSegments()) {
                            // 检查是否匹配要删除的航段
                            boolean match = this.matchSegment(segment, cancelSegment);

                            if (match) {
                                // 找到匹配的航段
                                foundSegment = true;
                            } else {
                                // 不匹配的航段保留
                                remainingFlights.add(flight);
                                emptySegment = false;
                            }
                        }
                    }
                }
            }
            // 更新航班列表
            preOccupyVo.setFlights(remainingFlights);
        }

        // 如果没有找到匹配的航段，抛出异常
        if (!foundSegment) {
            throw new SguiResultException("航段不存在");
        }

        // 5. 更新por缓存
        // 如果所有航段都被删除，则删除整个缓存
        if (emptySegment) {
            stringRedisTemplate.delete(cacheKey);
        } else {
            // 否则更新缓存
            String updatedPorJson = JSONUtil.toJsonStr(preOccupyVo);
            stringRedisTemplate.opsForValue().set(cacheKey,
                    updatedPorJson,
                    1,
                    TimeUnit.HOURS
            );
        }

        // 4. 返回结果
        AirCancelPorVo vo = new AirCancelPorVo();
        vo.setRs(true);
        vo.setEmptySegment(true);
        return vo;
    }

    /**
     * 判断航段是否匹配
     *
     * @param segment       缓存中的航段
     * @param cancelSegment 要删除的航段
     * @return 是否匹配
     */
    private boolean matchSegment(AirPreOccupyVo.Segment segment, AirCancelPorDto.Segment cancelSegment) {
        // 检查关键字段是否匹配
        boolean matchAirCode = false;
        if (segment.getAirlines() != null) {
            String fullAirCode = segment.getAirlines().getAirCode() + segment.getAirlines().getFlightNo();
            matchAirCode = fullAirCode.equals(cancelSegment.getAirCode());
        }

        boolean matchOrgCity = segment.getDepartureAirportCode().equals(cancelSegment.getOrgCity());
        boolean matchDesCity = segment.getArrivalAirportCode().equals(cancelSegment.getDesCity());
//        boolean matchLineIndex = segment.getElementNumber() != null && segment.getElementNumber().equals(cancelSegment.getLineIndex());
        boolean matchTktNum = segment.getElementNumber() != null && segment.getElementNumber().equals(cancelSegment.getTktNum());

        // 检查舱位是否匹配
        boolean matchCabin = false;
        if (CollUtil.isNotEmpty(segment.getCabins())) {
            for (AirPreOccupyVo.Cabin cabin : segment.getCabins()) {
                if (cabin.getCabinName().equals(cancelSegment.getFltClass())) {
                    matchCabin = true;
                    break;
                }
            }
        }

        // 检查出发时间是否匹配
        boolean matchDepartureTime = false;
        if (StrUtil.isNotEmpty(segment.getDepartureDate()) && StrUtil.isNotEmpty(cancelSegment.getDepartureTime())) {
            // 只比较日期部分，忽略时间
            String segmentDate = segment.getDepartureDate();
            String cancelDate = cancelSegment.getDepartureTime().split("T")[0];
            matchDepartureTime = segmentDate.equals(cancelDate);
        }

        // 所有字段都匹配才返回 true
        return matchAirCode && matchOrgCity && matchDesCity && matchTktNum && matchCabin && matchDepartureTime;
    }

    @Override
    public BookPnrVo bookPnr(BookPnrReqDto reqDto) throws SguiResultException {
        if (reqDto == null || StrUtil.isEmpty(reqDto.getReq())) {
            throw new SguiResultException("请求参数不能为空");
        }

        try {
            // Base64解码
            String jsonStr = new String(Base64.getDecoder().decode(reqDto.getReq()), StandardCharsets.UTF_8);

            // JSON转换为BookPnrDto对象
            BookPnrDto dto = JSONUtil.toBean(jsonStr, BookPnrDto.class);

            return iBookPnrService.bookPnr(dto);
        } catch (Exception e) {
            log.error("解析BookPnr请求参数异常", e);
            throw new SguiResultException("解析请求参数异常: " + e.getMessage());
        }
    }

    @Override
    public ModifySegStatusVo modifySegmentStatus(ModifySegStatusDto dto) throws SguiResultException {
        // 调用IModifySegStatusService的modifySegmentStatus方法
        return iModifySegStatusService.modifySegmentStatus(dto);
    }
}
