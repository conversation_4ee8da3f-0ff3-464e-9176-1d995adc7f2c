package com.swcares.service.rpt.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.entity.*;
import com.swcares.obj.dto.CrsDailySalesDto;
import com.swcares.obj.vo.CrsDailySalesVo;
import com.swcares.service.*;
import com.swcares.service.rpt.IRptV2Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报表V2服务实现类
 *
 * <AUTHOR>
 * @date 2025/1/2 11:00
 */
@Service
public class RptV2ServiceImpl implements IRptV2Service {

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Override
    public CrsDailySalesVo crsDailySales(CrsDailySalesDto dto) throws SguiResultException {
        // 参数校验
        if (StrUtil.isEmpty(dto.getDate())) {
            throw new SguiResultException("查询日期不能为空");
        }
        if (StrUtil.isEmpty(dto.getDeviceNumber())) {
            throw new SguiResultException("打票机序号不能为空");
        }

        CrsDailySalesVo result = new CrsDailySalesVo();

        // 获取当前用户信息
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        if (userInfo == null) {
            throw new SguiResultException("无法获取当前用户信息");
        }

        // 根据打票机序号获取打票机信息
        MnjxOffice currentOffice = iMnjxOfficeService.lambdaQuery()
                .eq(MnjxOffice::getOfficeNo, userInfo.getOfficeNo())
                .one();
        
        if (currentOffice == null) {
            throw new SguiResultException("无法获取当前办公室信息");
        }

        MnjxPrinter printer = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getPrinterNo, dto.getDeviceNumber())
                .eq(MnjxPrinter::getOfficeId, currentOffice.getOfficeId())
                .one();

        if (printer == null) {
            throw new SguiResultException("未找到指定的打票机");
        }

        // 设置基本信息
        result.setOffice(currentOffice.getOfficeNo());
        result.setIata("00000000"); // 默认IATA代码

        // 根据value判断查询类型
        if ("2".equals(dto.getValue())) {
            // 异常票查询 - 目前返回空数据
            result.setItems(new ArrayList<>());
            result.setFilter(createEmptyFilter());
            result.setIssueErrorNumber(0);
            result.setRefundErrorNumber(0);
            result.setTotalErrorNumber(0);
        } else {
            // 正常票查询
            processNormalTickets(dto, printer, result);
        }

        return result;
    }

    /**
     * 处理正常票查询
     */
    private void processNormalTickets(CrsDailySalesDto dto, MnjxPrinter printer, CrsDailySalesVo result) {
        List<CrsDailySalesVo.TicketItem> items = new ArrayList<>();
        
        // 根据日期和打票机查询出票记录
        List<MnjxPnrNmTn> tnList = iMnjxPnrNmTnService.lambdaQuery()
                .eq(MnjxPnrNmTn::getPrinterId, printer.getPrinterId())
                .like(MnjxPnrNmTn::getIssuedTime, dto.getDate())
                .list();

        if (CollUtil.isNotEmpty(tnList)) {
            for (MnjxPnrNmTn tn : tnList) {
                // 查询对应的票务信息
                List<MnjxPnrNmTicket> tickets = iMnjxPnrNmTicketService.lambdaQuery()
                        .eq(MnjxPnrNmTicket::getPnrNmTnId, tn.getTnId())
                        .list();

                for (MnjxPnrNmTicket ticket : tickets) {
                    CrsDailySalesVo.TicketItem item = buildTicketItem(tn, ticket, dto);
                    if (item != null) {
                        items.add(item);
                    }
                }
            }
        }

        result.setItems(items);
        result.setFilter(buildFilterInfo(items));
        result.setIssueErrorNumber(0);
        result.setRefundErrorNumber(0);
        result.setTotalErrorNumber(0);
    }

    /**
     * 构建票务明细项
     */
    private CrsDailySalesVo.TicketItem buildTicketItem(MnjxPnrNmTn tn, MnjxPnrNmTicket ticket, CrsDailySalesDto dto) {
        try {
            CrsDailySalesVo.TicketItem item = new CrsDailySalesVo.TicketItem();

            // 格式化票号
            String ticketNo = ticket.getTicketNo();
            if (StrUtil.isNotEmpty(ticketNo) && ticketNo.length() >= 10) {
                String prefix = ticketNo.substring(0, 3);
                String suffix = ticketNo.substring(3);
                item.setTicket(prefix + "-" + suffix);
            } else {
                item.setTicket(ticketNo);
            }

            // 获取PNR信息
            String pnrNmId = tn.getPnrNmId();
            String pnrCrs = null;
            if (StrUtil.isNotEmpty(pnrNmId)) {
                MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
                if (pnrNm != null) {
                    MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
                    if (pnr != null) {
                        pnrCrs = pnr.getPnrCrs();
                    }
                }
            }
            item.setPnr(pnrCrs);

            // 设置基本信息
            item.setPayType(tn.getIssuedAirline());
            item.setJobNo("49041"); // 默认工作号
            item.setAirline(tn.getIssuedAirline());
            item.setPrntNo("1");

            // 获取航段信息构建目的地
            String desArr = buildDesArr(pnrNmId);
            item.setDesArr(desArr);

            // 格式化时间
            if (tn.getIssuedTime() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String salesTime = sdf.format(tn.getIssuedTime());
                item.setSalesTime(salesTime);
                item.setSalesDate(salesTime.substring(0, 10));
            }

            // 获取运价信息
            setPriceInfo(item, pnrNmId, tn.getNmXnId());

            // 设置其他固定信息
            item.setTicketTypeCode("D"); // 国内客票
            item.setTicketType("BSP国内客票");
            item.setCurrencyType("CNY");
            item.setTicketStatus("ISSU");
            item.setInvoluntaryIdentification("-");
            item.setObTax("0");
            item.setCouponNo("0");
            item.setRefundNo("");

            return item;
        } catch (Exception e) {
            // 记录日志但不影响其他数据处理
            return null;
        }
    }

    /**
     * 构建目的地信息
     */
    private String buildDesArr(String pnrNmId) {
        if (StrUtil.isEmpty(pnrNmId)) {
            return "";
        }

        try {
            MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
            if (pnrNm == null) {
                return "";
            }

            List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                    .eq(MnjxPnrSeg::getPnrId, pnrNm.getPnrId())
                    .ne(MnjxPnrSeg::getPnrSegType, "SA")
                    .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                    .list();

            if (CollUtil.isNotEmpty(segments)) {
                String org = segments.get(0).getOrg();
                String dst = segments.get(segments.size() - 1).getDst();
                return org + dst;
            }
        } catch (Exception e) {
            // 忽略异常
        }

        return "";
    }

    /**
     * 设置价格信息
     */
    private void setPriceInfo(CrsDailySalesVo.TicketItem item, String pnrNmId, String nmXnId) {
        // 默认值
        item.setAgencyFee("0.00");
        item.setAgencyFeePercent("0.00");
        item.setTaxAmount("0.00");
        item.setAmount("0.00");
        item.setOtherDeduction(null);
        item.setServiceCharge("");

        try {
            // 查询运价信息
            if (StrUtil.isNotEmpty(pnrNmId)) {
                List<MnjxNmFn> nmFnList = iMnjxNmFnService.lambdaQuery()
                        .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                        .list();

                if (CollUtil.isNotEmpty(nmFnList)) {
                    MnjxNmFn nmFn = nmFnList.get(0);
                    if (nmFn.getSPrice() != null) {
                        item.setAmount(nmFn.getSPrice().toString());
                    }
                    if (nmFn.getAPrice() != null) {
                        item.setAgencyFee(nmFn.getAPrice().toString());
                    }
                    if (nmFn.getXPrice() != null) {
                        item.setTaxAmount(nmFn.getXPrice().toString());
                    }
                }
            }
        } catch (Exception e) {
            // 忽略异常，使用默认值
        }
    }

    /**
     * 构建过滤器信息
     */
    private CrsDailySalesVo.FilterInfo buildFilterInfo(List<CrsDailySalesVo.TicketItem> items) {
        CrsDailySalesVo.FilterInfo filter = new CrsDailySalesVo.FilterInfo();

        if (CollUtil.isEmpty(items)) {
            filter.setAirlines(new ArrayList<>());
            filter.setCurrencyTypes(new ArrayList<>());
            filter.setJobNos(new ArrayList<>());
            filter.setTicketKinds(new ArrayList<>());
            filter.setTicketTypes(new ArrayList<>());
            filter.setPayTypes(new ArrayList<>());
            filter.setPrntNos(new ArrayList<>());
            return filter;
        }

        // 从items中提取唯一值
        filter.setAirlines(items.stream().map(CrsDailySalesVo.TicketItem::getAirline)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        filter.setCurrencyTypes(items.stream().map(CrsDailySalesVo.TicketItem::getCurrencyType)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        filter.setJobNos(items.stream().map(CrsDailySalesVo.TicketItem::getJobNo)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        filter.setTicketKinds(items.stream().map(CrsDailySalesVo.TicketItem::getTicketType)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        filter.setTicketTypes(items.stream().map(CrsDailySalesVo.TicketItem::getTicketStatus)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        filter.setPayTypes(items.stream().map(CrsDailySalesVo.TicketItem::getPayType)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        filter.setPrntNos(items.stream().map(CrsDailySalesVo.TicketItem::getPrntNo)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList()));

        return filter;
    }

    /**
     * 创建空的过滤器信息
     */
    private CrsDailySalesVo.FilterInfo createEmptyFilter() {
        CrsDailySalesVo.FilterInfo filter = new CrsDailySalesVo.FilterInfo();
        filter.setAirlines(new ArrayList<>());
        filter.setCurrencyTypes(new ArrayList<>());
        filter.setJobNos(new ArrayList<>());
        filter.setTicketKinds(new ArrayList<>());
        filter.setTicketTypes(new ArrayList<>());
        filter.setPayTypes(new ArrayList<>());
        filter.setPrntNos(new ArrayList<>());
        return filter;
    }
}
