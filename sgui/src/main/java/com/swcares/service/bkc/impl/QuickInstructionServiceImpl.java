package com.swcares.service.bkc.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.entity.SguiData;
import com.swcares.obj.vo.SearchTipVo;
import com.swcares.service.bkc.IQuickInstructionService;
import com.swcares.service.ISguiDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/4/24 16:17
 */
@Service
public class QuickInstructionServiceImpl implements IQuickInstructionService {

    @Resource
    private ISguiDataService iSguiDataService;

    @Override
    public SearchTipVo searchTip() {
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "search_tip")
                .one();
        String jsonValue = data.getValue();
        return JSONUtil.parseObj(jsonValue).toBean(SearchTipVo.class);
    }
}
