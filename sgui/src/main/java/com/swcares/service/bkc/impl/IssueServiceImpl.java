package com.swcares.service.bkc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.IssueTicketDto;
import com.swcares.obj.vo.IssueTicketVo;
import com.swcares.service.*;
import com.swcares.service.bkc.IIssueService;
import com.swcares.service.bkc.IUpdatePnrService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 出票服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/29 15:00
 */
@Slf4j
@Service
public class IssueServiceImpl implements IIssueService {

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxAgentAirlineService iMnjxAgentAirlineService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrFcService iMnjxPnrFcService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxPnrFpService iMnjxPnrFpService;

    @Resource
    private IMnjxNmFcService iMnjxNmFcService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxNmFpService iMnjxNmFpService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxTicketPriceService iMnjxTicketPriceService;

    @Resource
    private IMnjxPnrTkService iMnjxPnrTkService;

    @Resource
    private IMnjxPnrRmkService iMnjxPnrRmkService;

    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;

    @Resource
    private IMnjxPsgSeatService iMnjxPsgSeatService;

    @Resource
    private IMnjxTicketOperateRecordService iMnjxTicketOperateRecordService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private IMnjxPnrEiService iMnjxPnrEiService;

    @Resource
    private IMnjxNmEiService iMnjxNmEiService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IUpdatePnrService iUpdatePnrService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IssueTicketVo issueTicket(IssueTicketDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null || StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        if (dto.getIssue() == null || StrUtil.isEmpty(dto.getIssue().getPrinterNo())) {
            throw new SguiResultException("打票机编号不能为空");
        }

        // 获取当前用户信息
        UserInfo currentUser = iSguiCommonService.getCurrentUserInfo();
        if (ObjectUtil.isEmpty(currentUser)) {
            throw new SguiResultException("获取当前用户信息失败");
        }

        // 查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();
        if (ObjectUtil.isEmpty(pnr)) {
            throw new SguiResultException("未找到对应的PNR信息");
        }

        MnjxSi mnjxSi = iMnjxSiService.getById(currentUser.getSiId());
        // 查询Office信息
        MnjxOffice office = iMnjxOfficeService.getById(mnjxSi.getOfficeId());
        if (ObjectUtil.isEmpty(office)) {
            throw new SguiResultException("获取Office信息失败");
        }

        // 1. 检查出票条件
        List<MnjxPnrNm> pnrNmList = this.checkIssueConditions(dto, pnr, office, mnjxSi);

        // 2. 执行出票
        return this.performIssue(dto, pnr, pnrNmList, office, mnjxSi);
    }

    /**
     * 检查出票条件，并返回需要出票的旅客列表
     */
    private List<MnjxPnrNm> checkIssueConditions(IssueTicketDto dto, MnjxPnr pnr, MnjxOffice office, MnjxSi mnjxSi) throws SguiResultException {
        // 1.1 检查PNR是否存在（已在主方法中检查）

        // 1.2 检查PNR状态是否是OP
        if (!"OP".equals(pnr.getPnrStatus())) {
            throw new SguiResultException("PNR状态不是OP，无法出票");
        }

        // 1.3 检查出票权限
        this.checkIssueAuthority(dto, pnr, office);

        // 1.4 检查航段组中最早的航班起飞时间是否已超过当前时间
        this.checkFlightTime(pnr);

        // 1.5 检查打票机
        this.checkPrinter(dto.getIssue().getPrinterNo(), pnr, office, mnjxSi, dto.getIssue().getIssueItems().size());

        List<MnjxPnrNm> passengers = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();
        // 筛选单独出票的旅客，如果为空表示全部出票
        List<IssueTicketDto.IssueItemDto> issuePassengerList = dto.getIssue().getIssueItems().stream()
                .filter(i -> StrUtil.isNotEmpty(i.getPassengerId()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(issuePassengerList)) {
            List<Integer> passengerIdList = issuePassengerList.stream()
                    .map(i -> Integer.parseInt(i.getPassengerId().replace("P", "")))
                    .distinct()
                    .collect(Collectors.toList());
            passengers = passengers.stream()
                    .filter(p -> passengerIdList.contains(p.getPsgIndex()))
                    .collect(Collectors.toList());
        }
        // 1.6 检查非婴儿旅客的证件信息是否存在SSR FOID或SSR DOCS
        this.checkSsrFoid(passengers, issuePassengerList);

        // 1.7 检查婴儿旅客的SSR INFT行动代码是否是HK
        this.checkInfantSsr(pnr, passengers, issuePassengerList);

        // 1.8 检查运价组PNRFP PNRFC PNRFN或NMFP NMFC NMFN是否完整
        this.checkPriceInfo(pnr, passengers, issuePassengerList);

        // 1.9 检查出票时限
        this.checkIssueTimeLimit(pnr);

        return passengers;
    }

    /**
     * 执行出票
     */
    private IssueTicketVo performIssue(IssueTicketDto dto, MnjxPnr pnr, List<MnjxPnrNm> passengers, MnjxOffice office, MnjxSi currentUser) throws SguiResultException {
        // 生成新的封口编号
        String newAtNo = iSguiCommonService.generateNewAtNo(pnr.getPnrId());

        List<MnjxPnrNmTn> tnList = new ArrayList<>();
        List<MnjxPnrNmTicket> ticketList = new ArrayList<>();
        List<MnjxNmSsr> nmSsrList = new ArrayList<>();
        // 2.1 获取所有出票的旅客
        List<IssueTicketDto.IssueItemDto> issueItems = dto.getIssue().getIssueItems();
        if (issueItems.stream().noneMatch(i -> StrUtil.isNotEmpty(i.getPassengerId()))) {
            // 过滤掉已经出票的旅客
            List<String> pnrNmIds = passengers.stream()
                    .map(MnjxPnrNm::getPnrNmId)
                    .collect(Collectors.toList());

            List<MnjxPnrNmTn> existingTickets = iMnjxPnrNmTnService.lambdaQuery()
                    .in(MnjxPnrNmTn::getPnrNmId, pnrNmIds)
                    .list();

            if (CollUtil.isNotEmpty(existingTickets)) {
                Set<String> ticketedPassengerIds = existingTickets.stream()
                        .map(MnjxPnrNmTn::getPnrNmId)
                        .collect(Collectors.toSet());

                passengers = passengers.stream()
                        .filter(p -> !ticketedPassengerIds.contains(p.getPnrNmId()))
                        .collect(Collectors.toList());
            }

            if (CollUtil.isEmpty(passengers)) {
                throw new SguiResultException("所有旅客已出票");
            }
        }

        // 2.2 计算票号，生成tn、nmTicket、ssr tkne记录
        this.generateTicketNumbers(dto, pnr, passengers, office, tnList, ticketList, nmSsrList);
        iMnjxPnrNmTnService.saveBatch(tnList);
        iMnjxPnrNmTicketService.saveBatch(ticketList);
        iMnjxNmSsrService.saveBatch(nmSsrList);

        // 2.3 存储数据到pnr_nm_ticket_price表
        this.saveTicketPriceData(dto, pnr, passengers, tnList, ticketList);

        // 2.4 删除TK FC EI记录
        this.deleteTkFcEiRecords(dto, pnr, passengers, newAtNo);

        // 2.5 生成新的TK项，值为"T"
        this.generateTkRecord(pnr);

        // 如果请求的dorr为true，需要把航段组行动代码更新为RR
        if (Boolean.TRUE.equals(dto.getIssue().getDorr())) {
            this.changeSegActionCodeToRr(pnr);
        }

        // 2.6 生成新的RMK项，值为"TJ 当前OFFICE号"
        this.generateRmkRecord(pnr, office);

        // 2.7 生成psgCki和psgSeat数据
        this.generatePsgCkiAndSeat(pnr, passengers);

        // 2.8 生成客票操作记录ticketOperateRecord
        this.generateTicketOperateRecord(ticketList, currentUser);

        // 3. PNR项的重新排序和封口
        this.reorderAndSeal(pnr, newAtNo, currentUser);

        // 构建返回结果
        return this.buildResult(dto);
    }

    /**
     * 修改航段组行动代码为RR
     *
     * @param pnr
     */
    private void changeSegActionCodeToRr(MnjxPnr pnr) {
        List<MnjxPnrSeg> segList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .ne(MnjxPnrSeg::getPnrSegType, "SA")
                .list();
        segList = segList.stream()
                .filter(s -> !"RR".equals(s.getActionCode()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(segList)) {
            return;
        }
        segList.forEach(s -> {
            s.setActionCode("RR");
            String inputValue = s.getInputValue();
            s.setInputValue(inputValue.replace(" HK", " RR"));
        });
        iMnjxPnrSegService.updateBatchById(segList);
    }

    /**
     * 检查出票权限
     */
    private void checkIssueAuthority(IssueTicketDto dto, MnjxPnr pnr, MnjxOffice office) throws SguiResultException {
        // 查询航段信息
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        if (CollUtil.isEmpty(segments)) {
            throw new SguiResultException("PNR中没有航段信息");
        }

        String validateAirline = dto.getIssue().getValidateAirline();

        if (StrUtil.isNotEmpty(validateAirline)) {
            // 检查指定航司的出票权限
            MnjxAirline airline = iMnjxAirlineService.lambdaQuery()
                    .eq(MnjxAirline::getAirlineCode, validateAirline)
                    .one();
            if (ObjectUtil.isEmpty(airline)) {
                throw new SguiResultException("航司代码不存在");
            }

            List<MnjxAgentAirline> agentAirlines = iMnjxAgentAirlineService.lambdaQuery()
                    .eq(MnjxAgentAirline::getAgentId, office.getOrgId())
                    .eq(MnjxAgentAirline::getAirlineId, airline.getAirlineId())
                    .list();

            if (CollUtil.isEmpty(agentAirlines)) {
                throw new SguiResultException("没有该航司的出票权限");
            }
        } else {
            // 检查所有航段的航司出票权限
            List<String> airlineCodes = new ArrayList<>();
            for (MnjxPnrSeg segment : segments) {
                if (StrUtil.isNotEmpty(segment.getFlightNo()) && segment.getFlightNo().length() >= 2) {
                    String airlineCode = segment.getFlightNo().substring(0, 2);
                    airlineCodes.add(airlineCode);
                }
            }

            if (CollUtil.isNotEmpty(airlineCodes)) {
                List<String> airlineIds = iMnjxAirlineService.lambdaQuery()
                        .in(MnjxAirline::getAirlineCode, airlineCodes)
                        .list()
                        .stream()
                        .map(MnjxAirline::getAirlineId)
                        .collect(Collectors.toList());

                List<String> agentAirlineIds = iMnjxAgentAirlineService.lambdaQuery()
                        .eq(MnjxAgentAirline::getAgentId, office.getOrgId())
                        .list()
                        .stream()
                        .map(MnjxAgentAirline::getAirlineId)
                        .collect(Collectors.toList());

                List<String> unauthorizedAirlines = airlineIds.stream()
                        .filter(airlineId -> !agentAirlineIds.contains(airlineId))
                        .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(unauthorizedAirlines)) {
                    throw new SguiResultException("没有部分航司的出票权限");
                }
            }
        }
    }

    /**
     * 检查航班时间
     */
    private void checkFlightTime(MnjxPnr pnr) throws SguiResultException {
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        if (CollUtil.isNotEmpty(segments)) {
            MnjxPnrSeg firstSegment = segments.get(0);
            if (!"SA".equals(firstSegment.getPnrSegType())) {
                // 检查首段航班是否已起飞
                String flightDate = firstSegment.getFlightDate();
                String estimateOff = firstSegment.getEstimateOff();

                if (StrUtil.isNotEmpty(flightDate) && StrUtil.isNotEmpty(estimateOff)) {

                    String flightDateTime = flightDate + " " + estimateOff.substring(0, 2) + ":" + estimateOff.substring(2);
                    Date flightTime = DateUtil.parse(flightDateTime, "yyyy-MM-dd HH:mm");
                    if (flightTime.before(new Date())) {
                        throw new SguiResultException("航班已起飞，无法出票");
                    }
                }
            }
        }
    }

    /**
     * 检查打票机
     */
    private void checkPrinter(String printerNo, MnjxPnr pnr, MnjxOffice office, MnjxSi currentUser, int passengerCount) throws SguiResultException {
        MnjxPrinter printer = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getPrinterNo, printerNo)
                .eq(MnjxPrinter::getOfficeId, office.getOfficeId())
                .one();

        if (ObjectUtil.isEmpty(printer)) {
            throw new SguiResultException("打票机不存在");
        }

        // 检查打票机类型是否是4
        if (!"4".equals(printer.getPrinterType())) {
            throw new SguiResultException("打票机类型错误");
        }

        // 检查打票机是否由当前用户建控
        if (StrUtil.isEmpty(printer.getSiId()) || !currentUser.getSiId().equals(printer.getSiId())) {
            throw new SguiResultException("打票机未建控或不是当前用户建控");
        }

        // 检查打票机状态是否是UP
        if (!"UP".equals(printer.getPrinterStatus())) {
            throw new SguiResultException("打票机状态不是UP");
        }

        // 检查打票机属性是否是TAT/ET
        if (!"TAT/ET".equals(printer.getPrintAttribute())) {
            throw new SguiResultException("打票机属性不是TAT/ET");
        }

        // 检查打票机输入状态是否是ACTIVE
        if (!"ACTIVE".equals(printer.getInputStatus())) {
            throw new SguiResultException("打票机输入状态不是ACTIVE");
        }

        // 检查票号库存
        if (ObjectUtil.isEmpty(printer.getTicketStart()) || ObjectUtil.isEmpty(printer.getTicketEnd())) {
            throw new SguiResultException("打票机未上票");
        }

        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .list();

        int segmentCount = (int) segments.stream().filter(s -> !"SA".equals(s.getPnrSegType())).count();
        int ticketsPerPassenger = (segmentCount + 1) / 2; // 每个旅客需要的票数
        int totalTicketsNeeded = passengerCount * ticketsPerPassenger;

        BigInteger availableTickets;
        if (ObjectUtil.isNotEmpty(printer.getLastTicket())) {
            availableTickets = printer.getTicketEnd().subtract(printer.getLastTicket());
        } else {
            availableTickets = printer.getTicketEnd().subtract(printer.getTicketStart()).add(BigInteger.ONE);
        }

        if (availableTickets.compareTo(BigInteger.valueOf(totalTicketsNeeded)) < 0) {
            throw new SguiResultException("票号库存不足");
        }
    }

    /**
     * 检查SSR FOID
     */
    private void checkSsrFoid(List<MnjxPnrNm> passengers, List<IssueTicketDto.IssueItemDto> issueItems) throws SguiResultException {
        // 检查非婴儿旅客的证件信息
        for (MnjxPnrNm passenger : passengers) {
            if (CollUtil.isNotEmpty(issueItems)
                    && issueItems.stream().anyMatch(i -> passenger.getPsgIndex() == Integer.parseInt(i.getPassengerId().replace("P", "")) && "INF".equals(i.getPassengerType()))) {
                continue;
            }
            List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getPnrNmId, passenger.getPnrNmId())
                    .in(MnjxNmSsr::getSsrType, "FOID", "DOCS")
                    .ne(MnjxNmSsr::getActionCode, "XX")
                    .list();

            if (CollUtil.isEmpty(ssrList)) {
                throw new SguiResultException(StrUtil.format("旅客{}缺少有效的证件信息(SSR FOID或DOCS)", passenger.getPsgIndex()));
            }

        }
    }

    /**
     * 检查婴儿SSR INFT
     */
    private void checkInfantSsr(MnjxPnr pnr, List<MnjxPnrNm> passengers, List<IssueTicketDto.IssueItemDto> issueItems) throws SguiResultException {
        if (CollUtil.isNotEmpty(issueItems) && issueItems.stream().noneMatch(i -> "INF".equals(i.getPassengerType()))) {
            return;
        }

        List<String> pnrNmIds = passengers.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(issueItems)) {
            pnrNmIds = passengers.stream()
                    .filter(
                            p -> issueItems.stream()
                                    .anyMatch(
                                            i -> p.getPsgIndex() == Integer.parseInt(i.getPassengerId().replace("P", "")) && "INF".equals(i.getPassengerType())
                                    )
                    )
                    .map(MnjxPnrNm::getPnrNmId)
                    .collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(pnrNmIds)) {
            return;
        }

        // 查询婴儿信息
        List<MnjxNmXn> infants = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId, pnrNmIds)
                .list();

        if (CollUtil.isNotEmpty(infants)) {
            // 查询航段数量（非SA类型）
            long segmentCount = iMnjxPnrSegService.lambdaQuery()
                    .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                    .ne(MnjxPnrSeg::getPnrSegType, "SA")
                    .count();

            for (MnjxNmXn infant : infants) {
                // 检查每个婴儿的SSR INFT记录
                List<MnjxNmSsr> inftSsrList = iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrNmId, infant.getPnrNmId())
                        .eq(MnjxNmSsr::getSsrType, "INFT")
                        .list();

                // 检查SSR INFT数量是否与航段数量匹配
                if (inftSsrList.size() != segmentCount) {
                    throw new SguiResultException("婴儿SSR INFT数量与航段数量不匹配");
                }

                // 检查所有SSR INFT的行动代码是否为HK
                boolean allHK = inftSsrList.stream()
                        .allMatch(ssr -> "HK".equals(ssr.getActionCode()));

                if (!allHK) {
                    throw new SguiResultException("婴儿SSR INFT行动代码不是HK");
                }
            }
        }
    }

    /**
     * 检查运价信息
     */
    private void checkPriceInfo(MnjxPnr pnr, List<MnjxPnrNm> passengers, List<IssueTicketDto.IssueItemDto> issueItems) throws SguiResultException {
        List<MnjxPnrSeg> segList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .list();

        // 出票旅客中是否包含成人
        boolean haveAdt = false;
        // 出票旅客中是否包含婴儿
        boolean haveInf = false;
        // 出票旅客中是否包含儿童
        boolean haveChd = false;
        // 部分出票时，只检查出票旅客的运价
        if (CollUtil.isNotEmpty(issueItems)) {
            // 筛选对应序号的旅客
            List<Integer> passengerIdList = issueItems.stream()
                    .map(i -> Integer.parseInt(i.getPassengerId().replace("P", "")))
                    .collect(Collectors.toList());
            passengers = passengers.stream()
                    .filter(p -> passengerIdList.contains(p.getPsgIndex()))
                    .collect(Collectors.toList());
            // 根据传入参数判断出票包含的旅客类型
            haveAdt = issueItems.stream().anyMatch(i -> "ADL".equals(i.getPassengerType()));
            haveInf = issueItems.stream().anyMatch(i -> "INF".equals(i.getPassengerType()));
            haveChd = issueItems.stream().anyMatch(i -> "CHD".equals(i.getPassengerType()));
        }
        // 全部出票
        else {
            // 获取pnr中所有旅客的nmId
            List<String> pnrNmIdList = passengers.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList());
            // 检查ssr chld确认是否有儿童旅客
            List<MnjxNmSsr> chldSsrList = iMnjxNmSsrService.lambdaQuery()
                    .in(MnjxNmSsr::getPnrNmId, pnrNmIdList)
                    .eq(MnjxNmSsr::getSsrType, "CHLD")
                    .list();
            haveChd = CollUtil.isNotEmpty(chldSsrList);
            // 检查旅客类型确认是否有旅客携带婴儿
            haveInf = passengers.stream().anyMatch(p -> "3".equals(p.getPsgType()));
            // 检查是否有成人旅客
            if (passengers.stream().noneMatch(p -> "0".equals(p.getPsgType()) || "4".equals(p.getPsgType()))) {
                // 无陪旅客，包含了无陪儿童和无陪老人，无陪老人要算在成人旅客
                if (passengers.stream().anyMatch(p -> "2".equals(p.getPsgType()))) {
                    List<MnjxPnrNm> umPassengerList = passengers.stream()
                            .filter(p -> "2".equals(p.getPsgType()))
                            .collect(Collectors.toList());
                    List<String> umNmIdList = umPassengerList.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(chldSsrList)) {
                        long count = chldSsrList.stream()
                                .filter(s -> umNmIdList.contains(s.getPnrNmId()))
                                .count();
                        if (umPassengerList.size() != count) {
                            haveAdt = true;
                        }
                    } else {
                        haveAdt = true;
                    }
                }
            }
            // 0或4都是成人旅客，4是特殊的GM JC
            else {
                haveAdt = true;
            }
        }

        // 检查PNR级别的运价信息
        List<MnjxPnrFc> pnrFcList = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                .list();
        List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                .list();
        List<MnjxPnrFp> pnrFpList = iMnjxPnrFpService.lambdaQuery()
                .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                .list();

        // 旅客级别的运价检查
        List<String> pnrNmIds = passengers.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());

        // 先获取所有旅客的运价信息
        List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                .in(MnjxNmFc::getPnrNmId, pnrNmIds)
                .list();
        List<MnjxNmFn> nmFnList = iMnjxNmFnService.lambdaQuery()
                .in(MnjxNmFn::getPnrNmId, pnrNmIds)
                .list();
        List<MnjxNmFp> nmFpList = iMnjxNmFpService.lambdaQuery()
                .in(MnjxNmFp::getPnrNmId, pnrNmIds)
                .list();

        boolean notCompleteFc = CollUtil.isEmpty(pnrFcList) && CollUtil.isEmpty(nmFcList);
        if (notCompleteFc) {
            throw new SguiResultException(Constant.INCOMPLETE_PNR_FC);
        }
        boolean notCompleteFn = CollUtil.isEmpty(pnrFnList) && CollUtil.isEmpty(nmFnList);
        if (notCompleteFn) {
            throw new SguiResultException(Constant.INCOMPLETE_PNR_FP);
        }
        boolean notCompleteFp = CollUtil.isEmpty(pnrFpList) && CollUtil.isEmpty(nmFpList);
        if (notCompleteFp) {
            throw new SguiResultException(Constant.INCOMPLETE_PNR_FN);
        }

        // 如果同时存在PNR和旅客级别的运价
        if (CollUtil.isNotEmpty(pnrFcList) && CollUtil.isNotEmpty(pnrFnList) && CollUtil.isNotEmpty(pnrFpList)
                && CollUtil.isNotEmpty(nmFcList) && CollUtil.isNotEmpty(nmFnList) && CollUtil.isNotEmpty(nmFpList)) {
            switch (segList.size()) {
                case 1:
                    if (!"SA".equals(segList.get(0).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg1Cabin()) && StrUtil.isEmpty(nmFcList.get(0).getSeg1Cabin())) {
                        throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                    }
                    break;
                case 2:
                    if (!"SA".equals(segList.get(1).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg2Cabin()) && StrUtil.isEmpty(nmFcList.get(0).getSeg2Cabin())) {
                        throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                    }
                    break;
                case 3:
                    if (!"SA".equals(segList.get(2).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg3Cabin()) && StrUtil.isEmpty(nmFcList.get(0).getSeg3Cabin())) {
                        throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                    }
                    break;
                case 4:
                    if (!"SA".equals(segList.get(3).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg4Cabin()) && StrUtil.isEmpty(nmFcList.get(0).getSeg4Cabin())) {
                        throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                    }
                    break;
                case 5:
                    if (!"SA".equals(segList.get(4).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg5Cabin()) && StrUtil.isEmpty(nmFcList.get(0).getSeg5Cabin())) {
                        throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                    }
                    break;
                default:
                    break;
            }
            // 同时存在的规则：一个旅客类型有PNR运价时，不会有对应的旅客运价，如果两种运价都没有则报错
            if (haveAdt) {
                if (pnrFcList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "AD", "GM", "JC")) && nmFcList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "AD", "GM", "JC")))                {
                    throw new SguiResultException(Constant.INCOMPLETE_PNR_FC);
                }
                if (pnrFpList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "AD", "GM", "JC")) && nmFpList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "AD", "GM", "JC")))                {
                    throw new SguiResultException(Constant.INCOMPLETE_PNR_FP);
                }
                if (pnrFnList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "AD", "GM", "JC")) && nmFnList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "AD", "GM", "JC")))                {
                    throw new SguiResultException(Constant.INCOMPLETE_PNR_FN);
                }
            }
            if (haveChd) {
                if (pnrFcList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "CH")) && nmFcList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "CH")))                {
                    throw new SguiResultException(Constant.INCOMPLETE_PNR_FC);
                }
                if (pnrFpList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "CH")) && nmFpList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "CH")))                {
                    throw new SguiResultException(Constant.INCOMPLETE_PNR_FP);
                }
                if (pnrFnList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "CH")) && nmFnList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "CH")))                {
                    throw new SguiResultException(Constant.INCOMPLETE_PNR_FN);
                }
            }
            if (haveInf) {
                if (pnrFcList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "IN")) && nmFcList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "IN")))                {
                    throw new SguiResultException(Constant.INCOMPLETE_PNR_FC);
                }
                if (pnrFpList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "IN")) && nmFpList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "IN")))                {
                    throw new SguiResultException(Constant.INCOMPLETE_PNR_FP);
                }
                if (pnrFnList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "IN")) && nmFnList.stream().noneMatch(f -> StrUtil.equalsAny(f.getPatType(), "IN")))                {
                    throw new SguiResultException(Constant.INCOMPLETE_PNR_FN);
                }
            }
        } else {
            // 如果只存在PNR级别运价
            if (CollUtil.isNotEmpty(pnrFcList) && CollUtil.isNotEmpty(pnrFnList) && CollUtil.isNotEmpty(pnrFpList)) {
                switch (segList.size()) {
                    case 1:
                        if (!"SA".equals(segList.get(0).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg1Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 2:
                        if (!"SA".equals(segList.get(1).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg2Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 3:
                        if (!"SA".equals(segList.get(2).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg3Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 4:
                        if (!"SA".equals(segList.get(3).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg4Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 5:
                        if (!"SA".equals(segList.get(4).getPnrSegType()) && StrUtil.isEmpty(pnrFcList.get(0).getSeg5Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    default:
                        break;
                }
                // 验证成人运价组
                if (haveAdt) {
                    this.validatePnrPrice(pnrFcList, pnrFnList, pnrFpList, "AD", "GM", "JC");
                }
                // 验证儿童运价组
                if (haveChd) {
                    this.validatePnrPrice(pnrFcList, pnrFnList, pnrFpList, "CH");
                }
                // 验证婴儿运价组
                if (haveInf) {
                    this.validateInfPnrPrice(pnrFcList, pnrFnList, pnrFpList);
                }
            }
            // 只存在旅客级别运价
            else if (CollUtil.isNotEmpty(nmFcList) && CollUtil.isNotEmpty(nmFnList) && CollUtil.isNotEmpty(nmFpList)) {
                switch (segList.size()) {
                    case 1:
                        if (!"SA".equals(segList.get(0).getPnrSegType()) && StrUtil.isEmpty(nmFcList.get(0).getSeg1Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 2:
                        if (!"SA".equals(segList.get(1).getPnrSegType()) && StrUtil.isEmpty(nmFcList.get(0).getSeg2Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 3:
                        if (!"SA".equals(segList.get(2).getPnrSegType()) && StrUtil.isEmpty(nmFcList.get(0).getSeg3Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 4:
                        if (!"SA".equals(segList.get(3).getPnrSegType()) && StrUtil.isEmpty(nmFcList.get(0).getSeg4Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    case 5:
                        if (!"SA".equals(segList.get(4).getPnrSegType()) && StrUtil.isEmpty(nmFcList.get(0).getSeg5Cabin())) {
                            throw new SguiResultException("ITINERARY DOES NOT MATCH FC");
                        }
                        break;
                    default:
                        break;
                }

                // 检查每个旅客是否都有完整的运价信息
                for (MnjxPnrNm passenger : passengers) {
                    Integer psgIndex = passenger.getPsgIndex();
                    // 指定部分旅客时，筛选对应P序号的旅客
                    if (CollUtil.isNotEmpty(issueItems)) {
                        // 指定的非婴儿旅客
                        List<IssueTicketDto.IssueItemDto> adtOrChdIssueItemList = issueItems.stream()
                                .filter(i -> !"INF".equals(i.getPassengerType()) && psgIndex == Integer.parseInt(i.getPassengerId().replace("P", "")))
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(adtOrChdIssueItemList)) {
                            this.validateNmPrice(nmFcList, nmFnList, nmFpList, passenger, psgIndex);
                        }

                        // 指定的婴儿旅客
                        List<IssueTicketDto.IssueItemDto> infIssueItemList = issueItems.stream()
                                .filter(i -> "INF".equals(i.getPassengerType()) && psgIndex == Integer.parseInt(i.getPassengerId().replace("P", "")))
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(infIssueItemList)) {
                            this.validateInfNmPrice(nmFcList, nmFnList, nmFpList, passenger, psgIndex);
                        }
                    } else {
                        this.validateNmPrice(nmFcList, nmFnList, nmFpList, passenger, psgIndex);
                        // 有婴儿额外检查旅客携带婴儿的运价
                        if (haveInf) {
                            this.validateInfNmPrice(nmFcList, nmFnList, nmFpList, passenger, psgIndex);
                        }
                    }
                }
            }
        }
    }

    private void validateInfNmPrice(List<MnjxNmFc> nmFcList, List<MnjxNmFn> nmFnList, List<MnjxNmFp> nmFpList, MnjxPnrNm passenger, Integer psgIndex) throws SguiResultException {
        if (nmFcList.stream().noneMatch(f -> f.getIsBaby() == 1 && f.getPnrNmId().equals(passenger.getPnrNmId()))) {
            throw new SguiResultException(StrUtil.format("INCOMPLETE PNR/FC/IN/P{}", psgIndex));
        }
        if (nmFnList.stream().noneMatch(f -> f.getIsBaby() == 1 && f.getPnrNmId().equals(passenger.getPnrNmId()))) {
            throw new SguiResultException(StrUtil.format("INCOMPLETE PNR/FN/IN/P{}", psgIndex));
        }
        if (nmFpList.stream().noneMatch(f -> f.getIsBaby() == 1 && f.getPnrNmId().equals(passenger.getPnrNmId()))) {
            throw new SguiResultException(StrUtil.format("INCOMPLETE PNR/FP/IN/P{}", psgIndex));
        }
    }

    private void validateNmPrice(List<MnjxNmFc> nmFcList, List<MnjxNmFn> nmFnList, List<MnjxNmFp> nmFpList, MnjxPnrNm passenger, Integer psgIndex) throws SguiResultException {
        if (nmFcList.stream().noneMatch(f -> f.getIsBaby() == 0 && f.getPnrNmId().equals(passenger.getPnrNmId()))) {
            throw new SguiResultException(StrUtil.format("INCOMPLETE PNR/FC/P{}", psgIndex));
        }
        if (nmFnList.stream().noneMatch(f -> f.getIsBaby() == 0 && f.getPnrNmId().equals(passenger.getPnrNmId()))) {
            throw new SguiResultException(StrUtil.format("INCOMPLETE PNR/FN/P{}", psgIndex));
        }
        if (nmFpList.stream().noneMatch(f -> f.getIsBaby() == 0 && f.getPnrNmId().equals(passenger.getPnrNmId()))) {
            throw new SguiResultException(StrUtil.format("INCOMPLETE PNR/FP/P{}", psgIndex));
        }
    }

    private void validateInfPnrPrice(List<MnjxPnrFc> pnrFcList, List<MnjxPnrFn> pnrFnList, List<MnjxPnrFp> pnrFpList) throws SguiResultException {
        if (pnrFcList.stream().noneMatch(p -> StrUtil.equals(p.getPatType(), "IN"))) {
            throw new SguiResultException(Constant.INCOMPLETE_PNR_FC_IN);
        }
        if (pnrFnList.stream().noneMatch(p -> StrUtil.equals(p.getPatType(), "IN"))) {
            throw new SguiResultException(Constant.INCOMPLETE_PNR_FN_IN);
        }
        if (pnrFpList.stream().noneMatch(p -> StrUtil.equals(p.getPatType(), "IN"))) {
            throw new SguiResultException(Constant.INCOMPLETE_PNR_FP_IN);
        }
    }

    private void validatePnrPrice(List<MnjxPnrFc> pnrFcList, List<MnjxPnrFn> pnrFnList, List<MnjxPnrFp> pnrFpList, String... patType) throws SguiResultException {
        if (pnrFcList.stream().noneMatch(p -> StrUtil.equalsAny(p.getPatType(), patType))) {
            throw new SguiResultException(Constant.INCOMPLETE_PNR_FC);
        }
        if (pnrFnList.stream().noneMatch(p -> StrUtil.equalsAny(p.getPatType(), patType))) {
            throw new SguiResultException(Constant.INCOMPLETE_PNR_FN);
        }
        if (pnrFpList.stream().noneMatch(p -> StrUtil.equalsAny(p.getPatType(), patType))) {
            throw new SguiResultException(Constant.INCOMPLETE_PNR_FP);
        }
    }

    /**
     * 检查出票时限
     */
    private void checkIssueTimeLimit(MnjxPnr pnr) throws SguiResultException {
        // 查询首段航班信息
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        if (CollUtil.isNotEmpty(segments)) {
            MnjxPnrSeg firstSegment = segments.get(0);
            if (!"SA".equals(firstSegment.getPnrSegType())) {
                String flightDate = firstSegment.getFlightDate();
                String estimateOff = firstSegment.getEstimateOff();

                if (StrUtil.isNotEmpty(flightDate) && StrUtil.isNotEmpty(estimateOff)) {
                    String flightDateTime = flightDate + " " + estimateOff.substring(0, 2) + ":" + estimateOff.substring(2);
                    Date flightTime = DateUtil.parse(flightDateTime, "yyyy-MM-dd HH:mm");

                    // 检查是否超过出票时限（这里简化处理，实际可能需要更复杂的逻辑）
                    if (flightTime.before(new Date())) {
                        throw new SguiResultException("已超过出票时限");
                    }
                }
            }
        }
    }

    /**
     * 获取出票旅客
     */
    private List<MnjxPnrNm> getIssuePassengers(MnjxPnr pnr) throws SguiResultException {
        List<MnjxPnrNm> passengers = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrNm::getPsgIndex)
                .list();

        if (CollUtil.isEmpty(passengers)) {
            throw new SguiResultException("PNR中没有旅客信息");
        }

        // 过滤掉已经出票的旅客
        List<String> pnrNmIds = passengers.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());

        List<MnjxPnrNmTn> existingTickets = iMnjxPnrNmTnService.lambdaQuery()
                .in(MnjxPnrNmTn::getPnrNmId, pnrNmIds)
                .list();

        if (CollUtil.isNotEmpty(existingTickets)) {
            Set<String> ticketedPassengerIds = existingTickets.stream()
                    .map(MnjxPnrNmTn::getPnrNmId)
                    .collect(Collectors.toSet());

            passengers = passengers.stream()
                    .filter(p -> !ticketedPassengerIds.contains(p.getPnrNmId()))
                    .collect(Collectors.toList());
        }

        if (CollUtil.isEmpty(passengers)) {
            throw new SguiResultException("所有旅客已出票");
        }

        return passengers;
    }

    /**
     * 生成票号
     */
    private void generateTicketNumbers(IssueTicketDto dto, MnjxPnr pnr, List<MnjxPnrNm> passengers, MnjxOffice office,
                                       List<MnjxPnrNmTn> tnList, List<MnjxPnrNmTicket> ticketList, List<MnjxNmSsr> nmSsrList) throws SguiResultException {
        // 获取打票机信息
        MnjxPrinter printer = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getPrinterNo, dto.getIssue().getPrinterNo())
                .eq(MnjxPrinter::getOfficeId, office.getOfficeId())
                .one();

        if (ObjectUtil.isEmpty(printer)) {
            throw new SguiResultException("打票机不存在");
        }

        // 获取航段信息
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        // 获取航司信息
        String airlineCode = StrUtil.isNotEmpty(dto.getIssue().getValidateAirline())
                ? dto.getIssue().getValidateAirline()
                : segments.get(0).getFlightNo().substring(0, 2);

        MnjxAirline airline = iMnjxAirlineService.lambdaQuery()
                .eq(MnjxAirline::getAirlineCode, airlineCode)
                .one();

        // 计算票号并生成相关记录
        BigInteger currentTicketNo = ObjectUtil.isNotEmpty(printer.getLastTicket())
                ? printer.getLastTicket().add(BigInteger.ONE)
                : printer.getTicketStart();

        for (MnjxPnrNm passenger : passengers) {
            // 指定旅客出票
            if (dto.getIssue().getIssueItems().stream().anyMatch(i -> StrUtil.isNotEmpty(i.getPassengerId()))) {
                List<IssueTicketDto.IssueItemDto> filterIssueItems = dto.getIssue().getIssueItems().stream()
                        .filter(i -> passenger.getPsgIndex() == Integer.parseInt(i.getPassengerId().replace("P", "")))
                        .collect(Collectors.toList());
                // 按序号筛选后列表数量大于1说明是成人+携带的婴儿一起出票
                if (filterIssueItems.size() > 1) {
                    // 构建tn、nm_ticket、ssr tkne，并返回结束票号
                    currentTicketNo = this.generatePassengerTickets(dto, passenger, segments, printer, airline, currentTicketNo, null, tnList, ticketList, nmSsrList);
                    // 上一个结束票号+1为当前最新票号
                    currentTicketNo = currentTicketNo.add(BigInteger.ONE);

                    // 有婴儿，婴儿继续出票
                    MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                            .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                            .one();
                    // 构建tn、nm_ticket、ssr tkne，并返回结束票号
                    currentTicketNo = this.generatePassengerTickets(dto, passenger, segments, printer, airline, currentTicketNo, nmXn, tnList, ticketList, nmSsrList);
                    // 上一个结束票号+1为当前最新票号
                    currentTicketNo = currentTicketNo.add(BigInteger.ONE);
                } else {
                    String passengerType = filterIssueItems.get(0).getPassengerType();
                    // 指定出成人或儿童票，指定儿童票时前端没有传passengerType，只能用非INF判断
                    if (!"INF".equals(passengerType)) {
                        // 构建tn、nm_ticket、ssr tkne，并返回结束票号
                        currentTicketNo = this.generatePassengerTickets(dto, passenger, segments, printer, airline, currentTicketNo, null, tnList, ticketList, nmSsrList);
                        // 上一个结束票号+1为当前最新票号
                        currentTicketNo = currentTicketNo.add(BigInteger.ONE);
                    }
                    // 指定出婴儿票
                    else {
                        MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                                .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                                .one();
                        if (ObjectUtil.isNotEmpty(nmXn)) {
                            // 构建tn、nm_ticket、ssr tkne，并返回结束票号
                            currentTicketNo = this.generatePassengerTickets(dto, passenger, segments, printer, airline, currentTicketNo, nmXn, tnList, ticketList, nmSsrList);
                            // 上一个结束票号+1为当前最新票号
                            currentTicketNo = currentTicketNo.add(BigInteger.ONE);
                        }
                    }
                }
            }
            // 全部出票
            else {
                // 构建tn、nm_ticket、ssr tkne，并返回结束票号
                currentTicketNo = this.generatePassengerTickets(dto, passenger, segments, printer, airline, currentTicketNo, null, tnList, ticketList, nmSsrList);
                // 上一个结束票号+1为当前最新票号
                currentTicketNo = currentTicketNo.add(BigInteger.ONE);

                // 如果有婴儿，婴儿继续出票
                MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                        .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                        .one();
                if (ObjectUtil.isNotEmpty(nmXn)) {
                    // 构建tn、nm_ticket、ssr tkne，并返回结束票号
                    currentTicketNo = this.generatePassengerTickets(dto, passenger, segments, printer, airline, currentTicketNo, nmXn, tnList, ticketList, nmSsrList);
                    // 上一个结束票号+1为当前最新票号
                    currentTicketNo = currentTicketNo.add(BigInteger.ONE);
                }
            }
        }

        // 更新打票机的最后票号
        printer.setLastTicket(currentTicketNo.subtract(BigInteger.ONE));
        iMnjxPrinterService.updateById(printer);
    }

    /**
     * 为单个旅客生成票据
     */
    private BigInteger generatePassengerTickets(IssueTicketDto dto, MnjxPnrNm passenger, List<MnjxPnrSeg> segments,
                                                MnjxPrinter printer, MnjxAirline airline, BigInteger startTicketNo, MnjxNmXn nmXn,
                                                List<MnjxPnrNmTn> tnList, List<MnjxPnrNmTicket> ticketList, List<MnjxNmSsr> nmSsrList) {
        String settlementCode = airline.getAirlineSettlementCode();
        // 生成TN记录
        MnjxPnrNmTn tn = new MnjxPnrNmTn();
        tn.setTnId(IdUtil.getSnowflake(1, 1).nextIdStr());
        if (ObjectUtil.isNotEmpty(nmXn)) {
            tn.setNmXnId(nmXn.getNmXnId());
        } else {
            tn.setPnrNmId(passenger.getPnrNmId());
        }
        tn.setPrinterId(printer.getPrinterId());
        tn.setIssuedTime(DateUtils.now());
        tn.setIssuedSiId(printer.getSiId());
        if (StrUtil.isNotEmpty(dto.getIssue().getValidateAirline())) {
            tn.setIssuedAirline(dto.getIssue().getValidateAirline());
        } else {
            tn.setIssuedAirline(airline.getAirlineCode());
        }

        // 计算票号范围
        int ticketsPerPassenger = (segments.size() - 1) / 2;
        BigInteger endTicketNo = startTicketNo.add(BigInteger.valueOf(ticketsPerPassenger));

        String inputValue;
        if (segments.size() < 3) {
            inputValue = String.format("TN/%s-%s/P%d", settlementCode, endTicketNo, passenger.getPsgIndex());
            if (ObjectUtil.isNotEmpty(nmXn)) {
                inputValue = String.format("TN/IN/%s-%s/P%d", settlementCode, endTicketNo, passenger.getPsgIndex());
            }
        } else {
            String endSub = endTicketNo.toString().substring(endTicketNo.toString().length() - 2);
            inputValue = String.format("TN/%s-%s-%s/P%d", settlementCode, startTicketNo, endSub, passenger.getPsgIndex());
            if (ObjectUtil.isNotEmpty(nmXn)) {
                inputValue = String.format("TN/IN/%s-%s-%s/P%d", settlementCode, startTicketNo, endSub, passenger.getPsgIndex());
            }
        }
        tn.setInputValue(inputValue);

        tnList.add(tn);

        // 生成票据记录和SSR TKNE记录
        this.generateTicketRecords(passenger, segments, tn, settlementCode, startTicketNo, nmXn, ticketList, nmSsrList);

        return endTicketNo;
    }

    /**
     * 生成票据记录
     */
    private void generateTicketRecords(MnjxPnrNm passenger, List<MnjxPnrSeg> segments,
                                       MnjxPnrNmTn tn, String settlementCode, BigInteger startTicketNo, MnjxNmXn nmXn,
                                       List<MnjxPnrNmTicket> ticketList, List<MnjxNmSsr> nmSsrList) {
        BigInteger currentTicketNo = startTicketNo;

        for (int i = 0; i < segments.size(); i += 2) {

            MnjxPnrNmTicket ticket = new MnjxPnrNmTicket();
            ticket.setNmTicketId(IdUtil.getSnowflake(1, 1).nextIdStr());
            ticket.setPnrNmTnId(tn.getTnId());
            ticket.setTicketNo(settlementCode + currentTicketNo);
            ticket.setReceiptPrint("0");
            ticket.setIsEt("1");

            // 设置第一个航段
            if (i < segments.size()) {
                MnjxPnrSeg segment1 = segments.get(i);
                if (!"SA".equals(segment1.getPnrSegType())) {
                    ticket.setS1Id(segment1.getPnrSegId());
                    ticket.setTicketStatus1("OPEN FOR USE");
                    // 生成SSR TKNE记录
                    this.generateSsrTkne(passenger, ticket, segment1, nmXn, nmSsrList);
                }
            }

            // 设置第二个航段
            if (i + 1 < segments.size()) {
                MnjxPnrSeg segment2 = segments.get(i + 1);
                if (!"SA".equals(segment2.getPnrSegType())) {
                    ticket.setS2Id(segment2.getPnrSegId());
                    ticket.setTicketStatus2("OPEN FOR USE");
                    // 生成SSR TKNE记录
                    this.generateSsrTkne(passenger, ticket, segment2, nmXn, nmSsrList);
                }
            }

            ticketList.add(ticket);

            currentTicketNo = currentTicketNo.add(BigInteger.ONE);
        }
    }

    /**
     * 生成SSR TKNE记录
     */
    private void generateSsrTkne(MnjxPnrNm passenger, MnjxPnrNmTicket ticket, MnjxPnrSeg segment, MnjxNmXn nmXn, List<MnjxNmSsr> nmSsrList) {
        if ("SA".equals(segment.getPnrSegType())) {
            return;
        }

        MnjxNmSsr nmSsr = new MnjxNmSsr();
        nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
        nmSsr.setPnrNmId(passenger.getPnrNmId());
        nmSsr.setPnrSegNo(segment.getPnrSegNo());
        nmSsr.setSsrType(Constant.TKNE);
        nmSsr.setActionCode(segment.getActionCode());
        nmSsr.setOrgDst(StrUtil.format("{}{}", segment.getOrg(), segment.getDst()));
        nmSsr.setAirlineCode(StrUtil.subPre(segment.getFlightNo(), 2));
        nmSsr.setFltDate(segment.getFlightDate());
        String inputValue = StrUtil.format("SSR {} {} {}1 {} {} {}{} {}/{}/P{}",
                nmSsr.getSsrType(), nmSsr.getAirlineCode(), nmSsr.getActionCode(), nmSsr.getOrgDst(),
                StrUtil.subSuf(segment.getFlightNo(), 2), segment.getSellCabin(), DateUtils.ymd2PreCom(nmSsr.getFltDate(), 5), ticket.getTicketNo(),
                nmSsr.getPnrSegNo(), passenger.getPsgIndex());
        if (ObjectUtil.isNotEmpty(nmXn)) {
            inputValue = StrUtil.format("SSR {} {} {}1 {} {} {}{} INF{}/{}/P{}",
                    nmSsr.getSsrType(), nmSsr.getAirlineCode(), nmSsr.getActionCode(), nmSsr.getOrgDst(),
                    StrUtil.subSuf(segment.getFlightNo(), 2), segment.getSellCabin(), DateUtils.ymd2PreCom(nmSsr.getFltDate(), 5), ticket.getTicketNo(),
                    nmSsr.getPnrSegNo(), passenger.getPsgIndex());
        }
        nmSsr.setSsrInfo(inputValue);
        nmSsr.setInputValue(inputValue);

        nmSsrList.add(nmSsr);
    }

    /**
     * 保存票价数据
     */
    private void saveTicketPriceData(IssueTicketDto dto, MnjxPnr pnr, List<MnjxPnrNm> passengers, List<MnjxPnrNmTn> tnList, List<MnjxPnrNmTicket> ticketList) {
        // 获取航段信息
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        for (MnjxPnrNm passenger : passengers) {
            // 获取该旅客的票号
            List<MnjxPnrNmTn> operateTnList = tnList.stream()
                    .filter(t -> passenger.getPnrNmId().equals(t.getPnrNmId()))
                    .collect(Collectors.toList());
            MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                    .one();
            if (ObjectUtil.isNotEmpty(nmXn)) {
                // 指定旅客时
                if (dto.getIssue().getIssueItems().stream().anyMatch(i -> StrUtil.isNotEmpty(i.getPassengerId()))) {
                    // 同时指定了成人和婴儿
                    if (dto.getIssue().getIssueItems().stream().filter(i -> passenger.getPsgIndex() == Integer.parseInt(i.getPassengerId().replace("P", ""))).count() > 1) {
                        operateTnList.addAll(tnList.stream()
                                .filter(t -> nmXn.getNmXnId().equals(t.getNmXnId()))
                                .collect(Collectors.toList()));
                    }
                    // 只指定了婴儿
                    else if ("INF".equals(dto.getIssue().getIssueItems().get(0).getPassengerType())) {
                        operateTnList = tnList.stream()
                                .filter(t -> nmXn.getNmXnId().equals(t.getNmXnId()))
                                .collect(Collectors.toList());
                    }
                }
            }

            for (MnjxPnrNmTn tn : operateTnList) {
                List<MnjxPnrNmTicket> tickets = ticketList.stream()
                        .filter(t -> tn.getTnId().equals(t.getPnrNmTnId()))
                        .collect(Collectors.toList());

                for (MnjxPnrNmTicket ticket : tickets) {
                    MnjxTicketPrice ticketPrice = new MnjxTicketPrice();
                    ticketPrice.setTicketNo(ticket.getTicketNo());

                    // 获取运价信息
                    this.setTicketPriceInfo(ticketPrice, passenger, pnr, segments);

                    iMnjxTicketPriceService.save(ticketPrice);
                }
            }
        }
    }

    /**
     * 设置票价信息
     */
    private void setTicketPriceInfo(MnjxTicketPrice ticketPrice, MnjxPnrNm passenger, MnjxPnr pnr, List<MnjxPnrSeg> segments) {
        // 获取FC信息
        List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                .eq(MnjxNmFc::getPnrNmId, passenger.getPnrNmId())
                .list();

        if (CollUtil.isNotEmpty(nmFcList)) {
            ticketPrice.setFcInfo(nmFcList.get(0).getInputValue());
        } else {
            List<MnjxPnrFc> pnrFcList = iMnjxPnrFcService.lambdaQuery()
                    .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                    .list();
            if (CollUtil.isNotEmpty(pnrFcList)) {
                ticketPrice.setFcInfo(pnrFcList.get(0).getInputValue());
            }
        }

        // 获取FN信息
        List<MnjxNmFn> nmFnList = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, passenger.getPnrNmId())
                .list();

        if (CollUtil.isNotEmpty(nmFnList)) {
            ticketPrice.setFnInfo(nmFnList.get(0).getInputValue());
        } else {
            List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .list();
            if (CollUtil.isNotEmpty(pnrFnList)) {
                ticketPrice.setFnInfo(pnrFnList.get(0).getInputValue());
            }
        }

        // 获取FP信息
        List<MnjxNmFp> nmFpList = iMnjxNmFpService.lambdaQuery()
                .eq(MnjxNmFp::getPnrNmId, passenger.getPnrNmId())
                .list();

        if (CollUtil.isNotEmpty(nmFpList)) {
            ticketPrice.setPayType(nmFpList.get(0).getPayType());
        } else {
            List<MnjxPnrFp> pnrFpList = iMnjxPnrFpService.lambdaQuery()
                    .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                    .list();
            if (CollUtil.isNotEmpty(pnrFpList)) {
                ticketPrice.setPayType(pnrFpList.get(0).getPayType());
            }
        }

        // 构建航段信息
        StringBuilder segInfoBuilder = new StringBuilder();
        for (int i = 0; i < segments.size(); i++) {
            MnjxPnrSeg segment = segments.get(i);
            if (StrUtil.isNotEmpty(segment.getFlightNo())) {
                segInfoBuilder.append(String.format("%s %s %s",
                        segment.getFlightNo(), segment.getFlightDate(), segment.getSellCabin()));
            } else {
                segInfoBuilder.append(String.format("SA %s%s", segment.getOrg(), segment.getDst()));
            }
            if (i < segments.size() - 1) {
                segInfoBuilder.append("/");
            }
        }
        ticketPrice.setSegInfo(segInfoBuilder.toString());
    }

    /**
     * 删除TK FC EI记录
     */
    private void deleteTkFcEiRecords(IssueTicketDto dto, MnjxPnr pnr, List<MnjxPnrNm> passengers, String newAtNo) {
        // 删除TK记录（除了T类型）
        MnjxPnrTk pnrTk = iMnjxPnrTkService.lambdaQuery()
                .eq(MnjxPnrTk::getPnrId, pnr.getPnrId())
                .ne(MnjxPnrTk::getPnrTkType, "T")
                .one();
        if (ObjectUtil.isNotEmpty(pnrTk)) {
            // record更新修改标识
            MnjxPnrRecord tkRecord = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrRecord::getPnrIndex, pnrTk.getPnrIndex())
                    .isNull(MnjxPnrRecord::getChangeMark)
                    .one();
            tkRecord.setChangeMark("X");
            tkRecord.setChangeAtNo(newAtNo);
            iMnjxPnrRecordService.updateById(tkRecord);
            iMnjxPnrTkService.removeById(pnrTk.getPnrTkId());
        }

        // 如果运价是PNR级别的，需要判断该pnr的旅客是不是全部出完票，才能删除pnrFc和pnrEi
        List<MnjxPnrFc> pnrFcList = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                .list();
        if (CollUtil.isNotEmpty(pnrFcList)) {
            List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .list();
            List<String> pnrNmIdList = pnrNmList.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList());
            List<MnjxNmXn> nmXnList = iMnjxNmXnService.lambdaQuery()
                    .in(MnjxNmXn::getPnrNmId, pnrNmIdList)
                    .list();
            List<MnjxPnrNmTn> existingTickets = new ArrayList<>();
            int allPsgCount = 0;
            if (pnrFcList.stream().anyMatch(f -> f.getIsBaby() == 0)) {
                existingTickets.addAll(iMnjxPnrNmTnService.lambdaQuery()
                        .in(MnjxPnrNmTn::getPnrNmId, pnrNmIdList)
                        .list());
                allPsgCount += pnrNmList.size();
            }
            if (CollUtil.isNotEmpty(nmXnList) && pnrFcList.stream().anyMatch(f -> f.getIsBaby() == 1)) {
                existingTickets.addAll(iMnjxPnrNmTnService.lambdaQuery()
                        .in(MnjxPnrNmTn::getNmXnId, nmXnList.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList()))
                        .list());
                allPsgCount += nmXnList.size();
            }
            // 是否全部出完，来确定是否可以删除FC EI
            boolean canDeleteFcEi = allPsgCount == existingTickets.size();
            if (canDeleteFcEi) {
                List<MnjxPnrRecord> fcRecordList = iMnjxPnrRecordService.lambdaQuery()
                        .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                        .in(MnjxPnrRecord::getPnrIndex, pnrFcList.stream().map(MnjxPnrFc::getPnrIndex).collect(Collectors.toList()))
                        .isNull(MnjxPnrRecord::getChangeMark)
                        .list();
                fcRecordList.forEach(f -> {
                    f.setChangeMark("X");
                    f.setChangeAtNo(newAtNo);
                });
                iMnjxPnrRecordService.updateBatchById(fcRecordList);
                iMnjxPnrFcService.removeByIds(pnrFcList.stream().map(MnjxPnrFc::getPnrFcId).collect(Collectors.toList()));

                // 删除EI记录
                List<MnjxPnrEi> pnrEiList = iMnjxPnrEiService.lambdaQuery()
                        .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                        .list();
                if (CollUtil.isNotEmpty(pnrEiList)) {
                    List<MnjxPnrRecord> pnrEiRecordList = iMnjxPnrRecordService.lambdaQuery()
                            .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                            .in(MnjxPnrRecord::getPnrIndex, pnrEiList.stream().map(MnjxPnrEi::getPnrIndex).collect(Collectors.toList()))
                            .isNull(MnjxPnrRecord::getChangeMark)
                            .list();
                    pnrEiRecordList.forEach(f -> {
                        f.setChangeMark("X");
                        f.setChangeAtNo(newAtNo);
                    });
                    iMnjxPnrRecordService.updateBatchById(pnrEiRecordList);
                    iMnjxPnrEiService.removeByIds(pnrEiList.stream().map(MnjxPnrEi::getPnrEiId).collect(Collectors.toList()));
                }
            }
        }

        // 同时还可能包含旅客级别的（当旅客组成是成人、儿童、婴儿时，成人、儿童的fc是旅客级别的，婴儿是pnr级别的，因此上面处理完婴儿的pnr级别，这里还需要处理成人、儿童的旅客级别）
        // 旅客级别的直接删除对应出票旅客的nmFc和nmEi
        // 指定旅客出票时
        if (dto.getIssue().getIssueItems().stream().anyMatch(i -> StrUtil.isNotEmpty(i.getPassengerId()))) {
            List<MnjxNmFc> toDeleteNmFcList = new ArrayList<>();
            List<MnjxPnrRecord> toDeleteRecordList = new ArrayList<>();
            List<MnjxNmEi> toDeleteNmEiList = new ArrayList<>();
            for (MnjxPnrNm passenger : passengers) {
                // size大于1时只能是同时指定了该成人旅客和其携带的婴儿
                if (dto.getIssue().getIssueItems().size() > 1) {
                    List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                            .eq(MnjxNmFc::getPnrNmId, passenger.getPnrNmId())
                            .list();
                    if (CollUtil.isNotEmpty(nmFcList)) {
                        List<MnjxPnrRecord> nmFcRecordList = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .in(MnjxPnrRecord::getPnrIndex, nmFcList.stream().map(MnjxNmFc::getPnrIndex).collect(Collectors.toList()))
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .list();
                        toDeleteNmFcList.addAll(nmFcList);
                        toDeleteRecordList.addAll(nmFcRecordList);
                    }
                    List<MnjxNmEi> nmEiList = iMnjxNmEiService.lambdaQuery()
                            .eq(MnjxNmEi::getPnrNmId, passenger.getPnrNmId())
                            .list();
                    if (CollUtil.isNotEmpty(nmEiList)) {
                        List<MnjxPnrRecord> nmEiRecordList = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .in(MnjxPnrRecord::getPnrIndex, nmEiList.stream().map(MnjxNmEi::getPnrIndex).collect(Collectors.toList()))
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .list();
                        toDeleteNmEiList.addAll(nmEiList);
                        toDeleteRecordList.addAll(nmEiRecordList);
                    }
                }
                // 只指定了婴儿，只删除婴儿的nmFc和nmEi
                else if ("INF".equals(dto.getIssue().getIssueItems().get(0).getPassengerType())) {
                    MnjxNmFc xnNmFc = iMnjxNmFcService.lambdaQuery()
                            .eq(MnjxNmFc::getPnrNmId, passenger.getPnrNmId())
                            .eq(MnjxNmFc::getIsBaby, 1)
                            .one();
                    if (xnNmFc != null) {
                        MnjxPnrRecord xnNmFcRecord = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .eq(MnjxPnrRecord::getPnrIndex, xnNmFc.getPnrIndex())
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .one();
                        toDeleteNmFcList.add(xnNmFc);
                        toDeleteRecordList.add(xnNmFcRecord);
                    }
                    MnjxNmEi xnNmEi = iMnjxNmEiService.lambdaQuery()
                            .eq(MnjxNmEi::getPnrNmId, passenger.getPnrNmId())
                            .like(MnjxNmEi::getInputValue, "/IN/")
                            .one();
                    if (xnNmEi != null) {
                        MnjxPnrRecord xnNmEiRecord = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .eq(MnjxPnrRecord::getPnrIndex, xnNmEi.getPnrIndex())
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .one();
                        toDeleteNmEiList.add(xnNmEi);
                        toDeleteRecordList.add(xnNmEiRecord);
                    }
                }
                // 只指定了成人或儿童
                else {
                    MnjxNmFc nmFc = iMnjxNmFcService.lambdaQuery()
                            .eq(MnjxNmFc::getPnrNmId, passenger.getPnrNmId())
                            .eq(MnjxNmFc::getIsBaby, 0)
                            .one();
                    if (nmFc != null) {
                        MnjxPnrRecord nmFcRecord = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .eq(MnjxPnrRecord::getPnrIndex, nmFc.getPnrIndex())
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .one();
                        toDeleteNmFcList.add(nmFc);
                        toDeleteRecordList.add(nmFcRecord);
                    }
                    MnjxNmEi nmEi = iMnjxNmEiService.lambdaQuery()
                            .eq(MnjxNmEi::getPnrNmId, passenger.getPnrNmId())
                            .notLike(MnjxNmEi::getInputValue, "/IN/")
                            .one();
                    if (nmEi != null) {
                        MnjxPnrRecord nmEiRecord = iMnjxPnrRecordService.lambdaQuery()
                                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                                .eq(MnjxPnrRecord::getPnrIndex, nmEi.getPnrIndex())
                                .isNull(MnjxPnrRecord::getChangeMark)
                                .one();
                        toDeleteNmEiList.add(nmEi);
                        toDeleteRecordList.add(nmEiRecord);
                    }
                }
            }
            if (CollUtil.isNotEmpty(toDeleteRecordList)) {
                toDeleteRecordList.forEach(f -> {
                    f.setChangeMark("X");
                    f.setChangeAtNo(newAtNo);
                });
                iMnjxPnrRecordService.updateBatchById(toDeleteRecordList);
            }
            if (CollUtil.isNotEmpty(toDeleteNmEiList)) {
                iMnjxNmEiService.removeByIds(toDeleteNmEiList.stream().map(MnjxNmEi::getNmEiId).collect(Collectors.toList()));
            }
            if (CollUtil.isNotEmpty(toDeleteNmFcList)) {
                iMnjxNmFcService.removeByIds(toDeleteNmFcList.stream().map(MnjxNmFc::getNmFcId).collect(Collectors.toList()));
            }
        } else {
            List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                    .in(MnjxNmFc::getPnrNmId, passengers.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                    .list();
            if (CollUtil.isNotEmpty(nmFcList)) {
                List<MnjxPnrRecord> nmFcRecordList = iMnjxPnrRecordService.lambdaQuery()
                        .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                        .in(MnjxPnrRecord::getPnrIndex, nmFcList.stream().map(MnjxNmFc::getPnrIndex).collect(Collectors.toList()))
                        .isNull(MnjxPnrRecord::getChangeMark)
                        .list();
                nmFcRecordList.forEach(f -> {
                    f.setChangeMark("X");
                    f.setChangeAtNo(newAtNo);
                });
                iMnjxPnrRecordService.updateBatchById(nmFcRecordList);
                iMnjxNmFcService.removeByIds(nmFcList.stream().map(MnjxNmFc::getNmFcId).collect(Collectors.toList()));
            }
            List<MnjxNmEi> nmEiList = iMnjxNmEiService.lambdaQuery()
                    .in(MnjxNmEi::getPnrNmId, passengers.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                    .list();
            if (CollUtil.isNotEmpty(nmEiList)) {
                List<MnjxPnrRecord> nmEiRecordList = iMnjxPnrRecordService.lambdaQuery()
                        .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                        .in(MnjxPnrRecord::getPnrIndex, nmEiList.stream().map(MnjxNmEi::getPnrIndex).collect(Collectors.toList()))
                        .isNull(MnjxPnrRecord::getChangeMark)
                        .list();
                nmEiRecordList.forEach(f -> {
                    f.setChangeMark("X");
                    f.setChangeAtNo(newAtNo);
                });
                iMnjxPnrRecordService.updateBatchById(nmEiRecordList);
                iMnjxNmEiService.removeByIds(nmEiList.stream().map(MnjxNmEi::getNmEiId).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 生成TK记录
     */
    private void generateTkRecord(MnjxPnr pnr) {
        // 检查是否已存在T类型的TK记录
        long count = iMnjxPnrTkService.lambdaQuery()
                .eq(MnjxPnrTk::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrTk::getPnrTkType, "T")
                .count();

        if (count == 0) {
            MnjxPnrTk tk = new MnjxPnrTk();
            tk.setPnrTkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            tk.setPnrId(pnr.getPnrId());
            tk.setPnrTkType("T");
            tk.setPlanEtdzDate(DateUtil.today());
            tk.setInputValue("T");

            iMnjxPnrTkService.save(tk);
        }
    }

    /**
     * 生成RMK记录
     */
    private void generateRmkRecord(MnjxPnr pnr, MnjxOffice office) {
        // 只生成一次，部分出票时后续出票不重复生成
        boolean hasTj = false;
        boolean hasTr = false;
        Integer tjCount = iMnjxPnrRmkService.lambdaQuery()
                .eq(MnjxPnrRmk::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrRmk::getRmkName, "TJ")
                .count();
        if (tjCount > 0) {
            hasTj = true;
        }
        Integer trCount = iMnjxPnrRmkService.lambdaQuery()
                .eq(MnjxPnrRmk::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrRmk::getRmkName, "TR")
                .count();
        if (trCount > 0) {
            hasTr = true;
        }
        String tjInfo = "RMK TJ " + office.getOfficeNo();
        String trInfo = "RMK TR " + office.getOfficeNo();

        // 生成RMK TJ记录
        if (!hasTj) {
            MnjxPnrRmk rmkTj = new MnjxPnrRmk();
            rmkTj.setPnrRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            rmkTj.setPnrId(pnr.getPnrId());
            rmkTj.setRmkName("TJ");
            rmkTj.setRmkInfo(tjInfo);
            rmkTj.setInputValue(tjInfo);

            iMnjxPnrRmkService.save(rmkTj);
        }

        // 生成RMK TR记录
        if (!hasTr) {
            MnjxPnrRmk rmkTr = new MnjxPnrRmk();
            rmkTr.setPnrRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            rmkTr.setPnrId(pnr.getPnrId());
            rmkTr.setRmkName("TR");
            rmkTr.setRmkInfo(trInfo);
            rmkTr.setInputValue(trInfo);

            iMnjxPnrRmkService.save(rmkTr);
        }
    }

    /**
     * 生成psgCki和psgSeat数据
     */
    private void generatePsgCkiAndSeat(MnjxPnr pnr, List<MnjxPnrNm> passengers) {
        // 获取航段信息
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        for (MnjxPnrNm passenger : passengers) {
            for (MnjxPnrSeg segment : segments) {
                if (!"SA".equals(segment.getPnrSegType())) {
                    // 生成psgCki记录
                    MnjxPsgCki psgCki = new MnjxPsgCki();
                    psgCki.setPsgCkiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    psgCki.setPnrNmId(passenger.getPnrNmId());
                    psgCki.setPnrSegNo(StrUtil.toString(segment.getPnrSegNo()));
                    psgCki.setCabinClass(segment.getCabinClass());
                    psgCki.setSellCabin(segment.getSellCabin());
                    psgCki.setAbdStatusInfi("0");
                    psgCki.setCkiStatus("NACC"); // 未值机

                    iMnjxPsgCkiService.save(psgCki);

                    // 生成psgSeat记录
                    MnjxPsgSeat psgSeat = new MnjxPsgSeat();
                    psgSeat.setPsgSeatId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    psgSeat.setPsgCkiId(psgCki.getPsgCkiId());

                    iMnjxPsgSeatService.save(psgSeat);
                }
            }
        }
    }

    /**
     * 生成客票操作记录
     */
    private void generateTicketOperateRecord(List<MnjxPnrNmTicket> ticketList, MnjxSi currentUser) {
        List<MnjxTicketOperateRecord> ticketOperateRecordList = new ArrayList<>();
        for (MnjxPnrNmTicket ticket : ticketList) {
            MnjxTicketOperateRecord record = new MnjxTicketOperateRecord();
            record.setTicketOperateRecordId(IdUtil.getSnowflake(1, 1).nextIdStr());
            record.setTicketNo(ticket.getTicketNo());
            record.setTicketStatus1(ticket.getTicketStatus1());
            record.setTicketStatus2(ticket.getTicketStatus2());
            record.setOperateTime(DateUtil.now());
            record.setSettlementCode(ticket.getTicketNo().substring(0, 3));
            record.setSiNo(currentUser.getSiNo());

            ticketOperateRecordList.add(record);
        }
        iMnjxTicketOperateRecordService.saveBatch(ticketOperateRecordList);
    }

    /**
     * PNR重新排序和封口
     */
    private void reorderAndSeal(MnjxPnr pnr, String newAtNo, MnjxSi currentUser) {
        // 重新排序所有项的pnr_index
        List<MnjxPnrRecord> recordList = new ArrayList<>();
        iUpdatePnrService.reorderAllPnrIndexesAndUpdate(pnr, recordList);

        // 生成封口记录
        this.generateSealingRecord(pnr, recordList, newAtNo, currentUser);
    }

    /**
     * 生成封口记录
     */
    private void generateSealingRecord(MnjxPnr pnr, List<MnjxPnrRecord> recordList, String atNo, MnjxSi currentUser) {
        // 生成封口记录
        MnjxPnrAt pnrAt = new MnjxPnrAt();
        pnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrAt.setPnrId(pnr.getPnrId());
        pnrAt.setAtNo(atNo);
        pnrAt.setAtSiId(currentUser.getSiId());
        pnrAt.setAtDateTime(new Date());

        iMnjxPnrAtService.save(pnrAt);

        // 历史记录，先删除以前changeMark为空的，再批量添加这次封口所有已排好序的记录
        iMnjxPnrRecordService.lambdaUpdate()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .isNull(MnjxPnrRecord::getChangeMark)
                .remove();
        // 保存历史记录
        iMnjxPnrRecordService.saveBatch(recordList);
    }

    /**
     * 构建返回结果
     */
    private IssueTicketVo buildResult(IssueTicketDto dto) {
        IssueTicketVo result = new IssueTicketVo();

        List<IssueTicketVo.BopBookTicketResVo> bookTicketResVoList = new ArrayList<>();
        if (dto.getIssue().getIssueItems().stream().anyMatch(i -> StrUtil.isNotEmpty(i.getPassengerId()))) {
            for (IssueTicketDto.IssueItemDto issueItem : dto.getIssue().getIssueItems()) {
                // 构建BOP出票结果
                IssueTicketVo.BopBookTicketResVo bopRes = new IssueTicketVo.BopBookTicketResVo();
                bopRes.setSuccessType(true);
                bopRes.setPassenger(issueItem.getPassengerType());
                String passengerName = new String(Base64.getDecoder().decode(issueItem.getName()), StandardCharsets.UTF_8);
                bopRes.setPassengerNames(passengerName);
                bopRes.setHasTkneFail(false);
                bopRes.setTxnTraceKey("XXXSAT" + System.currentTimeMillis());
                bopRes.setIssueDate(DateUtil.now());
                bopRes.setDescription(null);

                bookTicketResVoList.add(bopRes);
            }
        } else {
            // 构建BOP出票结果
            IssueTicketVo.BopBookTicketResVo bopRes = new IssueTicketVo.BopBookTicketResVo();
            bopRes.setSuccessType(true);
            bopRes.setPassenger("");
            bopRes.setPassengerNames("");
            bopRes.setHasTkneFail(false);
            bopRes.setTxnTraceKey("XXXSAT" + System.currentTimeMillis());
            bopRes.setIssueDate(DateUtil.now());
            bopRes.setDescription(null);

            bookTicketResVoList.add(bopRes);
        }


        result.setBopBookTicketRes(bookTicketResVoList);

        // 构建出票结果
        IssueTicketVo.IssueResVo issueRes = new IssueTicketVo.IssueResVo();
        issueRes.setResult("2");
        issueRes.setTxnTraceKey(null);

        result.setIssueRes(issueRes);
        result.setValidateIetFail(false);
        result.setCheckSameName(false);

        return result;
    }
}
