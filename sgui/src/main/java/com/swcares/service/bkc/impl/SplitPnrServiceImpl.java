package com.swcares.service.bkc.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.entity.*;
import com.swcares.obj.dto.SplitPnrByPassengerDto;
import com.swcares.obj.vo.SplitPnrByPassengerVo;
import com.swcares.service.*;
import com.swcares.service.bkc.ISplitPnrService;
import com.swcares.service.et.IBookPnrService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分离PNR服务实现类
 *
 * <AUTHOR>
 * @date 2025/1/15 16:30
 */
@Slf4j
@Service
public class SplitPnrServiceImpl implements ISplitPnrService {

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPnrCtService iMnjxPnrCtService;

    @Resource
    private IMnjxNmCtService iMnjxNmCtService;

    @Resource
    private IMnjxPnrTkService iMnjxPnrTkService;

    @Resource
    private IMnjxPnrFcService iMnjxPnrFcService;

    @Resource
    private IMnjxNmFcService iMnjxNmFcService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxPnrOsiService iMnjxPnrOsiService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IMnjxPnrRmkService iMnjxPnrRmkService;

    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;

    @Resource
    private IMnjxPnrTcService iMnjxPnrTcService;

    @Resource
    private IMnjxNmTcService iMnjxNmTcService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxPnrEiService iMnjxPnrEiService;

    @Resource
    private IMnjxNmEiService iMnjxNmEiService;

    @Resource
    private IMnjxNmOiService iMnjxNmOiService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrFpService iMnjxPnrFpService;

    @Resource
    private IMnjxNmFpService iMnjxNmFpService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IBookPnrService iBookPnrService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SplitPnrByPassengerVo splitPnrByPassenger(SplitPnrByPassengerDto dto) throws SguiResultException {
        // 参数校验
        this.validateRequest(dto);

        // 1. 根据PNR编码查询原PNR信息
        MnjxPnr originalPnr = this.queryOriginalPnr(dto.getPassengerRecordLocator());

        // 2. 查询原PNR的所有关联数据
        PnrData originalPnrData = this.queryAllPnrData(originalPnr.getPnrId());

        // 3. 获取要分离的旅客信息
        List<MnjxPnrNm> splitPassengers = this.getSplitPassengers(originalPnrData.pnrNmList, dto.getTravellers());

        // 4. 创建新PNR
        MnjxPnr newPnr = this.createNewPnr(originalPnr);

        Map<String, String> newAndOldSegIdMap = new HashMap<>();
        // 5. 复制PNR级别数据到新PNR
        PnrData newPnrData = this.copyPnrLevelData(originalPnrData, newPnr.getPnrId(), newAndOldSegIdMap);

        // 6. 分离旅客数据到新PNR
        this.splitPassengerData(originalPnrData, newPnrData, splitPassengers, newPnr.getPnrId(), newAndOldSegIdMap);

        // 7. 更新航段座位数量
        this.updateSegmentSeatNumbers(originalPnrData.pnrSegList, newPnrData.pnrSegList, splitPassengers.size());

        // 8. 重新排序新PNR并批量保存
        this.reorderAndSaveNewPnr(newPnr, newPnrData);

        // 9. 重新排序原PNR并批量更新
        this.reorderAndUpdateOriginalPnr(originalPnr, originalPnrData);

        // 10. 构建返回结果
        return this.buildResponse(originalPnr.getPnrCrs(), newPnr.getPnrCrs());
    }

    /**
     * 参数校验
     */
    private void validateRequest(SplitPnrByPassengerDto dto) throws SguiResultException {
        if (dto == null) {
            throw new SguiResultException("请求参数不能为空");
        }
        if (StrUtil.isEmpty(dto.getPassengerRecordLocator())) {
            throw new SguiResultException("PNR编号不能为空");
        }
        if (CollUtil.isEmpty(dto.getTravellers())) {
            throw new SguiResultException("要分离的旅客列表不能为空");
        }
    }

    /**
     * 查询原PNR信息
     */
    private MnjxPnr queryOriginalPnr(String pnrNo) throws SguiResultException {
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, pnrNo)
                .one();
        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息");
        }
        return pnr;
    }

    /**
     * 查询PNR的所有关联数据
     */
    private PnrData queryAllPnrData(String pnrId) throws SguiResultException {
        PnrData pnrData = new PnrData();

        // 查询旅客信息
        pnrData.pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnrId)
                .orderByAsc(MnjxPnrNm::getPsgIndex)
                .list();

        // 查询航段信息
        pnrData.pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnrId)
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        // 查询PNR级别联系方式
        pnrData.pnrCtList = iMnjxPnrCtService.lambdaQuery()
                .eq(MnjxPnrCt::getPnrId, pnrId)
                .list();

        // 获取旅客ID列表，用于查询旅客级别数据
        List<String> passengerIds = pnrData.pnrNmList.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());

        // 查询旅客级别联系方式
        if (CollUtil.isNotEmpty(passengerIds)) {
            pnrData.nmCtList = iMnjxNmCtService.lambdaQuery()
                    .in(MnjxNmCt::getPnrNmId, passengerIds)
                    .list();
        }

        // 查询出票时限
        pnrData.pnrTkList = iMnjxPnrTkService.lambdaQuery()
                .eq(MnjxPnrTk::getPnrId, pnrId)
                .list();

        // 查询PNR级别FC
        pnrData.pnrFcList = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnrId)
                .list();

        // 查询旅客级别FC
        if (CollUtil.isNotEmpty(passengerIds)) {
            pnrData.nmFcList = iMnjxNmFcService.lambdaQuery()
                    .in(MnjxNmFc::getPnrNmId, passengerIds)
                    .list();
        }

        // 查询旅客特服
        if (CollUtil.isNotEmpty(passengerIds)) {
            pnrData.nmSsrList = iMnjxNmSsrService.lambdaQuery()
                    .in(MnjxNmSsr::getPnrNmId, passengerIds)
                    .list();
        }

        // 查询PNR级别OSI
        pnrData.pnrOsiList = iMnjxPnrOsiService.lambdaQuery()
                .eq(MnjxPnrOsi::getPnrId, pnrId)
                .list();

        // 查询旅客级别OSI
        if (CollUtil.isNotEmpty(passengerIds)) {
            pnrData.nmOsiList = iMnjxNmOsiService.lambdaQuery()
                    .in(MnjxNmOsi::getPnrNmId, passengerIds)
                    .list();
        }

        // 查询PNR级别备注
        pnrData.pnrRmkList = iMnjxPnrRmkService.lambdaQuery()
                .eq(MnjxPnrRmk::getPnrId, pnrId)
                .list();

        // 查询旅客级别备注
        if (CollUtil.isNotEmpty(passengerIds)) {
            pnrData.nmRmkList = iMnjxNmRmkService.lambdaQuery()
                    .in(MnjxNmRmk::getPnrNmId, passengerIds)
                    .list();
        }

        // 查询PNR级别TC
        pnrData.pnrTcList = iMnjxPnrTcService.lambdaQuery()
                .eq(MnjxPnrTc::getPnrId, pnrId)
                .list();

        // 查询旅客级别TC
        if (CollUtil.isNotEmpty(passengerIds)) {
            pnrData.nmTcList = iMnjxNmTcService.lambdaQuery()
                    .in(MnjxNmTc::getPnrNmId, passengerIds)
                    .list();
        }

        // 查询PNR级别FN
        pnrData.pnrFnList = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnrId)
                .list();

        // 查询旅客级别FN
        if (CollUtil.isNotEmpty(passengerIds)) {
            pnrData.nmFnList = iMnjxNmFnService.lambdaQuery()
                    .in(MnjxNmFn::getPnrNmId, passengerIds)
                    .list();
        }

        // 查询PNR级别EI
        pnrData.pnrEiList = iMnjxPnrEiService.lambdaQuery()
                .eq(MnjxPnrEi::getPnrId, pnrId)
                .list();

        // 查询旅客级别EI
        if (CollUtil.isNotEmpty(passengerIds)) {
            pnrData.nmEiList = iMnjxNmEiService.lambdaQuery()
                    .in(MnjxNmEi::getPnrNmId, passengerIds)
                    .list();
        }

        // 查询旅客级别OI
        if (CollUtil.isNotEmpty(passengerIds)) {
            pnrData.nmOiList = iMnjxNmOiService.lambdaQuery()
                    .in(MnjxNmOi::getPnrNmId, passengerIds)
                    .list();
        }

        // 查询PNR级别TN
        if (CollUtil.isNotEmpty(passengerIds)) {
            pnrData.pnrTnList = iMnjxPnrNmTnService.lambdaQuery()
                    .in(MnjxPnrNmTn::getPnrNmId, passengerIds)
                    .list();
        }

        // 查询婴儿信息
        if (CollUtil.isNotEmpty(passengerIds)) {
            pnrData.nmXnList = iMnjxNmXnService.lambdaQuery()
                    .in(MnjxNmXn::getPnrNmId, passengerIds)
                    .list();
            if (CollUtil.isNotEmpty(pnrData.nmXnList)) {
                pnrData.pnrTnList.addAll(iMnjxPnrNmTnService.lambdaQuery()
                        .in(MnjxPnrNmTn::getNmXnId, pnrData.nmXnList.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList()))
                        .list());
            }
        }

        // 查询ticket
        if (CollUtil.isNotEmpty(pnrData.pnrTnList)) {
            pnrData.pnrTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                    .in(MnjxPnrNmTicket::getPnrNmTnId, pnrData.pnrTnList.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList()))
                    .list();
        }

        // 查询PNR级别FP
        pnrData.pnrFpList = iMnjxPnrFpService.lambdaQuery()
                .eq(MnjxPnrFp::getPnrId, pnrId)
                .list();

        // 查询旅客级别FP
        if (CollUtil.isNotEmpty(passengerIds)) {
            pnrData.nmFpList = iMnjxNmFpService.lambdaQuery()
                    .in(MnjxNmFp::getPnrNmId, passengerIds)
                    .list();
        }

        // 如果分离的PNR是未出票的，并且含有这些 自动 运价信息 PNRFC PNRFN PNRTC NMFC NMFN NMTC，必须先删除才能分离。手动运价可以直接分离
        if (CollUtil.isEmpty(pnrData.pnrTnList)) {
            if ((CollUtil.isNotEmpty(pnrData.pnrFcList) && pnrData.pnrFcList.stream().anyMatch(p -> p.getInputValue().contains("/A/")))
                    || (CollUtil.isNotEmpty(pnrData.pnrFnList) && pnrData.pnrFnList.stream().anyMatch(p -> p.getInputValue().contains("/A/")))
                    || (CollUtil.isNotEmpty(pnrData.pnrTcList) && pnrData.pnrTcList.stream().anyMatch(p -> p.getInputValue().contains("/A/")))
                    || (CollUtil.isNotEmpty(pnrData.nmFcList) && pnrData.nmFcList.stream().anyMatch(p -> p.getInputValue().contains("/A/")))
                    || (CollUtil.isNotEmpty(pnrData.nmFnList) && pnrData.nmFnList.stream().anyMatch(p -> p.getInputValue().contains("/A/")))
                    || (CollUtil.isNotEmpty(pnrData.nmTcList) && pnrData.nmTcList.stream().anyMatch(p -> p.getInputValue().contains("/A/")))) {
                throw new SguiResultException("主机错误:PLS DELETE AUTOMATIC FARE QUOTE FN/FC/TC FIRST");
            }
        }

        return pnrData;
    }

    /**
     * 获取要分离的旅客信息
     */
    private List<MnjxPnrNm> getSplitPassengers(List<MnjxPnrNm> allPassengers, List<SplitPnrByPassengerDto.Traveller> travellers) throws SguiResultException {
        List<MnjxPnrNm> splitPassengers = new ArrayList<>();

        for (SplitPnrByPassengerDto.Traveller traveller : travellers) {
            MnjxPnrNm passenger = allPassengers.stream()
                    .filter(p -> p.getPsgIndex().equals(traveller.getPaxId()))
                    .findFirst()
                    .orElse(null);

            if (passenger == null) {
                throw new SguiResultException("未找到旅客ID为 " + traveller.getPaxId() + " 的旅客信息");
            }

            splitPassengers.add(passenger);
        }

        return splitPassengers;
    }

    /**
     * 创建新PNR
     */
    private MnjxPnr createNewPnr(MnjxPnr originalPnr) {
        MnjxPnr newPnr = new MnjxPnr();

        // 生成新的PNR ID
        newPnr.setPnrId(IdUtil.getSnowflake(1, 1).nextIdStr());

        // 生成新的PNR编码
        newPnr.setPnrIcs(this.generatePnrCode("pnrIcs"));
        newPnr.setPnrCrs(this.generatePnrCode("pnrCrs"));

        // 复制原PNR的基本信息
        newPnr.setPnrStatus(originalPnr.getPnrStatus());
        newPnr.setCreateTime(new Date());
        newPnr.setMaxIndex(0);
        newPnr.setInterlink(originalPnr.getInterlink());

        // 设置创建者信息
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        if (userInfo != null) {
            newPnr.setCreateSiId(userInfo.getSiId());
            newPnr.setCreateOfficeNo(userInfo.getMnjxOffice().getOfficeNo());
        }

        return newPnr;
    }

    /**
     * 生成PNR编码
     */
    private String generatePnrCode(String fieldName) {
        String pnrCode;
        boolean exists;

        do {
            pnrCode = this.generateRandomPnrCode();
            long count;
            if ("pnrIcs".equals(fieldName)) {
                count = iMnjxPnrService.lambdaQuery()
                        .eq(MnjxPnr::getPnrIcs, pnrCode)
                        .count();
            } else {
                count = iMnjxPnrService.lambdaQuery()
                        .eq(MnjxPnr::getPnrCrs, pnrCode)
                        .count();
            }
            exists = count > 0;
        } while (exists);

        return pnrCode;
    }

    /**
     * 生成随机PNR编码
     */
    private String generateRandomPnrCode() {
        String letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String alphanumeric = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

        StringBuilder code = new StringBuilder();
        // 前2位字母
        for (int i = 0; i < 2; i++) {
            code.append(letters.charAt((int) (Math.random() * letters.length())));
        }
        // 后4位字母数字混合
        for (int i = 0; i < 4; i++) {
            code.append(alphanumeric.charAt((int) (Math.random() * alphanumeric.length())));
        }

        return code.toString();
    }

    /**
     * 复制PNR级别数据到新PNR
     */
    private PnrData copyPnrLevelData(PnrData originalData, String newPnrId, Map<String, String> newAndOldSegIdMap) {
        PnrData newData = new PnrData();

        // 复制航段信息
        for (MnjxPnrSeg originalSeg : originalData.pnrSegList) {
            MnjxPnrSeg newSeg = new MnjxPnrSeg();
            BeanUtil.copyProperties(originalSeg, newSeg);
            newSeg.setPnrSegId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newSeg.setPnrId(newPnrId);
            newData.pnrSegList.add(newSeg);
            newAndOldSegIdMap.put(newSeg.getPnrSegId(), originalSeg.getPnrSegId());
        }

        // 复制PNR级别联系方式
        for (MnjxPnrCt originalCt : originalData.pnrCtList) {
            MnjxPnrCt newCt = new MnjxPnrCt();
            BeanUtil.copyProperties(originalCt, newCt);
            newCt.setPnrCtId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newCt.setPnrId(newPnrId);
            newData.pnrCtList.add(newCt);
        }

        // 复制出票时限
        for (MnjxPnrTk originalTk : originalData.pnrTkList) {
            MnjxPnrTk newTk = new MnjxPnrTk();
            BeanUtil.copyProperties(originalTk, newTk);
            newTk.setPnrTkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newTk.setPnrId(newPnrId);
            newData.pnrTkList.add(newTk);
        }

        // 复制PNR级别FC
        for (MnjxPnrFc originalFc : originalData.pnrFcList) {
            MnjxPnrFc newFc = new MnjxPnrFc();
            BeanUtil.copyProperties(originalFc, newFc);
            newFc.setPnrFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newFc.setPnrId(newPnrId);
            newData.pnrFcList.add(newFc);
        }

        // 复制PNR级别OSI
        for (MnjxPnrOsi originalOsi : originalData.pnrOsiList) {
            MnjxPnrOsi newOsi = new MnjxPnrOsi();
            BeanUtil.copyProperties(originalOsi, newOsi);
            newOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newOsi.setPnrId(newPnrId);
            newData.pnrOsiList.add(newOsi);
        }

        // 复制PNR级别备注
        for (MnjxPnrRmk originalRmk : originalData.pnrRmkList) {
            MnjxPnrRmk newRmk = new MnjxPnrRmk();
            BeanUtil.copyProperties(originalRmk, newRmk);
            newRmk.setPnrRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newRmk.setPnrId(newPnrId);
            newData.pnrRmkList.add(newRmk);
        }

        // 复制PNR级别TC
        for (MnjxPnrTc originalTc : originalData.pnrTcList) {
            MnjxPnrTc newTc = new MnjxPnrTc();
            BeanUtil.copyProperties(originalTc, newTc);
            newTc.setPnrTcId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newTc.setPnrId(newPnrId);
            newData.pnrTcList.add(newTc);
        }

        // 复制PNR级别FN
        for (MnjxPnrFn originalFn : originalData.pnrFnList) {
            MnjxPnrFn newFn = new MnjxPnrFn();
            BeanUtil.copyProperties(originalFn, newFn);
            newFn.setPnrFnId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newFn.setPnrId(newPnrId);
            newData.pnrFnList.add(newFn);
        }

        // 复制PNR级别EI
        for (MnjxPnrEi originalEi : originalData.pnrEiList) {
            MnjxPnrEi newEi = new MnjxPnrEi();
            BeanUtil.copyProperties(originalEi, newEi);
            newEi.setPnrEiId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newEi.setPnrId(newPnrId);
            newData.pnrEiList.add(newEi);
        }

        // 复制PNR级别FP
        for (MnjxPnrFp originalFp : originalData.pnrFpList) {
            MnjxPnrFp newFp = new MnjxPnrFp();
            BeanUtil.copyProperties(originalFp, newFp);
            newFp.setPnrFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newFp.setPnrId(newPnrId);
            newData.pnrFpList.add(newFp);
        }

        return newData;
    }

    /**
     * 分离旅客数据到新PNR
     */
    private void splitPassengerData(PnrData originalData, PnrData newData, List<MnjxPnrNm> splitPassengers, String newPnrId, Map<String, String> newAndOldSegIdMap) {
        // 获取要分离的旅客ID列表
        List<String> splitPassengerIds = splitPassengers.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());

        // 分离旅客信息
        for (MnjxPnrNm splitPassenger : splitPassengers) {
            splitPassenger.setPnrId(newPnrId);
            newData.pnrNmList.add(splitPassenger);
        }

        // 分离旅客级别联系方式
        List<MnjxNmCt> splitNmCt = originalData.nmCtList.stream()
                .filter(ct -> splitPassengerIds.contains(ct.getPnrNmId()))
                .collect(Collectors.toList());
        for (MnjxNmCt originalCt : splitNmCt) {
            MnjxNmCt newCt = new MnjxNmCt();
            BeanUtil.copyProperties(originalCt, newCt);
            newCt.setPnrCtId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newData.nmCtList.add(originalCt);
        }

        // 分离旅客级别FC
        List<MnjxNmFc> splitNmFc = originalData.nmFcList.stream()
                .filter(fc -> splitPassengerIds.contains(fc.getPnrNmId()))
                .collect(Collectors.toList());
        for (MnjxNmFc originalFc : splitNmFc) {
            MnjxNmFc newFc = new MnjxNmFc();
            BeanUtil.copyProperties(originalFc, newFc);
            newFc.setNmFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newData.nmFcList.add(originalFc);
        }

        // 分离旅客特服
        List<MnjxNmSsr> splitNmSsr = originalData.nmSsrList.stream()
                .filter(ssr -> splitPassengerIds.contains(ssr.getPnrNmId()))
                .collect(Collectors.toList());
        for (MnjxNmSsr originalSsr : splitNmSsr) {
            MnjxNmSsr newSsr = new MnjxNmSsr();
            BeanUtil.copyProperties(originalSsr, newSsr);
            newSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newData.nmSsrList.add(originalSsr);
        }

        // 分离旅客级别OSI
        List<MnjxNmOsi> splitNmOsi = originalData.nmOsiList.stream()
                .filter(osi -> splitPassengerIds.contains(osi.getPnrNmId()))
                .collect(Collectors.toList());
        for (MnjxNmOsi originalOsi : splitNmOsi) {
            MnjxNmOsi newOsi = new MnjxNmOsi();
            BeanUtil.copyProperties(originalOsi, newOsi);
            newOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newData.nmOsiList.add(originalOsi);
        }

        // 分离旅客级别备注
        List<MnjxNmRmk> splitNmRmk = originalData.nmRmkList.stream()
                .filter(rmk -> splitPassengerIds.contains(rmk.getPnrNmId()))
                .collect(Collectors.toList());
        for (MnjxNmRmk originalRmk : splitNmRmk) {
            MnjxNmRmk newRmk = new MnjxNmRmk();
            BeanUtil.copyProperties(originalRmk, newRmk);
            newRmk.setNmRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newData.nmRmkList.add(originalRmk);
        }

        // 分离旅客级别TC
        List<MnjxNmTc> splitNmTc = originalData.nmTcList.stream()
                .filter(tc -> splitPassengerIds.contains(tc.getPnrNmId()))
                .collect(Collectors.toList());
        for (MnjxNmTc originalTc : splitNmTc) {
            MnjxNmTc newTc = new MnjxNmTc();
            BeanUtil.copyProperties(originalTc, newTc);
            newTc.setNmTcId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newData.nmTcList.add(originalTc);
        }

        // 分离旅客级别FN
        List<MnjxNmFn> splitNmFn = originalData.nmFnList.stream()
                .filter(fn -> splitPassengerIds.contains(fn.getPnrNmId()))
                .collect(Collectors.toList());
        for (MnjxNmFn originalFn : splitNmFn) {
            MnjxNmFn newFn = new MnjxNmFn();
            BeanUtil.copyProperties(originalFn, newFn);
            newFn.setNmFnId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newData.nmFnList.add(originalFn);
        }

        // 分离旅客级别EI
        List<MnjxNmEi> splitNmEi = originalData.nmEiList.stream()
                .filter(ei -> splitPassengerIds.contains(ei.getPnrNmId()))
                .collect(Collectors.toList());
        for (MnjxNmEi originalEi : splitNmEi) {
            MnjxNmEi newEi = new MnjxNmEi();
            BeanUtil.copyProperties(originalEi, newEi);
            newEi.setNmEiId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newData.nmEiList.add(originalEi);
        }

        // 分离旅客级别OI
        List<MnjxNmOi> splitNmOi = originalData.nmOiList.stream()
                .filter(oi -> splitPassengerIds.contains(oi.getPnrNmId()))
                .collect(Collectors.toList());
        for (MnjxNmOi originalOi : splitNmOi) {
            MnjxNmOi newOi = new MnjxNmOi();
            BeanUtil.copyProperties(originalOi, newOi);
            newOi.setNmOiId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newData.nmOiList.add(originalOi);
        }

        // 分离婴儿信息
        List<MnjxNmXn> splitNmXn = originalData.nmXnList.stream()
                .filter(xn -> splitPassengerIds.contains(xn.getPnrNmId()))
                .collect(Collectors.toList());
        for (MnjxNmXn originalXn : splitNmXn) {
            MnjxNmXn newXn = new MnjxNmXn();
            BeanUtil.copyProperties(originalXn, newXn);
            newXn.setNmXnId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newData.nmXnList.add(originalXn);
        }
        List<String> splitXnIdList = splitNmXn.stream()
                .map(MnjxNmXn::getNmXnId)
                .collect(Collectors.toList());

        // 分离旅客级别FP
        List<MnjxNmFp> splitNmFp = originalData.nmFpList.stream()
                .filter(fp -> splitPassengerIds.contains(fp.getPnrNmId()))
                .collect(Collectors.toList());
        for (MnjxNmFp originalFp : splitNmFp) {
            MnjxNmFp newFp = new MnjxNmFp();
            BeanUtil.copyProperties(originalFp, newFp);
            newFp.setNmFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
            newData.nmFpList.add(originalFp);
        }

        // 分离旅客TN
        List<MnjxPnrNmTn> splitPnrTn = originalData.pnrTnList.stream()
                .filter(tn -> splitPassengerIds.contains(tn.getPnrNmId())
                        || (CollUtil.isNotEmpty(splitNmXn) && splitXnIdList.contains(tn.getNmXnId())))
                .collect(Collectors.toList());
        newData.pnrTnList.addAll(splitPnrTn);
        List<String> splitTnIdList = splitPnrTn.stream()
                .map(MnjxPnrNmTn::getTnId)
                .collect(Collectors.toList());

        // 分离旅客ticket，需要同时更新票对应航段的id
        List<MnjxPnrNmTicket> splitPnrTicket = originalData.pnrTicketList.stream()
                .filter(ticket -> splitPnrTn.stream().anyMatch(tn -> tn.getTnId().equals(ticket.getPnrNmTnId())))
                .collect(Collectors.toList());
        for (MnjxPnrNmTicket nmTicket : splitPnrTicket) {
            List<MnjxPnrSeg> pnrSegList = newData.pnrSegList;
            for (MnjxPnrSeg seg : pnrSegList) {
                String newSegId = seg.getPnrSegId();
                if (newAndOldSegIdMap.containsKey(newSegId)) {
                    String oldSegId = newAndOldSegIdMap.get(newSegId);
                    if (oldSegId.equals(nmTicket.getS1Id())) {
                        nmTicket.setS1Id(newSegId);
                    } else if (oldSegId.equals(nmTicket.getS2Id())) {
                        nmTicket.setS2Id(newSegId);
                    }
                }
            }
            newData.pnrTicketList.add(nmTicket);
        }

        // 从原PNR中移除分离的旅客数据
        this.removePassengerDataFromOriginal(originalData, splitPassengerIds, splitXnIdList, splitTnIdList);
    }

    /**
     * 获取新的旅客ID
     */
    private String getNewPassengerId(List<MnjxPnrNm> newPassengers, String originalPassengerId, List<MnjxPnrNm> splitPassengers) {
        // 根据原旅客ID找到对应的分离旅客
        MnjxPnrNm originalPassenger = splitPassengers.stream()
                .filter(p -> p.getPnrNmId().equals(originalPassengerId))
                .findFirst()
                .orElse(null);

        if (originalPassenger == null) {
            return null;
        }

        // 根据psgIndex找到新的旅客ID
        return newPassengers.stream()
                .filter(p -> p.getPsgIndex().equals(originalPassenger.getPsgIndex()))
                .map(MnjxPnrNm::getPnrNmId)
                .findFirst()
                .orElse(null);
    }

    /**
     * 从原PNR中移除分离的旅客数据
     */
    private void removePassengerDataFromOriginal(PnrData originalData, List<String> splitPassengerIds, List<String> splitXnIdList, List<String> splitTnIdList) {
        // 移除旅客信息
        originalData.pnrNmList.removeIf(nm -> splitPassengerIds.contains(nm.getPnrNmId()));

        // 移除旅客级别数据
        originalData.nmCtList.removeIf(ct -> splitPassengerIds.contains(ct.getPnrNmId()));
        originalData.nmFcList.removeIf(fc -> splitPassengerIds.contains(fc.getPnrNmId()));
        originalData.nmSsrList.removeIf(ssr -> splitPassengerIds.contains(ssr.getPnrNmId()));
        originalData.nmOsiList.removeIf(osi -> splitPassengerIds.contains(osi.getPnrNmId()));
        originalData.nmRmkList.removeIf(rmk -> splitPassengerIds.contains(rmk.getPnrNmId()));
        originalData.nmTcList.removeIf(tc -> splitPassengerIds.contains(tc.getPnrNmId()));
        originalData.nmFnList.removeIf(fn -> splitPassengerIds.contains(fn.getPnrNmId()));
        originalData.nmEiList.removeIf(ei -> splitPassengerIds.contains(ei.getPnrNmId()));
        originalData.nmOiList.removeIf(oi -> splitPassengerIds.contains(oi.getPnrNmId()));
        originalData.nmXnList.removeIf(xn -> splitPassengerIds.contains(xn.getPnrNmId()));
        originalData.nmFpList.removeIf(fp -> splitPassengerIds.contains(fp.getPnrNmId()));
        originalData.pnrTnList.removeIf(tn -> splitPassengerIds.contains(tn.getPnrNmId()) || (CollUtil.isNotEmpty(splitXnIdList) && splitXnIdList.contains(tn.getNmXnId())));
        originalData.pnrTicketList.removeIf(ticket -> CollUtil.isNotEmpty(splitTnIdList) && splitTnIdList.contains(ticket.getPnrNmTnId()));
    }

    /**
     * 更新航段座位数量
     */
    private void updateSegmentSeatNumbers(List<MnjxPnrSeg> originalSegments, List<MnjxPnrSeg> newSegments, int splitPassengerCount) {
        // 更新原PNR航段座位数量（减去分离的旅客数量）
        for (MnjxPnrSeg originalSeg : originalSegments) {
            if ("SA".equals(originalSeg.getPnrSegType())) {
                continue;
            }
            int newSeatNumber = originalSeg.getSeatNumber() - splitPassengerCount;
            originalSeg.setSeatNumber(newSeatNumber);

            // 更新inputValue中的行动代码后的数字
            if (StrUtil.isNotEmpty(originalSeg.getInputValue())) {
                String inputValue = originalSeg.getInputValue();
                String actionCode = originalSeg.getActionCode();
                if (StrUtil.isNotEmpty(actionCode)) {
                    // 替换行动代码后的数字
                    String pattern = actionCode + "\\d+";
                    String replacement = actionCode + newSeatNumber;
                    inputValue = inputValue.replaceAll(pattern, replacement);
                    originalSeg.setInputValue(inputValue);
                }
            }
        }

        // 更新新PNR航段座位数量（设置为分离的旅客数量）
        for (MnjxPnrSeg newSeg : newSegments) {
            if ("SA".equals(newSeg.getPnrSegType())) {
                continue;
            }
            newSeg.setSeatNumber(splitPassengerCount);

            // 更新inputValue中的行动代码后的数字
            if (StrUtil.isNotEmpty(newSeg.getInputValue())) {
                String inputValue = newSeg.getInputValue();
                String actionCode = newSeg.getActionCode();
                if (StrUtil.isNotEmpty(actionCode)) {
                    // 替换行动代码后的数字
                    String pattern = actionCode + "\\d+";
                    String replacement = actionCode + splitPassengerCount;
                    inputValue = inputValue.replaceAll(pattern, replacement);
                    newSeg.setInputValue(inputValue);
                }
            }
        }
    }

    /**
     * 重新排序新PNR并批量保存
     */
    private void reorderAndSaveNewPnr(MnjxPnr newPnr, PnrData newPnrData) throws SguiResultException {
        List<MnjxPnrRecord> pnrRecordList = new ArrayList<>();

        // 调用reorderPnrIndexes重新排序
        iBookPnrService.reorderPnrIndexes(newPnr, newPnrData.pnrNmList, newPnrData.pnrSegList,
                newPnrData.pnrCtList, newPnrData.nmCtList, newPnrData.pnrTkList,
                newPnrData.pnrFcList, newPnrData.nmFcList, newPnrData.nmSsrList,
                newPnrData.pnrOsiList, newPnrData.nmOsiList, newPnrData.pnrRmkList,
                newPnrData.nmRmkList, newPnrData.pnrTcList, newPnrData.nmTcList,
                newPnrData.pnrFnList, newPnrData.nmFnList, newPnrData.pnrEiList,
                newPnrData.nmEiList, newPnrData.nmOiList, newPnrData.pnrTnList,
                newPnrData.nmXnList, newPnrData.pnrFpList, newPnrData.nmFpList, pnrRecordList);

        // 批量保存新PNR数据
        this.batchSaveNewPnrData(newPnr, newPnrData, pnrRecordList);

        // 创建封口记录
        // 生成新的封口编号
        String newAtNo = iSguiCommonService.generateNewAtNo(newPnr.getPnrId());
        MnjxPnrAt pnrAt = new MnjxPnrAt();
        pnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrAt.setPnrId(newPnr.getPnrId());
        pnrAt.setAtNo(newAtNo);

        // 获取当前用户信息
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        if (userInfo != null) {
            pnrAt.setAtSiId(userInfo.getSiId());
        }

        pnrAt.setAtDateTime(new Date());

        // 保存封口记录
        iMnjxPnrAtService.save(pnrAt);
    }

    /**
     * 重新排序原PNR并批量更新
     */
    private void reorderAndUpdateOriginalPnr(MnjxPnr originalPnr, PnrData originalPnrData) throws SguiResultException {
        List<MnjxPnrRecord> pnrRecordList = new ArrayList<>();

        // 调用reorderPnrIndexes重新排序
        iBookPnrService.reorderPnrIndexes(originalPnr, originalPnrData.pnrNmList, originalPnrData.pnrSegList,
                originalPnrData.pnrCtList, originalPnrData.nmCtList, originalPnrData.pnrTkList,
                originalPnrData.pnrFcList, originalPnrData.nmFcList, originalPnrData.nmSsrList,
                originalPnrData.pnrOsiList, originalPnrData.nmOsiList, originalPnrData.pnrRmkList,
                originalPnrData.nmRmkList, originalPnrData.pnrTcList, originalPnrData.nmTcList,
                originalPnrData.pnrFnList, originalPnrData.nmFnList, originalPnrData.pnrEiList,
                originalPnrData.nmEiList, originalPnrData.nmOiList, originalPnrData.pnrTnList,
                originalPnrData.nmXnList, originalPnrData.pnrFpList, originalPnrData.nmFpList, pnrRecordList);

        // 批量更新原PNR数据
        this.batchUpdateOriginalPnrData(originalPnr, originalPnrData);

        // 创建封口记录
        // 生成新的封口编号
        String newAtNo = iSguiCommonService.generateNewAtNo(originalPnr.getPnrId());
        MnjxPnrAt pnrAt = new MnjxPnrAt();
        pnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrAt.setPnrId(originalPnr.getPnrId());
        pnrAt.setAtNo(newAtNo);

        // 获取当前用户信息
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        if (userInfo != null) {
            pnrAt.setAtSiId(userInfo.getSiId());
        }

        pnrAt.setAtDateTime(new Date());

        // 保存封口记录
        iMnjxPnrAtService.save(pnrAt);

        // 历史记录，先删除以前changeMark为空的，再批量添加这次封口所有已排好序的记录
        iMnjxPnrRecordService.lambdaUpdate()
                .eq(MnjxPnrRecord::getPnrId, originalPnr.getPnrId())
                .isNull(MnjxPnrRecord::getChangeMark)
                .remove();
        iMnjxPnrRecordService.saveBatch(pnrRecordList);
    }

    /**
     * 构建返回结果
     */
    private SplitPnrByPassengerVo buildResponse(String originalPnrNo, String newPnrNo) {
        SplitPnrByPassengerVo vo = new SplitPnrByPassengerVo();

        SplitPnrByPassengerVo.OrderInfo originalOrder = new SplitPnrByPassengerVo.OrderInfo();
        originalOrder.setOrderId(null);
        originalOrder.setPassengerRecordLocator(originalPnrNo);
        vo.setOrder(originalOrder);

        SplitPnrByPassengerVo.OrderInfo splitedOrder = new SplitPnrByPassengerVo.OrderInfo();
        splitedOrder.setOrderId(null);
        splitedOrder.setPassengerRecordLocator(newPnrNo);
        vo.setSplitedOrder(splitedOrder);

        return vo;
    }

    /**
     * 复制对象属性（排除ID字段）
     */
    private void copyProperties(Object source, Object target) {
        BeanUtil.copyProperties(source, target);
        try {
            java.lang.reflect.Field[] sourceFields = source.getClass().getDeclaredFields();
            java.lang.reflect.Field[] targetFields = target.getClass().getDeclaredFields();

            for (java.lang.reflect.Field sourceField : sourceFields) {
                sourceField.setAccessible(true);
                String fieldName = sourceField.getName();

                // 跳过ID字段和静态字段
                if (fieldName.toLowerCase().endsWith("id") ||
                        java.lang.reflect.Modifier.isStatic(sourceField.getModifiers()) ||
                        java.lang.reflect.Modifier.isFinal(sourceField.getModifiers())) {
                    continue;
                }

                for (java.lang.reflect.Field targetField : targetFields) {
                    if (targetField.getName().equals(fieldName)) {
                        targetField.setAccessible(true);
                        Object value = sourceField.get(source);
                        targetField.set(target, value);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("复制对象属性失败", e);
        }
    }

    /**
     * 批量保存新PNR数据
     */
    private void batchSaveNewPnrData(MnjxPnr newPnr, PnrData newPnrData, List<MnjxPnrRecord> pnrRecordList) {
        // 保存PNR基本信息
        iMnjxPnrService.save(newPnr);

        // 更新分离出来的旅客信息
        if (CollUtil.isNotEmpty(newPnrData.pnrNmList)) {
            iMnjxPnrNmService.updateBatchById(newPnrData.pnrNmList);
        }

        // 保存航段信息
        if (CollUtil.isNotEmpty(newPnrData.pnrSegList)) {
            iMnjxPnrSegService.saveBatch(newPnrData.pnrSegList);
        }

        // 保存PNR级别联系方式
        if (CollUtil.isNotEmpty(newPnrData.pnrCtList)) {
            iMnjxPnrCtService.saveBatch(newPnrData.pnrCtList);
        }

        // 保存旅客级别联系方式
        if (CollUtil.isNotEmpty(newPnrData.nmCtList)) {
            iMnjxNmCtService.updateBatchById(newPnrData.nmCtList);
        }

        // 保存出票时限
        if (CollUtil.isNotEmpty(newPnrData.pnrTkList)) {
            iMnjxPnrTkService.saveBatch(newPnrData.pnrTkList);
        }

        // 保存PNR级别FC
        if (CollUtil.isNotEmpty(newPnrData.pnrFcList)) {
            iMnjxPnrFcService.saveBatch(newPnrData.pnrFcList);
        }

        // 保存旅客级别FC
        if (CollUtil.isNotEmpty(newPnrData.nmFcList)) {
            iMnjxNmFcService.updateBatchById(newPnrData.nmFcList);
        }

        // 保存旅客特服
        if (CollUtil.isNotEmpty(newPnrData.nmSsrList)) {
            iMnjxNmSsrService.updateBatchById(newPnrData.nmSsrList);
        }

        // 保存PNR级别OSI
        if (CollUtil.isNotEmpty(newPnrData.pnrOsiList)) {
            iMnjxPnrOsiService.saveBatch(newPnrData.pnrOsiList);
        }

        // 保存旅客级别OSI
        if (CollUtil.isNotEmpty(newPnrData.nmOsiList)) {
            iMnjxNmOsiService.updateBatchById(newPnrData.nmOsiList);
        }

        // 保存PNR级别备注
        if (CollUtil.isNotEmpty(newPnrData.pnrRmkList)) {
            iMnjxPnrRmkService.saveBatch(newPnrData.pnrRmkList);
        }

        // 保存旅客级别备注
        if (CollUtil.isNotEmpty(newPnrData.nmRmkList)) {
            iMnjxNmRmkService.updateBatchById(newPnrData.nmRmkList);
        }

        // 保存PNR级别TC
        if (CollUtil.isNotEmpty(newPnrData.pnrTcList)) {
            iMnjxPnrTcService.saveBatch(newPnrData.pnrTcList);
        }

        // 保存旅客级别TC
        if (CollUtil.isNotEmpty(newPnrData.nmTcList)) {
            iMnjxNmTcService.updateBatchById(newPnrData.nmTcList);
        }

        // 保存PNR级别FN
        if (CollUtil.isNotEmpty(newPnrData.pnrFnList)) {
            iMnjxPnrFnService.saveBatch(newPnrData.pnrFnList);
        }

        // 保存旅客级别FN
        if (CollUtil.isNotEmpty(newPnrData.nmFnList)) {
            iMnjxNmFnService.updateBatchById(newPnrData.nmFnList);
        }

        // 保存PNR级别EI
        if (CollUtil.isNotEmpty(newPnrData.pnrEiList)) {
            iMnjxPnrEiService.saveBatch(newPnrData.pnrEiList);
        }

        // 保存旅客级别EI
        if (CollUtil.isNotEmpty(newPnrData.nmEiList)) {
            iMnjxNmEiService.updateBatchById(newPnrData.nmEiList);
        }

        // 保存旅客级别OI
        if (CollUtil.isNotEmpty(newPnrData.nmOiList)) {
            iMnjxNmOiService.updateBatchById(newPnrData.nmOiList);
        }

        // 保存TN
        if (CollUtil.isNotEmpty(newPnrData.pnrTnList)) {
            iMnjxPnrNmTnService.updateBatchById(newPnrData.pnrTnList);
        }

        // 保存TICKET
        if (CollUtil.isNotEmpty(newPnrData.pnrTicketList)) {
            iMnjxPnrNmTicketService.updateBatchById(newPnrData.pnrTicketList);
        }

        // 保存婴儿信息
        if (CollUtil.isNotEmpty(newPnrData.nmXnList)) {
            iMnjxNmXnService.updateBatchById(newPnrData.nmXnList);
        }

        // 保存PNR级别FP
        if (CollUtil.isNotEmpty(newPnrData.pnrFpList)) {
            iMnjxPnrFpService.saveBatch(newPnrData.pnrFpList);
        }

        // 保存旅客级别FP
        if (CollUtil.isNotEmpty(newPnrData.nmFpList)) {
            iMnjxNmFpService.updateBatchById(newPnrData.nmFpList);
        }

        // 保存PNR记录
        if (CollUtil.isNotEmpty(pnrRecordList)) {
            iMnjxPnrRecordService.saveBatch(pnrRecordList);
        }
    }

    /**
     * 批量更新原PNR数据
     */
    private void batchUpdateOriginalPnrData(MnjxPnr originalPnr, PnrData originalPnrData) {
        // 更新PNR基本信息
        iMnjxPnrService.updateById(originalPnr);

        // 更新旅客信息
        if (CollUtil.isNotEmpty(originalPnrData.pnrNmList)) {
            iMnjxPnrNmService.updateBatchById(originalPnrData.pnrNmList);
        }

        // 更新航段信息
        if (CollUtil.isNotEmpty(originalPnrData.pnrSegList)) {
            iMnjxPnrSegService.updateBatchById(originalPnrData.pnrSegList);
        }

        // 保存PNR级别联系方式
        if (CollUtil.isNotEmpty(originalPnrData.pnrCtList)) {
            iMnjxPnrCtService.updateBatchById(originalPnrData.pnrCtList);
        }

        // 保存旅客级别联系方式
        if (CollUtil.isNotEmpty(originalPnrData.nmCtList)) {
            iMnjxNmCtService.updateBatchById(originalPnrData.nmCtList);
        }

        // 保存出票时限
        if (CollUtil.isNotEmpty(originalPnrData.pnrTkList)) {
            iMnjxPnrTkService.updateBatchById(originalPnrData.pnrTkList);
        }

        // 保存PNR级别FC
        if (CollUtil.isNotEmpty(originalPnrData.pnrFcList)) {
            iMnjxPnrFcService.updateBatchById(originalPnrData.pnrFcList);
        }

        // 保存旅客级别FC
        if (CollUtil.isNotEmpty(originalPnrData.nmFcList)) {
            iMnjxNmFcService.updateBatchById(originalPnrData.nmFcList);
        }

        // 保存旅客特服
        if (CollUtil.isNotEmpty(originalPnrData.nmSsrList)) {
            iMnjxNmSsrService.updateBatchById(originalPnrData.nmSsrList);
        }

        // 保存PNR级别OSI
        if (CollUtil.isNotEmpty(originalPnrData.pnrOsiList)) {
            iMnjxPnrOsiService.updateBatchById(originalPnrData.pnrOsiList);
        }

        // 保存旅客级别OSI
        if (CollUtil.isNotEmpty(originalPnrData.nmOsiList)) {
            iMnjxNmOsiService.updateBatchById(originalPnrData.nmOsiList);
        }

        // 保存PNR级别备注
        if (CollUtil.isNotEmpty(originalPnrData.pnrRmkList)) {
            iMnjxPnrRmkService.updateBatchById(originalPnrData.pnrRmkList);
        }

        // 保存旅客级别备注
        if (CollUtil.isNotEmpty(originalPnrData.nmRmkList)) {
            iMnjxNmRmkService.updateBatchById(originalPnrData.nmRmkList);
        }

        // 保存PNR级别TC
        if (CollUtil.isNotEmpty(originalPnrData.pnrTcList)) {
            iMnjxPnrTcService.updateBatchById(originalPnrData.pnrTcList);
        }

        // 保存旅客级别TC
        if (CollUtil.isNotEmpty(originalPnrData.nmTcList)) {
            iMnjxNmTcService.updateBatchById(originalPnrData.nmTcList);
        }

        // 保存PNR级别FN
        if (CollUtil.isNotEmpty(originalPnrData.pnrFnList)) {
            iMnjxPnrFnService.updateBatchById(originalPnrData.pnrFnList);
        }

        // 保存旅客级别FN
        if (CollUtil.isNotEmpty(originalPnrData.nmFnList)) {
            iMnjxNmFnService.updateBatchById(originalPnrData.nmFnList);
        }

        // 保存PNR级别EI
        if (CollUtil.isNotEmpty(originalPnrData.pnrEiList)) {
            iMnjxPnrEiService.updateBatchById(originalPnrData.pnrEiList);
        }

        // 保存旅客级别EI
        if (CollUtil.isNotEmpty(originalPnrData.nmEiList)) {
            iMnjxNmEiService.updateBatchById(originalPnrData.nmEiList);
        }

        // 保存旅客级别OI
        if (CollUtil.isNotEmpty(originalPnrData.nmOiList)) {
            iMnjxNmOiService.updateBatchById(originalPnrData.nmOiList);
        }

        // 保存PNR级别TN
        if (CollUtil.isNotEmpty(originalPnrData.pnrTnList)) {
            iMnjxPnrNmTnService.updateBatchById(originalPnrData.pnrTnList);
        }

        // 保存婴儿信息
        if (CollUtil.isNotEmpty(originalPnrData.nmXnList)) {
            iMnjxNmXnService.updateBatchById(originalPnrData.nmXnList);
        }

        // 保存PNR级别FP
        if (CollUtil.isNotEmpty(originalPnrData.pnrFpList)) {
            iMnjxPnrFpService.updateBatchById(originalPnrData.pnrFpList);
        }

        // 保存旅客级别FP
        if (CollUtil.isNotEmpty(originalPnrData.nmFpList)) {
            iMnjxNmFpService.updateBatchById(originalPnrData.nmFpList);
        }
    }

    /**
     * PNR数据容器类
     */
    private static class PnrData {
        List<MnjxPnrNm> pnrNmList = new ArrayList<>();
        List<MnjxPnrSeg> pnrSegList = new ArrayList<>();
        List<MnjxPnrCt> pnrCtList = new ArrayList<>();
        List<MnjxNmCt> nmCtList = new ArrayList<>();
        List<MnjxPnrTk> pnrTkList = new ArrayList<>();
        List<MnjxPnrFc> pnrFcList = new ArrayList<>();
        List<MnjxNmFc> nmFcList = new ArrayList<>();
        List<MnjxNmSsr> nmSsrList = new ArrayList<>();
        List<MnjxPnrOsi> pnrOsiList = new ArrayList<>();
        List<MnjxNmOsi> nmOsiList = new ArrayList<>();
        List<MnjxPnrRmk> pnrRmkList = new ArrayList<>();
        List<MnjxNmRmk> nmRmkList = new ArrayList<>();
        List<MnjxPnrTc> pnrTcList = new ArrayList<>();
        List<MnjxNmTc> nmTcList = new ArrayList<>();
        List<MnjxPnrFn> pnrFnList = new ArrayList<>();
        List<MnjxNmFn> nmFnList = new ArrayList<>();
        List<MnjxPnrEi> pnrEiList = new ArrayList<>();
        List<MnjxNmEi> nmEiList = new ArrayList<>();
        List<MnjxNmOi> nmOiList = new ArrayList<>();
        List<MnjxPnrNmTn> pnrTnList = new ArrayList<>();
        List<MnjxPnrNmTicket> pnrTicketList = new ArrayList<>();
        List<MnjxNmXn> nmXnList = new ArrayList<>();
        List<MnjxPnrFp> pnrFpList = new ArrayList<>();
        List<MnjxNmFp> nmFpList = new ArrayList<>();
    }
}
