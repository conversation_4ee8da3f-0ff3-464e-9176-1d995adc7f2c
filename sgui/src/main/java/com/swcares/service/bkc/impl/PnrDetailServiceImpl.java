package com.swcares.service.bkc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.QueryPnrDetailDto;
import com.swcares.obj.vo.QueryPnrDetailVo;
import com.swcares.service.*;
import com.swcares.service.bkc.IPnrDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * PNR通用服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/12 15:46
 */
@Slf4j
@Service
public class PnrDetailServiceImpl implements IPnrDetailService {

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPnrRmkService iMnjxPnrRmkService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxCityService iMnjxCityService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxPnrCtService iMnjxPnrCtService;

    @Resource
    private IMnjxPnrOsiService iMnjxPnrOsiService;

    @Resource
    private IMnjxPnrEiService iMnjxPnrEiService;

    @Resource
    private IMnjxPnrFcService iMnjxPnrFcService;

    @Resource
    private IMnjxPnrFpService iMnjxPnrFpService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxPnrTkService iMnjxPnrTkService;

    @Resource
    private IMnjxPnrTcService iMnjxPnrTcService;

    @Resource
    private IMnjxPnrGnService iMnjxPnrGnService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private IMnjxNmCtService iMnjxNmCtService;

    @Resource
    private IMnjxNmEiService iMnjxNmEiService;

    @Resource
    private IMnjxNmFcService iMnjxNmFcService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxNmFpService iMnjxNmFpService;

    @Resource
    private IMnjxNmOiService iMnjxNmOiService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrNmUmService iMnjxPnrNmUmService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Override
    public QueryPnrDetailVo queryPnrDetail(QueryPnrDetailDto dto) throws SguiResultException {
        // 参数校验
        if (StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 根据PNR编号查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();
        if (ObjectUtil.isEmpty(pnr)) {
            throw new SguiResultException("未找到对应的PNR信息");
        }

        // 构建返回对象
        QueryPnrDetailVo vo = new QueryPnrDetailVo();
        if ("DEL".equals(pnr.getPnrStatus())) {
            vo.setFlight(Collections.emptyList());
            vo.setPassengers(Collections.emptyList());
            vo.setOriginalLineContents(Collections.emptyList());
            vo.setPnrCanceled(true);
            vo.setInternational(false);
            vo.setGovernment(false);
            vo.setSalvageChange(false);
            vo.setGroup(false);
            QueryPnrDetailVo.QueuePnrDetail queuePnrDetail = new QueryPnrDetailVo.QueuePnrDetail();
            queuePnrDetail.setCanceled(true);
            queuePnrDetail.setGroup(false);
            vo.setQueuePnrDetail(queuePnrDetail);
            vo.setHistoryLineContents(this.buildHistoryLineContents(pnr));
            return vo;
        }
        vo.setPnrNo(dto.getPnrNo());

        // 查询PNR相关数据
        // 1. 查询航段信息
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        // 2. 查询旅客信息
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrNm::getPsgIndex)
                .list();

        // 3. 查询联系信息
        List<MnjxPnrCt> pnrCtList = iMnjxPnrCtService.lambdaQuery()
                .eq(MnjxPnrCt::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrCt::getPnrIndex)
                .list();

        // 4. 查询OSI信息
        List<MnjxPnrOsi> pnrOsiList = iMnjxPnrOsiService.lambdaQuery()
                .eq(MnjxPnrOsi::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrOsi::getPnrIndex)
                .list();

        // 5. 查询备注信息
        List<MnjxPnrRmk> pnrRmkList = iMnjxPnrRmkService.lambdaQuery()
                .eq(MnjxPnrRmk::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrRmk::getPnrIndex)
                .list();

        // 6. 查询签注信息
        List<MnjxPnrEi> pnrEiList = iMnjxPnrEiService.lambdaQuery()
                .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrEi::getPnrIndex)
                .list();

        // 7. 查询运价组信息
        List<MnjxPnrFc> pnrFcList = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrFc::getPnrIndex)
                .list();

        List<MnjxPnrFp> pnrFpList = iMnjxPnrFpService.lambdaQuery()
                .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrFp::getPnrIndex)
                .list();

        List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrFn::getPnrIndex)
                .list();

        // 8. 查询出票时限信息
        List<MnjxPnrTk> pnrTkList = iMnjxPnrTkService.lambdaQuery()
                .eq(MnjxPnrTk::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrTk::getPnrIndex)
                .list();

        // 9. 查询TC信息
        List<MnjxPnrTc> pnrTcList = iMnjxPnrTcService.lambdaQuery()
                .eq(MnjxPnrTc::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrTc::getPnrIndex)
                .list();

        // 10. 查询团队信息
        List<MnjxPnrGn> pnrGnList = iMnjxPnrGnService.lambdaQuery()
                .eq(MnjxPnrGn::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrGn::getPnrIndex)
                .list();

        // 12. 查询封口历史记录信息
        List<MnjxPnrAt> pnrAtList = iMnjxPnrAtService.lambdaQuery()
                .eq(MnjxPnrAt::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrAt::getAtDateTime)
                .orderByAsc(MnjxPnrAt::getAtNo)
                .list();

        List<String> pnrNmIds = pnrNmList.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList());

        // 18. 查询旅客婴儿信息
        List<MnjxNmXn> nmXnList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrNmIds)) {
            nmXnList = iMnjxNmXnService.lambdaQuery()
                    .in(MnjxNmXn::getPnrNmId, pnrNmIds)
                    .list();
        }

        // 13. 查询旅客出票信息
        List<MnjxPnrNmTn> pnrNmTnList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrNmIds)) {
            pnrNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                    .in(MnjxPnrNmTn::getPnrNmId, pnrNmIds)
                    .list();
            if (CollUtil.isNotEmpty(nmXnList)) {
                pnrNmTnList.addAll(iMnjxPnrNmTnService.lambdaQuery()
                        .in(MnjxPnrNmTn::getNmXnId, nmXnList.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList()))
                        .list());
            }
        }

        // 14. 查询票号信息
        List<String> pnrNmTnIds = pnrNmTnList.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList());
        List<MnjxPnrNmTicket> pnrNmTicketList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrNmTnIds)) {
            pnrNmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                    .in(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTnIds)
                    .list();
        }

        // 15. 查询旅客SSR信息
        List<MnjxNmSsr> nmSsrList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrNmIds)) {
            nmSsrList = iMnjxNmSsrService.lambdaQuery()
                    .in(MnjxNmSsr::getPnrNmId, pnrNmIds)
                    .list();
        }

        // 16. 查询旅客OSI信息
        List<MnjxNmOsi> nmOsiList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrNmIds)) {
            nmOsiList = iMnjxNmOsiService.lambdaQuery()
                    .in(MnjxNmOsi::getPnrNmId, pnrNmIds)
                    .list();
        }

        // 17. 查询旅客备注信息
        List<MnjxNmRmk> nmRmkList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrNmIds)) {
            nmRmkList = iMnjxNmRmkService.lambdaQuery()
                    .in(MnjxNmRmk::getPnrNmId, pnrNmIds)
                    .list();
        }

        // 19. 查询旅客无陪信息
        List<MnjxPnrNmUm> nmUmList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrNmIds)) {
            nmUmList = iMnjxPnrNmUmService.lambdaQuery()
                    .in(MnjxPnrNmUm::getPnrNmId, pnrNmIds)
                    .list();
        }

        // 20. 查询旅客运价信息
        List<MnjxNmFn> nmFnList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrNmIds)) {
            nmFnList = iMnjxNmFnService.lambdaQuery()
                    .in(MnjxNmFn::getPnrNmId, pnrNmIds)
                    .list();
        }

        // 21. 查询旅客支付信息
        List<MnjxNmFp> nmFpList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrNmIds)) {
            nmFpList = iMnjxNmFpService.lambdaQuery()
                    .in(MnjxNmFp::getPnrNmId, pnrNmIds)
                    .list();
        }

        // 22. 查询旅客FC运价信息
        List<MnjxNmFc> nmFcList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrNmIds)) {
            nmFcList = iMnjxNmFcService.lambdaQuery()
                    .in(MnjxNmFc::getPnrNmId, pnrNmIds)
                    .list();
        }

        // 22. 查询旅客签证信息
        List<MnjxNmEi> nmEiList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrNmIds)) {
            nmEiList = iMnjxNmEiService.lambdaQuery()
                    .in(MnjxNmEi::getPnrNmId, pnrNmIds)
                    .list();
        }

        // 检查是否有出票记录，设置出票状态
        if (CollUtil.isNotEmpty(pnrNmTnList)) {
            // 是否已出完票
            if (pnrNmList.size() + nmXnList.size() != pnrNmTnList.size()) {
                vo.setIssueStatus("1"); // 部分出票
            } else {
                vo.setIssueStatus("2"); // 已出票
            }
        } else {
            vo.setIssueStatus("0"); // 未出票
        }

        // 填充基本信息区域
        this.fillBasicArea(vo, pnr, pnrOsiList, pnrRmkList, pnrTkList, pnrSegList.get(0), pnrCtList);

        // 填充航班信息区域
        this.fillFlightInfo(vo, pnrSegList);

        // 填充旅客信息区域
        this.fillPassengerInfo(vo, pnrNmList, pnrSegList, nmSsrList, nmOsiList, pnrNmTnList, pnrNmTicketList, nmXnList, nmUmList);

        // 填充价格信息区域
        this.fillPricingArea(vo, pnrFnList, pnrFpList, pnrFcList, pnrEiList, pnrNmList, nmFnList, nmFpList, nmFcList, nmEiList);

        // 填充备注信息区域
        this.fillRemarkArea(vo, pnrRmkList, pnrOsiList, nmRmkList, nmOsiList, pnrNmList);

        // 构建原始行内容
        List<QueryPnrDetailVo.LineContent> originalLineContents = this.buildOriginalLineContents(
                pnr, pnrNmList, pnrSegList, pnrCtList, pnrOsiList, pnrRmkList, pnrFnList, pnrFpList, pnrTkList,
                pnrFcList, pnrEiList, pnrGnList, pnrTcList, pnrNmTnList, pnrNmTicketList,
                nmSsrList, nmOsiList, nmRmkList, nmFcList, nmFnList, nmFpList, nmEiList, nmXnList);
        vo.setOriginalLineContents(originalLineContents);
        vo.setHistoryLineContents(Collections.emptyList());

        // 设置其他信息
        vo.setPnrCanceled("DEL".equals(pnr.getPnrStatus()));
        vo.setInternational(false); // 默认为国内航班
        vo.setGroup(CollUtil.isNotEmpty(pnrGnList)); // 根据是否有团队信息判断
        vo.setGovernment(false); // 默认不是政府航班
        vo.setSalvageChange(false); // 默认不是救援变更

        // 设置队列PNR详情
        vo.setQueuePnrDetail(this.buildQueuePnrDetail(pnr, pnrNmList, pnrSegList));

        return vo;
    }

    /**
     * 填充基本信息区域
     *
     * @param vo         返回对象
     * @param pnr        PNR信息
     * @param pnrOsiList OSI信息列表
     * @param pnrRmkList 备注信息列表
     */
    private void fillBasicArea(QueryPnrDetailVo vo, MnjxPnr pnr, List<MnjxPnrOsi> pnrOsiList, List<MnjxPnrRmk> pnrRmkList,
                               List<MnjxPnrTk> pnrTkList, MnjxPnrSeg firstSeg, List<MnjxPnrCt> pnrCtList) {
        QueryPnrDetailVo.BasicArea basicArea = new QueryPnrDetailVo.BasicArea();

        // 设置责任组
        basicArea.setResponsibilityGroup(pnr.getCreateOfficeNo());

        // 设置出票限制
        MnjxPnrTk tk = pnrTkList.get(0);
        if ("TL".equals(tk.getPnrTkType())) {
            basicArea.setIssueLimitCrs(tk.getPlanEtdzDate() + " " + tk.getPlanEtdzTime().substring(0, 2) + ":" + tk.getPlanEtdzTime().substring(2));
            basicArea.setIssueLimitType(tk.getPnrTkType());
            basicArea.setIssueLimitOffice(tk.getEtdzOffice());
        } else {
            basicArea.setIssueLimitCrs("T");
            basicArea.setIssueLimitType("T");
        }

        basicArea.setFirstDepartureTime(firstSeg.getFlightDate() + " " + firstSeg.getEstimateOff().substring(0, 2) + ":" + firstSeg.getEstimateOff().substring(2));

        // 设置创建时间
        if (pnr.getCreateTime() != null) {
            basicArea.setCreateDate(DateUtil.format(pnr.getCreateTime(), "yyyy-MM-dd"));
            basicArea.setCreateTime(DateUtil.format(pnr.getCreateTime(), "HHmm"));
        }

        // 初始化列表
        basicArea.setAdtks(new ArrayList<>());
        basicArea.setCts(new ArrayList<>());
        basicArea.setContactPhones(new ArrayList<>());
        basicArea.setContactAirlines(new ArrayList<>());
        basicArea.setContact(new ArrayList<>());
        basicArea.setContactLineIndex(new ArrayList<>());
        basicArea.setAuthorizes(new ArrayList<>());
        basicArea.setSatHeader(new ArrayList<>());

        // 如果已出票，设置SAT头信息
        if ("2".equals(vo.getIssueStatus())) {
            basicArea.getSatHeader().add("**ELECTRONIC TICKET PNR**");
        }

        // 设置默认值
        basicArea.setIssueWarn(false);
        basicArea.setIssueAbnormal(false);

        // 查询并设置航空公司PNR
        if (CollUtil.isNotEmpty(pnrRmkList)) {
            for (MnjxPnrRmk rmk : pnrRmkList) {
                if (rmk.getRmkInfo() != null && rmk.getRmkInfo().contains("/") && ReUtil.isMatch("RMK [A-Z0-9]{2}/[A-Z0-9]{6}", rmk.getRmkInfo())) {
                    basicArea.setAirlinePnr(rmk.getRmkInfo().substring(4));
                    break;
                }
            }
        }

        // 查询并设置联系信息
        basicArea.setFirstContactLineIndex("-1");
        basicArea.setLastContactCTCELineIndex("-1");
        if (CollUtil.isNotEmpty(pnrOsiList)) {
            for (MnjxPnrOsi osi : pnrOsiList) {
                if ("CTCT".equals(osi.getPnrOsiType())) {
                    // 处理电话联系信息
                    String phoneNumber = extractPhoneNumber(osi.getPnrOsiInfo());
                    if (StrUtil.isNotEmpty(phoneNumber)) {
                        basicArea.getContactPhones().add(phoneNumber);
                        basicArea.getContactAirlines().add(osi.getAirlineCode());

                        QueryPnrDetailVo.Contact contact = new QueryPnrDetailVo.Contact();
                        contact.setAirline(osi.getAirlineCode());
                        contact.setPhoneNumber(phoneNumber);
                        basicArea.getContact().add(contact);

                        basicArea.getContactLineIndex().add(String.valueOf(osi.getPnrIndex()));
                        if (basicArea.getFirstContactLineIndex() == null) {
                            basicArea.setFirstContactLineIndex(String.valueOf(osi.getPnrIndex()));
                        }
                    }
                }
                else if ("CTCE".equals(osi.getPnrOsiType())) {
                    basicArea.setLastContactCTCELineIndex(String.valueOf(osi.getPnrIndex()));
                }
            }
        }
        if (CollUtil.isNotEmpty(pnrCtList)) {
            for (MnjxPnrCt pnrCt : pnrCtList) {
                // 获取pnrCt.getCtText()的数字部分
                String number = pnrCt.getCtText().replaceAll("\\D+", "");
                basicArea.getCts().add(number);
                basicArea.getContactLineIndex().add(String.valueOf(pnrCt.getPnrIndex()));
            }
        }

        // 查询并设置Office信息
        if (StrUtil.isNotEmpty(pnr.getCreateOfficeNo())) {
            MnjxOffice office = iMnjxOfficeService.lambdaQuery()
                    .eq(MnjxOffice::getOfficeNo, pnr.getCreateOfficeNo())
                    .one();
            if (office != null) {
                // 查询代理人信息
                MnjxAgent agent = iMnjxAgentService.lambdaQuery()
                        .eq(MnjxAgent::getAgentId, office.getOrgId())
                        .one();
                if (agent != null) {
                    basicArea.setOfficeName(agent.getAgentContactCname());
                    basicArea.setOfficePhone(agent.getAgentContactPhone());
                    basicArea.setOfficeAddress(agent.getAgentContactAddress());
                }
            }
        }

        // 初始化Office授权
        QueryPnrDetailVo.OfficeAuth officeAuth = new QueryPnrDetailVo.OfficeAuth();
        officeAuth.setLineIndex(0);
        basicArea.setOfficeAuth(officeAuth);

        vo.setBasicArea(basicArea);
    }

    /**
     * 从输入值中提取电话号码
     *
     * @param input 输入值
     * @return 电话号码
     */
    private String extractPhoneNumber(String input) {
        if (StrUtil.isEmpty(input)) {
            return "";
        }

        // 假设格式为 "OSI XX CTCT1234567890"
        if (input.contains("CTCT")) {
            return input.substring(input.indexOf("CTCT") + 4).trim();
        }
        return "";
    }

    /**
     * 填充航班信息区域
     *
     * @param vo         返回对象
     * @param pnrSegList 航段信息列表
     */
    private void fillFlightInfo(QueryPnrDetailVo vo, List<MnjxPnrSeg> pnrSegList) {
        if (CollUtil.isEmpty(pnrSegList)) {
            vo.setFlight(new ArrayList<>());
            return;
        }

        // 按照出发地-目的地分组
        List<QueryPnrDetailVo.Flight> flightList = new ArrayList<>();

        for (MnjxPnrSeg pnrSeg : pnrSegList) {
            QueryPnrDetailVo.Flight flight = new QueryPnrDetailVo.Flight();

            // 设置出发城市和到达城市
            QueryPnrDetailVo.City departureCity = new QueryPnrDetailVo.City();
            departureCity.setCode(pnrSeg.getOrg());
            MnjxAirport depAirport = iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, pnrSeg.getOrg())
                    .one();
            if (depAirport != null) {
                departureCity.setName(depAirport.getAirportCname());
            } else {
                departureCity.setName(pnrSeg.getOrg());
            }
            flight.setDepartureCity(departureCity);

            // 到达城市
            QueryPnrDetailVo.City arriveCity = new QueryPnrDetailVo.City();
            arriveCity.setCode(pnrSeg.getDst());
            MnjxAirport arrAirport = iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, pnrSeg.getDst())
                    .one();
            if (arrAirport != null) {
                arriveCity.setName(arrAirport.getAirportCname());
            } else {
                arriveCity.setName(pnrSeg.getDst());
            }
            flight.setArriveCity(arriveCity);

            // 设置日期信息
            QueryPnrDetailVo.DateInfo dateInfo = new QueryPnrDetailVo.DateInfo();
            dateInfo.setDepartureDate(pnrSeg.getFlightDate());
            flight.setDate(dateInfo);

            // 设置其他属性
            flight.setOpenFlag(false);
            // 如果是SA航段（地面航段），设置notchPath为true
            flight.setNotchPath("SA".equals(pnrSeg.getPnrSegType()));
            flight.setKey(pnrSeg.getOrg() + pnrSeg.getDst() + pnrSeg.getFlightDate() + UUID.randomUUID().toString());

            // 处理航段信息
            List<QueryPnrDetailVo.Segment> segmentList = new ArrayList<>();
            QueryPnrDetailVo.Segment segment = new QueryPnrDetailVo.Segment();

            // 判断是否为SA航段（地面航段）
            boolean isSaSegment = "SA".equals(pnrSeg.getPnrSegType());

            // 设置时间信息（确保HH:mm格式）
            if (!isSaSegment && StrUtil.isNotEmpty(pnrSeg.getEstimateOff())) {
                segment.setDepartureTime(formatTimeToHHmm(pnrSeg.getEstimateOff()));
            }
            if (!isSaSegment && StrUtil.isNotEmpty(pnrSeg.getEstimateArr())) {
                segment.setArrivalTime(formatTimeToHHmm(pnrSeg.getEstimateArr()));
            }

            // 计算飞行时间（非SA航段）
            if (!isSaSegment && StrUtil.isNotEmpty(pnrSeg.getEstimateOff()) && StrUtil.isNotEmpty(pnrSeg.getEstimateArr())) {
                try {
                    // 先将时间格式化为HH:mm格式
                    String formattedDepTime = formatTimeToHHmm(pnrSeg.getEstimateOff());
                    String formattedArrTime = formatTimeToHHmm(pnrSeg.getEstimateArr());

                    Date depTime = DateUtil.parse(pnrSeg.getFlightDate() + " " + formattedDepTime, "yyyy-MM-dd HH:mm");
                    Date arrTime = DateUtil.parse(pnrSeg.getFlightDate() + " " + formattedArrTime, "yyyy-MM-dd HH:mm");

                    // 如果到达时间小于出发时间，说明跨天，需要调整
                    if (arrTime.before(depTime)) {
                        arrTime = DateUtil.offsetDay(arrTime, 1);
                    }

                    long diffMinutes = DateUtil.between(depTime, arrTime, DateUnit.MINUTE);
                    int hours = (int) (diffMinutes / 60);
                    int minutes = (int) (diffMinutes % 60);
                    segment.setFlightTime(String.format("%02d:%02d", hours, minutes));
                } catch (Exception e) {
                    log.error("计算飞行时间异常: {}", e.getMessage(), e);
                }
            }

            // 设置出发信息
            segment.setDepartureAirportCode(pnrSeg.getOrg());
            if (depAirport != null) {
                segment.setDepartureAirportCN(depAirport.getAirportCname());
            } else {
                segment.setDepartureAirportCN(pnrSeg.getOrg());
            }

            if (!isSaSegment) {
                segment.setDepartureDate(pnrSeg.getFlightDate() + "T" + formatTimeToHHmm(pnrSeg.getEstimateOff()));
                // 设置出发航站楼为T2
                segment.setDepartureTerminal("T2");
            } else {
                // SA航段不设置出发日期和时间
                segment.setDepartureDate("");
                // SA航段设置出发航站楼为--
                segment.setDepartureTerminal("--");
            }

            // 设置到达信息
            segment.setArrivalAirportCode(pnrSeg.getDst());
            if (arrAirport != null) {
                segment.setArrivalAirportCN(arrAirport.getAirportCname());
            } else {
                segment.setArrivalAirportCN(pnrSeg.getDst());
            }

            if (!isSaSegment) {
                segment.setArrivalDate(pnrSeg.getFlightDate() + "T" + formatTimeToHHmm(pnrSeg.getEstimateArr()));
                // 设置到达航站楼为T2
                segment.setArrivalTerminal("T2");
            } else {
                // SA航段不设置到达日期和时间
                segment.setArrivalDate("");
                // SA航段设置到达航站楼为--
                segment.setArrivalTerminal("--");
            }

            // 设置舱位信息
            segment.setSubCabin("");
            List<QueryPnrDetailVo.Cabin> cabins = new ArrayList<>();
            QueryPnrDetailVo.Cabin cabin = new QueryPnrDetailVo.Cabin();
            if (!isSaSegment) {
                cabin.setCabinName(pnrSeg.getSellCabin());
            } else {
                // SA航段不设置舱位
                cabin.setCabinName("");
            }
            cabins.add(cabin);
            segment.setCabins(cabins);

            // 设置航空公司信息
            QueryPnrDetailVo.Airlines airlines = new QueryPnrDetailVo.Airlines();

            if (!isSaSegment) {
                // 非SA航段处理
                String airlineCode = pnrSeg.getFlightNo().substring(0, 2);
                airlines.setAirCode(airlineCode);

                MnjxAirline airline = iMnjxAirlineService.lambdaQuery()
                        .eq(MnjxAirline::getAirlineCode, airlineCode)
                        .one();
                if (airline != null) {
                    airlines.setAirCN(airline.getAirlineShortName());
                } else {
                    airlines.setAirCN(airlineCode);
                }

                QueryPnrDetailVo.AviationDepartmentGeneral aviationDepartmentGeneral = new QueryPnrDetailVo.AviationDepartmentGeneral();
                aviationDepartmentGeneral.setAirlineCode(airlineCode);
                airlines.setAviationDepartmentGeneral(aviationDepartmentGeneral);
                airlines.setFlightNo(pnrSeg.getFlightNo().substring(2));

                // 处理共享航班信息
                if (StrUtil.isNotEmpty(pnrSeg.getCarrierFlight())) {
                    airlines.setIsShared(pnrSeg.getCarrierFlight());
                    String ocAirlineCode = pnrSeg.getCarrierFlight().substring(0, 2);
                    String ocFlightNumber = pnrSeg.getCarrierFlight().substring(2);
                    airlines.setOcAirline(ocAirlineCode);
                    airlines.setOcFlightNumber(ocFlightNumber);
                } else {
                    airlines.setIsShared(null);
                }
            } else {
                // SA航段处理
                airlines.setAirCode("");
                airlines.setAirCN("");

                QueryPnrDetailVo.AviationDepartmentGeneral aviationDepartmentGeneral = new QueryPnrDetailVo.AviationDepartmentGeneral();
                aviationDepartmentGeneral.setAirlineCode("CA"); // 默认设置为CA
                airlines.setAviationDepartmentGeneral(aviationDepartmentGeneral);

                // SA航段的航班号设置为ARNK
                airlines.setFlightNo("ARNK");
                airlines.setIsShared(null);
            }

            segment.setAirlines(airlines);

            // 设置其他信息
            segment.setActionCode(isSaSegment ? "" : pnrSeg.getActionCode());
            segment.setLineIndex(pnrSeg.getPnrIndex());
            segment.setSegmentType("1");
            segment.setIssue(false);

            segmentList.add(segment);
            flight.setSegments(segmentList);
            flightList.add(flight);
        }

        vo.setFlight(flightList);
    }

    /**
     * 格式化时间为HH:mm格式
     *
     * @param time 时间字符串
     * @return 格式化后的时间
     */
    private String formatTimeToHHmm(String time) {
        if (StrUtil.isEmpty(time)) {
            return "";
        }

        // 如果已经是HH:mm格式，直接返回
        if (time.contains(":") && time.length() == 5) {
            return time;
        }

        // 如果是四位数字，转换为HH:mm格式
        if (time.length() == 4 && time.matches("\\d{4}")) {
            return time.substring(0, 2) + ":" + time.substring(2, 4);
        }

        // 如果是三位数字，转换为HH:mm格式，如"930"转换为"09:30"
        if (time.length() == 3 && time.matches("\\d{3}")) {
            return "0" + time.charAt(0) + ":" + time.substring(1, 3);
        }

        // 如果是两位数字，转换为HH:mm格式，如"45"转换为"00:45"
        if (time.length() == 2 && time.matches("\\d{2}")) {
            return "00:" + time;
        }

        // 如果是一位数字，转换为HH:mm格式，如"5"转换为"00:05"
        if (time.length() == 1 && time.matches("\\d")) {
            return "00:0" + time;
        }

        // 其他情况下尝试使用DateUtil解析
        try {
            Date date = DateUtil.parse(time);
            return DateUtil.format(date, "HH:mm");
        } catch (Exception e) {
            log.warn("无法解析时间格式: {}", time);
        }

        // 如果无法解析，返回原始字符串
        return time;
    }

    /**
     * 从SSR信息中提取身份证号码
     *
     * @param ssrInfo SSR信息
     * @return 身份证号码
     */
    private String extractFoidCardNumber(String ssrInfo) {
        if (StrUtil.isEmpty(ssrInfo)) {
            return "";
        }

        // 假设格式为 "SSR FOID XX HK1 NI123456789012345678/P1" "SSR FOID XX HK1 UU123456789012345678/P1"
        if (ssrInfo.contains(" NI")) {
            int startIndex = ssrInfo.indexOf(" NI") + 3;
            int endIndex = ssrInfo.indexOf("/", startIndex);
            if (endIndex == -1) {
                endIndex = ssrInfo.length();
            }
            return ssrInfo.substring(startIndex, endIndex).trim();
        } else if (ssrInfo.contains(" UU")) {
            int startIndex = ssrInfo.indexOf(" UU") + 3;
            int endIndex = ssrInfo.indexOf("/", startIndex);
            if (endIndex == -1) {
                endIndex = ssrInfo.length();
            }
            return ssrInfo.substring(startIndex, endIndex).trim();
        }

        return "";
    }

    /**
     * 填充旅客信息区域
     *
     * @param vo              返回对象
     * @param pnrNmList       旅客信息列表
     * @param pnrSegList      航段信息列表
     * @param nmSsrList       旅客SSR信息列表
     * @param nmOsiList       旅客OSI信息列表
     * @param pnrNmTnList     旅客出票信息列表
     * @param pnrNmTicketList 旅客票号信息列表
     * @param nmXnList        旅客婴儿信息列表
     * @param nmUmList        旅客无陪信息列表
     */
    private void fillPassengerInfo(QueryPnrDetailVo vo, List<MnjxPnrNm> pnrNmList, List<MnjxPnrSeg> pnrSegList,
                                   List<MnjxNmSsr> nmSsrList, List<MnjxNmOsi> nmOsiList, List<MnjxPnrNmTn> pnrNmTnList,
                                   List<MnjxPnrNmTicket> pnrNmTicketList, List<MnjxNmXn> nmXnList, List<MnjxPnrNmUm> nmUmList) {
        if (CollUtil.isEmpty(pnrNmList)) {
            vo.setPassengers(new ArrayList<>());
            return;
        }

        List<QueryPnrDetailVo.Passenger> passengerList = new ArrayList<>();

        // 处理每个旅客
        for (MnjxPnrNm pnrNm : pnrNmList) {
            List<MnjxPnrNmTn> currentNmTnList = pnrNmTnList.stream()
                    .filter(t -> pnrNm.getPnrNmId().equals(t.getPnrNmId()))
                    .collect(Collectors.toList());
            List<MnjxPnrNmTicket> currentNmTicketList = pnrNmTicketList.stream()
                    .filter(t -> currentNmTnList.stream().anyMatch(n -> n.getTnId().equals(t.getPnrNmTnId())))
                    .collect(Collectors.toList());
            QueryPnrDetailVo.Passenger passenger = new QueryPnrDetailVo.Passenger();

            // 设置基本信息
            passenger.setFullName(pnrNm.getName());
            passenger.setPassengerNameInPnr(pnrNm.getName());
            passenger.setLineIndex(pnrNm.getPnrIndex());
            passenger.setPassengerId(pnrNm.getPsgIndex());

            // 设置旅客类型
            String passengerType = "ADT";
            switch (pnrNm.getPsgType()) {
                case "1":
                    passengerType = "CHD";
                    break;
                case "3":
                    passengerType = "INF";
                    break;
                default:
                    passengerType = "ADT";
                    break;
            }
            passenger.setPassengerType(passengerType);
            passenger.setSpecialPassengerType(passengerType);

            List<MnjxNmSsr> currentPassengerSsrList = nmSsrList.stream()
                    .filter(s -> pnrNm.getPnrNmId().equals(s.getPnrNmId()))
                    .collect(Collectors.toList());

            // 处理证件信息
            this.processPassengerDocument(passenger, currentPassengerSsrList, pnrNm.getPnrNmId(), pnrNm);

            // 处理联系信息
            this.processPassengerContact(passenger, nmOsiList, nmSsrList, pnrNm.getPnrNmId());

            // 处理航段信息
            this.processPassengerSegments(passenger, pnrSegList, pnrNm, currentNmTnList, currentNmTicketList, currentPassengerSsrList);

            // 处理婴儿信息
            this.processInfantInfo(passenger, nmXnList, pnrNm.getPnrNmId(), pnrNm.getPnrId(), currentPassengerSsrList);

            // 处理无陪儿童信息
            this.processUnaccompaniedMinor(passenger, nmUmList, pnrNm.getPnrNmId());

            passengerList.add(passenger);
        }

        vo.setPassengers(passengerList);
    }

    /**
     * 处理旅客证件信息
     *
     * @param passenger 旅客信息
     * @param nmSsrList SSR信息列表
     * @param pnrNmId   旅客ID
     */
    private void processPassengerDocument(QueryPnrDetailVo.Passenger passenger, List<MnjxNmSsr> nmSsrList, String pnrNmId, MnjxPnrNm pnrNm) {
        if (CollUtil.isEmpty(nmSsrList)) {
            return;
        }

        // 初始化证件信息对象
        QueryPnrDetailVo.Document document = new QueryPnrDetailVo.Document();

        // 查找FOID信息
        List<MnjxNmSsr> foidSsrList = nmSsrList.stream()
                .filter(ssr -> "FOID".equals(ssr.getSsrType()) && pnrNmId.equals(ssr.getPnrNmId()))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(foidSsrList)) {
            passenger.setDocument(document);
            MnjxNmSsr foidSsr = foidSsrList.get(0);
            if (foidSsr.getInputValue().contains(" UU")) {
                document.setDocumentType("UU");
            } else {
                document.setDocumentType("NI");
            }
            document.setSsrType("FOID");
            String idCardNumber = this.extractFoidCardNumber(foidSsr.getInputValue());
            document.setIdCardNumber(idCardNumber);

            // 如果是身份证，尝试提取生日和性别
            if (idCardNumber.length() == 18 && foidSsr.getInputValue().contains(" NI")) {
                try {
                    // 提取生日 (格式: YYYYMMDD)
                    String birthDateStr = idCardNumber.substring(6, 14);
                    String birthYear = birthDateStr.substring(0, 4);
                    String birthMonth = birthDateStr.substring(4, 6);
                    String birthDay = birthDateStr.substring(6, 8);
                    String formattedBirthday = birthYear + "-" + birthMonth + "-" + birthDay;
                    document.setBirthday(formattedBirthday);
                    passenger.setBirthday(formattedBirthday);

                    // 提取性别 (倒数第二位: 奇数为男，偶数为女)
                    int sexCode = Integer.parseInt(idCardNumber.substring(16, 17));
                    document.setGender(sexCode % 2 == 1 ? "M" : "F");
                } catch (Exception e) {
                    log.warn("解析身份证信息异常: {}", e.getMessage());
                }
            } else {
                if (StrUtil.isNotEmpty(pnrNm.getSex())) {
                    document.setGender(pnrNm.getSex());
                } else {
                    document.setGender("U");
                }
            }
        } else {
            List<MnjxNmSsr> docsSsrList = nmSsrList.stream()
                    .filter(ssr -> "DOCS".equals(ssr.getSsrType()) && pnrNmId.equals(ssr.getPnrNmId()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(docsSsrList)) {
                // SSR DOCS CA HK1 P/CN/32322110/CN/10MAY20/M/22MAY22/ZHANG/SAN/H/P1
                passenger.setDocument(document);
                MnjxNmSsr docsSsr = docsSsrList.get(0);
                document.setSsrType("DOCS");
                String docsReg = "SSR DOCS [A-Z]{2} HK1 (P|IP|M|G|A|C|I|F|T)/([A-Z]{2})/([a-zA-Z\\d]+)/([A-Z]{2})/(\\d{2}[A-Z]{3}\\d{2})/([MF]{1})/(\\d{2}[A-Z]{3}\\d{2})/([a-zA-Z\u4E00-\u9FA5\\s]{2,})/([a-zA-Z\u4E00-\u9FA5\\s]{2,})/H/P\\d+";
                List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(docsReg), docsSsr.getInputValue());

                document.setDocumentType(allGroups.get(1));
                document.setPassengerNationality(allGroups.get(2));
                document.setIdCardNumber(allGroups.get(3));
                document.setBirthday(DateUtils.birthDayCom2ymd(allGroups.get(5)));
                passenger.setBirthday(DateUtils.birthDayCom2ymd(allGroups.get(5)));
                document.setGender(allGroups.get(6));
                document.setVisaExpiryDate(DateUtils.com2ymd(allGroups.get(7)));
                document.setFirstName(allGroups.get(8));
                document.setLastName(allGroups.get(9));
                document.setDocsName(document.getFirstName() + "/" + document.getLastName());
            }
        }

        List<MnjxNmSsr> chldSsrList = nmSsrList.stream()
                .filter(ssr -> "CHLD".equals(ssr.getSsrType()) && pnrNmId.equals(ssr.getPnrNmId()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(chldSsrList)) {
            // SSR CHLD CZ HK1 08MAR17/P1
            MnjxNmSsr chldNmSsr = chldSsrList.get(0);
            String inputValue = chldNmSsr.getInputValue();
            String birthday = DateUtils.birthDayCom2ymd(inputValue.split("/")[0].split(" ")[4]);
            passenger.setBirthday(birthday);
            document.setBirthday(birthday);

            List<String> ssrChdList = new ArrayList<>();
            ssrChdList.add(birthday);
            passenger.setChildSsrTypeTextInPnr(ssrChdList);

            QueryPnrDetailVo.ChldSsr chldSsr = new QueryPnrDetailVo.ChldSsr();
            chldSsr.setText(chldNmSsr.getInputValue());
            chldSsr.setLineIndex(chldNmSsr.getPnrIndex().toString());
            chldSsr.setAirline(chldNmSsr.getAirlineCode());
            List<QueryPnrDetailVo.ChldSsr> ssrCHD = new ArrayList<>();
            ssrCHD.add(chldSsr);
            passenger.setSsrCHD(ssrCHD);
        }
    }

    /**
     * 处理旅客联系信息
     *
     * @param passenger 旅客信息
     * @param nmOsiList OSI信息列表
     * @param pnrNmId   旅客ID
     */
    private void processPassengerContact(QueryPnrDetailVo.Passenger passenger, List<MnjxNmOsi> nmOsiList, List<MnjxNmSsr> ssrList, String pnrNmId) {
        if (CollUtil.isNotEmpty(nmOsiList)) {
            // 查找OSI CTCM信息 (手机号)
            List<MnjxNmOsi> ctcmOsiList = nmOsiList.stream()
                    .filter(osi -> pnrNmId.equals(osi.getPnrNmId()) && "CTCM".equals(osi.getPnrOsiType()))
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(ctcmOsiList)) {
                MnjxNmOsi ctcmOsi = ctcmOsiList.get(0);
                String phoneInfo = ctcmOsi.getPnrOsiInfo();
                if (phoneInfo.contains("CTCM")) {
                    String phoneNumber = phoneInfo.substring(phoneInfo.indexOf("CTCM") + 4).split("/P")[0].trim();
                    passenger.setOsiCtcm(phoneNumber);
                }
            }
        }

        if (CollUtil.isNotEmpty(ssrList)) {
            // 查找SSR CTCM信息
            List<MnjxNmSsr> ctcmSsrList = ssrList.stream()
                    .filter(ssr -> pnrNmId.equals(ssr.getPnrNmId()) && "CTCM".equals(ssr.getSsrType()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(ctcmSsrList)) {
                MnjxNmSsr ctcmSsr = ctcmSsrList.get(0);
                String phoneNumber = ctcmSsr.getInputValue().substring(ctcmSsr.getInputValue().indexOf("HK1") + 3).split("/P")[0].trim();
                passenger.setSsrCtcm(phoneNumber);
            }
        }
    }

    /**
     * 处理旅客航段信息
     *
     * @param passenger       旅客信息
     * @param pnrSegList      航段信息列表
     * @param pnrNm           旅客基本信息
     * @param pnrNmTnList     旅客出票信息列表
     * @param pnrNmTicketList 旅客票号信息列表
     * @param nmSsrList       旅客SSR信息列表
     */
    private void processPassengerSegments(QueryPnrDetailVo.Passenger passenger, List<MnjxPnrSeg> pnrSegList,
                                          MnjxPnrNm pnrNm, List<MnjxPnrNmTn> pnrNmTnList, List<MnjxPnrNmTicket> pnrNmTicketList,
                                          List<MnjxNmSsr> nmSsrList) {
        if (CollUtil.isEmpty(pnrSegList)) {
            passenger.setSegments(new ArrayList<>());
            return;
        }

        List<QueryPnrDetailVo.PassengerSegment> segments = new ArrayList<>();

        for (MnjxPnrSeg pnrSeg : pnrSegList) {
            QueryPnrDetailVo.PassengerSegment segment = new QueryPnrDetailVo.PassengerSegment();

            // 设置基本信息
            segment.setActionCode(pnrSeg.getActionCode());
            segment.setOrigin(pnrSeg.getOrg());
            segment.setDestination(pnrSeg.getDst());
            segment.setArnkInd("SA".equals(pnrSeg.getPnrSegType()));
            segment.setOpenInd("OPEN".equals(pnrSeg.getFlightNo()));
            segment.setLineIndex(pnrSeg.getPnrIndex());
            segment.setTkne(new QueryPnrDetailVo.Tkne());

            // 设置航班信息
            if (segment.getArnkInd()) {
                // SA航段（地面航段）处理
                segment.setAirline("");
                segment.setFlightNumber("ARNK");
                segment.setClassId("");
                segment.setFlightDate(pnrSeg.getFlightDate());
                // SA航段不设置出发和到达时间
            } else {
                String airlineCode = pnrSeg.getFlightNo().substring(0, 2);
                String flightNumber = pnrSeg.getFlightNo().substring(2);
                segment.setAirline(airlineCode);
                segment.setFlightNumber(flightNumber);
                segment.setClassId(pnrSeg.getSellCabin());
                segment.setFlightDate(pnrSeg.getFlightDate());
                segment.setDepartureTime(this.formatTimeToHHmm(pnrSeg.getEstimateOff()));
                segment.setArrivalTime(this.formatTimeToHHmm(pnrSeg.getEstimateArr()));

                // 处理共享航班信息
                if (StrUtil.isNotEmpty(pnrSeg.getCarrierFlight())) {
                    String ocAirlineCode = pnrSeg.getCarrierFlight().substring(0, 2);
                    String ocFlightNumber = pnrSeg.getCarrierFlight().substring(2);
                    segment.setOcAirline(ocAirlineCode);
                    segment.setOcFlightNumber(ocFlightNumber);
                }
                // 设置票号信息
                if (CollUtil.isNotEmpty(pnrNmTicketList)) {
                    MnjxPnrNmTicket ticket = pnrNmTicketList.stream()
                            .filter(t -> StrUtil.equalsAny(pnrSeg.getPnrSegId(), t.getS1Id(), t.getS2Id()))
                            .collect(Collectors.toList())
                            .get(0);
                    segment.setTicketNumber(ticket.getTicketNo().substring(0, 3) + "-" + ticket.getTicketNo().substring(3));
                    segment.setTicketStatus(pnrSeg.getPnrSegId().equals(ticket.getS1Id()) ? ticket.getTicketStatus1() : ticket.getTicketStatus2());

                    // 从旅客的SSR信息中获取TKNE信息
                    if (CollUtil.isNotEmpty(nmSsrList)) {
                        // 查找TKNE类型的SSR信息
                        List<MnjxNmSsr> tkneSsrList = nmSsrList.stream()
                                .filter(ssr -> "TKNE".equals(ssr.getSsrType())
                                        && !ssr.getInputValue().contains(" INF")
                                        && pnrNm.getPnrNmId().equals(ssr.getPnrNmId())
                                        && ssr.getPnrSegNo() != null
                                        && ssr.getPnrSegNo() == pnrSeg.getPnrSegNo())
                                .collect(Collectors.toList());

                        if (CollUtil.isNotEmpty(tkneSsrList)) {
                            MnjxNmSsr tkneSsr = tkneSsrList.get(0);
                            // 设置TKNE信息
                            QueryPnrDetailVo.Tkne tkne = segment.getTkne();
                            tkne.setContent(tkneSsr.getSsrInfo());
                            tkne.setLineIndex(String.valueOf(tkneSsr.getPnrIndex()));
                        }
                    }
                }

                // 设置服务信息
                this.setService(segment, pnrSeg, nmSsrList);
            }

            segments.add(segment);
        }

        passenger.setSegments(segments);

        // 设置票号连接字符串
        if (CollUtil.isNotEmpty(pnrNmTnList) && CollUtil.isNotEmpty(pnrNmTicketList)) {
            List<String> ticketNumbers = new ArrayList<>();
            List<String> tnLineIndices = new ArrayList<>();
            List<String> tkneLineIndices = new ArrayList<>();

            for (MnjxPnrNmTn pnrNmTn : pnrNmTnList) {
                if (pnrNm.getPnrNmId().equals(pnrNmTn.getPnrNmId())) {
                    tnLineIndices.add(String.valueOf(pnrNmTn.getPnrIndex()));

                    for (MnjxPnrNmTicket ticket : pnrNmTicketList) {
                        if (pnrNmTn.getTnId().equals(ticket.getPnrNmTnId())) {
                            String ticketNumber = ticket.getTicketNo();
                            ticketNumbers.add(ticketNumber);
                            passenger.setTicketNumberJoin(ticketNumber.replace("-", ""));
                        }
                    }
                }
            }
            nmSsrList.stream()
                    .filter(s -> "TKNE".equals(s.getSsrType()) && !s.getInputValue().contains(" INF"))
                    .forEach(s -> tkneLineIndices.add(String.valueOf(s.getPnrIndex())));

            passenger.setTicketNumbersForTN(ticketNumbers);
            passenger.setTnLineIndex(tnLineIndices);
            passenger.setTkneLineIndex(tkneLineIndices);
        }

        // 设置国内/国际标识
        passenger.setAirlineType("D"); // 默认为国内
        passenger.setForeign(false);   // 默认为非外籍

        // 设置出票状态
        passenger.setIssueType(CollUtil.isNotEmpty(pnrNmTnList) ? "2" : "0");

        // 初始化其他列表
        passenger.setVipTexts(new ArrayList<>());
        passenger.setVipType("");
        passenger.setChildSsrTypeTextInPnr(new ArrayList<>());
        passenger.setSupplementaryIdentityInfoList(new ArrayList<>());
        passenger.setUnidentifiedTknes(new ArrayList<>());
    }

    private void setService(QueryPnrDetailVo.PassengerSegment segment, MnjxPnrSeg pnrSeg, List<MnjxNmSsr> nmSsrList) {
        List<QueryPnrDetailVo.PassengerSegmentService> services = new ArrayList<>();
        for (MnjxNmSsr ssr : nmSsrList) {
            if (StrUtil.equalsAny(ssr.getSsrType(), "INFT", "FOID", "DOCS", "TKNE")) {
                continue;
            }
            if (ssr.getPnrSegNo() == pnrSeg.getPnrSegNo()) {
                QueryPnrDetailVo.PassengerSegmentService service = new QueryPnrDetailVo.PassengerSegmentService();
                service.setSsrCode(ssr.getSsrType());
                service.setActionCode(ssr.getActionCode());
                service.setLineIndex(String.valueOf(ssr.getPnrIndex()));
                services.add(service);
            }
        }
        segment.setServices(services);
    }

    /**
     * 处理婴儿信息
     *
     * @param passenger 旅客信息
     * @param nmXnList  婴儿信息列表
     * @param pnrNmId   旅客ID
     */
    private void processInfantInfo(QueryPnrDetailVo.Passenger passenger, List<MnjxNmXn> nmXnList, String pnrNmId, String pnrId, List<MnjxNmSsr> ssrList) {
        if (CollUtil.isEmpty(nmXnList)) {
            passenger.setInfantDetail(null);
            return;
        }

        List<MnjxNmXn> infantList = nmXnList.stream()
                .filter(xn -> pnrNmId.equals(xn.getPnrNmId()))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(infantList)) {
            MnjxNmXn infant = infantList.get(0);
            MnjxNmSsr ssr = ssrList.stream()
                    .filter(s -> "INFT".equals(s.getSsrType()))
                    .collect(Collectors.toList())
                    .get(0);

            // 创建婴儿详情对象
            QueryPnrDetailVo.InfantDetail infantDetail = new QueryPnrDetailVo.InfantDetail();
            String split = ssr.getInputValue().split("/P")[0];
            String[] split1 = split.split(" ");
            String comBirthday = split1[split1.length - 1];
            infantDetail.setBirthday(DateUtils.com2ymd(comBirthday));
            infantDetail.setFullName(infant.getXnCname());

            // 如果婴儿名称包含"/"，则分割为姓和名
            if (infant.getXnCname() != null && infant.getXnCname().contains("/")) {
                String[] nameParts = infant.getXnCname().split("/");
                infantDetail.setLastName(nameParts[0]);
                infantDetail.setFirstName(nameParts.length > 1 ? nameParts[1] : "");
            } else {
                infantDetail.setLastName(infant.getXnCname());
                infantDetail.setFirstName("");
            }

            // 设置其他属性
            infantDetail.setChineseName(null); // 可能需要从其他地方获取中文名
            infantDetail.setPassengerNameInInft(infant.getXnCname());
            infantDetail.setPassengerNameInPnr(infant.getXnCname() + " INF(" + comBirthday.substring(2) + ")");
            infantDetail.setLineIndex(infant.getPnrIndex());
            infantDetail.setPassengerId(passenger.getPassengerId());

            List<MnjxPnrNmTn> xnTnList = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getNmXnId, infant.getNmXnId())
                    .list();

            // 初始化婴儿的航段信息
            infantDetail.setSegments(new ArrayList<>());

            // 查找婴儿的SSR INFT信息
            List<MnjxNmSsr> infantSsrList = iMnjxNmSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                    .eq(MnjxNmSsr::getSsrType, "INFT")
                    .list();

            // 如果有INFT特服信息，处理婴儿航段
            if (CollUtil.isNotEmpty(infantSsrList)) {
                List<QueryPnrDetailVo.InftInfo> inftInfoList = new ArrayList<>();

                for (MnjxNmSsr inftSsr : infantSsrList) {
                    // 根据航段编号查找对应的航段信息
                    MnjxPnrSeg pnrSeg = iMnjxPnrSegService.lambdaQuery()
                            .eq(MnjxPnrSeg::getPnrId, pnrId)
                            .eq(MnjxPnrSeg::getPnrSegNo, inftSsr.getPnrSegNo())
                            .one();

                    if (pnrSeg != null) {
                        // 添加婴儿航段信息
                        QueryPnrDetailVo.InftInfo inftInfo = new QueryPnrDetailVo.InftInfo();
                        inftInfo.setSeg(pnrSeg.getOrg() + "-" + pnrSeg.getDst());
                        inftInfo.setActionCode(inftSsr.getActionCode());
                        inftInfoList.add(inftInfo);

                        // 复制成人的航段信息到婴儿的航段信息
                        for (QueryPnrDetailVo.PassengerSegment adultSegment : passenger.getSegments()) {
                            if (adultSegment.getOrigin().equals(pnrSeg.getOrg()) && adultSegment.getDestination().equals(pnrSeg.getDst())) {
                                QueryPnrDetailVo.PassengerSegment infantSegment = new QueryPnrDetailVo.PassengerSegment();
                                // 设置基本信息
                                infantSegment.setActionCode(pnrSeg.getActionCode());
                                infantSegment.setOrigin(pnrSeg.getOrg());
                                infantSegment.setDestination(pnrSeg.getDst());
                                infantSegment.setArnkInd("SA".equals(pnrSeg.getPnrSegType()));
                                infantSegment.setOpenInd("OPEN".equals(pnrSeg.getFlightNo()));
                                infantSegment.setLineIndex(pnrSeg.getPnrIndex());
                                // 设置航班信息
                                if (infantSegment.getArnkInd()) {
                                    // SA航段（地面航段）处理
                                    infantSegment.setAirline("");
                                    infantSegment.setFlightNumber("ARNK");
                                    infantSegment.setClassId("");
                                    infantSegment.setFlightDate(pnrSeg.getFlightDate());
                                    // SA航段不设置出发和到达时间
                                } else {
                                    List<QueryPnrDetailVo.PassengerSegmentService> services = new ArrayList<>();
                                    infantSegment.setServices(services);

                                    if (CollUtil.isNotEmpty(xnTnList)) {
                                        List<MnjxPnrNmTicket> xnTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                                                .in(MnjxPnrNmTicket::getPnrNmTnId, xnTnList.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList()))
                                                .list();
                                        MnjxPnrNmTicket ticket = xnTicketList.stream()
                                                .filter(t -> StrUtil.equalsAny(pnrSeg.getPnrSegId(), t.getS1Id(), t.getS2Id()))
                                                .collect(Collectors.toList())
                                                .get(0);
                                        infantSegment.setTicketNumber(ticket.getTicketNo().substring(0, 3) + "-" + ticket.getTicketNo().substring(3)); // 婴儿票号可能需要单独查询
                                        infantSegment.setTicketStatus(pnrSeg.getPnrSegId().equals(ticket.getS1Id()) ? ticket.getTicketStatus1() : ticket.getTicketStatus2());
                                    }
                                    infantSegment.setAirline(adultSegment.getAirline());
                                    infantSegment.setFlightNumber(adultSegment.getFlightNumber());
                                    infantSegment.setClassId(adultSegment.getClassId());
                                    infantSegment.setSubClassId(adultSegment.getSubClassId());
                                    infantSegment.setFlightDate(adultSegment.getFlightDate());
                                    infantSegment.setDepartureTime(adultSegment.getDepartureTime());
                                    infantSegment.setArrivalTime(adultSegment.getArrivalTime());
                                    infantSegment.setOcAirline(adultSegment.getOcAirline());
                                    infantSegment.setOcFlightNumber(adultSegment.getOcFlightNumber());
                                    infantSegment.setLineIndex(pnrSeg.getPnrIndex());
                                    infantSegment.setSelectedFlag("");
                                    infantSegment.setTkne(new QueryPnrDetailVo.Tkne());
                                    ssrList.stream()
                                            .filter(s -> pnrSeg.getPnrSegNo().equals(s.getPnrSegNo()))
                                            .filter(s -> "TKNE".equals(s.getSsrType()) && s.getInputValue().contains(" INF"))
                                            .forEach(s -> {
                                                infantSegment.getTkne().setContent(s.getSsrInfo());
                                                infantSegment.getTkne().setLineIndex(String.valueOf(s.getPnrIndex()));
                                            });

                                    infantSegment.setHasInft(true);
                                    infantSegment.setInftText(inftSsr.getInputValue());
                                    infantSegment.setInftActionCode(inftSsr.getActionCode());
                                    infantSegment.setFrequentNumber(null);
                                    infantSegment.setTchb(false);
                                }
                                infantDetail.getSegments().add(infantSegment);
                            }
                        }
                    }
                }

                // 设置婴儿信息列表
                infantDetail.setInft(inftInfoList);
            }

            // 设置婴儿类型
            infantDetail.setPassengerType("INF");
            infantDetail.setSpecialPassengerType("INF");

            // 初始化其他属性
            infantDetail.setDocument(new QueryPnrDetailVo.Document());
            infantDetail.setDocumentPP(new QueryPnrDetailVo.Document());
            infantDetail.setDocaInfoR(new QueryPnrDetailVo.DocaInfo());
            infantDetail.setDocaInfoD(new QueryPnrDetailVo.DocaInfo());
            infantDetail.setTicketNumbersForTN(new ArrayList<>());
            infantDetail.setTnLineIndex(new ArrayList<>());
            infantDetail.setTkneLineIndex(new ArrayList<>());
            infantDetail.setTicketNumberJoin("");
            if (CollUtil.isNotEmpty(xnTnList)) {
                infantDetail.setIssueType("2");
                List<MnjxPnrNmTicket> xnTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                        .in(MnjxPnrNmTicket::getPnrNmTnId, xnTnList.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList()))
                        .list();
                infantDetail.getTicketNumbersForTN().addAll(xnTicketList.stream().map(t -> t.getTicketNo().substring(0, 3) + "0" + t.getTicketNo().substring(3)).collect(Collectors.toList()));
                infantDetail.getTnLineIndex().addAll(xnTnList.stream().map(t -> String.valueOf(t.getPnrIndex())).collect(Collectors.toList()));
                ssrList.stream()
                        .filter(s -> "TKNE".equals(s.getSsrType()) && s.getInputValue().contains(" INF"))
                        .forEach(s -> infantDetail.getTkneLineIndex().add(String.valueOf(s.getPnrIndex())));
            } else {
                infantDetail.setIssueType("0");
            }
            infantDetail.setUnidentifiedTknes(null);

            // 设置婴儿详情
            passenger.setInfantDetail(infantDetail);

            // 当有婴儿时，成人的旅客类型应该是 ADT
            passenger.setPassengerType("ADT");
            passenger.setSpecialPassengerType("ADT");
        } else {
            passenger.setInfantDetail(null);
        }
    }

    /**
     * 处理无陪儿童信息
     *
     * @param passenger 旅客信息
     * @param nmUmList  无陪儿童信息列表
     * @param pnrNmId   旅客ID
     */
    private void processUnaccompaniedMinor(QueryPnrDetailVo.Passenger passenger, List<MnjxPnrNmUm> nmUmList, String pnrNmId) {
        if (CollUtil.isEmpty(nmUmList)) {
            return;
        }

        List<MnjxPnrNmUm> umList = nmUmList.stream()
                .filter(um -> pnrNmId.equals(um.getPnrNmId()))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(umList)) {
            MnjxPnrNmUm um = umList.get(0);
            passenger.setUnMinor(true);

            // 尝试从年龄字段提取年龄
            if (ObjectUtil.isNotEmpty(um.getUmAge())) {
                try {
                    passenger.setUnMinorAge(um.getUmAge());
                } catch (NumberFormatException e) {
                    log.warn("解析无陪儿童年龄异常: {}", e.getMessage());
                }
            }
        } else {
            passenger.setUnMinor(false);
            passenger.setUnMinorAge(0);
        }
    }

    /**
     * 填充价格信息区域
     *
     * @param vo        返回对象
     * @param pnrFnList FN信息列表
     * @param pnrFpList FP信息列表
     * @param pnrFcList FC信息列表
     * @param pnrNmList 旅客信息列表
     * @param nmFnList  旅客个人运价信息列表
     * @param nmFpList  旅客个人支付信息列表
     * @param nmFcList  旅客个人运价计算信息列表
     */
    private void fillPricingArea(QueryPnrDetailVo vo, List<MnjxPnrFn> pnrFnList, List<MnjxPnrFp> pnrFpList,
                                 List<MnjxPnrFc> pnrFcList, List<MnjxPnrEi> pnrEiList, List<MnjxPnrNm> pnrNmList,
                                 List<MnjxNmFn> nmFnList, List<MnjxNmFp> nmFpList, List<MnjxNmFc> nmFcList, List<MnjxNmEi> nmEiList) {
        QueryPnrDetailVo.PricingArea pricingArea = new QueryPnrDetailVo.PricingArea();

        // 初始化列表
        pricingArea.setEndorsementInfos(new ArrayList<>());
        pricingArea.setFareBoxes(new ArrayList<>());
        pricingArea.setFareCaluations(new ArrayList<>());
        pricingArea.setTourCodes(new ArrayList<>());
        pricingArea.setFormOfPayments(new ArrayList<>());
        pricingArea.setOperateInfos(new ArrayList<>());
        pricingArea.setSvcInfos(new ArrayList<>());
        QueryPnrDetailVo.IssuePrice issuePrice = new QueryPnrDetailVo.IssuePrice();
        issuePrice.setIssuePriceItemList(new ArrayList<>());
        for (MnjxPnrNm pnrNm : pnrNmList) {
            QueryPnrDetailVo.IssuePriceItem issuePriceItem = new QueryPnrDetailVo.IssuePriceItem();
            issuePriceItem.setPassengerId(pnrNm.getPnrIndex());
            issuePriceItem.setInfant(false);
            issuePriceItem.setPrice("");
            issuePrice.getIssuePriceItemList().add(issuePriceItem);
        }
        pricingArea.setIssuePrice(issuePrice);

        // 判断是否使用统一运价或旅客个人运价
        if (CollUtil.isNotEmpty(pnrFnList)) {
            // 处理统一运价信息
            this.processPnrFnList(pricingArea, pnrFnList);
        }
        if (CollUtil.isNotEmpty(nmFnList)) {
            // 处理旅客个人运价信息
            this.processNmFnList(pricingArea, nmFnList, pnrNmList);
        }

        if (CollUtil.isEmpty(pnrFnList) && CollUtil.isEmpty(nmFnList)) {
            pricingArea.setFareType("M");
        }

        // 处理运价计算信息
        if (CollUtil.isNotEmpty(pnrFcList)) {
            // 处理统一运价计算信息
            this.processPnrFcList(pricingArea, pnrFcList);
        }
        if (CollUtil.isNotEmpty(nmFcList)) {
            // 处理旅客个人运价计算信息
            this.processNmFcList(pricingArea, nmFcList, pnrNmList);
        }

        // 处理支付信息
        if (CollUtil.isNotEmpty(pnrFpList)) {
            // 处理统一支付信息
            this.processPnrFpList(pricingArea, pnrFpList);
        }
        if (CollUtil.isNotEmpty(nmFpList)) {
            // 处理旅客个人支付信息
            this.processNmFpList(pricingArea, nmFpList, pnrNmList);
        }

        // 处理签注信息
        this.processEndorsementInfos(pricingArea, pnrEiList, nmEiList, pnrNmList);

        QueryPnrDetailVo.Price ob = new QueryPnrDetailVo.Price();
        pricingArea.setOb(ob);

        vo.setPricingArea(pricingArea);
    }

    /**
     * 处理签注信息
     *
     * @param pricingArea 价格信息区域
     * @param pnrEiList   PNR签注信息列表
     * @param nmEiList    旅客签注信息列表
     * @param pnrNmList   旅客信息列表
     */
    private void processEndorsementInfos(QueryPnrDetailVo.PricingArea pricingArea, List<MnjxPnrEi> pnrEiList,
                                         List<MnjxNmEi> nmEiList, List<MnjxPnrNm> pnrNmList) {
        // 处理PNR级别的签注信息
        if (CollUtil.isNotEmpty(pnrEiList)) {
            for (MnjxPnrEi pnrEi : pnrEiList) {
                QueryPnrDetailVo.EndorsementInfo endorsementInfo = new QueryPnrDetailVo.EndorsementInfo();
                endorsementInfo.setLineIndex(pnrEi.getPnrIndex());
                endorsementInfo.setText(pnrEi.getEiInfo());
                endorsementInfo.setInfantInd(pnrEi.getInputValue().contains("IN/"));

                // 解析旅客ID
                List<Integer> passengerIds = new ArrayList<>();

                // 如果签注信息中包含旅客索引，则提取出来
                if (pnrEi.getEiInfo() != null && pnrEi.getEiInfo().contains("/P")) {
                    String[] parts = pnrEi.getEiInfo().split("/P");
                    if (parts.length > 1) {
                        String psgIndexStr = parts[1].trim();
                        // 如果包含多个旅客索引，例如 "1/2"
                        if (psgIndexStr.contains("/")) {
                            String[] psgIndexes = psgIndexStr.split("/");
                            for (String psgIndex : psgIndexes) {
                                try {
                                    passengerIds.add(Integer.parseInt(psgIndex.trim()));
                                } catch (NumberFormatException e) {
                                    // 忽略非数字的情况
                                }
                            }
                        } else {
                            try {
                                passengerIds.add(Integer.parseInt(psgIndexStr.trim()));
                            } catch (NumberFormatException e) {
                                // 忽略非数字的情况
                            }
                        }
                    }
                }

                endorsementInfo.setPassengerIds(passengerIds);
                pricingArea.getEndorsementInfos().add(endorsementInfo);
            }
        }

        // 处理旅客级别的签注信息
        if (CollUtil.isNotEmpty(nmEiList)) {
            for (MnjxNmEi nmEi : nmEiList) {
                QueryPnrDetailVo.EndorsementInfo endorsementInfo = new QueryPnrDetailVo.EndorsementInfo();
                endorsementInfo.setLineIndex(nmEi.getPnrIndex());
                endorsementInfo.setText(nmEi.getEiInfo());
                endorsementInfo.setInfantInd(nmEi.getInputValue().contains("IN/"));

                // 设置旅客ID
                List<Integer> passengerIds = new ArrayList<>();
                for (MnjxPnrNm pnrNm : pnrNmList) {
                    if (pnrNm.getPnrNmId().equals(nmEi.getPnrNmId())) {
                        passengerIds.add(pnrNm.getPsgIndex());
                        break;
                    }
                }

                endorsementInfo.setPassengerIds(passengerIds);
                pricingArea.getEndorsementInfos().add(endorsementInfo);
            }
        }
    }

    /**
     * 处理统一运价信息
     *
     * @param pricingArea 价格信息区域
     * @param pnrFnList   FN信息列表
     */
    private void processPnrFnList(QueryPnrDetailVo.PricingArea pricingArea, List<MnjxPnrFn> pnrFnList) {
        for (MnjxPnrFn pnrFn : pnrFnList) {
            QueryPnrDetailVo.FareBox fareBox = new QueryPnrDetailVo.FareBox();
            fareBox.setLineIndex(pnrFn.getPnrIndex());
            fareBox.setPassengerIds(new ArrayList<>());
            fareBox.setText(pnrFn.getInputValue());

            // 设置旅客类型
            // pnrFn.getPatType() 的值为AD、CH、IN，对应成人、儿童、婴儿
            switch (pnrFn.getPatType()) {
                case "CH":
                    fareBox.setPassengerType("CHD");
                    fareBox.setSpecialPassengerType("CHD");
                    break;
                case "IN":
                    fareBox.setPassengerType("INF");
                    fareBox.setSpecialPassengerType("INF");
                    break;
                default:
                    fareBox.setPassengerType("ADT");
                    fareBox.setSpecialPassengerType("ADT");
                    break;
            }

            // 设置代理费率
            if (pnrFn.getCRate() != null) {
                fareBox.setAgencyRate(pnrFn.getCRate().toString());
            }

            // 设置票价类型
            String fnInfo = pnrFn.getInputValue();
            if (StrUtil.isNotEmpty(fnInfo) && fnInfo.contains("/A/")) {
                fareBox.setFareType("A");
                pricingArea.setFareType("A");
            } else {
                pricingArea.setFareType("M");
            }

            // 设置票面价
            if (pnrFn.getSPrice() != null) {
                // 设置票面价
                QueryPnrDetailVo.Price price = new QueryPnrDetailVo.Price();
                price.setAmount(pnrFn.getSPrice().toString());
                price.setCurrency(pnrFn.getSCurrency());
                fareBox.setPrice(price);

                // 设置票价分摊价格
                QueryPnrDetailVo.Price ticketSpreadPrice = new QueryPnrDetailVo.Price();
                ticketSpreadPrice.setAmount(pnrFn.getSPrice().toString());
                ticketSpreadPrice.setCurrency(pnrFn.getSCurrency());
                fareBox.setTicketSpreadPrice(ticketSpreadPrice);

                // 设置SCNY
                QueryPnrDetailVo.Price scny = new QueryPnrDetailVo.Price();
                scny.setAmount(pnrFn.getSPrice().toString().replace(".", "").replace("00", ""));
                scny.setCurrency(pnrFn.getSCurrency());
                fareBox.setScny(scny);
            }

            // 设置税费
            if (pnrFn.getXPrice() != null) {
                // 设置税费分摊价格
                QueryPnrDetailVo.Price taxSpreadPrice = new QueryPnrDetailVo.Price();
                taxSpreadPrice.setAmount(pnrFn.getXPrice().toString());
                taxSpreadPrice.setCurrency(pnrFn.getXCurrency());
                fareBox.setTaxSpreadPrice(taxSpreadPrice);

                // 设置XCNY
                QueryPnrDetailVo.Price xcny = new QueryPnrDetailVo.Price();
                xcny.setAmount(pnrFn.getXPrice().toString().replace(".", "").replace("00", ""));
                xcny.setCurrency(pnrFn.getXCurrency());
                fareBox.setXcny(xcny);
            }

            // 设置总价
            if (pnrFn.getAPrice() != null) {
                // 设置总价
                QueryPnrDetailVo.Price totalPrice = new QueryPnrDetailVo.Price();
                totalPrice.setAmount(pnrFn.getAPrice().toString());
                totalPrice.setCurrency(pnrFn.getACurrency());
                fareBox.setTotalPrice(totalPrice);

                // 设置ACNY
                QueryPnrDetailVo.Price acny = new QueryPnrDetailVo.Price();
                acny.setAmount(pnrFn.getAPrice().toString().replace(".", "").replace("00", ""));
                acny.setCurrency(pnrFn.getACurrency());
                fareBox.setAcny(acny);
            }

            // 解析税费详情
            List<QueryPnrDetailVo.Tax> taxes = new ArrayList<>();

            // 设置CN基建费
            if (pnrFn.getTCnPrice() != null) {
                QueryPnrDetailVo.Tax cnTax = new QueryPnrDetailVo.Tax();
                cnTax.setTaxCode("CN");

                QueryPnrDetailVo.Price cnPrice = new QueryPnrDetailVo.Price();
                cnPrice.setAmount(pnrFn.getTCnPrice().toString());
                cnPrice.setCurrency(pnrFn.getTCnCurrency());
                cnTax.setPrice(cnPrice);

                taxes.add(cnTax);
            }

            // 设置YQ燃油费
            if (pnrFn.getTYqPrice() != null) {
                QueryPnrDetailVo.Tax yqTax = new QueryPnrDetailVo.Tax();
                yqTax.setTaxCode("YQ");

                QueryPnrDetailVo.Price yqPrice = new QueryPnrDetailVo.Price();
                yqPrice.setAmount(pnrFn.getTYqPrice().toString());
                yqPrice.setCurrency(pnrFn.getTYqCurrency());
                yqTax.setPrice(yqPrice);

                taxes.add(yqTax);
            }

            fareBox.setTaxes(taxes);
            pricingArea.getFareBoxes().add(fareBox);
        }
    }

    /**
     * 处理旅客个人运价信息
     *
     * @param pricingArea 价格信息区域
     * @param nmFnList    旅客个人运价信息列表
     * @param pnrNmList   旅客信息列表
     */
    private void processNmFnList(QueryPnrDetailVo.PricingArea pricingArea, List<MnjxNmFn> nmFnList, List<MnjxPnrNm> pnrNmList) {
        for (MnjxNmFn nmFn : nmFnList) {
            QueryPnrDetailVo.FareBox fareBox = new QueryPnrDetailVo.FareBox();
            fareBox.setLineIndex(nmFn.getPnrIndex());
            fareBox.setText(nmFn.getInputValue());

            // 设置旅客ID
            List<Integer> passengerIds = new ArrayList<>();
            for (MnjxPnrNm pnrNm : pnrNmList) {
                if (pnrNm.getPnrNmId().equals(nmFn.getPnrNmId())) {
                    passengerIds.add(pnrNm.getPsgIndex());
                    // 设置旅客姓名
                    fareBox.setPassengerName(pnrNm.getName());
                    break;
                }
            }
            fareBox.setPassengerIds(passengerIds);

            // 设置旅客类型
            switch (nmFn.getPatType()) {
                case "CH":
                    fareBox.setPassengerType("CHD");
                    fareBox.setSpecialPassengerType("CHD");
                    break;
                case "IN":
                    fareBox.setPassengerType("INF");
                    fareBox.setSpecialPassengerType("INF");
                    break;
                default:
                    fareBox.setPassengerType("ADT");
                    fareBox.setSpecialPassengerType("ADT");
                    break;
            }

            // 设置代理费率
            if (nmFn.getCRate() != null) {
                fareBox.setAgencyRate(nmFn.getCRate().toString());
            }

            // 设置票价类型
            String fnInfo = nmFn.getInputValue();
            if (StrUtil.isNotEmpty(fnInfo) && fnInfo.contains("/A/")) {
                fareBox.setFareType("A");
                pricingArea.setFareType("A");
            } else {
                pricingArea.setFareType("M");
            }

            // 设置票面价
            if (nmFn.getSPrice() != null) {
                // 设置票面价
                QueryPnrDetailVo.Price price = new QueryPnrDetailVo.Price();
                price.setAmount(nmFn.getSPrice().toString());
                price.setCurrency(nmFn.getSCurrency());
                fareBox.setPrice(price);

                // 设置票价分摊价格
                QueryPnrDetailVo.Price ticketSpreadPrice = new QueryPnrDetailVo.Price();
                ticketSpreadPrice.setAmount(nmFn.getSPrice().toString());
                ticketSpreadPrice.setCurrency(nmFn.getSCurrency());
                fareBox.setTicketSpreadPrice(ticketSpreadPrice);

                // 设置SCNY
                QueryPnrDetailVo.Price scny = new QueryPnrDetailVo.Price();
                scny.setAmount(nmFn.getSPrice().toString().replace(".", "").replace("00", ""));
                scny.setCurrency(nmFn.getSCurrency());
                fareBox.setScny(scny);
            }

            // 设置税费
            if (nmFn.getXPrice() != null) {
                // 设置税费分摊价格
                QueryPnrDetailVo.Price taxSpreadPrice = new QueryPnrDetailVo.Price();
                taxSpreadPrice.setAmount(nmFn.getXPrice().toString());
                taxSpreadPrice.setCurrency(nmFn.getXCurrency());
                fareBox.setTaxSpreadPrice(taxSpreadPrice);

                // 设置XCNY
                QueryPnrDetailVo.Price xcny = new QueryPnrDetailVo.Price();
                xcny.setAmount(nmFn.getXPrice().toString().replace(".", "").replace("00", ""));
                xcny.setCurrency(nmFn.getXCurrency());
                fareBox.setXcny(xcny);
            }

            // 设置总价
            if (nmFn.getAPrice() != null) {
                // 设置总价
                QueryPnrDetailVo.Price totalPrice = new QueryPnrDetailVo.Price();
                totalPrice.setAmount(nmFn.getAPrice().toString());
                totalPrice.setCurrency(nmFn.getACurrency());
                fareBox.setTotalPrice(totalPrice);

                // 设置ACNY
                QueryPnrDetailVo.Price acny = new QueryPnrDetailVo.Price();
                acny.setAmount(nmFn.getAPrice().toString().replace(".", "").replace("00", ""));
                acny.setCurrency(nmFn.getACurrency());
                fareBox.setAcny(acny);
            }

            // 解析税费详情
            List<QueryPnrDetailVo.Tax> taxes = new ArrayList<>();

            // 设置CN基建费
            if (nmFn.getTCnPrice() != null) {
                QueryPnrDetailVo.Tax cnTax = new QueryPnrDetailVo.Tax();
                cnTax.setTaxCode("CN");

                QueryPnrDetailVo.Price cnPrice = new QueryPnrDetailVo.Price();
                cnPrice.setAmount(nmFn.getTCnPrice().toString());
                cnPrice.setCurrency(nmFn.getTCnCurrency());
                cnTax.setPrice(cnPrice);

                taxes.add(cnTax);
            }

            // 设置YQ燃油费
            if (nmFn.getTYqPrice() != null) {
                QueryPnrDetailVo.Tax yqTax = new QueryPnrDetailVo.Tax();
                yqTax.setTaxCode("YQ");

                QueryPnrDetailVo.Price yqPrice = new QueryPnrDetailVo.Price();
                yqPrice.setAmount(nmFn.getTYqPrice().toString());
                yqPrice.setCurrency(nmFn.getTYqCurrency());
                yqTax.setPrice(yqPrice);

                taxes.add(yqTax);
            }

            fareBox.setTaxes(taxes);
            pricingArea.getFareBoxes().add(fareBox);
        }
    }

    /**
     * 处理统一支付信息
     *
     * @param pricingArea 价格信息区域
     * @param pnrFpList   FP信息列表
     */
    private void processPnrFpList(QueryPnrDetailVo.PricingArea pricingArea, List<MnjxPnrFp> pnrFpList) {
        for (MnjxPnrFp pnrFp : pnrFpList) {
            QueryPnrDetailVo.FormOfPayment formOfPayment = new QueryPnrDetailVo.FormOfPayment();
            formOfPayment.setLineIndex(pnrFp.getPnrIndex());
            formOfPayment.setText(pnrFp.getInputValue());
            formOfPayment.setInfantInd(pnrFp.getIsBaby() == 1);

            pricingArea.getFormOfPayments().add(formOfPayment);
        }
    }

    /**
     * 处理旅客个人支付信息
     *
     * @param pricingArea 价格信息区域
     * @param nmFpList    旅客个人支付信息列表
     * @param pnrNmList   旅客信息列表
     */
    private void processNmFpList(QueryPnrDetailVo.PricingArea pricingArea, List<MnjxNmFp> nmFpList, List<MnjxPnrNm> pnrNmList) {
        for (MnjxNmFp nmFp : nmFpList) {
            QueryPnrDetailVo.FormOfPayment formOfPayment = new QueryPnrDetailVo.FormOfPayment();
            // 设置旅客ID
            List<String> passengerIds = new ArrayList<>();
            for (MnjxPnrNm pnrNm : pnrNmList) {
                if (pnrNm.getPnrNmId().equals(nmFp.getPnrNmId())) {
                    passengerIds.add(StrUtil.toString(pnrNm.getPsgIndex()));
                    break;
                }
            }
            formOfPayment.setPassengerIds(passengerIds);
            formOfPayment.setLineIndex(nmFp.getPnrIndex());
            formOfPayment.setText(nmFp.getInputValue());
            formOfPayment.setInfantInd(nmFp.getIsBaby() == 1);

            pricingArea.getFormOfPayments().add(formOfPayment);
        }
    }

    /**
     * 处理统一运价计算信息
     *
     * @param pricingArea 价格信息区域
     * @param pnrFcList   FC信息列表
     */
    private void processPnrFcList(QueryPnrDetailVo.PricingArea pricingArea, List<MnjxPnrFc> pnrFcList) {
        for (MnjxPnrFc pnrFc : pnrFcList) {
            QueryPnrDetailVo.FareCaluation fareCaluation = new QueryPnrDetailVo.FareCaluation();
            fareCaluation.setLineIndex(pnrFc.getPnrIndex());
            fareCaluation.setPassengerIds(new ArrayList<>());
            fareCaluation.setText(pnrFc.getInputValue());

            // 设置旅客类型
            switch (pnrFc.getPatType()) {
                case "CH":
                    fareCaluation.setPassengerType("CHD");
                    fareCaluation.setSpecialPassengerType("CHD");
                    break;
                case "IN":
                    fareCaluation.setPassengerType("INF");
                    fareCaluation.setSpecialPassengerType("INF");
                    break;
                default:
                    fareCaluation.setPassengerType("ADT");
                    fareCaluation.setSpecialPassengerType("ADT");
                    break;
            }

            // 设置票价类型
            String fcInfo = pnrFc.getInputValue();
            if (StrUtil.isNotEmpty(fcInfo) && fcInfo.contains("/A/")) {
                fareCaluation.setFareType("A");
            }

            // 初始化票价基本信息和行李信息列表
            List<QueryPnrDetailVo.FareBasic> fareBasicList = new ArrayList<>();
            List<QueryPnrDetailVo.Baggage> baggageList = new ArrayList<>();

            // 查询对应的航段信息
            List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                    .eq(MnjxPnrSeg::getPnrId, pnrFc.getPnrId())
                    .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                    .list();

            if (CollUtil.isNotEmpty(pnrSegList)) {
                // 处理每个航段
                for (MnjxPnrSeg pnrSeg : pnrSegList) {
                    // 构建航段代码
                    String segCode = pnrSeg.getOrg() + "-" + pnrSeg.getDst();

                    // 添加票价基本信息
                    QueryPnrDetailVo.FareBasic fareBasic = new QueryPnrDetailVo.FareBasic();

                    // 根据航段序号获取对应的舱位
                    String cabin = "";
                    if (pnrSeg.getPnrSegNo() == 1 && StrUtil.isNotEmpty(pnrFc.getSeg1Cabin())) {
                        cabin = pnrFc.getSeg1Cabin();
                    } else if (pnrSeg.getPnrSegNo() == 2 && StrUtil.isNotEmpty(pnrFc.getSeg2Cabin())) {
                        cabin = pnrFc.getSeg2Cabin();
                    } else if (pnrSeg.getPnrSegNo() == 3 && StrUtil.isNotEmpty(pnrFc.getSeg3Cabin())) {
                        cabin = pnrFc.getSeg3Cabin();
                    } else if (pnrSeg.getPnrSegNo() == 4 && StrUtil.isNotEmpty(pnrFc.getSeg4Cabin())) {
                        cabin = pnrFc.getSeg4Cabin();
                    } else if (pnrSeg.getPnrSegNo() == 5 && StrUtil.isNotEmpty(pnrFc.getSeg5Cabin())) {
                        cabin = pnrFc.getSeg5Cabin();
                    }

                    // 如果是SA航段，舱位设置为空
                    if ("SA".equals(pnrSeg.getPnrSegType())) {
                        cabin = "";
                    }

                    fareBasic.setFareBasic(cabin);
                    fareBasic.setSeg(segCode);
                    fareBasicList.add(fareBasic);

                    // 添加行李信息
                    QueryPnrDetailVo.Baggage baggage = new QueryPnrDetailVo.Baggage();
                    baggage.setBaggage("");
                    baggage.setSeg(segCode);
                    baggageList.add(baggage);
                }
            }

            fareCaluation.setFareBasic(fareBasicList);
            fareCaluation.setBaggage(baggageList);

            pricingArea.getFareCaluations().add(fareCaluation);
        }
    }

    /**
     * 处理旅客个人运价计算信息
     *
     * @param pricingArea 价格信息区域
     * @param nmFcList    旅客个人运价计算信息列表
     * @param pnrNmList   旅客信息列表
     */
    private void processNmFcList(QueryPnrDetailVo.PricingArea pricingArea, List<MnjxNmFc> nmFcList, List<MnjxPnrNm> pnrNmList) {
        for (MnjxNmFc nmFc : nmFcList) {
            QueryPnrDetailVo.FareCaluation fareCaluation = new QueryPnrDetailVo.FareCaluation();
            fareCaluation.setLineIndex(nmFc.getPnrIndex());
            fareCaluation.setText(nmFc.getInputValue());

            // 设置旅客ID
            List<String> passengerIds = new ArrayList<>();
            for (MnjxPnrNm pnrNm : pnrNmList) {
                if (pnrNm.getPnrNmId().equals(nmFc.getPnrNmId())) {
                    passengerIds.add(StrUtil.toString(pnrNm.getPsgIndex()));
                    break;
                }
            }
            fareCaluation.setPassengerIds(passengerIds);

            // 设置旅客类型
            switch (nmFc.getPatType()) {
                case "CH":
                    fareCaluation.setPassengerType("CHD");
                    fareCaluation.setSpecialPassengerType("CHD");
                    break;
                case "IN":
                    fareCaluation.setPassengerType("INF");
                    fareCaluation.setSpecialPassengerType("INF");
                    break;
                default:
                    fareCaluation.setPassengerType("ADT");
                    fareCaluation.setSpecialPassengerType("ADT");
                    break;
            }

            // 设置票价类型
            String fcInfo = nmFc.getInputValue();
            if (StrUtil.isNotEmpty(fcInfo) && fcInfo.contains("/A/")) {
                fareCaluation.setFareType("A");
            }

            // 初始化票价基本信息和行李信息列表
            List<QueryPnrDetailVo.FareBasic> fareBasicList = new ArrayList<>();
            List<QueryPnrDetailVo.Baggage> baggageList = new ArrayList<>();

            // 查询对应的航段信息
            List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                    .eq(MnjxPnrSeg::getPnrId, pnrNmList.get(0).getPnrId()) // 从旅客信息中提取PNR ID
                    .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                    .list();

            if (CollUtil.isNotEmpty(pnrSegList)) {
                // 处理每个航段
                for (MnjxPnrSeg pnrSeg : pnrSegList) {
                    // 构建航段代码
                    String segCode = pnrSeg.getOrg() + "-" + pnrSeg.getDst();

                    // 添加票价基本信息
                    QueryPnrDetailVo.FareBasic fareBasic = new QueryPnrDetailVo.FareBasic();

                    // 根据航段序号获取对应的舱位
                    String cabin = "";
                    if (pnrSeg.getPnrSegNo() == 1 && StrUtil.isNotEmpty(nmFc.getSeg1Cabin())) {
                        cabin = nmFc.getSeg1Cabin();
                    } else if (pnrSeg.getPnrSegNo() == 2 && StrUtil.isNotEmpty(nmFc.getSeg2Cabin())) {
                        cabin = nmFc.getSeg2Cabin();
                    } else if (pnrSeg.getPnrSegNo() == 3 && StrUtil.isNotEmpty(nmFc.getSeg3Cabin())) {
                        cabin = nmFc.getSeg3Cabin();
                    } else if (pnrSeg.getPnrSegNo() == 4 && StrUtil.isNotEmpty(nmFc.getSeg4Cabin())) {
                        cabin = nmFc.getSeg4Cabin();
                    } else if (pnrSeg.getPnrSegNo() == 5 && StrUtil.isNotEmpty(nmFc.getSeg5Cabin())) {
                        cabin = nmFc.getSeg5Cabin();
                    }

                    // 如果是SA航段，舱位设置为空
                    if ("SA".equals(pnrSeg.getPnrSegType())) {
                        cabin = "";
                    }

                    fareBasic.setFareBasic(cabin);
                    fareBasic.setSeg(segCode);
                    fareBasicList.add(fareBasic);

                    // 添加行李信息
                    QueryPnrDetailVo.Baggage baggage = new QueryPnrDetailVo.Baggage();
                    baggage.setBaggage("");
                    baggage.setSeg(segCode);
                    baggageList.add(baggage);
                }
            }

            fareCaluation.setFareBasic(fareBasicList);
            fareCaluation.setBaggage(baggageList);

            pricingArea.getFareCaluations().add(fareCaluation);
        }
    }


    /**
     * 填充备注信息区域
     *
     * @param vo         返回对象
     * @param pnrRmkList 备注信息列表
     * @param pnrOsiList OSI信息列表
     * @param nmRmkList  旅客备注信息列表
     * @param nmOsiList  旅客OSI信息列表
     * @param pnrNmList  记录信息列表
     */
    private void fillRemarkArea(QueryPnrDetailVo vo, List<MnjxPnrRmk> pnrRmkList, List<MnjxPnrOsi> pnrOsiList,
                                List<MnjxNmRmk> nmRmkList, List<MnjxNmOsi> nmOsiList, List<MnjxPnrNm> pnrNmList) {
        QueryPnrDetailVo.RemarkArea remarkArea = new QueryPnrDetailVo.RemarkArea();

        // 初始化列表
        remarkArea.setRemarks(new ArrayList<>());
        remarkArea.setRemarkOsis(new ArrayList<>());
        remarkArea.setOthers(new ArrayList<>());
        remarkArea.setCkins(new ArrayList<>());
        remarkArea.setClids(new ArrayList<>());
        remarkArea.setSsrContents(new ArrayList<>());

        // 添加PNR备注行
        if (CollUtil.isNotEmpty(pnrRmkList)) {
            for (MnjxPnrRmk rmk : pnrRmkList) {
                QueryPnrDetailVo.Remark remark = new QueryPnrDetailVo.Remark();
                remark.setPassengerName("All");
                remark.setText(rmk.getRmkInfo().replace("RMK ", ""));
                remark.setLineIndex(String.valueOf(rmk.getPnrIndex()));
                remarkArea.getRemarks().add(remark);
            }
        }

        // 添加旅客备注行
        if (CollUtil.isNotEmpty(nmRmkList)) {
            for (MnjxNmRmk rmk : nmRmkList) {
                QueryPnrDetailVo.Remark remark = new QueryPnrDetailVo.Remark();

                pnrNmList.stream()
                        .filter(p -> p.getPnrNmId().equals(rmk.getPnrNmId()))
                        .findFirst()
                        .ifPresent(p -> remark.setPassengerName(p.getName()));

                remark.setText(rmk.getRmkInfo().replace("RMK ", ""));
                remark.setLineIndex(String.valueOf(rmk.getPnrIndex()));
                remarkArea.getRemarks().add(remark);
            }
        }

        // 添加PNR OSI行
        if (CollUtil.isNotEmpty(pnrOsiList)) {
            for (MnjxPnrOsi osi : pnrOsiList) {
                QueryPnrDetailVo.Remark osiRemark = new QueryPnrDetailVo.Remark();
                osiRemark.setPassengerName("All");
                String text = osi.getPnrOsiInfo().replace("OSI ", "");
                if (StrUtil.isNotEmpty(osi.getAirlineCode())) {
                    text = text.replace(osi.getAirlineCode(), "");
                }
                osiRemark.setText(text);
                osiRemark.setLineIndex(String.valueOf(osi.getPnrIndex()));
                osiRemark.setAirline(osi.getAirlineCode());
                remarkArea.getRemarkOsis().add(osiRemark);
            }
        }

        // 添加旅客OSI行
        if (CollUtil.isNotEmpty(nmOsiList)) {
            for (MnjxNmOsi osi : nmOsiList) {
                QueryPnrDetailVo.Remark osiRemark = new QueryPnrDetailVo.Remark();

                pnrNmList.stream()
                        .filter(p -> p.getPnrNmId().equals(osi.getPnrNmId()))
                        .findFirst()
                        .ifPresent(p -> osiRemark.setPassengerName(p.getName()));

                osiRemark.setText(osi.getPnrOsiInfo().replace("OSI ", ""));
                osiRemark.setLineIndex(String.valueOf(osi.getPnrIndex()));
                osiRemark.setAirline(osi.getAirlineCode());
                remarkArea.getRemarkOsis().add(osiRemark);
            }
        }

        vo.setRemarkArea(remarkArea);
    }

    /**
     * 构建原始行内容
     *
     * @param pnr             PNR信息
     * @param pnrNmList       旅客信息列表
     * @param pnrSegList      航段信息列表
     * @param pnrCtList       联系信息列表
     * @param pnrOsiList      OSI信息列表
     * @param pnrRmkList      备注信息列表
     * @param pnrFnList       FN信息列表
     * @param pnrFpList       FP信息列表
     * @param pnrTkList       出票时限信息列表
     * @param pnrFcList       FC信息列表
     * @param pnrEiList       签注信息列表
     * @param pnrGnList       团队信息列表
     * @param pnrTcList       TC信息列表
     * @param pnrNmTnList     旅客出票信息列表
     * @param pnrNmTicketList 旅客票号信息列表
     * @param nmSsrList       旅客SSR信息列表
     * @param nmOsiList       旅客OSI信息列表
     * @param nmRmkList       旅客备注信息列表
     * @param nmFcList        旅客FC信息列表
     * @param nmFnList        旅客FN信息列表
     * @param nmFpList        旅客FP信息列表
     * @param nmXnList        旅客婴儿信息列表
     * @return 原始行内容列表
     */
    private List<QueryPnrDetailVo.LineContent> buildOriginalLineContents(MnjxPnr pnr, List<MnjxPnrNm> pnrNmList,
                                                                         List<MnjxPnrSeg> pnrSegList, List<MnjxPnrCt> pnrCtList,
                                                                         List<MnjxPnrOsi> pnrOsiList, List<MnjxPnrRmk> pnrRmkList,
                                                                         List<MnjxPnrFn> pnrFnList, List<MnjxPnrFp> pnrFpList, List<MnjxPnrTk> pnrTkList,
                                                                         List<MnjxPnrFc> pnrFcList, List<MnjxPnrEi> pnrEiList, List<MnjxPnrGn> pnrGnList, List<MnjxPnrTc> pnrTcList,
                                                                         List<MnjxPnrNmTn> pnrNmTnList, List<MnjxPnrNmTicket> pnrNmTicketList,
                                                                         List<MnjxNmSsr> nmSsrList, List<MnjxNmOsi> nmOsiList, List<MnjxNmRmk> nmRmkList,
                                                                         List<MnjxNmFc> nmFcList, List<MnjxNmFn> nmFnList, List<MnjxNmFp> nmFpList, List<MnjxNmEi> nmEiList, List<MnjxNmXn> nmXnList) {
        List<QueryPnrDetailVo.LineContent> originalLineContents = new ArrayList<>();

        // 检查是否有出票记录
        boolean hasTicket = CollUtil.isNotEmpty(pnrNmTnList);

        // 添加电子客票PNR标记（如果有出票记录）
        if (hasTicket) {
            QueryPnrDetailVo.LineContent headerLine = new QueryPnrDetailVo.LineContent();
            headerLine.setIndex("");
            headerLine.setContent("**ELECTRONIC TICKET PNR**");
            headerLine.setCanDelete(false);
            headerLine.setInvalidSeg(false);
            originalLineContents.add(headerLine);
        }

        // 添加旅客姓名行，姓名行添加特殊点：所有姓名放在一行处理
        if (CollUtil.isNotEmpty(pnrNmList)) {
            StringBuilder nmContentBuilder = new StringBuilder();
            QueryPnrDetailVo.LineContent nameLine = new QueryPnrDetailVo.LineContent();
            for (int i = 0; i < pnrNmList.size(); i++) {
                MnjxPnrNm pnrNm = pnrNmList.get(i);
                if (i == 0) {
                    nameLine.setIndex(String.format("%2d.", pnrNm.getPnrIndex()));
                } else {
                    nmContentBuilder.append(String.format("%2d.", pnrNm.getPnrIndex()));
                }
                // 最后一个姓名后面才添加PnrCrs
                if (i == pnrNmList.size() - 1) {
                    nmContentBuilder.append(pnrNm.getInputValue() + " " + pnr.getPnrCrs());
                } else {
                    nmContentBuilder.append(pnrNm.getInputValue()).append(" ");
                }
            }
            nameLine.setContent(nmContentBuilder.toString());
            nameLine.setCanDelete(true);
            nameLine.setInvalidSeg(false);
            originalLineContents.add(nameLine);
        }

        // 添加航段信息行
        if (CollUtil.isNotEmpty(pnrSegList)) {
            for (MnjxPnrSeg pnrSeg : pnrSegList) {
                QueryPnrDetailVo.LineContent segLine = new QueryPnrDetailVo.LineContent();
                segLine.setIndex(String.format("%2d.", pnrSeg.getPnrIndex()));
                segLine.setContent(pnrSeg.getInputValue());
                String actionCode = pnrSeg.getActionCode();

                segLine.setCanDelete(true);
                segLine.setInvalidSeg("XX".equals(actionCode) || "NO".equals(actionCode));
                originalLineContents.add(segLine);
            }
        }

        // 添加联系信息行
        if (CollUtil.isNotEmpty(pnrCtList)) {
            for (MnjxPnrCt pnrCt : pnrCtList) {
                QueryPnrDetailVo.LineContent contactLine = new QueryPnrDetailVo.LineContent();
                contactLine.setIndex(String.format("%2d.", pnrCt.getPnrIndex()));
                contactLine.setContent(pnrCt.getInputValue());
                contactLine.setCanDelete(true);
                contactLine.setInvalidSeg(false);
                originalLineContents.add(contactLine);
            }
        }

        // 添加票号限制行
        if (CollUtil.isNotEmpty(pnrTkList)) {
            for (MnjxPnrTk pnrTk : pnrTkList) {
                QueryPnrDetailVo.LineContent ticketLimitLine = new QueryPnrDetailVo.LineContent();
                ticketLimitLine.setIndex(String.format("%2d.", pnrTk.getPnrIndex()));
                ticketLimitLine.setContent(pnrTk.getInputValue());
                ticketLimitLine.setCanDelete(true);
                ticketLimitLine.setInvalidSeg(false);
                originalLineContents.add(ticketLimitLine);
            }
        }

        // 添加旅客SSR信息
        if (CollUtil.isNotEmpty(nmSsrList)) {
            for (MnjxNmSsr ssr : nmSsrList) {
                QueryPnrDetailVo.LineContent ssrFoidLine = new QueryPnrDetailVo.LineContent();
                ssrFoidLine.setIndex(String.format("%2d.", ssr.getPnrIndex()));
                ssrFoidLine.setContent(ssr.getInputValue());
                ssrFoidLine.setCanDelete(true);
                ssrFoidLine.setInvalidSeg(false);
                originalLineContents.add(ssrFoidLine);
            }
        }

        // 添加PNR OSI行
        if (CollUtil.isNotEmpty(pnrOsiList)) {
            for (MnjxPnrOsi osi : pnrOsiList) {
                QueryPnrDetailVo.LineContent osiLine = new QueryPnrDetailVo.LineContent();
                osiLine.setIndex(String.format("%2d.", osi.getPnrIndex()));
                osiLine.setContent(osi.getInputValue());
                osiLine.setCanDelete(true);
                osiLine.setInvalidSeg(false);
                originalLineContents.add(osiLine);
            }
        }

        // 添加备注行
        if (CollUtil.isNotEmpty(pnrRmkList)) {
            for (MnjxPnrRmk rmk : pnrRmkList) {
                QueryPnrDetailVo.LineContent rmkLine = new QueryPnrDetailVo.LineContent();
                rmkLine.setIndex(String.format("%2d.", rmk.getPnrIndex()));
                rmkLine.setContent(rmk.getInputValue());
                rmkLine.setCanDelete(true);
                rmkLine.setInvalidSeg(false);
                originalLineContents.add(rmkLine);
            }
        }

        // 添加旅客备注行
        if (CollUtil.isNotEmpty(nmRmkList)) {
            for (MnjxNmRmk rmk : nmRmkList) {
                QueryPnrDetailVo.LineContent rmkLine = new QueryPnrDetailVo.LineContent();
                rmkLine.setIndex(String.format("%2d.", rmk.getPnrIndex()));
                rmkLine.setContent(rmk.getInputValue());
                rmkLine.setCanDelete(true);
                rmkLine.setInvalidSeg(false);
                originalLineContents.add(rmkLine);
            }
        }

        // 添加旅客OSI行
        if (CollUtil.isNotEmpty(nmOsiList)) {
            for (MnjxNmOsi osi : nmOsiList) {
                QueryPnrDetailVo.LineContent osiLine = new QueryPnrDetailVo.LineContent();
                osiLine.setIndex(String.format("%2d.", osi.getPnrIndex()));
                osiLine.setContent(osi.getInputValue());
                osiLine.setCanDelete(true);
                osiLine.setInvalidSeg(false);
                originalLineContents.add(osiLine);
            }
        }

        // 添加签注信息行
        if (CollUtil.isNotEmpty(pnrEiList)) {
            for (MnjxPnrEi ei : pnrEiList) {
                QueryPnrDetailVo.LineContent eiLine = new QueryPnrDetailVo.LineContent();
                eiLine.setIndex(String.format("%2d.", ei.getPnrIndex()));
                eiLine.setContent(ei.getInputValue());
                eiLine.setCanDelete(true);
                eiLine.setInvalidSeg(false);
                originalLineContents.add(eiLine);
            }
        }

        // 添加团队信息行
        if (CollUtil.isNotEmpty(pnrGnList)) {
            for (MnjxPnrGn gn : pnrGnList) {
                QueryPnrDetailVo.LineContent gnLine = new QueryPnrDetailVo.LineContent();
                gnLine.setIndex(String.format("%2d.", gn.getPnrIndex()));
                gnLine.setContent(gn.getInputValue());
                gnLine.setCanDelete(true);
                gnLine.setInvalidSeg(false);
                originalLineContents.add(gnLine);
            }
        }

        // 添加TC信息行
        if (CollUtil.isNotEmpty(pnrTcList)) {
            for (MnjxPnrTc tc : pnrTcList) {
                QueryPnrDetailVo.LineContent tcLine = new QueryPnrDetailVo.LineContent();
                tcLine.setIndex(String.format("%2d.", tc.getPnrIndex()));
                tcLine.setContent(tc.getInputValue());
                tcLine.setCanDelete(true);
                tcLine.setInvalidSeg(false);
                originalLineContents.add(tcLine);
            }
        }

        // 添加婴儿信息行
        if (CollUtil.isNotEmpty(nmXnList)) {
            for (MnjxNmXn xn : nmXnList) {
                QueryPnrDetailVo.LineContent xnLine = new QueryPnrDetailVo.LineContent();
                xnLine.setIndex(String.format("%2d.", xn.getPnrIndex()));
                xnLine.setContent(xn.getInputValue());
                xnLine.setCanDelete(true);
                xnLine.setInvalidSeg(false);
                originalLineContents.add(xnLine);
            }
        }

        // 添加FN信息行
        if (CollUtil.isNotEmpty(pnrFnList)) {
            for (MnjxPnrFn fn : pnrFnList) {
                QueryPnrDetailVo.LineContent fnLine = new QueryPnrDetailVo.LineContent();
                fnLine.setIndex(String.format("%2d.", fn.getPnrIndex()));
                fnLine.setContent(fn.getInputValue());
                fnLine.setCanDelete(true);
                fnLine.setInvalidSeg(false);
                originalLineContents.add(fnLine);
            }
        }

        // 添加旅客FN信息行
        if (CollUtil.isNotEmpty(nmFnList)) {
            for (MnjxNmFn fn : nmFnList) {
                QueryPnrDetailVo.LineContent fnLine = new QueryPnrDetailVo.LineContent();
                fnLine.setIndex(String.format("%2d.", fn.getPnrIndex()));
                fnLine.setContent(fn.getInputValue());
                fnLine.setCanDelete(true);
                fnLine.setInvalidSeg(false);
                originalLineContents.add(fnLine);
            }
        }

        // 添加FC信息行
        if (CollUtil.isNotEmpty(pnrFcList)) {
            for (MnjxPnrFc fc : pnrFcList) {
                QueryPnrDetailVo.LineContent fcLine = new QueryPnrDetailVo.LineContent();
                fcLine.setIndex(String.format("%2d.", fc.getPnrIndex()));
                fcLine.setContent(fc.getInputValue());
                fcLine.setCanDelete(true);
                fcLine.setInvalidSeg(false);
                originalLineContents.add(fcLine);
            }
        }

        // 添加旅客FC信息行
        if (CollUtil.isNotEmpty(nmFcList)) {
            for (MnjxNmFc fc : nmFcList) {
                QueryPnrDetailVo.LineContent fcLine = new QueryPnrDetailVo.LineContent();
                fcLine.setIndex(String.format("%2d.", fc.getPnrIndex()));
                fcLine.setContent(fc.getInputValue());
                fcLine.setCanDelete(true);
                fcLine.setInvalidSeg(false);
                originalLineContents.add(fcLine);
            }
        }

        // 添加票号行
        if (CollUtil.isNotEmpty(pnrNmTnList)) {
            for (MnjxPnrNmTn pnrNmTn : pnrNmTnList) {
                QueryPnrDetailVo.LineContent tnLine = new QueryPnrDetailVo.LineContent();
                tnLine.setIndex(String.format("%2d.", pnrNmTn.getPnrIndex()));
                tnLine.setContent(pnrNmTn.getInputValue());
                tnLine.setCanDelete(true);
                tnLine.setInvalidSeg(false);
                originalLineContents.add(tnLine);
            }
        }

        // 添加FP信息行
        if (CollUtil.isNotEmpty(pnrFpList)) {
            for (MnjxPnrFp fp : pnrFpList) {
                QueryPnrDetailVo.LineContent fpLine = new QueryPnrDetailVo.LineContent();
                fpLine.setIndex(String.format("%2d.", fp.getPnrIndex()));
                fpLine.setContent(fp.getInputValue());
                fpLine.setCanDelete(true);
                fpLine.setInvalidSeg(false);
                originalLineContents.add(fpLine);
            }
        }

        // 添加旅客FP信息行
        if (CollUtil.isNotEmpty(nmFpList)) {
            for (MnjxNmFp fp : nmFpList) {
                QueryPnrDetailVo.LineContent fpLine = new QueryPnrDetailVo.LineContent();
                fpLine.setIndex(String.format("%2d.", fp.getPnrIndex()));
                fpLine.setContent(fp.getInputValue());
                fpLine.setCanDelete(true);
                fpLine.setInvalidSeg(false);
                originalLineContents.add(fpLine);
            }
        }

        // 添加旅客EI信息行
        if (CollUtil.isNotEmpty(nmEiList)) {
            for (MnjxNmEi ei : nmEiList) {
                QueryPnrDetailVo.LineContent eiLine = new QueryPnrDetailVo.LineContent();
                eiLine.setIndex(String.format("%2d.", ei.getPnrIndex()));
                eiLine.setContent("EI/" + ei.getInputValue());
                eiLine.setCanDelete(true);
                eiLine.setInvalidSeg(false);
                originalLineContents.add(eiLine);
            }
        }

        // 添加责任组行
        QueryPnrDetailVo.LineContent officeNoLine = new QueryPnrDetailVo.LineContent();
        // 责任组行使用最大的index + 1
        int maxIndex = originalLineContents.stream()
                .filter(line -> line.getIndex() != null && !line.getIndex().isEmpty())
                .map(line -> {
                    try {
                        return Integer.parseInt(line.getIndex().replace(".", "").trim());
                    } catch (NumberFormatException e) {
                        return 0;
                    }
                })
                .max(Integer::compareTo)
                .orElse(0);
        officeNoLine.setIndex(String.format("%2d.", maxIndex + 1));
        officeNoLine.setContent(pnr.getCreateOfficeNo());
        officeNoLine.setCanDelete(true);
        officeNoLine.setInvalidSeg(false);
        originalLineContents.add(officeNoLine);

        // 按照index升序排序
        originalLineContents.sort((o1, o2) -> {
            if (o1.getIndex() == null || o1.getIndex().isEmpty()) {
                return -1;
            }
            if (o2.getIndex() == null || o2.getIndex().isEmpty()) {
                return 1;
            }
            try {
                int index1 = Integer.parseInt(o1.getIndex().replace(".", "").trim());
                int index2 = Integer.parseInt(o2.getIndex().replace(".", "").trim());
                return Integer.compare(index1, index2);
            } catch (NumberFormatException e) {
                return o1.getIndex().compareTo(o2.getIndex());
            }
        });

        return originalLineContents;
    }

    /**
     * 构建历史行内容
     *
     * @param pnr pnr
     * @return 历史行内容列表
     */
    private List<QueryPnrDetailVo.LineContent> buildHistoryLineContents(MnjxPnr pnr) {
        // 查询记录信息
        List<MnjxPnrRecord> pnrRecordList = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrRecord::getPnrIndex)
                .list();
        List<MnjxPnrAt> atList = iMnjxPnrAtService.lambdaQuery()
                .eq(MnjxPnrAt::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrAt::getAtNo)
                .orderByAsc(MnjxPnrAt::getAtDateTime)
                .list();
        MnjxPnrAt latestAt = atList.get(atList.size() - 1);

        List<QueryPnrDetailVo.LineContent> historyLineContents = new ArrayList<>();
        QueryPnrDetailVo.LineContent firstLine = new QueryPnrDetailVo.LineContent();
        firstLine.setIndex("    ");
        firstLine.setContent("    **THIS PNR WAS ENTIRELY CANCELLED**");
        firstLine.setCanDelete(false);
        firstLine.setInvalidSeg(false);
        historyLineContents.add(firstLine);

        QueryPnrDetailVo.LineContent secondLine = new QueryPnrDetailVo.LineContent();
        // 格式化封口时间
        MnjxSi mnjxSi = iMnjxSiService.getById(latestAt.getAtSiId());
        MnjxOffice mnjxOffice = iMnjxOfficeService.getById(mnjxSi.getOfficeId());
        String officeNo = mnjxOffice.getOfficeNo();
        String siNo = mnjxSi.getSiNo();
        String atDate = DateUtils.ymd2Com(DateUtil.format(latestAt.getAtDateTime(), "yyyy-MM-dd")).substring(0, 5);
        String atTime = DateUtil.format(latestAt.getAtDateTime(), "HHmm");
        secondLine.setIndex(latestAt.getAtNo());
        String secondContent = StrUtil.format("    HDQ{} {} {} {} /RLC{}", latestAt.getAtType(), siNo, atTime, atDate, Integer.parseInt(latestAt.getAtNo()) - 1);
        secondLine.setContent(secondContent);
        secondLine.setCanDelete(false);
        secondLine.setInvalidSeg(false);
        historyLineContents.add(secondLine);

        if (CollUtil.isNotEmpty(pnrRecordList)) {
            List<MnjxPnrRecord> cancelledRecordList = pnrRecordList.stream()
                    .filter(r -> "D".equals(r.getChangeMark()))
                    .collect(Collectors.toList());
            StringBuilder nmBuilder = new StringBuilder();
            for (int i = 0; i < cancelledRecordList.size(); i++) {
                MnjxPnrRecord record = cancelledRecordList.get(i);
                if (i > 0 && "SEG".equals(cancelledRecordList.get(i - 1).getPnrType())) {
                    QueryPnrDetailVo.LineContent extraLineContent = new QueryPnrDetailVo.LineContent();
                    extraLineContent.setIndex("   ");
                    extraLineContent.setContent(StrUtil.format("    NN(001)  DK(001)  HK(001)  RR(001)  XX({})", record.getChangeAtNo()));
                    extraLineContent.setCanDelete(false);
                    extraLineContent.setInvalidSeg(false);
                    historyLineContents.add(extraLineContent);
                }
                if (i > 0 && "SSR".equals(cancelledRecordList.get(i - 1).getPnrType())) {
                    QueryPnrDetailVo.LineContent extraLineContent = new QueryPnrDetailVo.LineContent();
                    extraLineContent.setIndex("   ");
                    extraLineContent.setContent(StrUtil.format("    HK(001)   XX({})", record.getChangeAtNo()));
                    extraLineContent.setCanDelete(false);
                    extraLineContent.setInvalidSeg(false);
                    historyLineContents.add(extraLineContent);
                }

                QueryPnrDetailVo.LineContent lineContent = new QueryPnrDetailVo.LineContent();
                if ("NM".equals(record.getPnrType())) {
                    if (StrUtil.isEmpty(nmBuilder.toString())) {
                        nmBuilder.append("    ");
                    }
                    nmBuilder.append("X")
                            .append(record.getPnrIndex())
                            .append(".")
                            .append(record.getInputValue())
                            .append("(").append(record.getAtNo()).append(")")
                            .append(" ");
                    if (i < cancelledRecordList.size() - 1 && !"NM".equals(cancelledRecordList.get(i + 1).getPnrType())) {
                        nmBuilder.append(pnr.getPnrCrs());
                        lineContent.setIndex("   ");
                        lineContent.setContent(nmBuilder.toString());
                    } else {
                        continue;
                    }
                } else {
                    lineContent.setIndex(record.getAtNo());
                    String inputValue = record.getInputValue();
                    if (StrUtil.equalsAny(record.getPnrType(), "EI", "NM EI")) {
                        inputValue = "EI/" + inputValue;
                    }
                    lineContent.setContent(StrUtil.format("    X{}.{}", record.getPnrIndex(), inputValue));
                }
                lineContent.setCanDelete(false);
                lineContent.setInvalidSeg(false);
                historyLineContents.add(lineContent);
            }

            List<MnjxPnrRecord> changeRecordList = pnrRecordList.stream()
                    .filter(p -> StrUtil.equalsAny(p.getChangeMark(), "X", "I", "K", "KI"))
                    .collect(Collectors.toList());
            for (MnjxPnrAt at : atList) {
                QueryPnrDetailVo.LineContent lineContent = new QueryPnrDetailVo.LineContent();
                lineContent.setIndex(at.getAtNo());

                // 格式化封口时间
                atDate = DateUtils.ymd2Com(DateUtil.format(at.getAtDateTime(), "yyyy-MM-dd")).substring(0, 5);
                atTime = DateUtil.format(at.getAtDateTime(), "HHmm");

                // 构建封口行内容
                String content;
                if (StrUtil.isEmpty(at.getAtType())) {
                    content = StrUtil.format("    {} {} {} {}", pnr.getPnrIcs(), officeNo, atTime, atDate);
                } else {
                    if (StrUtil.equalsAny(at.getAtType(), "I", "KI")) {
                        content = StrUtil.format("    {} {} {} {} {}", pnr.getPnrIcs(), officeNo, atTime, atDate, at.getAtType());
                    } else {
                        if (Integer.parseInt(at.getAtNo()) > 1) {
                            content = StrUtil.format("    HDQ{} {} {} {} /RLC{}", at.getAtType(), siNo, atTime, atDate, Integer.parseInt(at.getAtNo()) - 1);
                        } else {
                            content = StrUtil.format("    HDQ{} {} {} {}", at.getAtType(), siNo, atTime, atDate);
                        }
                    }
                }

                lineContent.setContent(content);
                lineContent.setCanDelete(false);
                lineContent.setCode(null);
                lineContent.setInvalidSeg(false);

                historyLineContents.add(lineContent);

                for (MnjxPnrRecord record : changeRecordList) {
                    if ((StrUtil.isNotEmpty(at.getAtType()) && !StrUtil.equalsAny(at.getAtType(), "K", "I", "KI")) || !at.getAtNo().equals(record.getAtNo())) {
                        continue;
                    }
                    QueryPnrDetailVo.LineContent changelineContent = new QueryPnrDetailVo.LineContent();
                    changelineContent.setIndex(record.getAtNo() + "/" + record.getChangeAtNo());
                    changelineContent.setContent(record.getInputValue());
                    if (StrUtil.equalsAny(record.getPnrType(), "EI", "NM EI")) {
                        changelineContent.setContent("EI/" + record.getInputValue());
                    }
                    changelineContent.setCanDelete(false);
                    changelineContent.setCode(null);

                    changelineContent.setInvalidSeg(null);

                    historyLineContents.add(changelineContent);
                }
            }
        }
        return historyLineContents;
    }

    /**
     * 构建队列PNR详情
     *
     * @param pnr        PNR信息
     * @param pnrNmList  旅客信息列表
     * @param pnrSegList 航段信息列表
     * @return 队列PNR详情
     */
    private QueryPnrDetailVo.QueuePnrDetail buildQueuePnrDetail(MnjxPnr pnr, List<MnjxPnrNm> pnrNmList, List<MnjxPnrSeg> pnrSegList) {
        QueryPnrDetailVo.QueuePnrDetail queuePnrDetail = new QueryPnrDetailVo.QueuePnrDetail();
        queuePnrDetail.setPnrNo(pnr.getPnrCrs());
        queuePnrDetail.setCanceled("DEL".equals(pnr.getPnrStatus()));

        // 检查是否是团队
        List<MnjxPnrGn> pnrGnList = iMnjxPnrGnService.lambdaQuery()
                .eq(MnjxPnrGn::getPnrId, pnr.getPnrId())
                .list();
        queuePnrDetail.setGroup(CollUtil.isNotEmpty(pnrGnList));

        // 设置旅客姓名列表
        List<String> passengerNames = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrNmList)) {
            for (MnjxPnrNm pnrNm : pnrNmList) {
                passengerNames.add(pnrNm.getName());
            }
        }
        queuePnrDetail.setPassengerNames(passengerNames);

        // 设置航班列表
        List<QueryPnrDetailVo.QueueFlight> flights = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrSegList)) {
            for (MnjxPnrSeg pnrSeg : pnrSegList) {
                QueryPnrDetailVo.QueueFlight queueFlight = new QueryPnrDetailVo.QueueFlight();

                // 判断是否为SA航段（地面航段）
                boolean isSaSegment = "SA".equals(pnrSeg.getPnrSegType());

                if (isSaSegment) {
                    // SA航段处理
                    queueFlight.setFlightNo("ARNK");
                } else {
                    // 非SA航段处理
                    queueFlight.setFlightNo(pnrSeg.getFlightNo());
                }

                queueFlight.setDepartureCode(pnrSeg.getOrg());
                queueFlight.setArrivalCode(pnrSeg.getDst());
                queueFlight.setDepartureDate(pnrSeg.getFlightDate());

                if (!isSaSegment) {
                    queueFlight.setDepartureDateTime(pnrSeg.getFlightDate() + " " + formatTimeToHHmm(pnrSeg.getEstimateOff()));
                    queueFlight.setCabinName(pnrSeg.getSellCabin());
                } else {
                    // SA航段不设置出发时间和舱位
                    queueFlight.setDepartureDateTime(pnrSeg.getFlightDate());
                    queueFlight.setCabinName("");
                }

                queueFlight.setActionCode(pnrSeg.getActionCode());
                flights.add(queueFlight);
            }
        }
        queuePnrDetail.setFlights(flights);

        return queuePnrDetail;
    }
}