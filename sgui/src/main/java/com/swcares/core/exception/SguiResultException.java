package com.swcares.core.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.function.Supplier;

/**
 * 业务统一异常
 *
 * <AUTHOR>
 * @date 2025/4/18 11:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SguiResultException extends Exception implements Supplier<SguiResultException> {
    /**
     * 存储错误消息
     */
    private String message;

    public SguiResultException(String message) {
        super(message);
        this.message = message;
    }

    @Override
    public SguiResultException get() {
        return new SguiResultException(this.message);
    }
}
