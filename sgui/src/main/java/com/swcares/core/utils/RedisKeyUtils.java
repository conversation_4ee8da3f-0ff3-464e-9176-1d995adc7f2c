package com.swcares.core.utils;

import com.swcares.core.constants.AuthCacheConstants;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2025/4/17 15:44
 */
public class RedisKeyUtils {

    /**
     * Title: getUserInfoKey
     * Description: 用户信息存储键<br>
     *
     * @param username
     * @return {@link String}
     * <AUTHOR>
     * @date 2025/4/17 16:56
     */
    public static String getUserInfoKey(String username) {
        return AuthCacheConstants.USER_INFO + username;
    }

    public static String getSguiSessionId(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        String sguiSessionId = "";
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("sguiSessionId".equals(cookie.getName())) {
                    sguiSessionId = cookie.getValue();
                }
            }
        }
        return sguiSessionId;
    }
}
