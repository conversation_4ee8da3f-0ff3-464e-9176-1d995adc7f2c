package com.swcares.core.security;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Date;

/**
 * token的生成是使用JWT工具类来生成的
 *
 * <AUTHOR>
 * @date 2021-08-18 16:07:27
 */
@Slf4j
@Component
public class TokenJwtManager {

    public static final String KEY_AUTHORIZATION = "Authorization";

    /**
     * 过期时间8小时
     */
    private final Duration expiration = Duration.ofHours(8);
//    private final Duration expiration = Duration.ofMinutes(2);

    @Value("${security.jwt.token}")
    private String tokenSecret;

//    @Resource
//    private RedisTemplate<String, String> redisTemplate;

    /**
     * 生成JWT
     *
     * @param username 用户名
     * @return JWT
     */
    public String createToken(String username) {
//        // 先确认当前用户是否已在其他地方登录过
//        if (redisTemplate.hasKey(AuthCacheConstants.TOKEN + username)) {
//            redisTemplate.delete(AuthCacheConstants.TOKEN + username);
//        }
        // 过期时间
        Date expiryDate = new Date(System.currentTimeMillis() + expiration.toMillis());
        return Jwts.builder()
                // 将用户名放进JWT
                .setSubject(username)
                // 生效时间
                .setNotBefore(new Date())
                // 设置JWT签发时间
                .setIssuedAt(new Date())
                // 设置过期时间
                .setExpiration(expiryDate)
                // 设置加密算法和秘钥
                .signWith(Keys.hmacShaKeyFor(DigestUtil.sha256(tokenSecret, CharsetUtil.UTF_8)), SignatureAlgorithm.HS256)
                // 数据压缩方式
                .compressWith(CompressionCodecs.GZIP)
                //
                .compact();
    }

    /**
     * 解析JWT
     *
     * @param token JWT字符串
     * @return 解析成功返回Claims对象，解析失败返回null
     */
    public String parseToken(String token) {
        String username = StrUtil.EMPTY;
        // 如果是空字符串直接返回“”
        try {
            if (StrUtil.isNotEmpty(token)) {
                // 解析失败了会抛出异常，所以我们要捕捉一下。token过期、token非法都会导致解析失败
                Claims claims = Jwts.parserBuilder()
                        .setSigningKey(Keys.hmacShaKeyFor(DigestUtil.sha256(tokenSecret, CharsetUtil.UTF_8)))
                        .build()
                        .parseClaimsJws(token)
                        // 获得有效载荷
                        .getBody();
                if (ObjectUtil.isNotNull(claims)) {
                    username = claims.getSubject();
                }
            }
        } catch (ExpiredJwtException e) {
            throw new AuthenticationServiceException("登录凭证已过期，请刷新重新登录", e);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("token解析异常:", e.getMessage());
        }
        return username;
    }

}
