package com.swcares.core.security.custom;

import cn.hutool.core.util.ObjectUtil;
import com.swcares.entity.MnjxOffice;
import com.swcares.entity.MnjxSi;
import com.swcares.service.IMnjxOfficeService;
import com.swcares.service.IMnjxSiService;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/4/18 9:28
 */
@Service
public class CustomUserDetailsService implements UserDetailsService {

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Override
    public CustomUserDetails loadUserByUsername(String username) {
        MnjxSi mnjxSi = iMnjxSiService.lambdaQuery()
                .eq(MnjxSi::getSiNo, username)
                .one();
        if (ObjectUtil.isEmpty(mnjxSi)) {
            throw new UsernameNotFoundException("用户不存在");
        }
        MnjxOffice mnjxOffice = iMnjxOfficeService.getById(mnjxSi.getOfficeId());
        UserInfo userInfo = new UserInfo();
        userInfo.setSiId(mnjxSi.getSiId());
        userInfo.setSiNo(mnjxSi.getSiNo());
        userInfo.setSiPassword(mnjxSi.getSiPassword());
        userInfo.setMnjxOffice(mnjxOffice);
        return new CustomUserDetails(userInfo);
    }
}
