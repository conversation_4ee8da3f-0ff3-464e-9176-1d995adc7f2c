package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 航班占位请求DTO
 *
 * <AUTHOR>
 * @date 2025/5/12 14:30
 */
@Data
@ApiModel(description = "航班占位请求DTO")
public class AirPreOccupyDto {

    @ApiModelProperty("航段信息列表")
    private List<BookAirSeg> bookAirSegs;

    @Data
    public static class BookAirSeg {
        @ApiModelProperty("舱位等级")
        private String fltClass;

        @ApiModelProperty("出发城市")
        private String orgCity;

        @ApiModelProperty("目的城市")
        private String desCity;

        @ApiModelProperty("出发时间")
        private String departureTime;

        @ApiModelProperty("到达时间")
        private String arrivalTime;

        @ApiModelProperty("航班号")
        private String airCode;

        @ApiModelProperty("占位数量")
        private String tktNum;

        @ApiModelProperty("机型")
        private String equipmentCode;

        @ApiModelProperty("是否电子票")
        private Boolean etInd;

        @ApiModelProperty("出发航站楼")
        private String departureTerminal;

        @ApiModelProperty("到达航站楼")
        private String arrivalTerminal;

        @ApiModelProperty("操作代码")
        private String actionCode;
    }
}
