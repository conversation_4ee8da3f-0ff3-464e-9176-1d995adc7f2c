package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29 15:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "IssueTicketDto", description = "出票dto")
public class IssueTicketDto {

    @ApiModelProperty(value = "票据类型")
    private String ticketType;

    @ApiModelProperty(value = "PNR编号")
    private String pnrNo;

    @ApiModelProperty(value = "FP列表")
    private List<Object> fpList;

    @ApiModelProperty(value = "票价类型")
    private String fareType;

    @ApiModelProperty(value = "信用卡验证")
    private CreditCardValidateDto creditCardValidate;

    @ApiModelProperty(value = "出票信息")
    private IssueDto issue;

    @ApiModelProperty(value = "验证IET")
    private Boolean validateIet;

    @ApiModelProperty(value = "检查拼音姓名")
    private Boolean checkPinYinName;

    @Data
    public static class CreditCardValidateDto {
        // 根据需求可以添加具体的信用卡验证字段
    }

    @Data
    public static class IssueDto {

        @ApiModelProperty(value = "打票机编号")
        private String printerNo;

        @ApiModelProperty(value = "验证航司")
        private String validateAirline;

        @ApiModelProperty(value = "DORR标识")
        private Boolean dorr;

        @ApiModelProperty(value = "出票项列表")
        private List<IssueItemDto> issueItems;

        @ApiModelProperty(value = "DPAY密码")
        private String dpayPass;

        @ApiModelProperty(value = "DPAY票据类型")
        private String dpayTktType;

        @ApiModelProperty(value = "残值变更")
        private Boolean salvageChange;
    }

    @Data
    public static class IssueItemDto {

        @ApiModelProperty(value = "旅客ID")
        private String passengerId;

        @ApiModelProperty(value = "旅客类型")
        private String passengerType;

        @ApiModelProperty(value = "姓名，base64编码")
        private String name;
    }
}
