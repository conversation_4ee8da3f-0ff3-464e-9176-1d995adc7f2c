package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 航班特殊服务配额查询传输对象
 *
 * <AUTHOR>
 * @date 2025/5/15 11:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryQuotaDto", description = "航班特殊服务配额查询传输对象")
public class QueryQuotaDto {

    @ApiModelProperty(value = "航班号", required = true)
    private String flightNo;

    @ApiModelProperty(value = "特殊服务选项，多个选项用逗号分隔，如：INFT,AVIH,WCHC,UMNR", required = true)
    private String option;

    @ApiModelProperty(value = "出发日期，格式：yyyy-MM-dd", required = true)
    private String departureDate;

    @ApiModelProperty(value = "出发时间，格式：HHmm", required = true)
    private String departureTime;
}
