package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 更新PNR请求DTO
 *
 * <AUTHOR>
 * @date 2025/5/26 16:00
 */
@Data
@ApiModel(value = "UpdatePnrDto", description = "更新PNR请求DTO")
public class UpdatePnrDto {

    @ApiModelProperty(value = "PNR编号")
    private String pnrNo;

    @ApiModelProperty(value = "国际标识")
    private Boolean international;

    @ApiModelProperty(value = "封口类型")
    private String envelopType;

    @ApiModelProperty(value = "旅客信息")
    private Passengers passengers;

    @ApiModelProperty(value = "团队旅客信息")
    private List<Object> groupPassengers;

    @ApiModelProperty(value = "联系方式变更")
    private List<Contact> contacts;

    @ApiModelProperty(value = "备注信息变更")
    private List<Remark> remarks;

    @ApiModelProperty(value = "特服信息变更")
    private List<SpecialService> specialServices;

    @ApiModelProperty(value = "出票时限变更")
    private IssueLimit issueLimit;

    @ApiModelProperty(value = "手工运价")
    private ManualFare manualFare;

    @ApiModelProperty(value = "航段信息变更")
    private List<Segment> segments;

    @ApiModelProperty(value = "票号信息变更")
    private Object tn;

    @ApiModelProperty(value = "出票记录变更")
    private Object tkne;

    @ApiModelProperty(value = "XN信息")
    private Object xn;

    @ApiModelProperty(value = "是否修改运价")
    private Boolean hasChangeFare;

    @ApiModelProperty(value = "自动运价")
    private List<AutoFare> autoFares;

    /**
     * 旅客信息
     */
    @Data
    public static class Passengers {
        @ApiModelProperty(value = "新增旅客")
        private List<AddPassenger> addPassengers;

        @ApiModelProperty(value = "航段信息")
        private List<PassengerSegment> segments;

        @ApiModelProperty(value = "编辑旅客")
        private List<EditPassenger> editPassengers;
    }

    /**
     * 新增旅客
     */
    @Data
    public static class AddPassenger {
        // 参考BookPnrDto.Passenger的结构
        @ApiModelProperty(value = "旅客ID")
        private String id;

        @ApiModelProperty(value = "全名")
        private String fullName;

        @ApiModelProperty(value = "旅客类型")
        private String passengerType;

        @ApiModelProperty(value = "生日")
        private String birthday;

        @ApiModelProperty(value = "证件类型")
        private String certificateType;

        @ApiModelProperty(value = "证件号码")
        private String certificateNo;

        @ApiModelProperty(value = "航段信息")
        private List<OperatePassengerSegment> segments;
    }

    /**
     * 编辑旅客
     */
    @Data
    public static class EditPassenger {
        @ApiModelProperty(value = "旅客ID")
        private String id;

        @ApiModelProperty(value = "生日")
        private String birthday;

        @ApiModelProperty(value = "OSI联系方式")
        private String osiCtcm;

        @ApiModelProperty(value = "证件信息")
        private Document document;

        @ApiModelProperty(value = "全名")
        private String fullName;

        @ApiModelProperty(value = "姓")
        private String firstName;

        @ApiModelProperty(value = "名")
        private String lastName;

        @ApiModelProperty(value = "带标识的姓名")
        private String nameSuffix;

        @ApiModelProperty(value = "")
        private String airlineType;

        @ApiModelProperty(value = "旅客序号")
        private Integer passengerId;

        @ApiModelProperty(value = "航段信息")
        private List<OperatePassengerSegment> segments;

        @ApiModelProperty(value = "")
        private String foreign;

        @ApiModelProperty(value = "pnr crs编码")
        private String pnrNo;

        @ApiModelProperty(value = "旅客类型")
        private String passengerType;

        @ApiModelProperty(value = "特殊旅客类型")
        private String specialPassengerType;

        @ApiModelProperty(value = "操作标识")
        private OptFlag optFlag;

        @ApiModelProperty(value = "婴儿详情")
        private InfantDetail infantDetail;
    }

    /**
     * 证件信息
     */
    @Data
    public static class Document {
        @ApiModelProperty(value = "证件类型")
        private String documentType;

        @ApiModelProperty(value = "SSR类型")
        private String ssrType;

        @ApiModelProperty(value = "身份证号")
        private String idCardNumber;

        @ApiModelProperty(value = "生日")
        private String birthday;

        @ApiModelProperty(value = "性别")
        private String gender;

        @ApiModelProperty(value = "")
        private String visaIssueCountry;

        @ApiModelProperty(value = "")
        private String passengerNationality;

        @ApiModelProperty(value = "")
        private String visaExpiryDate;

        @ApiModelProperty(value = "")
        private String holder;

        @ApiModelProperty(value = "")
        private String docsName;
    }

    /**
     * 操作标识
     */
    @Data
    public static class OptFlag {
        @ApiModelProperty(value = "更新生日")
        private Boolean updateBirthday;

        @ApiModelProperty(value = "更新中文名")
        private Boolean updateChineseName;

        @ApiModelProperty(value = "更新全名")
        private Boolean updateFullName;

        @ApiModelProperty(value = "更新婴儿SSR INFT")
        private Boolean updateSsrInftInfo;

        @ApiModelProperty(value = "更新证件")
        private Boolean updateDocument;

        @ApiModelProperty(value = "更新OSI联系方式")
        private Boolean updateOsiCtcm;

        @ApiModelProperty(value = "更新SSR联系方式")
        private Boolean updateSsrCtcm;
    }

    /**
     * 婴儿详情
     */
    @Data
    public static class InfantDetail {
        @ApiModelProperty(value = "婴儿ID")
        private String id;

        @ApiModelProperty(value = "生日")
        private String birthday;

        @ApiModelProperty(value = "中文名")
        private String chineseName;

        @ApiModelProperty(value = "全名")
        private String fullName;

        @ApiModelProperty(value = "名")
        private String lastName;

        @ApiModelProperty(value = "姓")
        private String firstName;

        @ApiModelProperty(value = "")
        private String nameSuffix;

        @ApiModelProperty(value = "")
        private Boolean reUseCheck;

        @ApiModelProperty(value = "航段信息")
        private List<OperatePassengerSegment> segments;

        @ApiModelProperty(value = "证件信息")
        private Document document;

        @ApiModelProperty(value = "选择的航段")
        private List<String> selectedSegList;

        @ApiModelProperty(value = "")
        private String passengerNameInInft;

        @ApiModelProperty(value = "操作标识")
        private OptFlag optFlag;

        @ApiModelProperty(value = "操作标识")
        private List<Inft> inft;
    }

    /**
     * 旅客航段
     */
    @Data
    public static class PassengerSegment {
        @ApiModelProperty(value = "行动代码")
        private String actionCode;

        @ApiModelProperty(value = "出发日期")
        private String departureDate;

        @ApiModelProperty(value = "出发时间")
        private String departureTime;

        @ApiModelProperty(value = "到达日期")
        private String arrivalDate;

        @ApiModelProperty(value = "到达时间")
        private String arrivalTime;

        @ApiModelProperty(value = "出发航站楼")
        private String departureTerminal;

        @ApiModelProperty(value = "到达航站楼")
        private String arrivalTerminal;

        @ApiModelProperty(value = "出发机场")
        private String departureAirport;

        @ApiModelProperty(value = "到达机场")
        private String arrivalAirport;

        @ApiModelProperty(value = "无航司的航班号")
        private String marketingFlightNumber;

        @ApiModelProperty(value = "出发机场中文")
        private String sharedInfo;

        @ApiModelProperty(value = "机型")
        private String marketingPlaneType;

        @ApiModelProperty(value = "航司")
        private String marketingAirline;

        @ApiModelProperty(value = "舱位")
        private String cabinCode;

        @ApiModelProperty(value = "航段类型")
        private String segmentType;
    }

    /**
     * 操作旅客的航段
     */
    @Data
    public static class OperatePassengerSegment {
        @ApiModelProperty(value = "行动代码")
        private String actionCode;

        @ApiModelProperty(value = "出发机场")
        private String origin;

        @ApiModelProperty(value = "出发机场中文")
        private String originCH;

        @ApiModelProperty(value = "到达机场")
        private String destination;

        @ApiModelProperty(value = "到达机场中文")
        private String destinationCH;

        @ApiModelProperty(value = "")
        private List<OperatePassengerSegmentService> services;

        @ApiModelProperty(value = "航司")
        private String airline;

        @ApiModelProperty(value = "无航司的航班号")
        private String flightNumber;

        @ApiModelProperty(value = "舱位")
        private String classId;

        @ApiModelProperty(value = "")
        private String subClassId;

        @ApiModelProperty(value = "航班日期时间yyyy-MM-ddTHH:mm")
        private String flightDate;

        @ApiModelProperty(value = "出发日期时间yyyy-MM-ddTHH:mm")
        private String departureDate;

        @ApiModelProperty(value = "出发时间HH:mm")
        private String departureTime;

        @ApiModelProperty(value = "到达时间HH:mm")
        private String arrivalTime;

        @ApiModelProperty(value = "")
        private String ocAirline;

        @ApiModelProperty(value = "")
        private String ocFlightNumber;

        @ApiModelProperty(value = "是否ARNK")
        private Boolean arnkInd;

        @ApiModelProperty(value = "是否OPEN")
        private Boolean openInd;

        @ApiModelProperty(value = "是否有婴儿")
        private Boolean hasInft;

        @ApiModelProperty(value = "")
        private Boolean tchb;

        @ApiModelProperty(value = "婴儿行动代码")
        private String inftActionCode;

        @ApiModelProperty(value = "")
        private String selectedFlag;

        @ApiModelProperty(value = "")
        private String ticketNumber;

        @ApiModelProperty(value = "")
        private String ticketStatus;

        @ApiModelProperty(value = "INFT文本")
        private String inftText;

        @ApiModelProperty(value = "INFT序号")
        private Integer lineIndex;
;
        @ApiModelProperty(value = "")
        private OperatePassengerSegmentTkne tkne;
    }

    /**
     * 联系方式
     */
    @Data
    public static class Contact {
        @ApiModelProperty(value = "操作类型")
        private String operateType;

        @ApiModelProperty(value = "删除索引列表")
        private List<Integer> deleteIndexList;

        @ApiModelProperty(value = "类型")
        private String type;

        @ApiModelProperty(value = "文本")
        private String text;

        @ApiModelProperty(value = "航司")
        private String airline;
    }

    /**
     * 备注信息
     */
    @Data
    public static class Remark {
        @ApiModelProperty(value = "操作类型")
        private String operateType;

        @ApiModelProperty(value = "类型")
        private String type;

        @ApiModelProperty(value = "文本")
        private String text;

        @ApiModelProperty(value = "航司")
        private String airline;

        @ApiModelProperty(value = "删除索引列表")
        private List<Integer> deleteIndexList;
    }

    /**
     * 特殊服务
     */
    @Data
    public static class SpecialService {
        @ApiModelProperty(value = "操作类型")
        private String operateType;

        @ApiModelProperty(value = "删除索引列表")
        private List<Integer> deleteIndexList;

        @ApiModelProperty(value = "行动代码")
        private String actionCode;

        @ApiModelProperty(value = "航司")
        private String airline;

        @ApiModelProperty(value = "SSR代码")
        private String ssrCode;

        @ApiModelProperty(value = "出发机场")
        private String origin;

        @ApiModelProperty(value = "到达机场")
        private String destination;

        @ApiModelProperty(value = "无航司的航班号")
        private String flightNumber;

        @ApiModelProperty(value = "舱位")
        private String classId;

        @ApiModelProperty(value = "航班日期")
        private String flightDate;

        @ApiModelProperty(value = "文本")
        private String text;

        @ApiModelProperty(value = "旅客数量")
        private Integer nbrOfpassengers;

        @ApiModelProperty(value = "旅客ID列表")
        private List<Integer> paxIds;
    }

    /**
     * 出票时限
     */
    @Data
    public static class IssueLimit {
        @ApiModelProperty(value = "操作类型")
        private String operateType;

        @ApiModelProperty(value = "出票时限时间")
        private String issueLimitTime;

        @ApiModelProperty(value = "出票时限部门")
        private String issueLimitOffice;

        @ApiModelProperty(value = "票号")
        private String ticketNumber;
    }

    /**
     * 手工运价
     */
    @Data
    public static class ManualFare {
        @ApiModelProperty(value = "操作类型")
        private String operateType;

        @ApiModelProperty(value = "单独更新")
        private Boolean singleUpdate;

        @ApiModelProperty(value = "原有成人")
        private Boolean oldHasAdult;

        @ApiModelProperty(value = "待出票旅客ID")
        private Map<String, List<Integer>> pendingTicketPaxId;

        @ApiModelProperty(value = "元素编号列表")
        private List<Integer> elementNbrList;

        @ApiModelProperty(value = "国际标识")
        private Boolean international;

        @ApiModelProperty(value = "PNR信息")
        private PnrInfo pnrInfo;
    }

    /**
     * PNR信息
     */
    @Data
    public static class PnrInfo {
        @ApiModelProperty(value = "FC信息")
        private List<FareInfo> fcInfos;

        @ApiModelProperty(value = "FN信息")
        private List<FareInfo> fnInfos;

        @ApiModelProperty(value = "FP信息")
        private List<String> fpInfos;

        @ApiModelProperty(value = "EI信息")
        private List<String> eiInfos;

        @ApiModelProperty(value = "TC信息")
        private List<String> tcInfos;

        @ApiModelProperty(value = "OI信息")
        private List<Object> oiInfos;

        @ApiModelProperty(value = "SVC信息")
        private List<Object> svcInfos;
    }

    /**
     * 运价信息
     */
    @Data
    public static class FareInfo {
        @ApiModelProperty(value = "文本（Base64编码）")
        private String text;

        @ApiModelProperty(value = "是否婴儿")
        private Boolean infSign;
    }

    /**
     * 航段信息
     */
    @Data
    public static class Segment {
        @ApiModelProperty(value = "操作类型")
        private String operateType;

        @ApiModelProperty(value = "座位数")
        private Integer seatNumber;

        @ApiModelProperty(value = "类型")
        private String type;

        @ApiModelProperty(value = "航班号")
        private String flightNo;

        @ApiModelProperty(value = "舱位")
        private String cabin;

        @ApiModelProperty(value = "出发机场")
        private String departureAirport;

        @ApiModelProperty(value = "到达机场")
        private String arrivalAirport;

        @ApiModelProperty(value = "行动代码")
        private String actionCode;

        @ApiModelProperty(value = "出发日期时间")
        private String departureDateTime;

        @ApiModelProperty(value = "删除索引列表")
        private List<Integer> deleteIndexList;
    }

    /**
     * 自动运价
     */
    @Data
    public static class AutoFare {
        @ApiModelProperty(value = "操作类型")
        private String operateType;

        @ApiModelProperty(value = "国内自动运价")
        private DomesticAutoFare domesticAutoFare;

        @ApiModelProperty(value = "价格")
        private Price price;

        @ApiModelProperty(value = "")
        private Boolean frontDeleteTag;

        @ApiModelProperty(value = "内部团队自动运价")
        private InternalGroupAutoFare internalGroupAutoFareDTO;

        @ApiModelProperty(value = "删除索引列表")
        private List<Integer> deleteIndexList;
    }

    /**
     * 国内自动运价
     */
    @Data
    public static class DomesticAutoFare {
        @ApiModelProperty(value = "MU标识")
        private Boolean muFlag;
    }

    /**
     * 价格
     */
    @Data
    public static class Price {
        // 参考BookPnrDto.Price的结构
        @ApiModelProperty(value = "协议运价代码")
        private String negotiatedFareCode;

        @ApiModelProperty(value = "基本运价")
        private String fareBasic;

        @ApiModelProperty(value = "")
        private Boolean queryExclusiveNegotiatedFare;

        @ApiModelProperty(value = "")
        private String fareType;

        @ApiModelProperty(value = "")
        private String currencyCode;

        @ApiModelProperty(value = "出票航司")
        private String issuingAirline;

        @ApiModelProperty(value = "")
        private Boolean calculateLowestPrice;

        @ApiModelProperty(value = "")
        private Boolean calculateAllBrand;

        @ApiModelProperty(value = "")
        private String payMethod;

        @ApiModelProperty(value = "")
        private String airportCode;

        @ApiModelProperty(value = "价格项目")
        private List<PriceItem> priceItems;

        @ApiModelProperty(value = "")
        private Boolean chdUsingAdtPrice;

        @ApiModelProperty(value = "")
        private String singlePsgType;

        @ApiModelProperty(value = "")
        private Boolean change;

        @ApiModelProperty(value = "")
        private String discountCode;
    }

    /**
     * 价格项目
     */
    @Data
    public static class PriceItem {
        @ApiModelProperty(value = "旅客类型")
        private String passengerType;

        @ApiModelProperty(value = "旅客ID")
        private List<Integer> passengerId;

        @ApiModelProperty(value = "票面价")
        private String ticketAmount;

        @ApiModelProperty(value = "总价")
        private String totalAmount;

        @ApiModelProperty(value = "货币")
        private String currency;

        @ApiModelProperty(value = "燃油费")
        private String fuel;

        @ApiModelProperty(value = "基金")
        private String fund;

        @ApiModelProperty(value = "税率")
        private String commissionRate;

        @ApiModelProperty(value = "")
        private List<PriceItemSegmentInfo> segmentInfoList;

        @ApiModelProperty(value = "")
        private List<PriceItemBaggageWithSegIndex> baggageWithSegIndex;

        @ApiModelProperty(value = "")
        private String passengerTypeUpdate;

        @ApiModelProperty(value = "")
        private PriceItemQuery query;
    }

    /**
     * 内部团队自动运价
     */
    @Data
    public static class InternalGroupAutoFare {
        @ApiModelProperty(value = "团队计划")
        private Boolean teamSchedule;

        @ApiModelProperty(value = "有多个旅客")
        private Boolean hasMultiplePassengers;
    }

    /**
     * 运价航段信息
     */
    @Data
    public static class PriceItemSegmentInfo {
        @ApiModelProperty(value = "运价舱位")
        private String fareBasisCodes;

        @ApiModelProperty(value = "销售航司")
        private String companyCode;
    }

    /**
     * 运价行李信息
     */
    @Data
    public static class PriceItemBaggageWithSegIndex {
        @ApiModelProperty(value = "")
        private String baggage;

        @ApiModelProperty(value = "")
        private List<Integer> segments;
    }

    /**
     * 旅客航段TKNE信息
     */
    @Data
    public static class OperatePassengerSegmentTkne {
        @ApiModelProperty(value = "")
        private String content;

        @ApiModelProperty(value = "")
        private String lineIndex;
    }

    /**
     * INFT
     */
    @Data
    public static class Inft {
        @ApiModelProperty(value = "")
        private String actionCode;

        @ApiModelProperty(value = "")
        private String seg;
    }

    /**
     * 旅客航段特服信息
     */
    @Data
    public static class OperatePassengerSegmentService {
        @ApiModelProperty(value = "")
        private String actionCode;

        @ApiModelProperty(value = "")
        private String lineIndex;

        @ApiModelProperty(value = "")
        private String currency;

        @ApiModelProperty(value = "")
        private String description;

        @ApiModelProperty(value = "")
        private String emdNo;

        @ApiModelProperty(value = "")
        private String payAmount;

        @ApiModelProperty(value = "")
        private String ssrCode;
    }

    /**
     *
     */
    @Data
    public static class PriceItemQuery {
        @ApiModelProperty(value = "")
        private String negotiatedFareCode;

        @ApiModelProperty(value = "")
        private String fareBasic;

        @ApiModelProperty(value = "")
        private Boolean queryExclusiveNegotiatedFare;

        @ApiModelProperty(value = "")
        private String fareType;

        @ApiModelProperty(value = "")
        private String currencyCode;

        @ApiModelProperty(value = "")
        private String issuingAirline;

        @ApiModelProperty(value = "")
        private Boolean calculateLowestPrice;

        @ApiModelProperty(value = "")
        private Boolean calculateAllBrand;

        @ApiModelProperty(value = "")
        private String airportCode;

        @ApiModelProperty(value = "")
        private String brand;

        @ApiModelProperty(value = "")
        private String baggage;

        @ApiModelProperty(value = "")
        private String updatePrice;

        @ApiModelProperty(value = "")
        private String altTicketingDateTime;

        @ApiModelProperty(value = "")
        private String payMethod;

        @ApiModelProperty(value = "")
        private String discountCode;
    }
}
