package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/10 10:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryVisaDto", description = "签证查询传输对象")
public class QueryVisaDto {

    @ApiModelProperty(value = "查询类型")
    private String queryType;

    @ApiModelProperty(value = "国籍")
    private String naNationality;

    @ApiModelProperty(value = "外派国家")
    private String arExpatriateCountries;

    @ApiModelProperty(value = "旅游出发点")
    private String emTourDeparturePoint;

    @ApiModelProperty(value = "目的地")
    private String deDestinations;

    @ApiModelProperty(value = "中转点")
    private String trTransitPoint;

    @ApiModelProperty(value = "最近访问过")
    private String vtRecentlyVisited;

    @ApiModelProperty(value = "城市代码")
    private String cityCode;

    @ApiModelProperty(value = "国家相关")
    private String countryRelated;

    @ApiModelProperty(value = "特定项目")
    private String specificItems;

    @ApiModelProperty(value = "项目")
    private String tiItems;
}
