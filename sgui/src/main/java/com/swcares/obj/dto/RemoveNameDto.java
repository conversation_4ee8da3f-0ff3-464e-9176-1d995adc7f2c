package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 删除旅客请求DTO
 *
 * <AUTHOR>
 * @date 2025/1/15 10:30
 */
@Data
@ApiModel(value = "RemoveNameDto", description = "删除旅客请求DTO")
public class RemoveNameDto {

    @ApiModelProperty(value = "计数")
    private Integer count;

    @ApiModelProperty(value = "PNR编号")
    private String pnr;

    @ApiModelProperty(value = "要删除的旅客列表")
    private List<Traveller> travellers;

    @ApiModelProperty(value = "要删除的婴儿列表")
    private List<Infant> infants;

    @ApiModelProperty(value = "封口类型")
    private String envelopType;

    @Data
    @ApiModel(value = "Traveller", description = "旅客信息")
    public static class Traveller {
        @ApiModelProperty(value = "旅客姓名")
        private String fullName;

        @ApiModelProperty(value = "无陪儿童")
        private String unMinor;

        @ApiModelProperty(value = "无陪儿童年龄")
        private Integer unMinorAge;

        @ApiModelProperty(value = "旅客ID（对应psgIndex）")
        private Integer paxId;
    }

    @Data
    @ApiModel(value = "Infant", description = "婴儿信息")
    public static class Infant {
        @ApiModelProperty(value = "婴儿姓名")
        private String infantName;

        @ApiModelProperty(value = "成人旅客索引（对应psgIndex）")
        private String adaultIndex;
    }
}
