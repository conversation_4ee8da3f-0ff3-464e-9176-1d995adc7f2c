package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/15
 */
@Data
@ApiModel(value = "AirCancelPorDto", description = "删除PNR中添加的航段请求参数")
public class AirCancelPorDto {
    
    @ApiModelProperty(value = "航段列表")
    private List<Segment> segs;
    
    @Data
    @ApiModel(value = "Segment", description = "航段信息")
    public static class Segment {
        
        @ApiModelProperty(value = "舱位等级")
        private String fltClass;
        
        @ApiModelProperty(value = "出发城市")
        private String orgCity;
        
        @ApiModelProperty(value = "目的城市")
        private String desCity;
        
        @ApiModelProperty(value = "出发时间")
        private String departureTime;
        
        @ApiModelProperty(value = "操作代码")
        private String actionCode;
        
        @ApiModelProperty(value = "航空公司代码和航班号")
        private String airCode;
        
        @ApiModelProperty(value = "票号")
        private String tktNum;
        
        @ApiModelProperty(value = "行索引")
        private String lineIndex;
    }
}
