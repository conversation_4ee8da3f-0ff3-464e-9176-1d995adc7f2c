package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 代理人销售日报请求DTO
 *
 * <AUTHOR>
 * @date 2025/1/2 11:00
 */
@Data
@ApiModel(value = "代理人销售日报请求DTO")
public class CrsDailySalesDto {

    @ApiModelProperty(value = "查询日期")
    private String date;

    @ApiModelProperty(value = "打票机序号")
    private String deviceNumber;

    @ApiModelProperty(value = "票证类型")
    private String tktType;

    @ApiModelProperty(value = "销售状态代码列表")
    private List<String> saleStatusCodes;

    @ApiModelProperty(value = "查询类型：1-正常票，2-异常票")
    private String value;

    @ApiModelProperty(value = "外部标识")
    private Boolean outerSign;
}
