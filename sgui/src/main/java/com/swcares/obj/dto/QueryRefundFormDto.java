package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询退票单号请求DTO
 *
 * <AUTHOR>
 * @date 2025/1/2 19:00
 */
@Data
@ApiModel(value = "查询退票单号请求DTO")
public class QueryRefundFormDto {

    @ApiModelProperty(value = "票号")
    private String ticketNo;

    @ApiModelProperty(value = "票证类型")
    private String ticketType;

    @ApiModelProperty(value = "打票机号")
    private String printerNo;

    @ApiModelProperty(value = "票证管理机构代码")
    private String ticketManagementOrganizationCode;

    @ApiModelProperty(value = "二次验证因素")
    private SecondFactor secondFactor;

    /**
     * 二次验证因素
     */
    @Data
    @ApiModel(value = "二次验证因素")
    public static class SecondFactor {
        @ApiModelProperty(value = "二次验证因素代码")
        private String secondFactorCode;

        @ApiModelProperty(value = "二次验证因素值")
        private String secondFactorValue;
    }
}
