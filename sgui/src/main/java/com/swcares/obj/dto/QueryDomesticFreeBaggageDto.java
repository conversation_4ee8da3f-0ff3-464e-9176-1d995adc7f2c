package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryDomesticFreeBaggageDto", description = "国内免费行李额查询传输对象")
public class QueryDomesticFreeBaggageDto implements Serializable {

    @ApiModelProperty(value = "航段信息列表")
    private List<SegmentInfo> segmentInfos;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "SegmentInfo", description = "航段信息")
    public static class SegmentInfo implements Serializable {
        
        @ApiModelProperty(value = "是否共享航班")
        private Boolean codeshareInd;
        
        @ApiModelProperty(value = "共享类型")
        private String codeshareType;
        
        @ApiModelProperty(value = "出发日期")
        private String departureDate;
        
        @ApiModelProperty(value = "出发时间")
        private String departureTime;
        
        @ApiModelProperty(value = "运价基础")
        private String fareBasis;
        
        @ApiModelProperty(value = "市场航空公司")
        private String mcAirline;
        
        @ApiModelProperty(value = "市场航班舱位")
        private String mcRbd;
        
        @ApiModelProperty(value = "承运航空公司")
        private String ocAirline;
        
        @ApiModelProperty(value = "承运航班舱位")
        private String ocRbd;
        
        @ApiModelProperty(value = "旅客类型")
        private String passengerType;
    }
}
