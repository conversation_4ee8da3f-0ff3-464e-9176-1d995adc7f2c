package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/4/25 14:05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QuerySkDto", description = "航班查询Sk传输对象")
public class QuerySkDto {

    @ApiModelProperty(value = "航司")
    private String airCode;

    @ApiModelProperty(value = "联盟")
    private String alliance;

    @ApiModelProperty(value = "到达航站或城市")
    @NotNull(message = "到达航站必填")
    private String arriveCity;

    @ApiModelProperty(value = "出发航站或城市")
    @NotNull(message = "出发航站必填")
    private String departureCity;

    @ApiModelProperty(value = "出发日期")
    @NotNull(message = "出发日期必填")
    private String departureDate;

    @ApiModelProperty(value = "出发时间")
    private String departureTime;

    @ApiModelProperty(value = "仅直飞")
    private String onlyDirect;

    @ApiModelProperty(value = "中转点")
    private String transitCity;

    @ApiModelProperty(value = "非共享")
    private Boolean unsharedFlight;
}
