package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/23 15:29
 */
@Data
@ApiModel(value = "MenuVo", description = "MenuVo")
public class MenuVo implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "code")
    private String code;

    @ApiModelProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "menuEnName")
    private String menuEnName;

    @ApiModelProperty(value = "title")
    private String title;

    @ApiModelProperty(value = "type")
    private String type;

    @ApiModelProperty(value = "value")
    private String value;

    @ApiModelProperty(value = "url")
    private String url;

    @ApiModelProperty(value = "target")
    private String target;

    @ApiModelProperty(value = "options")
    private String options;

    @ApiModelProperty(value = "seq")
    private Integer seq;

    @ApiModelProperty(value = "isLeaf")
    private String isLeaf;

    @ApiModelProperty(value = "isSys")
    private String isSys;

    @ApiModelProperty(value = "inUse")
    private String inUse;

    @ApiModelProperty(value = "isVisible")
    private String isVisible;

    @ApiModelProperty(value = "parentId")
    private Long parentId;

    @ApiModelProperty(value = "menuLevel")
    private Integer menuLevel;

    @ApiModelProperty(value = "level")
    private Integer level;

    @ApiModelProperty(value = "path")
    private String path;

    @ApiModelProperty(value = "field1")
    private String field1;

    @ApiModelProperty(value = "field2")
    private String field2;

    @ApiModelProperty(value = "field3")
    private String field3;

    @ApiModelProperty(value = "field2")
    private String field4;

    @ApiModelProperty(value = "urlNew")
    private String urlNew;
}
