package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/16 11:54
 */
@Data
@ApiModel(value = "FirstLoginVo", description = "用户登录信息")
public class FirstLoginVo implements Serializable {

    @ApiModelProperty(value = "agentAbroad")
    private Boolean agentAbroad;

    @ApiModelProperty(value = "endUser")
    private String endUser;

    @ApiModelProperty(value = "SI登录产生的token")
    private String iamToken;

    @ApiModelProperty(value = "kickOutEnable")
    private Boolean kickOutEnable;

    @ApiModelProperty(value = "securityLevel")
    private Integer securityLevel;

    @ApiModelProperty(value = "token")
    private String token;
}
