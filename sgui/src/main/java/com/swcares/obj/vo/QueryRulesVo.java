package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryRulesVo", description = "查询规则返回对象")
public class QueryRulesVo implements Serializable {

    @ApiModelProperty(value = "航班规则列表")
    private List<FlightRule> flightRules;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "FlightRule", description = "航班规则")
    public static class FlightRule implements Serializable {
        
        @ApiModelProperty(value = "旅客类型")
        private String passType;
        
        @ApiModelProperty(value = "规则列表")
        private List<Rule> rules;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "Rule", description = "规则")
    public static class Rule implements Serializable {
        
        @ApiModelProperty(value = "出发机场")
        private String departureAirport;
        
        @ApiModelProperty(value = "出发机场城市中文名")
        private String departureAirportCityCh;
        
        @ApiModelProperty(value = "到达机场")
        private String arrivalAirport;
        
        @ApiModelProperty(value = "到达机场城市中文名")
        private String arrivalAirportCityCh;
        
        @ApiModelProperty(value = "规则信息")
        private RuleInfos ruleInfos;
        
        @ApiModelProperty(value = "运价规则信息列表")
        private List<FareRuleInfo> fareRuleInfos;
        
        @ApiModelProperty(value = "翻译后的规则信息")
        private Object translatedRuleInfo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "RuleInfos", description = "规则信息")
    public static class RuleInfos implements Serializable {
        
        @ApiModelProperty(value = "停留")
        private Object stay;
        
        @ApiModelProperty(value = "退票")
        private List<String> refund;
        
        @ApiModelProperty(value = "变更")
        private List<String> change;
        
        @ApiModelProperty(value = "误机")
        private List<String> noShow;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "FareRuleInfo", description = "运价规则信息")
    public static class FareRuleInfo implements Serializable {
        
        @ApiModelProperty(value = "标题")
        private String title;
        
        @ApiModelProperty(value = "代码")
        private String code;
        
        @ApiModelProperty(value = "内容列表")
        private List<String> contents;
        
        @ApiModelProperty(value = "编号")
        private String number;
    }
}
