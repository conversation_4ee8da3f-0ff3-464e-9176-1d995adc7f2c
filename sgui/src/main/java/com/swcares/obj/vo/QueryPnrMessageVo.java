package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 退票PNR信息查询响应VO
 *
 * <AUTHOR>
 * @date 2025/1/2 14:00
 */
@Data
@ApiModel(value = "退票PNR信息查询响应VO")
public class QueryPnrMessageVo {

    @ApiModelProperty(value = "旅客信息列表")
    private List<Passenger> passengers;

    @ApiModelProperty(value = "航班信息列表")
    private List<Flight> flights;

    @ApiModelProperty(value = "票证类型代码")
    private String ticketTypeCode;

    @ApiModelProperty(value = "PNR编号")
    private String pnrNo;

    @ApiModelProperty(value = "CRS PNR编号")
    private String csrPnrNo;

    /**
     * 旅客信息
     */
    @Data
    @ApiModel(value = "旅客信息")
    public static class Passenger {
        @ApiModelProperty(value = "旅客姓名")
        private String passengerName;

        @ApiModelProperty(value = "旅客姓名后缀")
        private String passengerNameSuffix;

        @ApiModelProperty(value = "旅客类型")
        private String passengerType;

        @ApiModelProperty(value = "PNR旅客类型")
        private String pnrPsgType;

        @ApiModelProperty(value = "特殊旅客类型")
        private String specialPassengerType;

        @ApiModelProperty(value = "旅客序号")
        private String index;

        @ApiModelProperty(value = "婴儿信息")
        private Infant infants;

        @ApiModelProperty(value = "票号列表")
        private List<String> ticketNos;

        @ApiModelProperty(value = "二次验证因素")
        private SecondFactor secondFactor;
    }

    /**
     * 婴儿信息
     */
    @Data
    @ApiModel(value = "婴儿信息")
    public static class Infant {
        @ApiModelProperty(value = "婴儿姓名")
        private String infantName;

        @ApiModelProperty(value = "婴儿姓名后缀")
        private String infantNameSuffix;

        @ApiModelProperty(value = "婴儿类型")
        private String infantType;

        @ApiModelProperty(value = "票号列表")
        private List<String> ticketNos;

        @ApiModelProperty(value = "二次验证因素")
        private SecondFactor secondFactor;
    }

    /**
     * 航班信息
     */
    @Data
    @ApiModel(value = "航班信息")
    public static class Flight {
        @ApiModelProperty(value = "航班号")
        private String flightNo;

        @ApiModelProperty(value = "航空公司")
        private String airline;

        @ApiModelProperty(value = "舱位")
        private String cabin;

        @ApiModelProperty(value = "运营航空公司")
        private String operationAirline;

        @ApiModelProperty(value = "出发时间")
        private String departureTime;

        @ApiModelProperty(value = "出发机场代码")
        private String departureCode;

        @ApiModelProperty(value = "出发航站楼")
        private String departureTerminal;

        @ApiModelProperty(value = "到达时间")
        private String arrivalTime;

        @ApiModelProperty(value = "到达机场代码")
        private String arrivalCode;

        @ApiModelProperty(value = "到达航站楼")
        private String arrivalTerminal;

        @ApiModelProperty(value = "行动代码")
        private String actionCode;

        @ApiModelProperty(value = "票号")
        private String tktNum;

        @ApiModelProperty(value = "行索引")
        private String lineIndex;

        @ApiModelProperty(value = "代码共享")
        private Boolean codeShare;

        @ApiModelProperty(value = "到达天数")
        private String arrDays;

        @ApiModelProperty(value = "票号信息列表")
        private List<TicketNoInfo> ticketNos;
    }

    /**
     * 票号信息
     */
    @Data
    @ApiModel(value = "票号信息")
    public static class TicketNoInfo {
        @ApiModelProperty(value = "序号")
        private String number;

        @ApiModelProperty(value = "票号")
        private String ticketNo;
    }

    /**
     * 二次验证因素
     */
    @Data
    @ApiModel(value = "二次验证因素")
    public static class SecondFactor {
        @ApiModelProperty(value = "二次验证因素代码")
        private String secondFactorCode;

        @ApiModelProperty(value = "二次验证因素值")
        private String secondFactorValue;
    }
}
