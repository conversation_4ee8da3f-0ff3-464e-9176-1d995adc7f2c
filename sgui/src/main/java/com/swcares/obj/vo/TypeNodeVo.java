package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/18 14:22
 */
@Data
@ApiModel(value = "TypeNodeVo", description = "机构节点")
public class TypeNodeVo implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "code")
    private String code;

    @ApiModelProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "seq")
    private Integer seq;

    @ApiModelProperty(value = "status")
    private String status;

    @ApiModelProperty(value = "lastDate")
    private String lastDate;

    @ApiModelProperty(value = "lastOpertor")
    private String lastOpertor;

    @ApiModelProperty(value = "path")
    private String path;

    @ApiModelProperty(value = "isSys")
    private String isSys;

    @ApiModelProperty(value = "operatorId")
    private Long operatorId;

    @ApiModelProperty(value = "parentId")
    private Long parentId;

    @ApiModelProperty(value = "isLeaf")
    private String isLeaf;
}
