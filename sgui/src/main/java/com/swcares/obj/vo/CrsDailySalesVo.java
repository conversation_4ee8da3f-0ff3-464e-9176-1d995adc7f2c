package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 代理人销售日报响应VO
 *
 * <AUTHOR>
 * @date 2025/1/2 11:00
 */
@Data
@ApiModel(value = "代理人销售日报响应VO")
public class CrsDailySalesVo {

    @ApiModelProperty(value = "办公室号")
    private String office;

    @ApiModelProperty(value = "IATA代码")
    private String iata;

    @ApiModelProperty(value = "票务明细列表")
    private List<TicketItem> items;

    @ApiModelProperty(value = "过滤器信息")
    private FilterInfo filter;

    @ApiModelProperty(value = "出票异常数量")
    private Integer issueErrorNumber;

    @ApiModelProperty(value = "退票异常数量")
    private Integer refundErrorNumber;

    @ApiModelProperty(value = "总异常数量")
    private Integer totalErrorNumber;

    @Data
    @ApiModel(value = "票务明细")
    public static class TicketItem {
        @ApiModelProperty(value = "票号")
        private String ticket;

        @ApiModelProperty(value = "PNR编号")
        private String pnr;

        @ApiModelProperty(value = "支付类型")
        private String payType;

        @ApiModelProperty(value = "工作号")
        private String jobNo;

        @ApiModelProperty(value = "航空公司")
        private String airline;

        @ApiModelProperty(value = "打印号")
        private String prntNo;

        @ApiModelProperty(value = "目的地到达地")
        private String desArr;

        @ApiModelProperty(value = "销售时间")
        private String salesTime;

        @ApiModelProperty(value = "销售日期")
        private String salesDate;

        @ApiModelProperty(value = "代理费")
        private String agencyFee;

        @ApiModelProperty(value = "其他扣除")
        private String otherDeduction;

        @ApiModelProperty(value = "服务费")
        private String serviceCharge;

        @ApiModelProperty(value = "税费金额")
        private String taxAmount;

        @ApiModelProperty(value = "客票类型代码")
        private String ticketTypeCode;

        @ApiModelProperty(value = "代理费率")
        private String agencyFeePercent;

        @ApiModelProperty(value = "客票类型")
        private String ticketType;

        @ApiModelProperty(value = "货币类型")
        private String currencyType;

        @ApiModelProperty(value = "客票状态")
        private String ticketStatus;

        @ApiModelProperty(value = "非自愿标识")
        private String involuntaryIdentification;

        @ApiModelProperty(value = "OB税")
        private String obTax;

        @ApiModelProperty(value = "联票号")
        private String couponNo;

        @ApiModelProperty(value = "票面价")
        private String amount;

        @ApiModelProperty(value = "退票号")
        private String refundNo;
    }

    @Data
    @ApiModel(value = "过滤器信息")
    public static class FilterInfo {
        @ApiModelProperty(value = "航空公司列表")
        private List<String> airlines;

        @ApiModelProperty(value = "货币类型列表")
        private List<String> currencyTypes;

        @ApiModelProperty(value = "工作号列表")
        private List<String> jobNos;

        @ApiModelProperty(value = "票证种类列表")
        private List<String> ticketKinds;

        @ApiModelProperty(value = "票证类型列表")
        private List<String> ticketTypes;

        @ApiModelProperty(value = "支付类型列表")
        private List<String> payTypes;

        @ApiModelProperty(value = "打印号列表")
        private List<String> prntNos;
    }
}
