package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 初始加载退票信息响应VO
 *
 * <AUTHOR>
 * @date 2025/1/2 12:00
 */
@Data
@ApiModel(value = "初始加载退票信息响应VO")
public class FindRefundTicketVo {

    @ApiModelProperty(value = "票务信息")
    private TicketInfo ticket;

    @ApiModelProperty(value = "办公室号")
    private String office;

    @ApiModelProperty(value = "IATA代码")
    private String iata;

    @ApiModelProperty(value = "代理人工作号")
    private String agent;

    @ApiModelProperty(value = "操作员工作号")
    private String operator;

    @ApiModelProperty(value = "联票标识")
    private String conjunction;

    @ApiModelProperty(value = "票务管理组织代码")
    private String ticketManagementOrganizationCode;

    @ApiModelProperty(value = "打票机号")
    private String printerNo;

    @ApiModelProperty(value = "退票打印号")
    private String refundPrintNumber;

    @ApiModelProperty(value = "收据已打印标识")
    private String receiptPrinted;

    /**
     * 票务信息
     */
    @Data
    @ApiModel(value = "票务信息")
    public static class TicketInfo {
        @ApiModelProperty(value = "票类型")
        private String tktType;

        @ApiModelProperty(value = "CDS票标识")
        private Boolean cdsTicket;

        @ApiModelProperty(value = "票号")
        private String ticketNo;

        @ApiModelProperty(value = "旅客类型")
        private String psgType;

        @ApiModelProperty(value = "票面旅客类型")
        private String ticketPsgType;

        @ApiModelProperty(value = "旅客姓名")
        private String name;

        @ApiModelProperty(value = "旅客姓名后缀")
        private String passengerNameSuffix;

        @ApiModelProperty(value = "特殊旅客类型")
        private String specialPassengerType;

        @ApiModelProperty(value = "支付类型")
        private String payType;

        @ApiModelProperty(value = "货币类型")
        private String currency;

        @ApiModelProperty(value = "ET标识")
        private String etTag;

        @ApiModelProperty(value = "航空公司")
        private String airline;

        @ApiModelProperty(value = "航段信息")
        private List<SegmentInfo> segment;

        @ApiModelProperty(value = "PNR编号")
        private String pnr;

        @ApiModelProperty(value = "CRS PNR编号")
        private String crsPnrNo;

        @ApiModelProperty(value = "联票标识")
        private String isCoupon;

        @ApiModelProperty(value = "承运航空公司")
        private String marketAirline;

        @ApiModelProperty(value = "机场控制标识")
        private String isAirportCntl;

        @ApiModelProperty(value = "总税费")
        private String totalTaxs;

        @ApiModelProperty(value = "税费明细")
        private List<TaxInfo> taxs;

        @ApiModelProperty(value = "总金额")
        private String totalAmount;

        @ApiModelProperty(value = "改签票号")
        private String exchangeTktNo;

        @ApiModelProperty(value = "政府采购标识")
        private Boolean governmentPurchase;

        @ApiModelProperty(value = "佣金")
        private String commission;

        @ApiModelProperty(value = "佣金率")
        private String commissionRate;

        @ApiModelProperty(value = "二次验证因素")
        private SecondFactor secondFactor;
    }

    /**
     * 航段信息
     */
    @Data
    @ApiModel(value = "航段信息")
    public static class SegmentInfo {
        @ApiModelProperty(value = "航空公司")
        private String airline;

        @ApiModelProperty(value = "是否可用")
        private String isAble;

        @ApiModelProperty(value = "出发机场代码")
        private String departureCode;

        @ApiModelProperty(value = "到达机场代码")
        private String arriveCode;

        @ApiModelProperty(value = "RPH")
        private String rph;

        @ApiModelProperty(value = "是否选中")
        private String isCheck;

        @ApiModelProperty(value = "票标识")
        private String tktTag;

        @ApiModelProperty(value = "E8 RPH")
        private String e8Rph;

        @ApiModelProperty(value = "票状态")
        private String ticketStatus;

        @ApiModelProperty(value = "航班号")
        private String flightNo;

        @ApiModelProperty(value = "舱位代码")
        private String cabinCode;

        @ApiModelProperty(value = "出发日期")
        private String departureDate;

        @ApiModelProperty(value = "出发时间")
        private String departureTime;

        @ApiModelProperty(value = "航段类型")
        private String segmentType;
    }

    /**
     * 税费信息
     */
    @Data
    @ApiModel(value = "税费信息")
    public static class TaxInfo {
        @ApiModelProperty(value = "税费名称")
        private String name;

        @ApiModelProperty(value = "税费金额")
        private String value;
    }

    /**
     * 二次验证因素
     */
    @Data
    @ApiModel(value = "二次验证因素")
    public static class SecondFactor {
        @ApiModelProperty(value = "二次验证因素代码")
        private String secondFactorCode;

        @ApiModelProperty(value = "二次验证因素值")
        private String secondFactorValue;
    }
}
