package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/11 06:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryTimeDiffVo", description = "时差查询返回对象")
public class QueryTimeDiffVo implements Serializable {

    @ApiModelProperty(value = "位置代码")
    private String location;

    @ApiModelProperty(value = "时差")
    private String timeDifference;

    @ApiModelProperty(value = "12小时制时间")
    private String time12Hr;

    @ApiModelProperty(value = "24小时制时间")
    private String time24Hr;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "GMT时间")
    private String gmt;

    @ApiModelProperty(value = "GMT时差")
    private String gmtTimeDiff;

    @ApiModelProperty(value = "位置中文名")
    private String locationCnName;
}
