package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/10 11:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AssistQueryVo", description = "辅助查询返回对象")
public class AssistQueryVo implements Serializable {

    @ApiModelProperty(value = "是否有IET")
    private String hasIET;

    @ApiModelProperty(value = "IET信息")
    private Boolean iet;

    @ApiModelProperty(value = "FSM列表")
    private List<FsmInfo> fsmList;

    @ApiModelProperty(value = "子MCT信息列表")
    private List<Object> submctInfoList;

    @ApiModelProperty(value = "航线结果")
    private Object airRoutRs;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "FsmInfo", description = "FSM信息")
    public static class FsmInfo implements Serializable {

        @ApiModelProperty(value = "票点里程")
        private String ticketPointMileage;

        @ApiModelProperty(value = "累计票点里程")
        private String cumulativeTPM;

        @ApiModelProperty(value = "最大允许里程")
        private String maximumPermittedMileage;

        @ApiModelProperty(value = "下一个更高级别的里程")
        private String nextHigherLevelMileage;

        @ApiModelProperty(value = "下一个更低级别的里程")
        private String nextLowerLevelMileage;

        @ApiModelProperty(value = "全球指示器")
        private String globalIndicator;

        @ApiModelProperty(value = "出发地")
        private String departureLocation;

        @ApiModelProperty(value = "到达地")
        private String arrivalLocation;

        @ApiModelProperty(value = "超额里程附加费级别")
        private String excessMileageSurchargeLvl;

        @ApiModelProperty(value = "25M")
        private String twentyFiveM;

        @ApiModelProperty(value = "额外扣除金额")
        private String extraDeductionAmount;
    }
}
