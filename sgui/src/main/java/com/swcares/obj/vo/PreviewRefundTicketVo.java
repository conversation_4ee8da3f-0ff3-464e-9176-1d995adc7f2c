package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 退前预览响应VO
 *
 * <AUTHOR>
 * @date 2025/1/14 15:30
 */
@Data
@ApiModel(value = "PreviewRefundTicketVo", description = "退前预览响应VO")
public class PreviewRefundTicketVo {

    @ApiModelProperty(value = "退票订单信息")
    private RefundTicketOrder refundTicketOrder;

    @ApiModelProperty(value = "退票计算信息")
    private RefundCompute refundCompute;

    @ApiModelProperty(value = "二次验证信息")
    private SecondFactor secondFactor;

    /**
     * 退票订单信息
     */
    @Data
    public static class RefundTicketOrder {
        @ApiModelProperty(value = "票信息")
        private Ticket ticket;

        @ApiModelProperty(value = "Office")
        private String office;

        @ApiModelProperty(value = "IATA")
        private String iata;

        @ApiModelProperty(value = "代理人")
        private String agent;

        @ApiModelProperty(value = "操作员")
        private String operator;

        @ApiModelProperty(value = "联程")
        private String conjunction;

        @ApiModelProperty(value = "票务管理组织代码")
        private String ticketManagementOrganizationCode;

        @ApiModelProperty(value = "打票机号")
        private String printerNo;

        @ApiModelProperty(value = "退票打印号")
        private String refundPrintNumber;

        @ApiModelProperty(value = "收据已打印")
        private String receiptPrinted;
    }

    /**
     * 票信息
     */
    @Data
    public static class Ticket {
        @ApiModelProperty(value = "票类型")
        private String tktType;

        @ApiModelProperty(value = "CDS票")
        private Boolean cdsTicket;

        @ApiModelProperty(value = "票号")
        private String ticketNo;

        @ApiModelProperty(value = "旅客类型")
        private String psgType;

        @ApiModelProperty(value = "票面旅客类型")
        private String ticketPsgType;

        @ApiModelProperty(value = "姓名")
        private String name;

        @ApiModelProperty(value = "旅客姓名后缀")
        private String passengerNameSuffix;

        @ApiModelProperty(value = "特殊旅客类型")
        private String specialPassengerType;

        @ApiModelProperty(value = "支付类型")
        private String payType;

        @ApiModelProperty(value = "货币")
        private String currency;

        @ApiModelProperty(value = "ET标签")
        private String etTag;

        @ApiModelProperty(value = "航空公司")
        private String airline;

        @ApiModelProperty(value = "航段列表")
        private List<Segment> segment;

        @ApiModelProperty(value = "PNR")
        private String pnr;

        @ApiModelProperty(value = "CRS PNR号")
        private String crsPnrNo;

        @ApiModelProperty(value = "是否联程票")
        private String isCoupon;

        @ApiModelProperty(value = "市场航空公司")
        private String marketAirline;

        @ApiModelProperty(value = "是否机场控制")
        private String isAirportCntl;

        @ApiModelProperty(value = "总税费")
        private String totalTaxs;

        @ApiModelProperty(value = "税费列表")
        private List<Tax> taxs;

        @ApiModelProperty(value = "总金额")
        private String totalAmount;

        @ApiModelProperty(value = "换票票号")
        private String exchangeTktNo;

        @ApiModelProperty(value = "政府采购")
        private Boolean governmentPurchase;

        @ApiModelProperty(value = "代理费")
        private String commission;

        @ApiModelProperty(value = "代理费率")
        private String commissionRate;

        @ApiModelProperty(value = "二次验证信息")
        private SecondFactor secondFactor;
    }

    /**
     * 航段信息
     */
    @Data
    public static class Segment {
        @ApiModelProperty(value = "航空公司")
        private String airline;

        @ApiModelProperty(value = "是否可用")
        private String isAble;

        @ApiModelProperty(value = "出发代码")
        private String departureCode;

        @ApiModelProperty(value = "到达代码")
        private String arriveCode;

        @ApiModelProperty(value = "RPH")
        private String rph;

        @ApiModelProperty(value = "是否选中")
        private String isCheck;

        @ApiModelProperty(value = "票标签")
        private String tktTag;

        @ApiModelProperty(value = "E8 RPH")
        private String e8Rph;

        @ApiModelProperty(value = "票状态")
        private String ticketStatus;

        @ApiModelProperty(value = "航班号")
        private String flightNo;

        @ApiModelProperty(value = "舱位代码")
        private String cabinCode;

        @ApiModelProperty(value = "出发日期")
        private String departureDate;

        @ApiModelProperty(value = "出发时间")
        private String departureTime;

        @ApiModelProperty(value = "航段类型")
        private String segmentType;
    }

    /**
     * 税费信息
     */
    @Data
    public static class Tax {
        @ApiModelProperty(value = "税费名称")
        private String name;

        @ApiModelProperty(value = "税费值")
        private String value;
    }

    /**
     * 退票计算信息
     */
    @Data
    public static class RefundCompute {
        @ApiModelProperty(value = "备注")
        private String remark;

        @ApiModelProperty(value = "联程")
        private String conjunction;

        @ApiModelProperty(value = "金额信息")
        private Amount amount;

        @ApiModelProperty(value = "信用卡")
        private String creditCard;
    }

    /**
     * 金额信息
     */
    @Data
    public static class Amount {
        @ApiModelProperty(value = "代理费")
        private String commision;

        @ApiModelProperty(value = "总金额")
        private String totalAmount;

        @ApiModelProperty(value = "代理费率")
        private String commisionRate;

        @ApiModelProperty(value = "应退总计")
        private String netRefund;

        @ApiModelProperty(value = "其他扣除")
        private String otherDeduction;

        @ApiModelProperty(value = "税费列表")
        private List<Tax> taxs;

        @ApiModelProperty(value = "总税费")
        private String totalTaxs;

        @ApiModelProperty(value = "票号")
        private String ticketNo;
    }

    /**
     * 二次验证信息
     */
    @Data
    public static class SecondFactor {
        @ApiModelProperty(value = "二次验证代码")
        private String secondFactorCode;

        @ApiModelProperty(value = "二次验证值")
        private String secondFactorValue;
    }
}
