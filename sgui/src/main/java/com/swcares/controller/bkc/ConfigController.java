package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.PageDto;
import com.swcares.core.unified.PageVo;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.dto.ContentDto;
import com.swcares.obj.dto.EntryDto;
import com.swcares.obj.vo.EntryVo;
import com.swcares.obj.vo.TypeTreeVo;
import com.swcares.service.bkc.IConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/4/18 14:21
 */
@Slf4j
@Api(tags = "配置接口")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@RestController
@RequestMapping("/sgui-bkc/config")
public class ConfigController {

    @Resource
    private IConfigService iConfigService;

    @ApiOperation(value = "queryTypeTree", notes = "queryTypeTree")
    @GetMapping("/type/queryTypeTree")
    public SguiResult queryTypeTree() {
        TypeTreeVo vo = iConfigService.queryTypeTree();
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryEntryList", notes = "queryEntryList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = PageDto.class)
    })
    @PostMapping("/entry/queryEntryList")
    public SguiResult queryEntryList(@RequestBody PageDto<EntryDto> dto) {
        PageVo<EntryVo> vo = iConfigService.queryEntryList(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "getContentByCode", notes = "getContentByCode")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = ContentDto.class)
    })
    @PostMapping("/entry/getContentByCode")
    public SguiResult getContentByCode(@RequestBody ContentDto dto) {
        String result = iConfigService.getContentByCode(dto);
        return SguiResult.ok(null, result);
    }
}
