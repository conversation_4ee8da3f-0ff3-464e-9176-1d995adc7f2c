package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.vo.MicroappVo;
import com.swcares.service.bkc.IMicroappService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/23 15:46
 */
@Slf4j
@Api(tags = "microapp接口")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@RestController
@RequestMapping("/sgui-bkc/microapp")
public class MicroappController {

    @Resource
    private IMicroappService iMicroappService;

    @ApiOperation(value = "queryMicroAppNameEntryAndRuleList", notes = "queryMicroAppNameEntryAndRuleList")
    @GetMapping("/queryMicroAppNameEntryAndRuleList")
    public SguiResult queryMicroAppNameEntryAndRuleList() {
        List<MicroappVo> microappVoList = iMicroappService.queryMicroAppNameEntryAndRuleList();
        return SguiResult.ok(null, microappVoList);
    }
}
