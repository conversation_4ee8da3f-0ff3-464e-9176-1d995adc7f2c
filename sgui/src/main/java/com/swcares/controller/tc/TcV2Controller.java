package com.swcares.controller.tc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;
import com.swcares.service.tc.ITcV2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:16
 */
@Slf4j
@Api(tags = "TC V2接口")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/sgui-tc/v2")
public class TcV2Controller {

    @Resource
    private ITcV2Service iTcV2Service;

    @ApiOperation(value = "getETReceiptDictEntry", notes = "getETReceiptDictEntry的接口")
    @GetMapping("/itinerary/getETReceiptDictEntry")
    public SguiResult getETReceiptDictEntry() {
        return SguiResult.ok(null, true);
    }

    @ApiOperation(value = "getSguiRefresh", notes = "getSguiRefresh的接口")
    @GetMapping("/itinerary/getSguiRefresh")
    public SguiResult getSguiRefresh() {
        return SguiResult.ok(null, true);
    }

    @ApiOperation(value = "queryOfficeInformation", notes = "queryOfficeInformation的接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryOfficeInformationDto.class)
    })
    @PostMapping("/crs/office/queryOfficeInformation")
    public SguiResult queryOfficeInformation(@RequestBody QueryOfficeInformationDto dto) {
        QueryOfficeInformationVo vo = iTcV2Service.queryOfficeInformation(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryTicketDetail", notes = "按票号查询客票详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryTicketDetailDto.class)
    })
    @PostMapping("/crs/ticket/queryTicketDetail")
    public SguiResult queryTicketDetail(@RequestBody QueryTicketDetailDto dto) throws SguiResultException {
        QueryTicketDetailVo vo = iTcV2Service.queryTicketDetail(dto);
        return SguiResult.ok(null, Collections.singletonList(vo));
    }

    @ApiOperation(value = "queryTicketByDetr", notes = "通过DETR查询票面信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryTicketByDetrDto.class)
    })
    @PostMapping("/crs/ticket/queryTicketByDetr")
    public SguiResult queryTicketByDetr(@RequestBody QueryTicketByDetrDto dto) throws SguiResultException {
        QueryTicketByDetrVo vo = iTcV2Service.queryTicketByDetr(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryTicketByRtkt", notes = "通过RTKT查询票面信息")
    @GetMapping("/crs/ticket/queryTicketByRtkt/{ticketNo}")
    public SguiResult queryTicketByRtkt(@PathVariable String ticketNo) throws SguiResultException {
        TicketByRtktVo vo = this.iTcV2Service.queryTicketByRtkt(ticketNo);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryTicketByPnr", notes = "按PNR查询客票信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryTicketByPnrDto.class)
    })
    @PostMapping("/crs/ticket/queryByPnr")
    public SguiResult queryTicketByPnr(@RequestBody QueryTicketByPnrDto dto) throws SguiResultException {
        List<QueryTicketByPnrVo> voList = this.iTcV2Service.queryTicketByPnr(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "queryTicketDigestsByCert", notes = "按证件号查询客票信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryTicketByCertDto.class)
    })
    @PostMapping("/crs/ticket/queryTicketDigestsByCert")
    public SguiResult queryTicketDigestsByCert(@RequestBody QueryTicketByCertDto dto) throws SguiResultException {
        List<QueryTicketByPnrVo> voList = this.iTcV2Service.queryTicketDigestsByCert(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "queryTicketWindowSwitch", notes = "")
    @GetMapping("/crs/business/queryTicketWindowSwitch")
    public SguiResult queryTicketWindowSwitch() throws SguiResultException {
        return SguiResult.ok(null, false);
    }

    @ApiOperation(value = "findRefundTicket", notes = "初始加载退票信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = FindRefundTicketDto.class)
    })
    @PostMapping("/apiRefundTicket/findRefundTicket")
    public SguiResult findRefundTicket(@RequestBody FindRefundTicketDto dto) throws SguiResultException {
        FindRefundTicketVo vo = iTcV2Service.findRefundTicket(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryPnrMessage", notes = "退票PNR信息查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryPnrMessageDto.class)
    })
    @PostMapping("/crs/involuntary/queryPnrMessage")
    public SguiResult queryPnrMessage(@RequestBody QueryPnrMessageDto dto) throws SguiResultException {
        QueryPnrMessageVo vo = iTcV2Service.queryPnrMessage(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "previewRefundTicket", notes = "退前预览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = PreviewRefundTicketDto.class)
    })
    @PostMapping("/apiRefundTicket/previewRefundTicket")
    public SguiResult previewRefundTicket(@RequestBody PreviewRefundTicketDto dto) throws SguiResultException {
        PreviewRefundTicketVo vo = iTcV2Service.previewRefundTicket(dto);
        return SguiResult.ok(null, vo);
    }
}
