package com.swcares.controller.rpt;

import com.swcares.core.exception.SguiResultException;
import com.swcares.core.result.SguiResult;
import com.swcares.core.swagger.SwaggerGroup;
import com.swcares.obj.dto.CrsDailySalesDto;
import com.swcares.obj.vo.CrsDailySalesVo;
import com.swcares.service.rpt.IRptV2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 报表V2接口控制器
 *
 * <AUTHOR>
 * @date 2025/1/2 11:00
 */
@Slf4j
@Api(tags = "报表V2接口")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/sgui-rpt/v2")
public class RptV2Controller {

    @Resource
    private IRptV2Service iRptV2Service;

    @ApiOperation(value = "crsDailySales", notes = "代理人销售日报")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = CrsDailySalesDto.class)
    })
    @PostMapping("/crs/apiReport/crsDailySales")
    public SguiResult crsDailySales(@RequestBody CrsDailySalesDto dto) throws SguiResultException {
        CrsDailySalesVo vo = iRptV2Service.crsDailySales(dto);
        return SguiResult.ok(null, vo);
    }
}
