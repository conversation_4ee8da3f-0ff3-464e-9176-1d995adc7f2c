-- ----------------------------
-- Table structure for sgui_user_preference
-- ----------------------------
DROP TABLE IF EXISTS `sgui_user_preference_remark`;
DROP TABLE IF EXISTS `sgui_user_preference_passenger_info`;
DROP TABLE IF EXISTS `sgui_user_preference_check_tip`;
DROP TABLE IF EXISTS `sgui_user_preference_airline_ctct`;
DROP TABLE IF EXISTS `sgui_user_preference`;

CREATE TABLE `sgui_user_preference` (
  `preference_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '偏好设置ID',
  `si_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工作号ID',
  `ct` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'CT联系方式',
  `ctct` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'CTCT联系方式',
  `ctce` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'CTCE联系方式',
  `unshared` tinyint(1) DEFAULT '0' COMMENT '是否非共享',
  `nonstop` tinyint(1) DEFAULT '0' COMMENT '是否仅直飞',
  `auto_select_cabin_class` tinyint(1) DEFAULT '0' COMMENT '是否自动选择舱位',
  `auto_occupy` tinyint(1) DEFAULT '0' COMMENT '是否自动占位',
  `use_qtb` tinyint(1) DEFAULT '0' COMMENT '是否使用QTB',
  `domestic_printerno` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国内打票机号',
  `international_printerno` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国际打票机号',
  `manual_query` tinyint(1) DEFAULT '0' COMMENT '是否手动查询',
  `backfield_en_name` tinyint(1) DEFAULT '1' COMMENT '是否回填英文姓名',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`preference_id`),
  UNIQUE KEY `idx_si_id` (`si_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户偏好设置表';

-- ----------------------------
-- Table structure for sgui_user_preference_airline_ctct
-- ----------------------------
CREATE TABLE `sgui_user_preference_airline_ctct` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ID',
  `preference_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '偏好设置ID',
  `airline` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '航司代码',
  `ctct` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'CTCT值',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_preference_id` (`preference_id`) USING BTREE,
  KEY `idx_airline_code` (`airline`) USING BTREE,
  CONSTRAINT `fk_airline_ctct_preference_id` FOREIGN KEY (`preference_id`) REFERENCES `sgui_user_preference` (`preference_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户偏好设置航司CTCT表';

-- ----------------------------
-- Table structure for sgui_user_preference_check_tip
-- ----------------------------
CREATE TABLE `sgui_user_preference_check_tip` (
  `check_tip_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '检查提示ID',
  `preference_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '偏好设置ID',
  `city_name` tinyint(1) DEFAULT '0' COMMENT '是否检查城市名称',
  `airport_name` tinyint(1) DEFAULT '0' COMMENT '是否检查机场名称',
  `passenger_information` tinyint(1) DEFAULT '0' COMMENT '是否检查旅客信息',
  `price_including_tax` tinyint(1) DEFAULT '0' COMMENT '是否检查含税价格',
  `airline_name_check_switch` tinyint(1) DEFAULT '0' COMMENT '是否检查航司名称',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`check_tip_id`),
  UNIQUE KEY `idx_preference_id` (`preference_id`) USING BTREE,
  CONSTRAINT `fk_check_tip_preference_id` FOREIGN KEY (`preference_id`) REFERENCES `sgui_user_preference` (`preference_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户偏好设置检查提示表';

-- ----------------------------
-- Table structure for sgui_user_preference_passenger_info
-- ----------------------------
CREATE TABLE `sgui_user_preference_passenger_info` (
  `passenger_info_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '旅客信息ID',
  `preference_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '偏好设置ID',
  `passenger_nationality` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '旅客国籍',
  `visa_issue_country` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签证签发国',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`passenger_info_id`),
  UNIQUE KEY `idx_preference_id` (`preference_id`) USING BTREE,
  CONSTRAINT `fk_passenger_info_preference_id` FOREIGN KEY (`preference_id`) REFERENCES `sgui_user_preference` (`preference_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户偏好设置旅客信息表';

-- ----------------------------
-- Table structure for sgui_user_preference_remark
-- ----------------------------
CREATE TABLE `sgui_user_preference_remark` (
  `remark_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注ID',
  `preference_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '偏好设置ID',
  `remark_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注内容',
  `remark_order` int(11) DEFAULT '0' COMMENT '备注顺序',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`remark_id`),
  KEY `idx_preference_id` (`preference_id`) USING BTREE,
  CONSTRAINT `fk_remark_preference_id` FOREIGN KEY (`preference_id`) REFERENCES `sgui_user_preference` (`preference_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户偏好设置备注表';
