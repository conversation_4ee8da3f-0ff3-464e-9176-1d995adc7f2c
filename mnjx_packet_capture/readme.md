#### 安装部署方式
    1.解压目录下的mnjx_packet_capture.rar，解压到无中文命名的路径下
    2.安装exe下的WinPcap_4_1_3.exe，将Jpcap.dll文件放到jre环境/bin下
    3.修改mnjx_packet_capture.jar的配置文件，对应实施部署的对象环境
    4.运行mnjx_packet_capture_front.bat，查看程序是否能正确运行，正常后关闭
    5.安装dotNetFx40_Full_setup.exe，确保电脑上.NET4已安装
    6.打开cmd命令窗口，定位到当前解压的目录下，执行mnjx_packet_capture.exe install命令（将该jar包启动做成服务的方式，避免崩溃无法自启动）
    7.提示成功后执行mnjx_packet_capture.exe start命令或者打开windows服务找到mnjx_packet_capture，启动服务
    8.将mnjx_packet_capture服务修改成自动（右键-属性-启动类型-自动，恢复栏下3个失败动作选择为重新启动服务）
    9.新建定时任务监控服务
    9.1计算机管理-系统工具-任务计划程序-任务计划程序库
    9.2创建任务
    9.3常规：填写名称；勾选不管用户是否登录都要运行；勾选使用最高权限运行
    9.4触发器：新建：按预定计划-一次-勾选重复任务间隔-输入1分钟-持续时间选择无限期-勾选已启用-确定
    9.5操作：新建：启动程序-浏览选择“监控服务.bat”-起始于填写bat所在盘符（如 D:\）-确定
    9.6条件：取消电源下的2个勾选
    9.7设置：额外勾选 如果过了计划开始时间，立即启动任务；最下方下拉列表选择停止现有实例
    9.8确定
    9.9找到建立好的计划任务，选择运行
    10.eterm连接随意执行指令，检查日志是否打印，确认正常后完成。


#### 其他启动方式，如果服务无法正常安装启动
    在jre/bin下，复制一份javaw.exe，并重命名为mnjx_packet_capture_back.exe
    在jre/bin下，复制一份java.exe，并重命名为mnjx_packet_capture_front.exe
    当程序启动时，只允许启动一份，需要在启动前kill已启动的程序（前台或后台）
#### 配置
    第一次启动后查看启动日志，找到mac地址列表，cmd执行 ipconfig /all 找到当前使用网卡的mac地址，确认日志中该mac地址的序号，
    将序号重新写入到mnjx_packet_capture.jar的application.yml中的device
#### 启动脚本
    原javaw的位置更换为mnjx_packet_capture_back
    start "mnjx_packet_capture" mnjx_packet_capture_back -Dfile.encoding=utf-8 -jar -Xms1024m -Xmx1536m mnjx_packet_capture.jar
    原java的位置更换为mnjx_packet_capture_front
    start "mnjx_packet_capture" mnjx_packet_capture_front -Dfile.encoding=utf-8 -jar -Xms1024m -Xmx1536m mnjx_packet_capture.jar
#### KILL脚本
    使用了tasklist去获取程序名并获取pid，这个程序名就是根据javaw.exe和java.exe更换的。
    @echo off
    for /f "tokens=1-5" %%i in ('tasklist^|findstr mnjx_packet_capture') do (
        taskkill /pid %%j -t -f
        goto start
    )