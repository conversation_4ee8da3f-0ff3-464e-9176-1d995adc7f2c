package com.swcares.core;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
public class EtermChineseCode {
    @Getter
    private final static List<EtermChineseCode> ETERM_CHINESE_CODES = new ArrayList<>();

    /*
     * 此为航信的中文硬编码，此对应不上传统的GBK编码
     * 如果发现对应不上，就要将对应的硬编码添加进来
     */
    static {
        ETERM_CHINESE_CODES.add(new EtermChineseCode("北", (byte) 39, (byte) 35));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("委", (byte) 37, (byte) 78));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("喜", (byte) 40, (byte) 79));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("贝", (byte) 35, (byte) 52));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("啊", (byte) 34, (byte) 33));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("吧", (byte) 34, (byte) 73));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("安", (byte) 40, (byte) 34));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("抱", (byte) 35, (byte) 39));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("石", (byte) 37, (byte) 74));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("表", (byte) 35, (byte) 109));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("八", (byte) 34, (byte) 75));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("尾", (byte) 40, (byte) 78));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("艾", (byte) 34, (byte) 44));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("备", (byte) 35, (byte) 56));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("曹", (byte) 36, (byte) 92));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("操", (byte) 36, (byte) 89));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("习", (byte) 38, (byte) 79));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("半", (byte) 34, (byte) 107));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("园", (byte) 38, (byte) 84));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("伟", (byte) 38, (byte) 78));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("蒋", (byte) 37, (byte) 61));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("斌", (byte) 35, (byte) 115));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("才", (byte) 36, (byte) 69));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("凡", (byte) 40, (byte) 55));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("椎", (byte) 87, (byte) 53));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("逼", (byte) 35, (byte) 70));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("硬", (byte) 40, (byte) 83));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("硝", (byte) 79, (byte) 117));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("辈", (byte) 40, (byte) 35));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("悲", (byte) 37, (byte) 35));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("紫", (byte) 87, (byte) 79));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("喊", (byte) 38, (byte) 58));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("遏", (byte) 54, (byte) 116));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("景", (byte) 38, (byte) 62));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("畴", (byte) 51, (byte) 107));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("践", (byte) 60, (byte) 121));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("跋", (byte) 34, (byte) 79));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("帽", (byte) 39, (byte) 67));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("税", (byte) 38, (byte) 75));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("策", (byte) 36, (byte) 95));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("筒", (byte) 40, (byte) 77));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("傲", (byte) 34, (byte) 65));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("堡", (byte) 35, (byte) 36));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("集", (byte) 37, (byte) 60));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("傍", (byte) 34, (byte) 120));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("储", (byte) 52, (byte) 34));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("奥", (byte) 34, (byte) 66));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("腊", (byte) 38, (byte) 64));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("惫", (byte) 35, (byte) 57));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("馋", (byte) 36, (byte) 118));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("装", (byte) 38, (byte) 87));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("童", (byte) 37, (byte) 77));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("渤", (byte) 36, (byte) 51));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("窗", (byte) 38, (byte) 52));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("遍", (byte) 35, (byte) 105));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("谤", (byte) 34, (byte) 121));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("隘", (byte) 37, (byte) 34));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("编", (byte) 35, (byte) 96));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("搏", (byte) 36, (byte) 43));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("摆", (byte) 34, (byte) 90));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("携", (byte) 37, (byte) 80));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("搬", (byte) 34, (byte) 97));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("摊", (byte) 37, (byte) 76));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("靶", (byte) 34, (byte) 80));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("槐", (byte) 39, (byte) 59));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("碍", (byte) 34, (byte) 45));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("碑", (byte) 35, (byte) 46));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("雹", (byte) 35, (byte) 34));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("睡", (byte) 37, (byte) 75));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("睬", (byte) 36, (byte) 71));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("鄙", (byte) 35, (byte) 73));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("暖", (byte) 37, (byte) 69));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("暗", (byte) 34, (byte) 53));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("遣", (byte) 40, (byte) 71));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("矮", (byte) 34, (byte) 43));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("鲍", (byte) 35, (byte) 43));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("颖", (byte) 39, (byte) 83));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("痹", (byte) 35, (byte) 84));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("滨", (byte) 35, (byte) 117));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("滩", (byte) 40, (byte) 76));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("寞", (byte) 37, (byte) 68));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("辟", (byte) 35, (byte) 89));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("媳", (byte) 39, (byte) 79));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("缠", (byte) 36, (byte) 120));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("静", (byte) 40, (byte) 62));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("碧", (byte) 35, (byte) 76));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("熬", (byte) 34, (byte) 62));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("撇", (byte) 40, (byte) 70));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("蔽", (byte) 35, (byte) 78));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("蔼", (byte) 34, (byte) 42));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("榜", (byte) 34, (byte) 113));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("蜡", (byte) 37, (byte) 64));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("蝉", (byte) 36, (byte) 117));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("鼻", (byte) 35, (byte) 71));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("貌", (byte) 40, (byte) 67));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("膊", (byte) 40, (byte) 36));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("膀", (byte) 34, (byte) 114));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("腐", (byte) 37, (byte) 56));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("辣", (byte) 39, (byte) 64));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("弊", (byte) 35, (byte) 87));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("漂", (byte) 37, (byte) 70));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("寨", (byte) 37, (byte) 85));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("察", (byte) 36, (byte) 108));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("播", (byte) 36, (byte) 37));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("撞", (byte) 40, (byte) 87));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("鞍", (byte) 38, (byte) 34));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("槽", (byte) 36, (byte) 91));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("磅", (byte) 34, (byte) 117));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("暴", (byte) 35, (byte) 41));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("嘲", (byte) 38, (byte) 51));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("影", (byte) 38, (byte) 83));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("踩", (byte) 36, (byte) 72));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("幢", (byte) 39, (byte) 52));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("镑", (byte) 34, (byte) 119));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("褒", (byte) 34, (byte) 125));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("瘪", (byte) 35, (byte) 113));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("瘫", (byte) 39, (byte) 76));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("憋", (byte) 35, (byte) 111));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("潜", (byte) 39, (byte) 71));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("潮", (byte) 39, (byte) 51));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("澳", (byte) 34, (byte) 68));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("懊", (byte) 34, (byte) 67));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("薇", (byte) 39, (byte) 94));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("薄", (byte) 35, (byte) 33));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("翰", (byte) 40, (byte) 58));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("瓢", (byte) 38, (byte) 70));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("餐", (byte) 36, (byte) 77));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("辨", (byte) 35, (byte) 102));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("辩", (byte) 35, (byte) 103));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("糙", (byte) 36, (byte) 90));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("濒", (byte) 35, (byte) 116));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("壁", (byte) 35, (byte) 90));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("避", (byte) 35, (byte) 92));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("擦", (byte) 36, (byte) 65));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("藏", (byte) 36, (byte) 88));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("瞬", (byte) 40, (byte) 75));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("蟀", (byte) 38, (byte) 115));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("繁", (byte) 39, (byte) 55));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("癌", (byte) 34, (byte) 41));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("辫", (byte) 35, (byte) 104));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("臂", (byte) 35, (byte) 91));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("鞭", (byte) 35, (byte) 94));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("覆", (byte) 40, (byte) 56));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("瞻", (byte) 38, (byte) 85));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("蹦", (byte) 35, (byte) 68));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("警", (byte) 37, (byte) 62));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("蹭", (byte) 36, (byte) 100));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("簿", (byte) 36, (byte) 62));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("颤", (byte) 36, (byte) 124));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("瓣", (byte) 34, (byte) 106));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("鳖", (byte) 35, (byte) 110));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("爆", (byte) 35, (byte) 44));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("馨", (byte) 38, (byte) 92));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("霸", (byte) 34, (byte) 84));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("织", (byte) 37, (byte) 86));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("绊", (byte) 34, (byte) 109));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("帮", (byte) 34, (byte) 111));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("玻", (byte) 36, (byte) 35));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("拱", (byte) 38, (byte) 57));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("赴", (byte) 38, (byte) 56));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("挡", (byte) 40, (byte) 53));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("拾", (byte) 38, (byte) 74));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("按", (byte) 34, (byte) 52));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("挪", (byte) 40, (byte) 69));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("茬", (byte) 36, (byte) 103));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("草", (byte) 36, (byte) 93));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("茶", (byte) 36, (byte) 104));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("标", (byte) 35, (byte) 106));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("柄", (byte) 35, (byte) 122));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("栋", (byte) 38, (byte) 54));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("查", (byte) 36, (byte) 105));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("柏", (byte) 34, (byte) 88));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("勃", (byte) 36, (byte) 42));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("砂", (byte) 38, (byte) 73));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("泵", (byte) 35, (byte) 67));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("残", (byte) 36, (byte) 80));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("背", (byte) 35, (byte) 51));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("虐", (byte) 38, (byte) 69));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("冒", (byte) 38, (byte) 67));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("拜", (byte) 34, (byte) 93));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("毡", (byte) 39, (byte) 85));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("便", (byte) 35, (byte) 99));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("保", (byte) 35, (byte) 35));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("徊", (byte) 40, (byte) 59));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("胞", (byte) 34, (byte) 123));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("急", (byte) 39, (byte) 60));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("饼", (byte) 35, (byte) 125));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("奖", (byte) 39, (byte) 61));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("哀", (byte) 34, (byte) 39));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("疮", (byte) 37, (byte) 52));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("疤", (byte) 34, (byte) 76));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("差", (byte) 36, (byte) 110));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("前", (byte) 38, (byte) 71));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("测", (byte) 36, (byte) 98));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("浏", (byte) 37, (byte) 100));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("扁", (byte) 35, (byte) 98));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("袄", (byte) 34, (byte) 64));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("盈", (byte) 37, (byte) 83));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("绑", (byte) 34, (byte) 115));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("耙", (byte) 34, (byte) 82));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("班", (byte) 34, (byte) 96));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("蚕", (byte) 36, (byte) 79));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("捕", (byte) 36, (byte) 54));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("捌", (byte) 34, (byte) 70));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("捅", (byte) 39, (byte) 77));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("埃", (byte) 34, (byte) 35));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("挨", (byte) 34, (byte) 36));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("梆", (byte) 34, (byte) 112));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("毙", (byte) 35, (byte) 80));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("柴", (byte) 36, (byte) 113));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("哺", (byte) 36, (byte) 56));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("蚌", (byte) 34, (byte) 118));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("唉", (byte) 34, (byte) 38));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("罢", (byte) 34, (byte) 85));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("圆", (byte) 40, (byte) 84));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("钳", (byte) 37, (byte) 71));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("缺", (byte) 39, (byte) 72));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("氨", (byte) 39, (byte) 34));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("笔", (byte) 35, (byte) 74));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("俺", (byte) 34, (byte) 51));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("倍", (byte) 35, (byte) 54));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("舱", (byte) 36, (byte) 85));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("般", (byte) 34, (byte) 99));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("爱", (byte) 34, (byte) 46));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("豺", (byte) 36, (byte) 114));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("豹", (byte) 35, (byte) 42));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("颁", (byte) 34, (byte) 100));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("桨", (byte) 38, (byte) 61));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("席", (byte) 37, (byte) 79));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("病", (byte) 36, (byte) 33));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("疾", (byte) 40, (byte) 60));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("部", (byte) 36, (byte) 63));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("宾", (byte) 35, (byte) 118));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("案", (byte) 34, (byte) 56));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("被", (byte) 35, (byte) 59));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("剥", (byte) 34, (byte) 126));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("埠", (byte) 36, (byte) 58));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("掠", (byte) 66, (byte) 83));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("掺", (byte) 36, (byte) 116));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("职", (byte) 38, (byte) 86));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("勘", (byte) 39, (byte) 63));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("菜", (byte) 36, (byte) 75));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("菠", (byte) 36, (byte) 36));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("彬", (byte) 35, (byte) 114));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("桶", (byte) 38, (byte) 77));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("副", (byte) 39, (byte) 56));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("票", (byte) 39, (byte) 70));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("彪", (byte) 35, (byte) 107));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("野", (byte) 38, (byte) 82));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("啦", (byte) 40, (byte) 64));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("略", (byte) 66, (byte) 84));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("崩", (byte) 35, (byte) 64));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("铲", (byte) 36, (byte) 121));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("笨", (byte) 35, (byte) 63));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("舶", (byte) 38, (byte) 36));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("斜", (byte) 39, (byte) 80));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("敛", (byte) 40, (byte) 65));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("彩", (byte) 36, (byte) 74));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("脖", (byte) 39, (byte) 36));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("猜", (byte) 36, (byte) 66));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("猖", (byte) 36, (byte) 126));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("阐", (byte) 36, (byte) 123));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("惭", (byte) 36, (byte) 81));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("悴", (byte) 40, (byte) 99));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("惨", (byte) 36, (byte) 82));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("谋", (byte) 39, (byte) 68));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("谍", (byte) 53, (byte) 125));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("弹", (byte) 37, (byte) 53));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("蛋", (byte) 38, (byte) 53));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("颈", (byte) 39, (byte) 62));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("绷", (byte) 35, (byte) 65));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("巢", (byte) 40, (byte) 51));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("斑", (byte) 34, (byte) 95));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("堪", (byte) 38, (byte) 63));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("博", (byte) 36, (byte) 41));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("插", (byte) 36, (byte) 101));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("搀", (byte) 36, (byte) 115));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("裁", (byte) 36, (byte) 67));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("搁", (byte) 56, (byte) 105));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("朝", (byte) 37, (byte) 51));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("棒", (byte) 34, (byte) 116));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("棱", (byte) 64, (byte) 98));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("植", (byte) 40, (byte) 86));
        //sxl
        ETERM_CHINESE_CODES.add(new EtermChineseCode("卜", (byte) 36, (byte) 55));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("匕", (byte) 38, (byte) 88));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("及", (byte) 38, (byte) 60));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("也", (byte) 40, (byte) 82));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("叉", (byte) 36, (byte) 102));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("不", (byte) 36, (byte) 59));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("比", (byte) 35, (byte) 72));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("夭", (byte) 40, (byte) 88));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("什", (byte) 40, (byte) 74));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("化", (byte) 37, (byte) 59));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("币", (byte) 35, (byte) 82));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("仓", (byte) 36, (byte) 86));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("巴", (byte) 34, (byte) 77));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("办", (byte) 34, (byte) 108));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("劝", (byte) 38, (byte) 72));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("刊", (byte) 37, (byte) 63));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("扒", (byte) 34, (byte) 71));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("节", (byte) 61, (byte) 90));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("本", (byte) 35, (byte) 62));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("丙", (byte) 35, (byte) 123));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("布", (byte) 36, (byte) 60));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("叭", (byte) 34, (byte) 72));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("凹", (byte) 34, (byte) 60));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("白", (byte) 34, (byte) 87));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("册", (byte) 36, (byte) 97));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("包", (byte) 34, (byte) 124));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("必", (byte) 35, (byte) 88));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("边", (byte) 35, (byte) 95));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("邦", (byte) 34, (byte) 110));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("动", (byte) 37, (byte) 54));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("共", (byte) 40, (byte) 57));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("百", (byte) 34, (byte) 89));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("邪", (byte) 38, (byte) 80));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("毕", (byte) 35, (byte) 79));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("当", (byte) 39, (byte) 53));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("伪", (byte) 39, (byte) 78));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("杀", (byte) 39, (byte) 73));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("爷", (byte) 37, (byte) 82));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("妆", (byte) 39, (byte) 87));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("冰", (byte) 35, (byte) 121));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("庄", (byte) 37, (byte) 87));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("产", (byte) 36, (byte) 122));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("闭", (byte) 35, (byte) 85));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("并", (byte) 36, (byte) 34));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("讲", (byte) 40, (byte) 61));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("寻", (byte) 38, (byte) 81));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("驯", (byte) 39, (byte) 81));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("巡", (byte) 40, (byte) 81));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("贡", (byte) 39, (byte) 57));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("汞", (byte) 37, (byte) 57));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("坝", (byte) 34, (byte) 83));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("扳", (byte) 34, (byte) 98));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("扮", (byte) 34, (byte) 103));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("坎", (byte) 40, (byte) 63));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("把", (byte) 34, (byte) 81));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("报", (byte) 35, (byte) 40));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("苍", (byte) 36, (byte) 84));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("芭", (byte) 34, (byte) 69));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("材", (byte) 36, (byte) 68));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("步", (byte) 36, (byte) 61));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("卤", (byte) 39, (byte) 66));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("时", (byte) 39, (byte) 74));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("员", (byte) 39, (byte) 84));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("别", (byte) 35, (byte) 112));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("吮", (byte) 39, (byte) 75));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("财", (byte) 36, (byte) 70));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("兵", (byte) 35, (byte) 120));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("伯", (byte) 36, (byte) 46));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("伴", (byte) 34, (byte) 105));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("岔", (byte) 36, (byte) 109));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("狈", (byte) 35, (byte) 55));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("床", (byte) 40, (byte) 52));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("庇", (byte) 35, (byte) 83));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("冶", (byte) 39, (byte) 82));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("灿", (byte) 36, (byte) 83));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("沧", (byte) 36, (byte) 87));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("补", (byte) 36, (byte) 57));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("罕", (byte) 39, (byte) 58));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("层", (byte) 36, (byte) 99));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("阿", (byte) 34, (byte) 34));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("驳", (byte) 36, (byte) 53));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("拔", (byte) 34, (byte) 78));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("拆", (byte) 36, (byte) 112));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("拎", (byte) 65, (byte) 96));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("拌", (byte) 34, (byte) 104));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("拨", (byte) 36, (byte) 38));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("茂", (byte) 37, (byte) 67));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("苞", (byte) 34, (byte) 122));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("直", (byte) 39, (byte) 86));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("杯", (byte) 35, (byte) 45));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("板", (byte) 34, (byte) 101));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("矾", (byte) 37, (byte) 55));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("厕", (byte) 36, (byte) 94));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("奔", (byte) 35, (byte) 60));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("虏", (byte) 40, (byte) 66));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("哎", (byte) 34, (byte) 37));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("昌", (byte) 36, (byte) 125));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("昂", (byte) 34, (byte) 58));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("岸", (byte) 34, (byte) 54));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("败", (byte) 34, (byte) 92));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("贬", (byte) 35, (byte) 97));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("秉", (byte) 35, (byte) 124));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("版", (byte) 34, (byte) 102));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("侧", (byte) 36, (byte) 96));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("卑", (byte) 38, (byte) 35));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("彼", (byte) 35, (byte) 75));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("刹", (byte) 40, (byte) 73));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("爸", (byte) 34, (byte) 86));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("采", (byte) 36, (byte) 73));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("贪", (byte) 38, (byte) 76));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("肮", (byte) 34, (byte) 57));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("胁", (byte) 40, (byte) 80));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("饱", (byte) 35, (byte) 37));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("变", (byte) 35, (byte) 100));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("疟", (byte) 39, (byte) 69));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("券", (byte) 37, (byte) 72));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("炉", (byte) 37, (byte) 66));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("泊", (byte) 36, (byte) 52));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("波", (byte) 36, (byte) 40));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("怖", (byte) 36, (byte) 64));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("怜", (byte) 37, (byte) 65));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("宝", (byte) 35, (byte) 38));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("帘", (byte) 39, (byte) 65));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("话", (byte) 38, (byte) 59));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("询", (byte) 37, (byte) 81));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("陌", (byte) 38, (byte) 68));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("函", (byte) 37, (byte) 58));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("参", (byte) 36, (byte) 78));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("璇", (byte) 37, (byte) 104));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("吕", (byte) 66, (byte) 64));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("蔡", (byte) 36, (byte) 76));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("儿", (byte) 54, (byte) 121));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("莎", (byte) 37, (byte) 73));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("绮", (byte) 40, (byte) 103));
        ETERM_CHINESE_CODES.add(new EtermChineseCode("绫", (byte) 39, (byte) 103));
    }

    /**
     * 中文
     */
    @Getter
    private String chinese;
    /**
     * 航信第一坐标码
     */
    @Getter
    private byte first;
    /**
     * 航信第二坐标码
     */
    @Getter
    private byte second;

    /**
     * 通过航信的坐标获取中文
     *
     * @param first  第一坐标码
     * @param second 第二坐标码
     * @return 通过航信的坐标获取中文
     */
    public static EtermChineseCode getChinese(Integer first, Integer second) {
        return ETERM_CHINESE_CODES.stream()
                .filter(etermChineseCode -> etermChineseCode.getFirst() == first && etermChineseCode.getSecond() == second)
                .findFirst()
                .orElse(null);
    }

}
