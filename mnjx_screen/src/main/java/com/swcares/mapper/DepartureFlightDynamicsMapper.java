package com.swcares.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.obj.vo.CictVo;
import com.swcares.obj.vo.DepartureFlightDynamicsReqVo;
import com.swcares.obj.vo.DepartureFlightVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DepartureFlightDynamicsMapper {

    /**
     * retrievePlanFlightId
     *
     * @param page                         page
     * @param departureFlightDynamicsReqVo departureFlightDynamicsReqVo
     * @return retrievePlanFlightId
     */
    IPage<DepartureFlightVo> retrievePlanFlightId(Page<DepartureFlightVo> page, @Param("departureFlightDynamicsReqVo") DepartureFlightDynamicsReqVo departureFlightDynamicsReqVo);

    List<String> retrievePlanFlightIdList(@Param("flightDate") String flightDate);

    /**
     * retrieveDepartureFlightDynamics
     *
     * @param planFlightIds planFlightIds
     * @return retrieveDepartureFlightDynamics
     */
    List<DepartureFlightVo> retrieveDepartureFlightDynamics(@Param("planFlightIds") List<String> planFlightIds);

    /**
     * retrieveCity
     *
     * @return retrieveCity
     */
    List<CictVo> retrieveCity();
}
