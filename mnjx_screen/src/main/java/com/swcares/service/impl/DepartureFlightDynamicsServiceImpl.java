package com.swcares.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.util.CacheKeyConstant;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.MnjxFlight;
import com.swcares.mapper.DepartureFlightDynamicsMapper;
import com.swcares.obj.vo.CictVo;
import com.swcares.obj.vo.DepartureFlightDynamicsReqVo;
import com.swcares.obj.vo.DepartureFlightVo;
import com.swcares.service.IDepartureFlightDynamicsService;
import com.swcares.obj.page.ScreenPage;
import com.swcares.service.IMnjxFlightService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by yaodan
 * 2022-12-15
 */
@Service
@Slf4j
public class DepartureFlightDynamicsServiceImpl implements IDepartureFlightDynamicsService {

    private static final String XI_AN = "西安";

    @Resource
    private DepartureFlightDynamicsMapper departureFlightDynamicsMapper;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private CacheManager cacheManager;

    @Override
    public ScreenPage<DepartureFlightVo> retrieveDepartureFlightDynamics(DepartureFlightDynamicsReqVo departureFlightDynamicsReqVo) {
        ScreenPage<DepartureFlightVo> screenPage = new ScreenPage<>();
        // 使用缓存
        Cache cache = cacheManager.getCache(CacheKeyConstant.CACHE_SCREEN_KEY);
        // key
        String key = StrUtil.format("{}-{}", departureFlightDynamicsReqVo.getFlightDate(), departureFlightDynamicsReqVo.getOrg());
        // 保存
        if (departureFlightDynamicsReqVo.isFirstScreen()) {
            cache.put(key, departureFlightDynamicsReqVo);
        }
        DepartureFlightDynamicsReqVo reqVo = cache.get(key, DepartureFlightDynamicsReqVo.class);
        if (ObjectUtil.isNotEmpty(reqVo)) {
            screenPage.setFirstScreen(reqVo.getCurrent());
        }

        // 城市信息
        List<CictVo> cictVos = departureFlightDynamicsMapper.retrieveCity();
        if (CollUtil.isNotEmpty(cictVos)) {
            cictVos.stream().forEach(c -> {
                if (XI_AN.equals(c.getCityCname())) {
                    c.setCityEname(StrUtil.upperFirst("xi'an"));
                }
            });
        }
        Map<String, CictVo> cictMap = cictVos.stream().collect(Collectors.toMap(CictVo::getAirportId, k -> k));
        Page<DepartureFlightVo> page = new Page<>(departureFlightDynamicsReqVo.getCurrent(), departureFlightDynamicsReqVo.getLimit());
        // 把机场码转换成机场Id
        if (StrUtil.isNotEmpty(departureFlightDynamicsReqVo.getOrg())) {
            List<CictVo> cictVoList = cictVos.stream().filter(k -> k.getAirportCode().equals(departureFlightDynamicsReqVo.getOrg())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(cictVoList)) {
                CictVo cictVo = cictVoList.get(0);
                departureFlightDynamicsReqVo.setOrg(cictVo.getAirportId());
            }
        }
        // 计划航班Id
        IPage<DepartureFlightVo> planFlightIdPage = departureFlightDynamicsMapper.retrievePlanFlightId(page, departureFlightDynamicsReqVo);
        BeanUtil.copyProperties(planFlightIdPage, screenPage);
        List<DepartureFlightVo> records = screenPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return screenPage;
        }
        List<String> planFlightIds = records.stream()
                .map(DepartureFlightVo::getPlanFlightId)
                .collect(Collectors.toList());
        List<DepartureFlightVo> departureFlightVos = departureFlightDynamicsMapper.retrieveDepartureFlightDynamics(planFlightIds);
        // 按计划Id分组
        Map<String, List<DepartureFlightVo>> departureFlightMap = departureFlightVos.stream()
                .collect(Collectors.groupingBy(DepartureFlightVo::getPlanFlightId));
        records.forEach(record -> {
            List<DepartureFlightVo> dfv = departureFlightMap.get(record.getPlanFlightId());
            dfv = dfv.stream().sorted(Comparator.comparing(DepartureFlightVo::getIsLastSection)).collect(Collectors.toList());
            List<DepartureFlightVo> flightVos = dfv.stream().filter(k -> StrUtil.isNotEmpty(departureFlightDynamicsReqVo.getOrg()) && k.getArrAptId().equals(departureFlightDynamicsReqVo.getOrg())).collect(Collectors.toList());
            // 不为空 说明有从org出发城市
            if (CollUtil.isNotEmpty(flightVos)) {
                DepartureFlightVo departureFlightVo = flightVos.get(0);
                int estimateOff = Integer.parseInt(departureFlightVo.getEstimateOff());
                // 大于计划时间
                dfv = dfv.stream()
                        .filter(k -> StrUtil.isEmpty(k.getEstimateOffChange()))
                        .filter(k -> Integer.parseInt(k.getEstimateOff()) > estimateOff)
                        .collect(Collectors.toList());
            }
            DepartureFlightVo departureFlightVo = dfv.get(0);
            BeanUtil.copyProperties(departureFlightVo, record);
            // 中文
            List<String> cName = dfv.stream().map(DepartureFlightVo::getArrAptId).map(f -> StrUtil.upperFirst(cictMap.get(f).getCityCname().toLowerCase())).collect(Collectors.toList());
            record.setCityCnames(cName);
            // 英文
            List<String> eName = dfv.stream().map(DepartureFlightVo::getArrAptId).map(f -> StrUtil.upperFirst(cictMap.get(f).getCityEname().toLowerCase())).collect(Collectors.toList());
            record.setCityEnames(eName);
            this.setFlightStatus(record);
            // 计划时间
            if (StrUtil.isNotEmpty(record.getEstimateOff())) {
                String estimateOff = record.getEstimateOff();
                record.setEstimateOff(StrUtil.format("{}:{}", estimateOff.substring(0, 2), estimateOff.substring(2)));
            }
            // 预计
            if (StrUtil.isNotEmpty(record.getActualOff())) {
                String actualOff = record.getActualOff();
                record.setActualOff(StrUtil.format("{}:{}", actualOff.substring(0, 2), actualOff.substring(2)));
            } else {
                record.setActualOff(record.getEstimateOff());
            }
            //查询共享航班
            String flightNo = record.getFlightNo();
            List<MnjxFlight> shareFlights = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getCarrierFlight, flightNo).eq(MnjxFlight::getShareState, Constant.STR_ONE).list();
            if (CollUtil.isNotEmpty(shareFlights)) {
                List<String> shareFlightList = shareFlights.stream().map(MnjxFlight::getFlightNo).collect(Collectors.toList());
                record.setShareFlight(shareFlightList);
            }
        });
        return screenPage;
    }

    private void setFlightStatus(DepartureFlightVo departureFlightVo) {
        String flightDate = departureFlightVo.getFlightDate();
        String flightStatus = departureFlightVo.getFlightStatus();
        String estimateOff = departureFlightVo.getEstimateOff();
        String actualOff = departureFlightVo.getActualOff();
        estimateOff = StrUtil.format("{} {}", flightDate, DateUtils.comHm2hmis(estimateOff));
        Date estimateOffTime = DateUtils.ymdhms2Date(estimateOff);
        Date nowDateTime = DateUtils.date();
        Date fltDateTime = null;
        if (StrUtil.equalsAny(flightStatus, Constant.CK_STATUS_CI, Constant.CK_STATUS_CL, Constant.CK_STATUS_CC)) {
            //取消 Cancelled 航班被取消
            departureFlightVo.setFlightStatusCname("取消");
            departureFlightVo.setFlightStatusEname("Cancelled");
        } else {
            //延误 Delay 航班无法按计划时间执飞，计划与预计时间相差30分钟及上的
            if (StrUtil.isNotEmpty(actualOff)) {
                actualOff = StrUtil.format("{} {}", flightDate, DateUtils.comHm2hmis(actualOff));
                Date actualOffTime = DateUtils.ymdhms2Date(actualOff);
                if (DateUtils.compare(nowDateTime, DateUtils.offsetHour(actualOffTime, -2)) < 0) {
                    departureFlightVo.setFlightStatusCname("正常");
                    departureFlightVo.setFlightStatusEname("On time");
                } else if (DateUtils.compare(estimateOffTime, DateUtils.offsetMinute(actualOffTime, -30)) <= 0 && DateUtils.compare(nowDateTime, DateUtils.offsetHour(actualOffTime, -2)) < 0) {
                    departureFlightVo.setFlightStatusCname("延误");
                    departureFlightVo.setFlightStatusEname("Delay");
                } else {
                    fltDateTime = actualOffTime;
                }
            } else {
                // 航班起飞前两小时，显示正常
                if (DateUtils.compare(nowDateTime, DateUtils.offsetHour(estimateOffTime, -2)) < 0) {
                    departureFlightVo.setFlightStatusCname("正常");
                    departureFlightVo.setFlightStatusEname("On time");
                } else {
                    fltDateTime = estimateOffTime;
                }
            }
            if (ObjectUtil.isNotEmpty(fltDateTime)) {
                if (DateUtils.compare(nowDateTime, fltDateTime) >= 0) {
                    departureFlightVo.setFlightStatusCname("已起飞");
                    departureFlightVo.setFlightStatusEname("Departed");
                } else if (DateUtils.compare(nowDateTime, DateUtils.offsetHour(fltDateTime, -2)) >= 0 && DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -35)) <= 0) {
                    //正在值机，航班起飞前两小时内到起飞前35分钟
                    departureFlightVo.setFlightStatusCname("正在值机");
                    departureFlightVo.setFlightStatusEname("Checking");
                } else if (DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -35)) > 0 && DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -30)) <= 0) {
                    //准备登机 航班起飞前35分钟到起飞前30分钟
                    departureFlightVo.setFlightStatusCname("准备登机");
                    departureFlightVo.setFlightStatusEname("Board Soon");
                } else if (DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -30)) > 0 && DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -20)) <= 0) {
                    //正在登机 Boarding 航班起飞前30分钟到起飞前20分钟
                    departureFlightVo.setFlightStatusCname("正在登机");
                    departureFlightVo.setFlightStatusEname("Boarding");
                } else if (DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -20)) > 0 && DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -15)) <= 0) {
                    //催促登机 Final Call 航班起飞前20分钟到起飞前15分钟
                    departureFlightVo.setFlightStatusCname("催促登机");
                    departureFlightVo.setFlightStatusEname("Final Call");
                } else if (DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -15)) > 0 || DateUtils.compare(nowDateTime, fltDateTime) >= 0) {
                    //结束登机 Gate Closed 航班起飞前15分钟到航班起飞时，显示为结束登机
                    departureFlightVo.setFlightStatusCname("结束登机");
                    departureFlightVo.setFlightStatusEname("Gate Closed");
                }
            }
        }
    }
}
