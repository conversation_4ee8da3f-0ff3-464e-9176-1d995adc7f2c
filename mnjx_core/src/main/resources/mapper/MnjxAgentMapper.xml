<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxAgentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxAgent">
        <id column="agent_id" property="agentId"/>
        <result column="agent_iata" property="agentIata"/>
        <result column="agent_cname" property="agentCname"/>
        <result column="agent_ename" property="agentEname"/>
        <result column="agent_contact_cname" property="agentContactCname"/>
        <result column="agent_contact_phone" property="agentContactPhone"/>
        <result column="agent_contact_address" property="agentContactAddress"/>
        <result column="agent_status" property="agentStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        agent_id, office_no, agent_iata, agent_cname, agent_ename, agent_contact_cname, agent_contact_phone, agent_contact_address, agent_status
    </sql>

</mapper>
