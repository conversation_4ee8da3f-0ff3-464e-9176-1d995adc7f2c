<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPlanFlightMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPlanFlight">
        <id column="plan_flight_id" property="planFlightId"/>
        <result column="tcard_id" property="tcardId"/>
        <result column="airline_code" property="airlineCode"/>
        <result column="cnd_no" property="cndNo"/>
        <result column="flight_status" property="flightStatus"/>
        <result column="flight_type" property="flightType"/>
        <result column="flight_date" property="flightDate"/>
        <result column="is_a" property="isA"/>
        <result column="is_x" property="isX"/>
        <result column="is_w" property="isW"/>
        <result column="is_e" property="isE"/>
        <result column="allow_asr" property="allowAsr"/>
        <result column="is_flight_initial" property="isFlightInitial"/>
        <result column="flight_initial_office" property="flightInitialOffice"/>
        <result column="share_flight_no" property="shareFlightNo"/>
        <result column="flight_suffix" property="flightSuffix"/>
        <result column="sell_model" property="sellModel"/>
        <result column="ck_status" property="ckStatus"/>
        <result column="pre_ck_status" property="preCkStatus"/>
        <result column="status_update_time" property="statusUpdateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        plan_flight_id, tcard_id, airline_code, cnd_no, flight_status, flight_type, flight_date, is_a,
        is_x, is_w, is_e, is_n, allow_asr, is_flight_initial, flight_initial_office, share_flight_no, flight_suffix,
        sell_model, ck_status, pre_ck_status, status_update_time
    </sql>

</mapper>
