<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPnrGnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPnrGn">
        <id column="pnr_gn_id" property="pnrGnId" />
        <result column="pnr_id" property="pnrId" />
        <result column="pnr_index" property="pnrIndex" />
        <result column="group_number" property="groupNumber" />
        <result column="name_number" property="nameNumber" />
        <result column="group_name" property="groupName" />
        <result column="input_value" property="inputValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        pnr_gn_id, pnr_id, pnr_index, group_number, name_number, group_name, input_value
    </sql>

</mapper>
