<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxSiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxSi">
        <id column="si_id" property="siId" />
        <result column="si_no" property="siNo" />
        <result column="office_id" property="officeId" />
        <result column="si_password" property="siPassword" />
        <result column="remark" property="remark" />
        <result column="si_status" property="siStatus" />
        <result column="si_pid" property="siPid" />
        <result column="level_id" property="levelId" />
        <result column="sign_in_datetime" property="signInDatetime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        si_id, si_no, office_id, si_password, remark, si_status, si_pid, level_id, sign_in_datetime
    </sql>

</mapper>
