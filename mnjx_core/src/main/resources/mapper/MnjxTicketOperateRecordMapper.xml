<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxTicketOperateRecordMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxTicketOperateRecord">
        <id column="ticket_operate_record_id" property="ticketOperateRecordId" />
        <result column="settlement_code" property="settlementCode"/>
        <result column="ticket_no" property="ticketNo" />
        <result column="ticket_status_1" property="ticketStatus1" />
        <result column="ticket_status_2" property="ticketStatus2" />
        <result column="si_no" property="siNo" />
        <result column="operate_time" property="operateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ticket_operate_record_id,settlement_code, ticket_no, ticket_status_1, ticket_status_2, si_no, operate_time
    </sql>

</mapper>
