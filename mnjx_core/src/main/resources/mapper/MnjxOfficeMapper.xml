<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxOfficeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxOffice">
        <id column="office_id" property="officeId"/>
        <result column="office_no" property="officeNo"/>
        <result column="office_type" property="officeType"/>
        <result column="office_status" property="officeStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        office_id, office_no, office_cname, office_ename, office_type, office_status
    </sql>

</mapper>
