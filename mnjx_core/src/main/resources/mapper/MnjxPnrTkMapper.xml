<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPnrTkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPnrTk">
        <id column="pnr_tk_id" property="pnrTkId" />
        <result column="pnr_id" property="pnrId" />
        <result column="pnr_index" property="pnrIndex" />
        <result column="pnr_tk_type" property="pnrTkType" />
        <result column="plan_etdz_date" property="planEtdzDate" />
        <result column="plan_etdz_time" property="planEtdzTime" />
        <result column="etdz_office" property="etdzOffice" />
        <result column="input_value" property="inputValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        pnr_tk_id, pnr_id, pnr_index, pnr_tk_type, plan_etdz_date, plan_etdz_time, etdz_office, input_value
    </sql>

</mapper>
