<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPsgCkiOptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPsgCkiOption">
        <id column="psg_cki_option_id" property="psgCkiOptionId" />
        <result column="psg_cki_id" property="psgCkiId" />
        <result column="option_type" property="optionType" />
        <result column="option_value" property="optionValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        psg_cki_option_id, psg_cki_id, option_type, option_value
    </sql>

</mapper>
