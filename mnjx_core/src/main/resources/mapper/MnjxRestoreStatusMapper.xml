<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxRestoreStatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxRestoreStatus">
        <id column="restore_status_id" property="restoreStatusId" />
        <result column="office_no" property="officeNo" />
        <result column="operate_time" property="operateTime" />
        <result column="jobnumber" property="jobnumber" />
        <result column="json_restore" property="jsonRestore" />
        <result column="id_card" property="idCard" />
        <result column="entity_name" property="entityName" />
        <result column="operate_type" property="operateType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        restore_status_id, office_no, operate_time, jobnumber, json_restore, id_card, entity_name, operate_type
    </sql>

</mapper>
