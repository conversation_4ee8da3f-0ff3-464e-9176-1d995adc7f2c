<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxNmOiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxNmOi">
        <id column="nm_oi_id" property="nmOiId" />
        <result column="pnr_nm_id" property="pnrNmId" />
        <result column="oi_info" property="oiInfo" />
        <result column="pnr_index" property="pnrIndex" />
        <result column="old_ticket_no" property="oldTicketNo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        nm_oi_id, pnr_nm_id, oi_info, pnr_index, old_ticket_no
    </sql>

</mapper>
