<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPlaneModelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPlaneModel">
        <id column="plane_model_id" property="planeModelId"/>
        <result column="plane_model_type" property="planeModelType"/>
        <result column="plane_model_version" property="planeModelVersion"/>
        <result column="plane_model_kind" property="planeModelKind"/>
        <result column="is_wide" property="isWide"/>
        <result column="engine_model" property="engineModel"/>
        <result column="is_o" property="isO"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        plane_model_id, cnd_no, plane_model_type, plane_model_version, plane_model_kind, is_wide, engine_model, is_o
    </sql>

</mapper>
