<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxSpInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxSpInfo">
        <id column="sp_info_id" property="spInfoId" />
        <result column="pnr_id" property="pnrId" />
        <result column="sp_to_crs_pnr" property="spToCrsPnr" />
        <result column="sp_from_crs_pnr" property="spFromCrsPnr" />
        <result column="at_no" property="atNo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        sp_info_id, pnr_id, sp_to_crs_pnr, sp_from_crs_pnr, at_no
    </sql>

</mapper>
