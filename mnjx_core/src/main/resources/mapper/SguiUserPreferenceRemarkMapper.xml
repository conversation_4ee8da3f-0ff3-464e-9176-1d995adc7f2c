<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.SguiUserPreferenceRemarkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.SguiUserPreferenceRemark">
        <id column="remark_id" property="remarkId" />
        <result column="preference_id" property="preferenceId" />
        <result column="remark_content" property="remarkContent" />
        <result column="remark_order" property="remarkOrder" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        remark_id, preference_id, remark_content, remark_order, create_time, update_time
    </sql>

</mapper>
