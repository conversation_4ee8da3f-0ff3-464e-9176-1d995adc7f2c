<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPnrRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPnrRecord">
        <id column="pnr_record_id" property="pnrRecordId"/>
        <result column="pnr_id" property="pnrId"/>
        <result column="at_no" property="atNo"/>
        <result column="pnr_index" property="pnrIndex"/>
        <result column="pnr_type" property="pnrType"/>
        <result column="input_value" property="inputValue"/>
        <result column="change_mark" property="changeMark"/>
        <result column="change_at_no" property="changeAtNo"/>
        <result column="pnr_nm_id" property="pnrNmId"/>
        <result column="pnr_seg_id" property="pnrSegId"/>
        <result column="issued_time" property="issuedTime"/>
        <result column="printer_id" property="printerId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        pnr_record_id, pnr_id, at_no, pnr_index, pnr_type, input_value, change_mark, change_at_no, pnr_nm_id, pnr_seg_id, issued_time, printer_id
    </sql>

</mapper>
