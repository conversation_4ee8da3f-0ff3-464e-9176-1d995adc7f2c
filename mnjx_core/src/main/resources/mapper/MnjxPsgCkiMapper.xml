<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPsgCkiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPsgCki">
        <id column="psg_cki_id" property="psgCkiId" />
        <result column="pnr_nm_id" property="pnrNmId" />
        <result column="pnr_seg_no" property="pnrSegNo" />
        <result column="cabin_class" property="cabinClass" />
        <result column="sell_cabin" property="sellCabin" />
        <result column="gate" property="gate" />
        <result column="cki_status" property="ckiStatus" />
        <result column="aboard_no" property="aboardNo" />
        <result column="aboard_time" property="aboardTime" />
        <result column="is_hb" property="isHb" />
        <result column="hb_no" property="hbNo" />
        <result column="is_aec" property="isAec" />
        <result column="upgn" property="upgn" />
        <result column="is_xres" property="isXres" />
        <result column="is_change" property="isChange" />
        <result column="luggage_weight" property="luggageWeight" />
        <result column="abd_status_infi" property="abdStatusInfi" />
        <result column="ures" property="ures" />
        <result column="nrec" property="nrec" />
        <result column="pre_upgn_operate" property="preUpgnOperate" />
        <result column="cap" property="cap" />
        <result column="is_through_check_in" property="isThroughCheckIn" />
        <result column="check_in_airport_code" property="checkInAirportCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        psg_cki_id, pnr_nm_id, pnr_seg_no, cabin_class, sell_cabin, gate, cki_status, aboard_no, aboard_time, is_hb, hb_no, is_aec, upgn, is_xres, is_change, luggage_weight, abd_status_infi, ures, nrec, pre_upgn_operate, cap, is_through_check_in, check_in_airport_code
    </sql>

</mapper>
