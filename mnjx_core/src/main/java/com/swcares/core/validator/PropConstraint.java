package com.swcares.core.validator;


import com.swcares.core.util.StrUtils;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 属性字段校验
 *
 * <AUTHOR>
 */
@Documented
@Constraint(validatedBy = {PropValidator.class})
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface PropConstraint {
    /**
     * 校验不通过时的报错信息
     *
     * @return 校验不通过时的报错信息
     */
    String message() default StrUtils.EMPTY;

    /**
     * 将validator进行分类，不同的类group中会执行不同的validator操作
     *
     * @return validator的分类类型
     */
    Class<?>[] groups() default {};

    /**
     * 主要是针对bean，很少使用
     *
     * @return 负载
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * 是否强制校验
     *
     * @return 是否强制校验的boolean值
     */
    boolean required() default true;

    /**
     * 要调用的接口名
     *
     * @return 接口名
     */
    Class<?> service();

    /**
     * 调用的接口方法
     *
     * @return 方法名
     */
    String method();

    /**
     * 时判定存在还是不存在
     * true代表存在，false代表不存在
     * 默认就是判断记录要存在
     *
     * @return 是否存在
     */
    boolean isExist() default true;
}
