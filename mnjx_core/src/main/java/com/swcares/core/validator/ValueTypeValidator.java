package com.swcares.core.validator;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Slf4j
public class ValueTypeValidator implements ConstraintValidator<ValueTypeConstraint, Object> {

    /**
     * 类型值
     */
    private String[] values;
    /**
     * 是否允许为空，true 允许。false不允许
     */
    private boolean openEmpty;

    /**
     * 初始化传入的是注解
     */
    @Override
    public void initialize(ValueTypeConstraint annotation) {
        values = annotation.value();
        openEmpty = annotation.openEmpty();
    }

    /**
     * 进行校验的逻辑判断,如果没有匹配上字符串则为空
     *
     * @param val     传入的值
     * @param context 校验器上下文
     * @return false 显示message，true 不显示message
     */
    @Override
    public boolean isValid(Object val, ConstraintValidatorContext context) {
        // openEmpty true为允许为空不验证,false 不允许为空
        if(openEmpty&&ObjectUtil.isEmpty(val)){
            return Boolean.TRUE;
        }
        if (!openEmpty&&ObjectUtil.isEmpty(val)) {
            return Boolean.FALSE;
        }
        String s = Arrays.stream(this.values).filter(v -> v.equals(val)).findAny().orElse(null);
       return ObjectUtil.isNotEmpty(s);
    }
}
