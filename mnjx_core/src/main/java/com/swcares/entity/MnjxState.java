package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_state")
public class MnjxState extends Model<MnjxState> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "state_id", type = IdType.ASSIGN_ID)
    private String stateId;

    @ApiModelProperty(value = "国家ID")
    @TableField("country_id")
    private String countryId;

    @ApiModelProperty(value = "州代码")
    @TableField("state_code")
    @NotBlank(message = "州代码不能为空")
    @Pattern(regexp = "[A-Za-z]{2}", message = "格式错误")
    private String stateCode;

    @ApiModelProperty(value = "州英文名")
    @TableField("state_ename")
    @NotBlank(message = "州英文名不能为空")
    @Pattern(regexp = "[A-Za-z\\s'.]{1,50}", message = "格式错误")
    private String stateEname;

    @ApiModelProperty(value = "州中文名")
    @TableField("state_cname")
    @NotBlank(message = "州中文名不能为空")
    @Pattern(regexp = "[\\u4e00-\\u9fa5]{1,50}", message = "格式错误")
    private String stateCname;

    @ApiModelProperty(value = "状态：0禁用，1启用")
    @TableField("status")
    @NotBlank(message = "状态不能为空")
    @Pattern(regexp = "[10]", message = "格式错误")
    private String status;

    @Override
    protected Serializable pkVal() {
        return this.stateId;
    }

}
