package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 姓名组RMK
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_nm_rmk")
@ApiModel(value="MnjxNmRmk对象", description="姓名组RMK")
public class MnjxNmRmk extends Model<MnjxNmRmk> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "nm_rmk_id", type = IdType.ASSIGN_ID)
    private String nmRmkId;

    @TableField("pnr_nm_id")
    private String pnrNmId;

    @ApiModelProperty(value = "PNR序号")
    @TableField("pnr_index")
    private Integer pnrIndex;

    @ApiModelProperty(value = "备注类型名")
    @TableField("rmk_name")
    private String rmkName;

    @ApiModelProperty(value = "备注内容")
    @TableField("rmk_info")
    private String rmkInfo;

    @ApiModelProperty(value = "输入值")
    @TableField("input_value")
    private String inputValue;


    @Override
    protected Serializable pkVal() {
        return this.nmRmkId;
    }

}
