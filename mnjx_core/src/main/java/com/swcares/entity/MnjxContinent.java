package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 国际州数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_continent")
@ApiModel(value="MnjxContinent对象", description="国际州数据表")
public class MnjxContinent extends Model<MnjxContinent> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "continent_id", type = IdType.ASSIGN_ID)
    private String continentId;

    @ApiModelProperty(value = "洲际代码")
    @TableField("continent_code")
    private String continentCode;

    @ApiModelProperty(value = "中文名称（简称）")
    @TableField("continent_cname")
    private String continentCname;

    @ApiModelProperty(value = "英文名称")
    @TableField("continent_ename")
    private String continentEname;

    @ApiModelProperty(value = "中文全称")
    @TableField("continent_full_cname")
    private String continentFullCname;


    @Override
    protected Serializable pkVal() {
        return this.continentId;
    }

}
