package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_plan_flight")
public class MnjxPlanFlight extends Model<MnjxPlanFlight> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "plan_flight_id", type = IdType.ASSIGN_ID)
    private String planFlightId;

    @ApiModelProperty(value = "tcard id")
    @TableField("tcard_id")
    private String tcardId;

    @ApiModelProperty(value = "")
    @TableField("airline_code")
    private String airlineCode;

    @ApiModelProperty(value = "CND表号")
    @TableField("cnd_no")
    private String cndNo;

    @ApiModelProperty(value = "航班状态	master是初始状态	active是激活的状态，代表可以订座	delete是删除状态，代表航班后期都不生成航班数据了	cancel是取消")
    @TableField("flight_status")
    private String flightStatus;

    @ApiModelProperty(value = "")
    @TableField("flight_type")
    private String flightType;

    @ApiModelProperty(value = "航班日期")
    @TableField("flight_date")
    private String flightDate;

    @ApiModelProperty(value = "")
    @TableField("is_a")
    private String isA;

    @ApiModelProperty(value = "")
    @TableField("is_x")
    private String isX;

    @ApiModelProperty(value = "")
    @TableField("is_w")
    private String isW;

    @ApiModelProperty(value = "")
    @TableField("is_e")
    private String isE;

    @ApiModelProperty(value = "")
    @TableField("allow_asr")
    private String allowAsr;

    @ApiModelProperty(value = "航班是否初始化 Y 初始化、N 或 NULL 都是未初始化")
    @TableField("is_flight_initial")
    private String isFlightInitial;

    @ApiModelProperty(value = "航班初始化office")
    @TableField("flight_initial_office")
    private String flightInitialOffice;

    @ApiModelProperty(value = "共享航班号")
    @TableField("share_flight_no")
    private String shareFlightNo;

    @ApiModelProperty(value = "")
    @TableField("flight_suffix")
    private String flightSuffix;

    @ApiModelProperty(value = "销售模式")
    @TableField("sell_model")
    private String sellModel;

    @ApiModelProperty(value = "修改前CND表号")
    @TableField("pre_cnd_no")
    private String preCndNo;

    @ApiModelProperty(value = "值机状态")
    @TableField("ck_status")
    private String ckStatus;

    @ApiModelProperty(value = "之前的值机状态")
    @TableField("pre_ck_status")
    private String preCkStatus;

    @ApiModelProperty(value = "值机状态修改时间")
    @TableField("status_update_time")
    private Date statusUpdateTime;

    @Override
    protected Serializable pkVal() {
        return this.planFlightId;
    }

}
