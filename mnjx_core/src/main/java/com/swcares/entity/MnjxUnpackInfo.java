package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_unpack_info")
public class MnjxUnpackInfo extends Model<MnjxUnpackInfo> {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID")
	@TableId(value = "unpack_id", type = IdType.ASSIGN_ID)
	private String unpackId;

	@ApiModelProperty(value = "行李Id")
	@TableField("luggage_id")
	private String luggageId;

	@ApiModelProperty(value = "行李编号")
	@TableField("luggage_no")
	private String luggageNo;
	
	@ApiModelProperty(value = "旅客姓名")
	@TableField("nm_name")
	private String nmName;
	
	@ApiModelProperty(value = "航班号")
	@TableField("flight_no")
	private String flightNo;
	
	@ApiModelProperty(value = "航班日期")
	@TableField("flight_date")
	private String flightDate;
	
	@ApiModelProperty(value = "状态")
	@TableField("status")
	private String status;
	
	@ApiModelProperty(value = "备注")
	@TableField(value = "remark",updateStrategy = FieldStrategy.IGNORED)
	private String remark;
	
	@ApiModelProperty(value = "销售舱位")
	@TableField("sell_cabin")
	private String sellCabin;
}
