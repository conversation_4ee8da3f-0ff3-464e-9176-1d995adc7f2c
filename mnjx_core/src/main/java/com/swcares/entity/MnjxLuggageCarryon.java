package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_luggage_carryon")
public class MnjxLuggageCarryon extends Model<MnjxLuggageCarryon> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "luggage_carryon_id", type = IdType.ASSIGN_ID)
    private String luggageCarryonId;

    @ApiModelProperty(value = "旅客ID")
    @TableField("pnr_nm_id")
    private String pnrNmId;

    @ApiModelProperty(value = "检查结果")
    @TableField("check_result")
    private String checkResult;

    @ApiModelProperty(value = "原因说明")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "检查时间")
    @TableField("check_time")
    private String checkTime;

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
