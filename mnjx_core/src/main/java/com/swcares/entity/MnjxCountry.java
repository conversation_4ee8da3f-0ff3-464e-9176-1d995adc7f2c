package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_country")
public class MnjxCountry extends Model<MnjxCountry> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "country_id", type = IdType.ASSIGN_ID)
    private String countryId;

    @ApiModelProperty(value = "国家代码 CN，唯一")
    @TableField("country_iso")
    @NotBlank(message = "国家代码不能为空")
    @Size(max = 4, message = "国家代码长度不合法，最大长度为4位")
    private String countryIso;

    @ApiModelProperty(value = "国家三字代码")
    @TableField("country_three_code")
    @NotBlank(message = "国家三字代码不能为空")
    @Size(max = 4, message = "国家三字代码长度不合法，最大长度为4位")
    private String countryThreeCode;

    @ApiModelProperty(value = "中文名")
    @NotBlank(message = "国家中文名不能为空")
    @TableField("country_cname")
    private String countryCname;

    @ApiModelProperty(value = "英文名")
    @TableField("country_ename")
    private String countryEname;

    @ApiModelProperty(value = "状态 0停用,1启用")
    @TableField("country_status")
    private String countryStatus = "1";

    @ApiModelProperty(value = "洲际ID")
    @TableField("continent_id")
    private String continentId;

    @ApiModelProperty(value = "首都城市代码")
    @TableField("capital_code")
    private String capitalCode;

    @Override
    protected Serializable pkVal() {
        return this.countryId;
    }

}
