package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_cnd")
public class MnjxCnd extends Model<MnjxCnd> {

    @ApiModelProperty(value = "ID")
    @TableId(value = "cnd_id", type = IdType.ASSIGN_ID)
    private String cndId;

    @ApiModelProperty(value = "机型ID")
    @TableField("plane_model_id")
    private String planeModelId;

    @ApiModelProperty(value = "CND号")
    @TableField("cnd_no")
    private String cndNo;

    @ApiModelProperty(value = "飞机布局")
    @TableField("layout")
    private String layout;

    @ApiModelProperty(value = "飞机源图")
    @TableField("layout_source")
    private String layoutSource;

    @ApiModelProperty(value = "一等舱")
    @TableField(value = "first_cabin_class",updateStrategy = FieldStrategy.IGNORED)
    private String firstCabinClass;

    @ApiModelProperty(value = "一等舱销售舱位")
    @TableField(value = "first_sell_cabin",updateStrategy = FieldStrategy.IGNORED)
    private String firstSellCabin;

    @ApiModelProperty(value = "一等舱座位数")
    @TableField(value = "first_seats",updateStrategy = FieldStrategy.IGNORED)
    private Integer firstSeats;

    @ApiModelProperty(value = "一等舱可托运行李重量")
    @TableField(value = "first_weight",updateStrategy = FieldStrategy.IGNORED)
    private Integer firstWeight;

    @ApiModelProperty(value = "一等舱折扣")
    @TableField(value = "first_discount",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal firstDiscount;

    @ApiModelProperty(value = "二等舱")
    @TableField(value = "second_cabin_class",updateStrategy = FieldStrategy.IGNORED)
    private String secondCabinClass;

    @ApiModelProperty(value = "二等舱销售舱位")
    @TableField(value = "second_sell_cabin",updateStrategy = FieldStrategy.IGNORED)
    private String secondSellCabin;

    @ApiModelProperty(value = "二等舱座位数")
    @TableField(value = "second_seats",updateStrategy = FieldStrategy.IGNORED)
    private Integer secondSeats;

    @ApiModelProperty(value = "二等舱可托运行李重量")
    @TableField(value = "second_weight",updateStrategy = FieldStrategy.IGNORED)
    private Integer secondWeight;

    @ApiModelProperty(value = "二等舱折扣")
    @TableField(value = "second_discount",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal secondDiscount;


    @ApiModelProperty(value = "三等舱")
    @TableField(value = "third_cabin_class",updateStrategy = FieldStrategy.IGNORED)
    private String thirdCabinClass;

    @ApiModelProperty(value = "三等舱销售舱位")
    @TableField(value = "third_sell_cabin",updateStrategy = FieldStrategy.IGNORED)
    private String thirdSellCabin;

    @ApiModelProperty(value = "三等舱座位数")
    @TableField(value = "third_seats",updateStrategy = FieldStrategy.IGNORED)
    private Integer thirdSeats;

    @ApiModelProperty(value = "三等舱可托运行李重量")
    @TableField(value = "third_weight",updateStrategy = FieldStrategy.IGNORED)
    private Integer thirdWeight;

    @ApiModelProperty(value = "三等舱折扣")
    @TableField(value = "third_discount",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal thirdDiscount;

    @ApiModelProperty(value = "四等舱")
    @TableField(value = "fourth_cabin_class",updateStrategy = FieldStrategy.IGNORED)
    private String fourthCabinClass;

    @ApiModelProperty(value = "四等舱销售舱位")
    @TableField(value = "fourth_sell_cabin",updateStrategy = FieldStrategy.IGNORED)
    private String fourthSellCabin;

    @ApiModelProperty(value = "四等舱座位数")
    @TableField(value = "fourth_seats",updateStrategy = FieldStrategy.IGNORED)
    private Integer fourthSeats;

    @ApiModelProperty(value = "四等舱可托运行李重量")
    @TableField(value = "fourth_weight",updateStrategy = FieldStrategy.IGNORED)
    private Integer fourthWeight;

    @ApiModelProperty(value = "四等舱折扣")
    @TableField(value = "fourth_discount",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal fourthDiscount;

    @ApiModelProperty(value = "五等舱")
    @TableField(value = "fifth_cabin_class",updateStrategy = FieldStrategy.IGNORED)
    private String fifthCabinClass;

    @ApiModelProperty(value = "五等舱销售舱位")
    @TableField(value = "fifth_sell_cabin",updateStrategy = FieldStrategy.IGNORED)
    private String fifthSellCabin;

    @ApiModelProperty(value = "五等舱座位数")
    @TableField(value = "fifth_seats",updateStrategy = FieldStrategy.IGNORED)
    private Integer fifthSeats;

    @ApiModelProperty(value = "五等舱可托运行李重量")
    @TableField(value="fifth_weight",updateStrategy = FieldStrategy.IGNORED)
    private Integer fifthWeight;

    @ApiModelProperty(value = "五等舱折扣")
    @TableField(value="fifth_discount",updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal fifthDiscount;


    @ApiModelProperty(value = "状态:0 启用 1停用")
    @TableField("status")
    private String status;

    @Override
    protected Serializable pkVal() {
        return this.cndId;
    }

}
