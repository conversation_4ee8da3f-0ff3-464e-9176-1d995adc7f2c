package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;


/**
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_ticket_limits")
public class MnjxTicketLimits extends Model<MnjxTicketLimits> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "ticket_limits_id", type = IdType.ASSIGN_ID)
    private String ticketLimitsId;

    @ApiModelProperty(value = "起始票号",example="1000000000")
    @TableField("start_ticket")
    @NotBlank(message = "起始票号不能为空")
    private String startTicket;

    @ApiModelProperty(value = "结束票号",example="1000000499")
    @TableField("end_ticket")
    @NotBlank(message = "结束票号不能为空")
    private String endTicket;

    @ApiModelProperty(value = "总票张数",example="500")
    @TableField("total_number")
    private BigInteger totalNumber;

    @ApiModelProperty(value = "发放单位OFFICE主键")
    @TableField("office_id")
    @NotBlank(message = "OFFICE号不能为空")
    private String officeId;

    @ApiModelProperty(value = "票证类型一 I:国际票 D:国内票",example="D")
    @TableField("ticket_type_one")
    @NotBlank(message = "票证类型一不能为空")
    private String ticketTypeOne;

    @ApiModelProperty(value = "票证类型二 DC:普通票证 RF:退票 MC:出票方式",example="DC")
    @TableField("ticket_type_two")
    @NotBlank(message = "票证类型二不能为空")
    private String ticketTypeTwo;

    @ApiModelProperty(value = "票证发放状态票证发放状态 UU：还未发放。 IU：已发放。 UO：使用完。",example="IU")
    @TableField("ticket_release_status")
    private String ticketReleaseStatus;

    @ApiModelProperty(value = "发放日期时间(即数据入库时间)",example="2022-02-17")
    @TableField("release_date")
    private Date releaseDate;

    @Override
    protected Serializable pkVal() {
        return this.ticketLimitsId;
    }

}
