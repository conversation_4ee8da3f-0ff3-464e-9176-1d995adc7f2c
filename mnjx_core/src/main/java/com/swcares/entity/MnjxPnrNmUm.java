package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_pnr_nm_um")
public class MnjxPnrNmUm extends Model<MnjxPnrNmUm> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "nm_um_id", type = IdType.ASSIGN_ID)
    private String nmUmId;

    @TableField("pnr_nm_id")
    private String pnrNmId;

    @TableField("um_age")
    private Integer umAge;


    @Override
    protected Serializable pkVal() {
        return this.nmUmId;
    }

}
