package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_pnr_nm")
public class MnjxPnrNm extends Model<MnjxPnrNm> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "旅客ID")
    @TableId(value = "pnr_nm_id", type = IdType.ASSIGN_ID)
    private String pnrNmId;

    @ApiModelProperty(value = "PNR_ID")
    @TableField("pnr_id")
    private String pnrId;

    @ApiModelProperty(value = "PNR序号")
    @TableField("pnr_index")
    private Integer pnrIndex;

    @ApiModelProperty(value = "旅客编号")
    @TableField(value = "psg_index", updateStrategy = FieldStrategy.IGNORED)
    private Integer psgIndex;

    @ApiModelProperty(value = "旅客类型 0成人 1儿童 2无人陪伴旅客 3 婴儿")
    @TableField("psg_type")
    private String psgType;

    @ApiModelProperty(value = "旅客姓名")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "拼音")
    @TableField("query_name")
    private String queryName;

    @ApiModelProperty(value = "旅客性别")
    @TableField("sex")
    private String sex;

    @ApiModelProperty(value = "中文姓名标识")
    @TableField("is_cnin")
    private String isCnin;

    @ApiModelProperty(value = "CTC标识")
    @TableField(value = "is_ctc", updateStrategy = FieldStrategy.IGNORED)
    private String isCtc;

    @ApiModelProperty(value = "ID标识")
    @TableField(value = "is_id", updateStrategy = FieldStrategy.IGNORED)
    private String isId;

    @ApiModelProperty(value = "ID原因")
    @TableField(value = "id_reason", updateStrategy = FieldStrategy.IGNORED)
    private String idReason;

    @ApiModelProperty(value = "输入值")
    @TableField("input_value")
    private String inputValue;

    @ApiModelProperty(value = "特殊旅客代码，如vip")
    @TableField("psg_ind")
    private String psgInd;

    @ApiModelProperty(value = "该条旅客被创建时的那次封口编号、如果首次创建为001，如果是从某一条已有旅客修改而来，则要匹配相应的封口编号，即创建封口编号并不总是为001")
    @TableField("create_at_no")
    private String createAtNo;

    @ApiModelProperty(value = "该条旅客被修改(及删除)后会变为无效进入PNR历史部分，此处记录了变更那次封口的编号")
    @TableField("change_at_no")
    private String changeAtNo;

    @ApiModelProperty(value = "修改（删除）类型，默认空，修改：C，删除：X")
    @TableField(value = "change_type", updateStrategy = FieldStrategy.IGNORED)
    private String changeType;

    @ApiModelProperty(value = "该旅客属于哪个团队的团队记录ID，关联至mnjx_pnr_gn表记录")
    @TableField("pnr_gn_id")
    private String pnrGnId;


    @Override
    protected Serializable pkVal() {
        return this.pnrNmId;
    }

}
