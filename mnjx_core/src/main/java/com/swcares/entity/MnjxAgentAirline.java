package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_agent_airline")
public class MnjxAgentAirline extends Model<MnjxAgentAirline> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "agent_airline_id", type = IdType.ASSIGN_ID)
    private String agentAirlineId;

    @ApiModelProperty(value = "代理人ID")
    @NotBlank(message = "代理人ID不能为空")
    @TableField("agent_id")
    private String agentId;

    @ApiModelProperty(value = "航空公司ID")
    @NotBlank(message = "航空公司ID不能为空")
    @TableField("airline_id")
    private String airlineId;


    @Override
    protected Serializable pkVal() {
        return this.agentAirlineId;
    }

}
