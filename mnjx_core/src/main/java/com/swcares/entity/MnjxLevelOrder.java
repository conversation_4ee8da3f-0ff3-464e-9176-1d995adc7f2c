package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_level_order")
public class MnjxLevelOrder extends Model<MnjxLevelOrder> {

    @ApiModelProperty(value = "levelOrderId")
    @TableId(value = "level_order_id", type = IdType.ASSIGN_ID)
    private String levelOrderId;

    @ApiModelProperty(value = "级别", example = "41")
    @TableField("level_code")
    private String levelCode;

    @ApiModelProperty(value = "指令Id")
    @TableField("order_Id")
    private String orderId;

    @Override
    protected Serializable pkVal() {
        return this.levelOrderId;
    }

}
