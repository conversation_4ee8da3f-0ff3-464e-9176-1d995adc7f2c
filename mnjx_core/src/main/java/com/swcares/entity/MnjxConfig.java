package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 模拟教学配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_config")
@ApiModel(value="MnjxConfig对象", description="模拟教学配置表")
public class MnjxConfig extends Model<MnjxConfig> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "类型")
    @TableField("type")
    private String type;

    @TableField("rmk")
    private String rmk;

    @ApiModelProperty(value = "1 可用 0不可用")
    @TableField("available")
    private String available;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
