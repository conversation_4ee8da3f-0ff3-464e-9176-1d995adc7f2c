package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * description：MnjxStandardPat对象
 *
 * <AUTHOR>
 * @date 2021/9/27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_standard_pat")
public class MnjxStandardPat extends Model<MnjxStandardPat> {
    @ApiModelProperty(value = "主键id")
    @TableId(value = "standard_pat_id", type = IdType.ASSIGN_ID)
    private String standardPatId;

    @ApiModelProperty(value = "起飞城市")
    @TableField("org_city_id")
    @NotBlank(message = "起飞城市不能为空")
    private String orgCityId;

    @ApiModelProperty(value = "到达城市")
    @TableField("dst_city_id")
    @NotBlank(message = "到达城市不能为空")
    private String dstCityId;

    @ApiModelProperty(value = "距离单价")
    @TableField("distance_price")
    @DecimalMin(message = "距离单价不能为负数", value = "0.00")
    private BigDecimal distancePrice;

    @ApiModelProperty(value = "飞行距离")
    @TableField("distance")
    @NotNull(message = "飞行距离不能为空")
    @Min(message = "距离不能为负数", value = 0L)
    private Integer distance;

    @ApiModelProperty(value = "币种代码")
    @TableField("currency_code")
    private String currencyCode;

    @ApiModelProperty(value = "正常运价")
    @TableField("normal_pat")
    @NotNull(message = "运价不能为空")
    private BigDecimal normalPat;


    @Override
    protected Serializable pkVal() {
        return this.standardPatId;
    }

}
