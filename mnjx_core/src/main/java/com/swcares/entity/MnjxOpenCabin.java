package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_open_cabin")
public class MnjxOpenCabin extends Model<MnjxOpenCabin> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "open_cabin_id", type = IdType.ASSIGN_ID)
    private String openCabinId;

    @ApiModelProperty(value = "航节飞行计划号")
    @TableField("plan_section_id")
    private String planSectionId;

    @ApiModelProperty(value = "票价等级")
    @TableField("ticket_level")
    private String ticketLevel;

    @ApiModelProperty(value = "舱位等级")
    @TableField("cabin_class")
    private String cabinClass;

    @ApiModelProperty(value = "销售舱位")
    @TableField("sell_cabin")
    private String sellCabin;

    @ApiModelProperty(value = "销售舱位价格")
    @TableField("sell_cabin_price")
    private Integer sellCabinPrice;

    @ApiModelProperty(value = "舱位等级状态")
    @TableField("open_cabin_status")
    private String openCabinStatus;

    @ApiModelProperty(value = "座位数")
    @TableField("seat_total")
    private Integer seatTotal;

    @ApiModelProperty(value = "")
    @TableField("seat_available")
    private Integer seatAvailable;

    @Override
    protected Serializable pkVal() {
        return this.openCabinId;
    }

}
