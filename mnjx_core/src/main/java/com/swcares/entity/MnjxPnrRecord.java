package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_pnr_record")
@ApiModel(value="MnjxPnrRecord对象", description="PNR历史记录项")
public class MnjxPnrRecord extends Model<MnjxPnrRecord> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "pnr_record_id", type = IdType.ASSIGN_ID)
    private String pnrRecordId;

    @ApiModelProperty(value = "PNR编号")
    @TableField("pnr_id")
    private String pnrId;

    @ApiModelProperty(value = "封口编号")
    @TableField("at_no")
    private String atNo;

    @ApiModelProperty(value = "历史中的PNR项序号")
    @TableField("pnr_index")
    private Integer pnrIndex;

    @ApiModelProperty(value = "历史中的PNR项类型")
    @TableField("pnr_type")
    private String pnrType;

    @ApiModelProperty(value = "历史中的PNR项内容")
    @TableField("input_value")
    private String inputValue;

    @ApiModelProperty(value = "删除/修改标记，删除X 修改姓名C，没有则空")
    @TableField("change_mark")
    private String changeMark;

    @ApiModelProperty(value = "删除/修改时的封口编号，没有则空")
    @TableField("change_at_no")
    private String changeAtNo;

    @ApiModelProperty(value = "成人或儿童旅客ID、婴儿ID")
    @TableField("pnr_nm_id")
    private String pnrNmId;

    @ApiModelProperty(value = "航段ID")
    @TableField("pnr_seg_id")
    private String pnrSegId;

    @ApiModelProperty(value = "出票时间")
    @TableField("issued_time")
    private String issuedTime;

    @ApiModelProperty(value = "打票机id")
    @TableField("printer_id")
    private String printerId;

    @Override
    protected Serializable pkVal() {
        return this.pnrRecordId;
    }

}
