package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_broadcast_status")
@ApiModel(value="MnjxBroadcastStatus对象", description="")
public class MnjxBroadcastStatus extends Model<MnjxBroadcastStatus> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "broadcast_status_id", type = IdType.ASSIGN_ID)
    private String broadcastStatusId;

    @TableField("template_no")
    private String templateNo;

    @TableField("flight_no")
    private String flightNo;

    @TableField("flight_date")
    private String flightDate;

    @TableField("status")
    private String status;


    @Override
    protected Serializable pkVal() {
        return this.broadcastStatusId;
    }

}
