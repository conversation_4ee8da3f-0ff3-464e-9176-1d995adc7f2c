package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MnjxDictType extends Model<MnjxDictType> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "字典类型id")
    @TableId(value = "dict_type_id", type = IdType.ASSIGN_ID)
    private String dictTypeId;

    @ApiModelProperty(value = "字典类型key（这个编码是唯一的，我们建议都大写，标准统一）", example = "RECORD_STATUS")
    @TableField(value = "dict_type_key")
    private String dictTypeKey;

    @ApiModelProperty(value = "字典类型名称")
    @TableField("dict_type_name")
    private String dictTypeName;

    @ApiModelProperty(value = "字典类型状态（1有效，0无效）")
    @TableField("dict_type_status")
    private String dictTypeStatus;


    @Override
    protected Serializable pkVal() {
        return this.dictTypeKey;
    }

}
