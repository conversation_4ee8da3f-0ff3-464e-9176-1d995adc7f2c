package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旅客值机特服信息存储
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_psg_cki_option")
@ApiModel(value="MnjxPsgCkiOption对象", description="旅客值机特服信息存储")
public class MnjxPsgCkiOption extends Model<MnjxPsgCkiOption> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "psg_cki_option_id", type = IdType.ASSIGN_ID)
    private String psgCkiOptionId;

    @TableField("psg_cki_id")
    private String psgCkiId;

    @ApiModelProperty(value = "特服类型")
    @TableField("option_type")
    private String optionType;

    @ApiModelProperty(value = "特服值")
    @TableField("option_value")
    private String optionValue;


    @Override
    protected Serializable pkVal() {
        return this.psgCkiOptionId;
    }

}
