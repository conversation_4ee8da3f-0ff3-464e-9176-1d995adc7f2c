package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@ToString
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_pnr")
public class MnjxPnr extends Model<MnjxPnr> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "pnr_id", type = IdType.ASSIGN_ID)
    private String pnrId;

    @ApiModelProperty(value = "PNR大编码 航空公司编码")
    @TableField("pnr_ics")
    private String pnrIcs;

    @ApiModelProperty(value = "PNR小编码 代理人编码")
    @TableField("pnr_crs")
    private String pnrCrs;

    @ApiModelProperty(value = "PNR状态：未提交状态OP  已提交状态CO  已删除状态DEL")
    @TableField("pnr_status")
    private String pnrStatus;

    @ApiModelProperty(value = "PNR创建者的SI-ID")
    @TableField("create_si_id")
    private String createSiId;

    @ApiModelProperty(value = "PNR创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "当前PNR索引最大值")
    @TableField("max_index")
    private Integer maxIndex;

    @ApiModelProperty(value = "同一个PNR中的所有旅客（不超过9人）会被自动视为一个团队，随机生成团队名，一般是一个字母")
    @TableField("default_group_name")
    private String defaultGroupName;

    @ApiModelProperty(value = "订票OFFICE，责任组")
    @TableField("create_office_no")
    private String createOfficeNo;

    @ApiModelProperty(value = "订票OFFICE的序号")
    @TableField("create_office_no_eid")
    private Integer createOfficeNoEid;

    @ApiModelProperty(value = "建控的线程ID")
    @TableField("control_thread_id")
    private String controlThreadId;

    @ApiModelProperty(value = "pnr联程标识")
    @TableField("interlink")
    private String interlink;

    @Override
    protected Serializable pkVal() {
        return this.pnrId;
    }

}
