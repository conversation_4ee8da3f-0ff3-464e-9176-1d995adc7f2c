<?xml version="1.0" encoding="UTF-8"?>
<!-- scan: 当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true。 -->
<!-- scanPeriod: 设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug: 当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration scan="true" debug="false" scanPeriod="60 seconds"
               xmlns="http://ch.qos.logback/xml/ns/logback"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xsi:schemaLocation="http://ch.qos.logback/xml/ns/logback https://raw.githubusercontent.com/enricopulatzo/logback-XSD/master/src/main/xsd/logback.xsd">


    <!-- 子节点<property> ：用来定义变量值，它有两个属性name和value，通过<property>定义的值会被插入到logger上下文中，可以使“${}”来使用变量。 -->
    <property name="contextName" value="com.swcares"/>

    <!-- 子节点<contextName>：用来设置上下文名称，每个logger都关联到logger上下文，默认上下文名称为default。但可以使用<contextName>设置成其他名字，用于区分不同应用程序的记录。一旦设置，不能修改。 -->
    <contextName>${contextName}</contextName>

    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径,我们这里使用的相对路径，不符合规范 -->
    <property name="log_path" value="logs/voiceBroadcast"/>
    <property name="log_name" value="voiceBroadcast.slf4j"/>
    <property name="log_name_suf" value="log"/>
    <property name="log_full_name" value="${log_path}/${log_name}.${log_name_suf}"/>

    <!-- %m输出的信息,%p日志级别,%t线程名,%d日期,%c类的全名,%i索引【从数字0开始递增】,,, -->
    <!-- <pattern>%d %p (%file:%line\)- %m%n</pattern> -->
    <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %msg：日志消息，%n是换行符,%logger{50}:是class的全名,后面的数字代表限制最长的字符 -->
    <!-- %d == %d{yyyy-MM-dd HH:mm:ss.SSS} -->
    <!-- %p == %-5level 字符个数有差别 -->
    <!-- %m == %msg -->
    <!-- %c == %logger{50} %c不简写，%logger{50}有简写 -->
    <!-- 有简写情况：org.springframework.boot.web.embedded.tomcat.TomcatWebServer 被简写为：o.s.boot.web.embedded.tomcat.TomcatWebServer -->
    <!-- %t == %thread -->
    <!-- %i 一般用于文件 -->
    <property name="pattern_formula" value="%d [%t] %p %c\\(%file:%line\\)- %m%n"/>

    <!-- 子节点<appender>：负责写日志的组件，它有两个必要属性name和class。name指定appender名称，class指定appender的全限定名 -->
    <!-- appender是configuration的子节点，是负责写日志的组件。 -->
    <!-- ConsoleAppender：把日志输出到控制台 -->
    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <!-- <encoder>：对记录事件进行格式化。负责两件事，一是把日志信息转换成字节数组，二是把字节数组写入到输出流。 -->
        <!-- PatternLayoutEncoder 是唯一有用的且默认的encoder ，有一个<pattern>节点，用来设置日志的输入格式。使用“%”加“转换符”方式，如果要输出“%”，则必须用“\”对“\%”进行转义。 -->
        <encoder>
            <pattern>${pattern_formula}</pattern>
            <!-- 控制台也要使用UTF-8，不要使用GBK，否则会中文乱码 -->
            <charset>UTF-8</charset>
        </encoder>
        <target>System.out</target>
    </appender>

    <!-- FileAppender：把日志添加到文件 -->
    <!-- RollingFileAppender：滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件 -->
    <!-- 以下的大概意思是：1.先按日期存日志，日期变了，将前一天的日志文件名重命名为XXX%日期%索引，新的日志仍然是sys.log -->
    <!-- 2.如果日期没有发生变化，但是当前日志的文件大小超过1KB时，对当前日志进行分割 重命名 -->

    <appender name="syslog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- <file>：被写入的文件名，可以是相对目录，也可以是绝对目录，如果上级目录不存在会自动创建，没有默认值。 -->
        <file>${log_full_name}</file>
        <!-- rollingPolicy:当发生滚动时，决定 RollingFileAppender 的行为，涉及文件移动和重命名。 -->
        <!-- TimeBasedRollingPolicy：基于时间的滚动策略  最常用的滚动策略，它根据时间来制定滚动策略，既负责滚动也负责出发滚动 -->
        <!-- SizeAndTimeBasedRollingPolicy：基于文件大小和时间滚策略 当在时间周期内日志文件超过指定大小，则创建新的日志文件，否则每一个时间周期生成一个日志-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--归档文件名称-->
            <!--当指定多个: %d参数时 必须声明次要的%d参数  也就是用 'aux' 来指定辅助参数-->
            <fileNamePattern>${log_path}/${log_name}/%d{yyyy-MM,aux}/%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--单个日志最大容量 至少10MB才能看得出来-->
            <maxFileSize>50MB</maxFileSize>
            <!--文件最大保存历史数量-->
            <maxHistory>6</maxHistory>
            <!--所有日志最多占多大容量-->
            <totalSizeCap>100MB</totalSizeCap>
        </rollingPolicy>
        <!--   encoder:  一是用于将日志信息转为字节数组，二是将字节数组写入到输入流,目前唯一有效并默认的实现类就是PatternLayoutEncoder-->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger -%msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!--暂时不区分目录存日志-->
        <!--        <filter class="ch.qos.logback.classic.filter.LevelFilter">-->
        <!--            <level>INFO</level>-->
        <!--            <onMatch>ACCEPT</onMatch>-->
        <!--            <onMismatch>DENY</onMismatch>-->
        <!--        </filter>-->
    </appender>


    <!-- 子节点<loger>：用来设置某一个包或具体的某一个类的日志打印级别、以及指定<appender> -->
    <!-- name: 用来指定受此loger约束的某一个包或者具体的某一个类 -->
    <!-- com.swcares为根包，也就是只要是发生在这个根包下面的所有日志操作行为的权限>=stdout -->
    <!-- 级别依次为【从高到低】：FATAL > ERROR > WARN > INFO > DEBUG > TRACE 大小写无关 -->
    <!-- addtivity: 是否向上级loger传递打印信息。默认是true -->
    <logger name="com.swcares" level="TRACE"/>

    <!-- 控制台输出日志级别 -->
    <!-- 配置表示把>=INFO级别的日志都输出到控制台 -->
    <root level="INFO">
        <appender-ref ref="stdout"/>
        <appender-ref ref="syslog"/>
    </root>
</configuration>
