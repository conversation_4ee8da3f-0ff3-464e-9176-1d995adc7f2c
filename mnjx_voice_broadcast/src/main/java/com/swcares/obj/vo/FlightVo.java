package com.swcares.obj.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/15 14:36
 */
@Data
public class FlightVo {

    private String planFlightId;

    private String tcardId;

    private String flightNo;

    private boolean changeGate = false;

    private boolean delay = false;

    private String ckStatus;

    private String actualOff;

    private String actualArr;

    private String arrDateChange;

    private String org;

    private String dst;

    private String orgAirport;

    private String dstAirport;

    private String gate;

    private String oldGate;

    private List<AudioMessageVo> audioMessageVoList = new ArrayList<>();
}
