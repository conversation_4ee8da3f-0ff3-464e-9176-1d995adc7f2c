<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.crs.mapper.RfisMapper">
    <select id="retrievePnrId" resultType="java.lang.String">
        select mpn.pnr_id from mnjx_pnr_nm mpn WHERE mpn.pnr_nm_id=
        <choose>
            <when test="pnrNmId!=null and pnrNmId !=''">
                #{pnrNmId}
            </when>
            <when test="nmXnId!=null and nmXnId!=''">
                (select mnx.pnr_nm_id from mnjx_nm_xn mnx WHERE mnx.nm_xn_id=#{nmXnId})
            </when>
        </choose>
    </select>
    <select id="retrieveOpenCabinList" resultType="com.swcares.entity.MnjxOpenCabin">
        select moc.*
        from mnjx_open_cabin moc
                 left join mnjx_plan_section mps on
            moc.plan_section_id = mps.plan_section_id
                 left join mnjx_plan_flight mpf on
            mps.plan_flight_id = mpf.plan_flight_id
                 left join mnjx_tcard mt on
            mpf.tcard_id = mt.tcard_id
                 left join mnjx_flight mf on
            mt.flight_id = mf.flight_id
        where mf.flight_no = #{flightNo}
          and mpf.flight_date = #{flightDate}
          and moc.sell_cabin = #{sellCabin}
    </select>

</mapper>