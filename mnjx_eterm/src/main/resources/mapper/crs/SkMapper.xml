<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.crs.mapper.SkMapper">
    <select id="retrieveTcardIdsByCityPair" resultType="java.lang.String">
        SELECT c.tcard_id FROM (
        SELECT a.tcard_id FROM mnjx_tcard_section a WHERE a.airport_id IN
        <foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
            #{orgId}
        </foreach>
        AND a.tcard_id IN
        (SELECT b.tcard_id FROM mnjx_tcard_section b WHERE b.airport_id IN
        <foreach collection="dstIdList" item="dstId" open="(" separator="," close=")">
            #{dstId}
        </foreach>
        )
        )c
    </select>
</mapper>
