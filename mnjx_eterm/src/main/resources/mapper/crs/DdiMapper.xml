<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.crs.mapper.DdiMapper">

    <select id="retrieveOfficeJoinAgent" resultType="com.swcares.eterm.crs.obj.vo.DdiVo">
        SELECT mo.office_id,
               mo.office_no,
               ma.agent_id,
               ma.agent_iata,
               ma.agent_ename,
               ma.agent_contact_cname,
               ma.agent_contact_address,
               ma.agent_contact_phone,
               mp.printer_no,
               mp.printer_pid,
               mp.currency_type,
               ms.si_pid,
               ms.si_no
        FROM mnjx_office mo
                 left JOIN mnjx_agent ma ON mo.office_type = '0' AND mo.org_id = ma.agent_id
                 left JOIN mnjx_printer mp ON mo.office_id = mp.office_id
                 left JOIN mnjx_si ms ON mp.si_id = ms.si_id
        where mo.office_no = #{officeNum}
        ORDER BY abs(mp.printer_no) asc
    </select>

</mapper>
