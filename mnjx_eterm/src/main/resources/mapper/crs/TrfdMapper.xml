<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.crs.mapper.TrfdMapper">

    <select id="retrievePnrId" resultType="java.lang.String">
        select mpn.pnr_id from mnjx_pnr_nm mpn WHERE mpn.pnr_nm_id=
        <choose>
            <when test="pnrNmId!=null and pnrNmId !=''">
                #{pnrNmId}
            </when>
            <when test="nmXnId!=null and nmXnId!=''">
                (select mnx.pnr_nm_id from mnjx_nm_xn mnx WHERE mnx.nm_xn_id=#{nmXnId})
            </when>
        </choose>
    </select>

    <select id="retrievePsgInfo" resultType="com.swcares.eterm.crs.obj.dto.TrfdZdto">
        SELECT
        mpn.pnr_id,
        mpn.pnr_nm_id,
        mnx.nm_xn_id,
        mpn.psg_index,
        <choose>
            <when test="pnrNmId!=null and pnrNmId !=''">
                CASE WHEN mpn.psg_type = '0' THEN 'ADT'
                WHEN mpn.psg_type = '1' THEN 'CHD'
                WHEN mpn.psg_type = '2' THEN 'UM'
                END AS psgType,
                IFNULL(mnx.xn_cname,mpn.`name`) as psgName
                FROM
                mnjx_pnr_nm mpn
                LEFT JOIN mnjx_nm_xn mnx ON mpn.pnr_nm_id=mnx.pnr_nm_id
                WHERE
                mpn.pnr_nm_id = #{pnrNmId}
            </when>
            <when test="nmXnId!=null and nmXnId!=''">
                CASE WHEN mnx.xn_cname is NOT NULL THEN 'INF' END AS psgType,
                IFNULL(mnx.xn_cname,mpn.`name`) as psgName
                FROM
                mnjx_pnr_nm mpn
                LEFT JOIN mnjx_nm_xn mnx ON mpn.pnr_nm_id=mnx.pnr_nm_id
                WHERE
                mpn.pnr_nm_id =
                (select mnx.pnr_nm_id from mnjx_nm_xn mnx WHERE mnx.nm_xn_id=#{nmXnId})
            </when>
        </choose>
    </select>

</mapper>