<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.cki.mapper.PrMapper">

    <!-- 查询Pr详细 -->
    <sql id="prSql">
		select
		mps.pnr_seg_id,
		mps.pnr_id ,
		mpn.pnr_nm_id,
		mps.flight_no,
		mps.carrier_flight,
		mps.flight_date ,
		mps.estimate_off,
		mps.estimate_arr,
		mpc.cabin_class ,
		mpc.sell_cabin,
		mps.org,
		mps.dst,
		mps.action_code,
		mpn.query_name,
		mpn.sex,
		mpset.seat_exst,
		mpn.name,
		mpn.psg_type,
		mpn.is_cnin,
		mpn.is_ctc,
		mp.pnr_ics ,
		mp.pnr_crs,
		mp.default_group_name,
		mpc.aboard_no,
		mpc.is_hb,
		mpc.hb_no,
		mpset.psg_seat seat,
		mpnt.tn_id,
		mp.interlink,
	    mpc.psg_cki_id,
		mpc.cki_status,
		mpc.ures,
		mpc.nrec,
		mpc.cap,
		mps.pnr_seg_no
		from
			mnjx_pnr_seg mps
		left join mnjx_pnr_nm mpn on
			mps.pnr_id = mpn.pnr_id
		left join mnjx_pnr mp on
			mpn.pnr_id = mp.pnr_id
		left join mnjx_psg_cki mpc on
			mpn.pnr_nm_id = mpc.pnr_nm_id and mps.pnr_seg_no = mpc.pnr_seg_no
		left join mnjx_psg_seat mpset on
			mpc.psg_cki_id = mpset.psg_cki_id
		left join mnjx_pnr_nm_tn mpnt on
			mpn.pnr_nm_id = mpnt.pnr_nm_id
		where
		1 = 1
	</sql>

    <select id="retrievePlanFlightInitial" resultType="com.swcares.entity.MnjxPlanFlight">
		select
			mpf.*
		from mnjx_flight mf
		join mnjx_tcard mt on mf.flight_id = mt.flight_id
		join mnjx_plan_flight mpf on mt.tcard_id = mpf.tcard_id
		where
			mf.flight_no = #{flightNo}
			and mpf.flight_date = #{flightDate}
    </select>

    <select id="retrieveSellCabin" resultType="com.swcares.entity.MnjxOpenCabin">
		select
			moc.*
		from mnjx_flight mf
		join mnjx_tcard mt on mf.flight_id = mt.flight_id
		join mnjx_plan_flight mpf on mt.tcard_id = mpf.tcard_id
		join mnjx_plan_section mps on mpf.plan_flight_id = mps.plan_flight_id
		join mnjx_open_cabin moc on mps.plan_section_id = moc.plan_section_id
		where
			mf.flight_no = #{flightNo}
			and mpf.flight_date = #{flightDate}
			and moc.sell_cabin = #{sellCabin}
			and mps.dep_apt_id = #{aptId}
	</select>

    <select id="retrieveDepartureCity" resultType="com.swcares.entity.MnjxTcardSection">
		select
			mts.*
		from mnjx_flight mf
		join mnjx_tcard mt on mf.flight_id = mt.flight_id
		join mnjx_tcard_section mts on mt.tcard_id = mts.tcard_id
		where
			mf.flight_no = #{flightNo}
			and mts.airport_id = #{airportId}
			and mts.is_last_section = 0
	</select>


    <select id="retrieveBySeatNo" resultType="com.swcares.entity.MnjxSeat">
		select
			ms.*
		from mnjx_flight mf
		join mnjx_tcard mt on mf.flight_id = mt.flight_id
		join mnjx_plan_flight mpf on mt.tcard_id = mpf.tcard_id
		join mnjx_plan_section mps on mpf.plan_flight_id = mps.plan_flight_id
		join mnjx_open_cabin moc on mps.plan_section_id = moc.plan_section_id
		join mnjx_seat ms on moc.open_cabin_id = ms.open_cabin_id
		where
			mf.flight_no = #{flightNo}
			and mps.dep_apt_id  = #{airportId}
			and mpf.flight_date = #{flightDate}
			and ms.seat_no = #{seatNo}
	</select>

    <select id="retrieveByQueryName" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
        <include refid="prSql"/>
        and mps.flight_no = #{flightNo}
        and mps.flight_date = #{flightDate}
        and mps.org = #{org}
        <if test='sellCabin != "*"'>
            and mpc.sell_cabin = #{sellCabin}
        </if>
        and mpn.query_name like concat('', #{queryName}, '%')
        group by mpn.pnr_nm_id
    </select>

	<select id="retrieveByQueryNameAndCarrierFlight" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
		<include refid="prSql"/>
		and mps.carrier_flight = #{flightNo}
		and mps.flight_date = #{flightDate}
		and mps.org = #{org}
		<if test='sellCabin != "*"'>
			and mpc.sell_cabin = #{sellCabin}
		</if>
		and mpn.query_name like concat('', #{queryName}, '%')
		group by mpn.pnr_nm_id
	</select>

    <select id="retrieveByPnr" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
        <include refid="prSql"/>
        and mps.flight_no = #{flightNo}
        and mps.flight_date = #{flightDate}
        and mps.org = #{org}
        <if test='sellCabin != "*"'>
            and mpc.sell_cabin = #{sellCabin}
        </if>
        and (mp.pnr_crs = #{pnr} or mp.pnr_ics = #{pnr})
        group by mpn.pnr_nm_id
    </select>

	<select id="retrieveByPnrAndCarrierFlight" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
		<include refid="prSql"/>
		and mps.carrier_flight = #{flightNo}
		and mps.flight_date = #{flightDate}
		and mps.org = #{org}
		<if test='sellCabin != "*"'>
			and mpc.sell_cabin = #{sellCabin}
		</if>
		and (mp.pnr_crs = #{pnr} or mp.pnr_ics = #{pnr})
		group by mpn.pnr_nm_id
	</select>

    <select id="retrieveBySeatNoPr" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
        <include refid="prSql"/>
        and mps.flight_no = #{flightNo}
        and mps.flight_date = #{flightDate}
        and mps.org = #{org}
        <if test='sellCabin != "*"'>
            and mpc.sell_cabin = #{sellCabin}
        </if>
        and mpset.psg_seat = #{seatNo}
        group by mpn.pnr_nm_id
    </select>

	<select id="retrieveBySeatNoPrAndCarrierFlight" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
		<include refid="prSql"/>
		and mps.flight_no = #{flightNo}
		and mps.flight_date = #{flightDate}
		and mps.org = #{org}
		<if test='sellCabin != "*"'>
			and mpc.sell_cabin = #{sellCabin}
		</if>
		and mpset.psg_seat = #{seatNo}
		group by mpn.pnr_nm_id
	</select>

    <select id="retrieveByAboardNo" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
        <include refid="prSql"/>
        and mps.flight_no = #{flightNo}
        and mps.flight_date = #{flightDate}
        and mps.org = #{org}
        <if test='sellCabin != "*"'>
            and mpc.sell_cabin = #{sellCabin}
        </if>
        and mpc.aboard_no = #{aboardNo}
        group by mpn.pnr_nm_id
    </select>

	<select id="retrieveByAboardNoAndCarrierFlight" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
		<include refid="prSql"/>
		and mps.carrier_flight = #{flightNo}
		and mps.flight_date = #{flightDate}
		and mps.org = #{org}
		<if test='sellCabin != "*"'>
			and mpc.sell_cabin = #{sellCabin}
		</if>
		and mpc.aboard_no = #{aboardNo}
		group by mpn.pnr_nm_id
	</select>

    <select id="retrieveByPnrNmId" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
        <include refid="prSql"/>
        and mpn.pnr_nm_id = #{pnrNmId}
        <if test="flightNo != null and flightNo != ''">
			and mps.flight_no = #{flightNo}
		</if>
		<if test="sellCabin != null and sellCabin != ''">
			and mpc.sell_cabin = #{sellCabin}
		</if>
		<if test="estimateOff != null and estimateOff != ''">
			and mps.estimate_off = #{estimateOff}
		</if>
		<if test="org != null and org != ''">
			and mps.org = #{org}
		</if>
    </select>

	<select id="retrieveByPnrNmIdAndCarrierFlight" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
		<include refid="prSql"/>
		and mpn.pnr_nm_id = #{pnrNmId}
		<if test="flightNo != null and flightNo != ''">
			and mps.carrier_flight = #{flightNo}
		</if>
		<if test="flightDate != null and flightDate != ''">
			and mps.flight_date = #{flightDate}
		</if>
		<if test="sellCabin != null and sellCabin != ''">
			and mpc.sell_cabin = #{sellCabin}
		</if>
		<if test="estimateOff != null and estimateOff != ''">
			and mps.estimate_off = #{estimateOff}
		</if>
		<if test="org != null and org != ''">
			and mps.org = #{org}
		</if>
	</select>

    <select id="retrieveByPnrHbNo" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
        <include refid="prSql"/>
        and mpc.hb_no = #{hbNo}
		and mps.flight_date in
		<foreach collection="rangeDate" index="index" item="date" separator="," close=")" open="(">
			#{date}
		</foreach>
    </select>

    <select id="retrieveByCity" resultType="com.swcares.entity.MnjxCity">
		select
			mc.*
		from
			mnjx_airport ma
		join mnjx_city mc on
			ma.city_id  = mc.city_id
		where
			ma.airport_code = #{airportCode}
	</select>

    <select id="retrieveByCnd" resultType="com.swcares.entity.MnjxCnd">
		select
			mc.*
		from
			mnjx_flight mf
		join mnjx_tcard mt on
			mf.flight_id = mt.flight_id
		join mnjx_plan_flight mpf on
			mt.tcard_id = mpf.tcard_id
		join mnjx_cnd mc on
			mpf.cnd_no = mc.cnd_no
		where
			mf.flight_no = #{flightNo}
			and mpf.flight_date = #{flightDate}
	</select>

    <select id="retrieveCtcByNmId" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrCtcDto">
		select
			mo.office_no,
			mpntn.issued_time
		from
			mnjx_pnr_nm_tn mpntn
		left join mnjx_si ms on
			mpntn.issued_si_id = ms.si_id
		left join mnjx_office mo on
			ms.office_id = mo.office_id
		where
			1 = 1
			and mpntn.pnr_nm_id = #{pnrNmId}
	</select>

    <select id="retrieveByPsgCity" resultType="com.swcares.eterm.dcs.cki.obj.dto.PsgCityDto">
		select
			mps.flight_no,
			mps.flight_date,
			mps.sell_cabin,
			mps.org,
			mps.dst,
			(select ma.city_id  from mnjx_airport ma where ma.airport_code = mps.org) org_city,
			(select ma.city_id from mnjx_airport ma where ma.airport_code = mps.dst) dst_city,
			mps.pnr_seg_no
		from
			mnjx_pnr_seg mps
		where
			mps.pnr_id = #{pnrId}
			<if test="pnrSegNo != null and pnrSegNo != ''">
				and mps.pnr_seg_no = #{pnrSegNo}
			</if>
	</select>

    <select id="retrieveByLuggageNo" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
		select
			mps.pnr_seg_id,
			mps.pnr_id ,
			mpn.pnr_nm_id,
			mps.flight_no,
			mps.carrier_flight,
			mps.flight_date ,
			mps.estimate_off,
			mpc.cabin_class ,
			mpc.sell_cabin,
			mps.org,
			mps.dst,
			mps.action_code,
			mpn.query_name,
			mpn.sex,
			mpn.name,
			mpn.psg_type,
			mpn.is_cnin,
			mpn.is_ctc,
			mp.pnr_ics ,
			mp.pnr_crs,
			mp.default_group_name,
			mpc.aboard_no,
			mpc.hb_no,
			mpset.psg_seat seat,
			mpnt.tn_id,
			mp.interlink,
			mpc.psg_cki_id,
			mpc.cki_status,
			mpc.cap,
			mps.pnr_seg_no
		from
			mnjx_pnr_seg mps
		left join mnjx_pnr_nm mpn on
			mps.pnr_id = mpn.pnr_id
		left join mnjx_pnr mp on
			mpn.pnr_id = mp.pnr_id
		left join mnjx_psg_cki mpc on
			mpn.pnr_nm_id = mpc.pnr_nm_id and mps.pnr_seg_no = mpc.pnr_seg_no
		left join mnjx_psg_seat mpset on
			mpc.psg_cki_id = mpset.psg_cki_id
		left join mnjx_pnr_nm_tn mpnt on
			mpn.pnr_nm_id = mpnt.pnr_nm_id
		left join mnjx_luggage ml on
			mpn.pnr_nm_id = ml.pnr_nm_id
		where
			1 = 1
			and ml.luggage_type  = 1
			and mps.flight_no = #{flightNo}
			and mps.flight_date = #{flightDate}
			and mps.org = #{org}
			and mpc.sell_cabin = #{sellCabin}
			and ml.luggage_no = #{luggageNo}
		group by
			mpn.pnr_nm_id
	</select>

	<select id="retrieveByLuggageNoAndCarrierFlight" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
		select
			mps.pnr_seg_id,
			mps.pnr_id ,
			mpn.pnr_nm_id,
			mps.flight_no,
			mps.carrier_flight,
			mps.flight_date ,
			mps.estimate_off,
			mpc.cabin_class ,
			mpc.sell_cabin,
			mps.org,
			mps.dst,
			mps.action_code,
			mpn.query_name,
			mpn.sex,
			mpn.name,
			mpn.psg_type,
			mpn.is_cnin,
			mpn.is_ctc,
			mp.pnr_ics ,
			mp.pnr_crs,
			mp.default_group_name,
			mpc.aboard_no,
			mpc.hb_no,
			mpset.psg_seat seat,
			mpnt.tn_id,
			mp.interlink,
			mpc.psg_cki_id,
			mpc.cki_status,
			mpc.cap,
			mps.pnr_seg_no
		from
			mnjx_pnr_seg mps
				left join mnjx_pnr_nm mpn on
				mps.pnr_id = mpn.pnr_id
				left join mnjx_pnr mp on
				mpn.pnr_id = mp.pnr_id
				left join mnjx_psg_cki mpc on
				mpn.pnr_nm_id = mpc.pnr_nm_id and mps.pnr_seg_no = mpc.pnr_seg_no
				left join mnjx_psg_seat mpset on
				mpc.psg_cki_id = mpset.psg_cki_id
				left join mnjx_pnr_nm_tn mpnt on
				mpn.pnr_nm_id = mpnt.pnr_nm_id
				left join mnjx_luggage ml on
				mpn.pnr_nm_id = ml.pnr_nm_id
		where
			1 = 1
		  and ml.luggage_type  = 1
		  and mps.carrier_flight = #{flightNo}
		  and mps.flight_date = #{flightDate}
		  and mps.org = #{org}
		  and mpc.sell_cabin = #{sellCabin}
		  and ml.luggage_no = #{luggageNo}
		group by
			mpn.pnr_nm_id
	</select>

    <select id="retrieveByTicketNo" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
        select
        mps.pnr_seg_id,
        mps.pnr_id ,
        mpn.pnr_nm_id,
        mps.flight_no,
        mps.flight_date ,
        mps.estimate_off,
		mpc.cabin_class ,
		mpc.sell_cabin,
        mps.org,
        mps.dst,
        mps.action_code,
        mpn.query_name,
        mpn.sex,
        mpn.name,
        mpn.psg_type,
        mpn.is_cnin,
        mpn.is_ctc,
        mp.pnr_ics ,
        mp.pnr_crs,
        mp.default_group_name,
        mpc.aboard_no,
        mpc.hb_no,
        mpset.psg_seat seat,
        mpnt.tn_id,
        mp.interlink,
        mpc.psg_cki_id,
        mpc.cki_status,
        mpntk.ticket_no,
		mpc.cap,
		mps.pnr_seg_no
        from
        mnjx_pnr_seg mps
        left join mnjx_pnr_nm mpn on
        mps.pnr_id = mpn.pnr_id
        left join mnjx_pnr mp on
        mpn.pnr_id = mp.pnr_id
        left join mnjx_psg_cki mpc on
        mpn.pnr_nm_id = mpc.pnr_nm_id and mps.pnr_seg_no = mpc.pnr_seg_no
        left join mnjx_psg_seat mpset on
        mpc.psg_cki_id = mpset.psg_cki_id
        left join mnjx_pnr_nm_tn mpnt on
        mpn.pnr_nm_id = mpnt.pnr_nm_id
        left join mnjx_pnr_nm_ticket mpntk on
        mpnt.tn_id = mpntk.pnr_nm_tn_id
        where
        1 = 1
        and mps.flight_no = #{flightNo}
        and mps.flight_date = #{flightDate}
        and mps.org = #{org}
        <if test='sellCabin != "*"'>
            and mpc.sell_cabin = #{sellCabin}
        </if>
        and mpntk.ticket_no = #{ticketNo}
        group by mpn.pnr_nm_id
    </select>

	<select id="retrieveByTicketNoAndCarrierFlight" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
		select
		mps.pnr_seg_id,
		mps.pnr_id ,
		mpn.pnr_nm_id,
		mps.flight_no,
		mps.carrier_flight,
		mps.flight_date ,
		mps.estimate_off,
		mpc.cabin_class ,
		mpc.sell_cabin,
		mps.org,
		mps.dst,
		mps.action_code,
		mpn.query_name,
		mpn.sex,
		mpn.name,
		mpn.psg_type,
		mpn.is_cnin,
		mpn.is_ctc,
		mp.pnr_ics ,
		mp.pnr_crs,
		mp.default_group_name,
		mpc.aboard_no,
		mpc.hb_no,
		mpset.psg_seat seat,
		mpnt.tn_id,
		mp.interlink,
		mpc.psg_cki_id,
		mpc.cki_status,
		mpntk.ticket_no,
		mpc.cap,
		mps.pnr_seg_no
		from
		mnjx_pnr_seg mps
		left join mnjx_pnr_nm mpn on
		mps.pnr_id = mpn.pnr_id
		left join mnjx_pnr mp on
		mpn.pnr_id = mp.pnr_id
		left join mnjx_psg_cki mpc on
		mpn.pnr_nm_id = mpc.pnr_nm_id and mps.pnr_seg_no = mpc.pnr_seg_no
		left join mnjx_psg_seat mpset on
		mpc.psg_cki_id = mpset.psg_cki_id
		left join mnjx_pnr_nm_tn mpnt on
		mpn.pnr_nm_id = mpnt.pnr_nm_id
		left join mnjx_pnr_nm_ticket mpntk on
		mpnt.tn_id = mpntk.pnr_nm_tn_id
		where
		1 = 1
		and mps.carrier_flight = #{flightNo}
		and mps.flight_date = #{flightDate}
		and mps.org = #{org}
		<if test='sellCabin != "*"'>
			and mpc.sell_cabin = #{sellCabin}
		</if>
		and mpntk.ticket_no = #{ticketNo}
		group by mpn.pnr_nm_id
	</select>

    <select id="retrieveByBtLuggageNo" resultType="com.swcares.entity.MnjxLuggage">
        select
        ml.*
        from
        mnjx_luggage ml
        left join mnjx_pnr_nm mpn on
        ml.pnr_nm_id = mpn.pnr_nm_id
        left join mnjx_pnr_seg mps on
        mpn.pnr_id = mps.pnr_id
        where
        1 = 1
        and ml.luggage_type = '1'
        and ml.luggage_no = #{luggageNo}
        and mps.flight_date in
        <foreach collection="rangeDate" index="index" item="date" separator="," close=")" open="(">
            #{date}
        </foreach>
    </select>

	<select id="retrieveBySegTicketNo" resultType="com.swcares.entity.MnjxPnrNmTicket">
		select
			mpnt.*
		from
			mnjx_pnr_nm mpn
				left join mnjx_pnr mp on mpn.pnr_id = mp.pnr_id
				left join mnjx_pnr_seg mps on mp.pnr_id = mps.pnr_id
				LEFT JOIN mnjx_pnr_nm_tn mptn ON mpn.pnr_nm_id = mptn.pnr_nm_id
				left join mnjx_pnr_nm_ticket mpnt on (mptn.tn_id = mpnt.pnr_nm_tn_id AND mps.pnr_seg_id  = mpnt.s1_id or mps.pnr_seg_id = mpnt.s2_id)
		where
			mpn.pnr_nm_id = #{pnrNmId}
		  and mps.flight_no = #{flightNo}
		  and mps.flight_date = #{flightDate}
	</select>

	<select id="retrieveByInterline" resultType="com.swcares.eterm.dcs.cki.obj.dto.PrDto">
		<include refid="prSql"/>
		<if test="pnrNmId != null and pnrNmId != ''">
			and mpn.pnr_nm_id = #{pnrNmId}
		</if>
		and mps.pnr_id = #{pnrId}
		order by mps.pnr_seg_no
	</select>

	<select id="retrieveByFlightSection" resultType="com.swcares.eterm.dcs.cki.obj.dto.FlightSectionDto">
		SELECT
			mf.flight_no,
			mpf.flight_date,
			mps.dep_apt_id,
			mps.arr_apt_id,
			mps.estimate_off,
			mps.estimate_arr,
			mps.estimate_arr_change
		FROM
			mnjx_flight mf
		JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
		JOIN mnjx_plan_flight mpf ON mt.tcard_id = mpf.tcard_id
		JOIN mnjx_plan_section mps ON mpf.plan_flight_id = mps.plan_flight_id
		WHERE
			mf.flight_no in
			<foreach collection="flightNos" index="index" item="flightNo" separator="," close=")" open="(">
				#{flightNo}
			</foreach>
			and mpf.flight_date in
			<foreach collection="flightDates" index="index" item="flightDate" separator="," close=")" open="(">
				#{flightDate}
			</foreach>
	</select>

	<select id="retrievePlanSection" resultType="com.swcares.entity.MnjxPlanSection">
		select
			mps.*
		from
			mnjx_plan_section mps,
			mnjx_plan_flight mpf ,
			mnjx_flight mf ,
			mnjx_tcard mt
		where
			mf.flight_no = #{flightNo}
			and mf.flight_id = mt.flight_id
			and mt.tcard_id = mpf.tcard_id
			and mpf.flight_date = #{flightDate}
			and mpf.plan_flight_id = mps.plan_flight_id
	</select>
</mapper>
