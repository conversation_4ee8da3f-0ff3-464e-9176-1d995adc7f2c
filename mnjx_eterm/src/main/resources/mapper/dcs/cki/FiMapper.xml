<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.cki.mapper.FiMapper">
    <select id="retrievePlanFlt" resultType="com.swcares.eterm.dcs.cki.obj.dto.FiPlanFltDto">
        SELECT DISTINCT mf.flight_status  as mfStatus,
                        mf.flight_no,
                        mpf.flight_status as mpfStatus,
                        mt.cycle,
                        mt.start_date,
                        mt.end_date,
                        mt.vers,
                        mpf.cnd_no,
                        mt.eqt,
                        CASE
                            mt.t_type
                            WHEN '国内' THEN 'D'
                            WHEN '国际' THEN 'L'
                            ELSE ''
                            END           AS type,
                        mpf.flight_date,
                        mpf.plan_flight_id,
                        mpf.tcard_id
        FROM mnjx_flight mf
                 LEFT JOIN mnjx_tcard mt
                           ON mt.flight_id = mf.flight_id
                 LEFT JOIN mnjx_plan_flight mpf
                           ON mpf.tcard_id = mt.tcard_id
        WHERE mf.airline_id = #{airlineId}
          AND mf.flight_no = #{flightNo}
          AND mpf.flight_date = #{fltDate}
    </select>
    <select id="retrieveTcardSection" resultType="com.swcares.eterm.dcs.cki.obj.dto.FiTcardSection">
        SELECT mts.tcard_section_id,
               mts.tcard_id,
               mts.section_no,
               mts.is_last_section,
               (SELECT airport_code FROM mnjx_airport WHERE airport_id = mts.airport_id) airportCode,
               mts.dep_time,
               mts.arr_time,
               mts.arr_date_change,
               mts.off_date_change
        FROM mnjx_tcard_section mts
        WHERE tcard_id = #{tcardId}
    </select>
    <select id="retrievePlanSection" resultType="com.swcares.eterm.dcs.cki.obj.dto.FiPlanSectionDto">
        SELECT `estimate_off`,
               `estimate_off_change`,
               `estimate_arr`,
               `estimate_arr_change`,
               (SELECT airport_code FROM mnjx_airport  WHERE airport_id = mps.`dep_apt_id`) offCity,
               (SELECT airport_code FROM mnjx_airport WHERE airport_id = mps.`arr_apt_id`) arrCity,
               `gate`,
               `actual_off`,
               `actual_off_change`,
               `actual_arr`,
               `actual_arr_change`,
               (SELECT plane_no FROM mnjx_plane WHERE plane_id = mps.`plane_id`)     planeNo,
               (SELECT plane_no  FROM mnjx_plane WHERE plane_id = mps.`pre_plane_id`) prePlaneNo
        FROM mnjx_plan_section mps
        WHERE mps.`plan_flight_id` = #{planFlightId}
        ORDER BY is_last_section
    </select>
</mapper>
