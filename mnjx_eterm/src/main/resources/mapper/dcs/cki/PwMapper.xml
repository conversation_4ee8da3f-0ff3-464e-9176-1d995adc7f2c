<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.cki.mapper.PwMapper">

    <select id="queryNmTicket" resultType="com.swcares.entity.MnjxPnrNmTicket">
    	select t1.* 
    	from mnjx_pnr_nm_ticket t1
		inner join mnjx_pnr_nm_tn t2 on t2.tn_id = t1.pnr_nm_tn_id
		inner join mnjx_pnr_nm    t3 on t3.pnr_nm_id = t2.pnr_nm_id
		left join mnjx_pnr_seg   t4 on (t4.pnr_seg_id = t1.s1_id or t4.pnr_seg_id = t1.s2_id)
		where t3.pnr_nm_id = #{pnrNmId}
		and t4.flight_no = #{fltNo}
		and t4.flight_date = #{fltDate}
    </select>
    
    <select id="queryXnTicket" resultType="com.swcares.entity.MnjxPnrNmTicket">
    	select t1.* 
    	from mnjx_pnr_nm_ticket t1
		inner join mnjx_pnr_nm_tn t2 on t2.tn_id = t1.pnr_nm_tn_id
		inner join mnjx_nm_xn     t3 on t3.nm_xn_id = t2.nm_xn_id
		left join mnjx_pnr_seg   t4 on (t4.pnr_seg_id = t1.s1_id or t4.pnr_seg_id = t1.s2_id)
		where t3.pnr_nm_id = #{pnrNmId}
		and t4.flight_no = #{fltNo}
		and t4.flight_date = #{fltDate}
    </select>

</mapper>