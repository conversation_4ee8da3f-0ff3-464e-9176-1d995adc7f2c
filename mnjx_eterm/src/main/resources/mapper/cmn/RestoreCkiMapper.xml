<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.base.mapper.RestoreCkiMapper">

    <select id="queryRestoreStatus" resultType="com.swcares.entity.MnjxRestoreStatus">
        SELECT
            *
        FROM
            mnjx_restore_status
        where
            id_card = #{idCard}
        ORDER BY
            operate_time desc, FIELD(operate_type, 'I', 'U', 'D')
    </select>

</mapper>