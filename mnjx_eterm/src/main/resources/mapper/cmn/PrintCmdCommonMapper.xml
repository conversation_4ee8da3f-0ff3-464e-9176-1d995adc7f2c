<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.base.mapper.PrintCmdCommonMapper">

    <select id="retrieveTicketLimitsIsInput" resultType="java.util.Map">
        SELECT mp.printer_no
        FROM mnjx_printer mp
                 JOIN mnjx_office mo ON mp.office_id = mo.office_id
                 JOIN mnjx_ticket_limits mtl ON mtl.office_id = mo.office_id
        WHERE mp.printer_no = #{printNo}
          AND mo.office_no = #{officeNum}
    </select>
    <select id="retrieveTicketLimitsIsLoaded" resultType="java.util.Map">
        SELECT mp.ticket_start,
               mp.ticket_end
        FROM mnjx_printer mp
                 JOIN mnjx_office mo ON mp.office_id = mo.office_id
        WHERE mp.printer_no = #{printNo}
          AND mo.office_no = #{officeNum}
    </select>
</mapper>
