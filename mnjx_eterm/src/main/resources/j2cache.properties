# J2Cache configuration


#########################################
# Cache Broadcast Method 缓存广播方法
# values:
# jgroups -> use jgroups's multicast 使用jgroups的多播
# redis -> use redis publish/subscribe mechanism  使用redis的发布/订阅机制（使用jedis）
# lettuce -> use redis publish/subscribe mechanism (using lettuce, Recommend) 使用redis的发布/订阅机制（使用lettuce，推荐）
# rabbitmq -> use RabbitMQ publisher/consumer mechanism 使用RabbitMQ发布者/消费者机制
# rocketmq -> use RocketMQ publisher/consumer mechanism 使用RocketMQ发布者/消费者机制
# none -> don't notify the other nodes in cluster 不要通知集群中的其他节点,就是单节点使用
# xx.xxxx.xxxx.xxxxx your own cache broadcast policy classname that implement net.oschina.j2cache.cluster.ClusterPolicy 您自己的实现net.oschina.j2cache.cluster.ClusterPolicy的缓存广播策略类名
#########################################
# 广播策略
# j2cache.broadcast = net.oschina.j2cache.cache.support.redis.SpringRedisPubSubPolicy
# 单节点使用
j2cache.broadcast = none

#组播配置
# jgroups properties
# jgroups.channel.name = j2cache
# jgroups.configXml = /network.xml

# RabbitMQ properties
# rabbitmq.exchange = j2cache
# rabbitmq.host = localhost
# rabbitmq.port = 5672
# rabbitmq.username = guest
# rabbitmq.password = guest

# RocketMQ properties
# rocketmq.name = j2cache
# rocketmq.topic = j2cache
# use ; to split multi hosts
# rocketmq.hosts = 127.0.0.1:9876

#########################################
# Level 1&2 provider 一级与二级提供者
# values:
# none -> disable this level cache 禁用此级别的缓存
# ehcache -> use ehcache2 as level 1 cache 使用ehcache2作为1级缓存
# ehcache3 -> use ehcache3 as level 1 cache 使用ehcache3作为1级缓存
# caffeine -> use caffeine as level 1 cache(only in memory) 使用caffeine作为1级缓存（仅在内存中）
# redis -> use redis as level 2 cache (using jedis) 将redis用作2级缓存（使用jedis）
# lettuce -> use redis as level 2 cache (using lettuce) 使用redis作为2级缓存（使用 lettuce)
# readonly-redis -> use redis as level 2 cache ,but never write data to it. if use this provider, you must uncomment `j2cache.L2.config_section` to make the redis configurations available. 将redis用作2级缓存，但不要向其写入数据。如果使用此提供程序，则必须取消注释“ j2cache.L2.config_section”以使redis配置可用。
# memcached -> use memcached as level 2 cache (xmemcached), 使用memcached作为2级缓存（xmemcached）
# [classname] -> use custom provider 使用自定义提供程序
#########################################

# 一级缓存策略为ehcache2
j2cache.L1.provider_class = ehcache

# 二级缓存策略为redis->none(禁用二级缓存)
j2cache.L2.provider_class = none

# When L2 provider isn't `redis`, using `L2.config_section = redis` to read redis configurations 当L2提供者不是`redis`时，使用`L2.config_section = redis`读取redis配置
#j2cache.L2.config_section = redis

# Enable/Disable ttl in redis cache data (if disabled, the object in redis will never expire, default:true) 在redis缓存数据中启用/禁用ttl（如果禁用，则redis中的对象将永不过期，默认值：true）
# NOTICE: redis hash mode (redis.storage = hash) do not support this feature) 注意：redis哈希模式（redis.storage =哈希）不支持此功能）
j2cache.sync_ttl_to_redis = true

# Whether to cache null objects by default (default false) 默认情况下是否缓存空对象（默认为false）
j2cache.default_cache_null_object = true

#########################################
# Cache Serialization Provider 缓存序列化提供程序
# values:
# fst -> using fast-serialization (recommend) 使用fast-serialization序列化（推荐）
# kryo -> using kryo serialization kryo序列化
# json -> using fst's json serialization (testing) 使用fst的json序列化（测试）
# fastjson -> using fastjson serialization (embed non-static class not support) 使用fastjson序列化（不支持嵌入的非静态类）
# java -> java standard Java标准
# fse -> using fse serialization 使用fse序列化
# [classname implements Serializer]
#########################################

j2cache.serialization = fst

#########################################
# Ehcache configuration
#########################################

ehcache.configXml = ehcache2.xml
#ehcache3.configXml = ehcache3.xml
# ehcache3.defaultHeapSize = 1000

#########################################
# Caffeine configuration
# caffeine.region.[name] = size, xxxx[s|m|h|d]
#
#########################################
#caffeine.properties = /caffeine.properties
#caffeine.region.default = 1000, 1h

#########################################
# Redis connection configuration
#########################################

#########################################
# Redis Cluster Mode
#
# single -> single redis server 单Redis服务器
# sentinel -> master-slaves servers 主从服务器
# cluster -> cluster servers (数据库配置无效，使用 database = 0）
# sharded -> sharded servers  (密码、数据库必须在 hosts 中指定，且连接池配置无效 ; redis://user:password@127.0.0.1:6379/0）
#
#########################################

#redis.mode = single

#redis storage mode (generic|hash)
#redis.storage = generic

#cluster name just for sharded
#redis.cluster_name = mymaster

## redis cache namespace optional, default[j2cache]
#redis.namespace = j2cache

## connection
# Separate multiple redis nodes with commas, such as ************:6379,************:6379,************:6379
# redis.hosts = *************:6379
# redis.timeout = 2000
# redis.password =
# redis.database = 0

## redis pub/sub channel name
# redis.channel = j2cache

## redis pool properties
#redis.maxTotal = -1
#redis.maxIdle = 2000
#redis.maxWaitMillis = 100
#redis.minEvictableIdleTimeMillis = 864000000
#redis.minIdle = 1000
#redis.numTestsPerEvictionRun = 10
#redis.lifo = false
#redis.softMinEvictableIdleTimeMillis = 10
#redis.testOnBorrow = true
#redis.testOnReturn = false
#redis.testWhileIdle = false
#redis.timeBetweenEvictionRunsMillis = 300000
#redis.blockWhenExhausted = true