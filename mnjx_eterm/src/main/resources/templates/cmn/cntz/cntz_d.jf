#set(firstRes = results[0])
#for(res:results)
#if(res.airlineEname.length() > 15)
#(res.airlineCode),#(res.airlineThreeCode), #(fillString(res.airlineEname, 50, false))#(firstRes.countryIso)#(wrap())
        #(res.airlineFullName)#(wrap())
-#(wrap())
#else
#(res.airlineCode),#(res.airlineThreeCode), #(fillString(res.airlineEname, 20, false))#(fillString(res.airlineFullName, 20, false))-                   #(firstRes.countryIso)#(wrap())
-#(wrap())
#end
#end