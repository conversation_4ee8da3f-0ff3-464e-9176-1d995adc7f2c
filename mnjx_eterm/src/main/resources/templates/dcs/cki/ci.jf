#set(c=args[0])
#if(contains(c,"-"))
ACCEPTED
#else
#set(ci = results[0])
 CI: #(ci.flightNo)/#(ymd2PreCom(ci.flightDate,5))#(ci.dst)       ACCEPTED           #(ci.statusUpdateTime)/NAM#(wrap())
#(ci.planeModelType??" ")/#(ci.planeModelVersion??" ")  GTD/????  POS/GATE   BDT#(ci.boarding??" ")   SD#(ci.estimateOff??" ")  ED#(ci.off??" ")  SA0000  FT#(ci.flyTime??" ")#(wrap())
AV #(ci.layout??" ")   PAD #(ci.cabinClass??" ")#(wrap())
  *#(ci.arr??" ")* ********************#(wrap())
NIL
#end
