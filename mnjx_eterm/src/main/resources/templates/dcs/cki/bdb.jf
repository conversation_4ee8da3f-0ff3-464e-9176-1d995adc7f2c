#set(babRenderVo = results[0], acceptedNum = results[1], failNum = results[2], null)
#(args[0])#(wrap())
#if(acceptedNum > 0)
#(fillIndex((acceptedNum), 3, false))UNBOARDED#(wrap())
#end
#if(failNum > 0)
#set(oneBab = babRenderVo.failVoList.get(0), null)
#(fillIndex((failNum), 3, false))BOARDING NUMBER#if(failNum > 1)S#end#(' ')NOT ACCEPTED#(wrap())
BN          REASON        #if(oneBab.isContainsInf())INFANT#end#(wrap())
#for(bab:babRenderVo.failVoList)
#(bab.aboardNo)         #(bab.reason)#(wrap())
#end
#end
#if(acceptedNum > 0)
#set(oneBab = babRenderVo.acceptVoList.get(0), null)
#(fillIndex((acceptedNum), 3, false))BOARDING NUMBER#if(acceptedNum > 1)S#end#('     ')ACCEPTED#(wrap())
BN          NAME        #if(oneBab.isContainsInf())INFANT#end#(wrap())
#for(bab:babRenderVo.acceptVoList)
#(bab.aboardNo)         #(bab.name)#(wrap())
#end
#end