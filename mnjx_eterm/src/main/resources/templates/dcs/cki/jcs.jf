#set(res = results[0], arg = args[1], null)
JCS: #(res.flightNo)/#(res.flightDate)#(res.sellCabin)#(res.dstAirport)#(arg)#(wrap())
AV #(res.avLayout)    PAD #(res.padLayout)#(wrap())
#(res.planeType)/#(res.planeVersion) GTD/#(res.gate) POS/GATE#if(res.bdt??) BDT#(res.bdt)#end#[[ SD]]##(res.sd)#if(res.ed??) ED#(res.ed)#end#if(res.sa??)  SA#(res.sa)#end#if(res.ft??)  FT#(res.ft)#end#(wrap())
  1. #(res.queryName)          #(res.seatNo)    CRS ADV-PA/NP #if(res.ures??)URES #end#if(res.nrec??)NREC #end#if(res.asr??)ASR #end#[[SNR]]##(res.seatNo)#(wrap())
#if(res.isEt())#for(ticketNo:res.ticketNoList)                                        ET TKNE/#(ticketNo)/#(for.count)#(wrap())#end#end
#if(res.foidNo??)                                        FOID/#(res.foidNo)#(wrap())#end
                                        #if(res.isCnin())CNIN/#(res.name) #end#(wrap())
#if(res.msg??) MSG-#(res.msg)#(wrap())#end
#if(res.infName??)INF-#(res.infName)#(wrap())#end
#if(res.psm?? && res.isVip() && res.chld??)PSM-#(res.psm) /#(res.chld) /VIP#(wrap())#elseif(res.isVip())PSM-#if(res.psm??)#(res.psm) #end#if(res.chld??)/#(res.chld) #end/VIP#(wrap())#elseif(res.psm??)PSM-#(res.psm)#if(res.isVip()) /VIP#end#if(res.chld??) /#(res.chld)#end#(wrap())#elseif(res.chld??)PSM-#if(res.psm??)#(res.psm) #end#if(res.isVip()) /VIP#end/#(res.chld)#(wrap())#end
#if(res.pil??)PIL-#(res.pil)#(wrap())#end
#if(res.isVip())ATTENTION:THIS IS A VIP PASSENGER#(wrap())#end
#if(res.phone??)CTC-#(res.orgAirport) #(res.airlineCode)-#if(res.orderDate??)#(res.orderDate)-#end#(res.phone)#(wrap())#end
#if(res.spml??)SPML-#(res.spml)#(wrap())#end
  *#(res.dstAirport)* WELCOME TO #(res.cityName)#(wrap())