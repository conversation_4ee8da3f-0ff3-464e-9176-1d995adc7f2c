#set(skDto=results[1])
#set(skResultList=results[0])
#(skDto.beforeFltDateCom)/#(skDto.afterFltDateCom) #(skDto.cityPair) #((skDto.airlineCode??)?'VIA'+skDto.airlineCode:"") #(skDto.cabin??) #((skDto.stop??)?skDto.stopDetail:'')#(wrap())
#for(x:skResultList)
#(fillIndex((for.index + 1), 2, false)) #if(x.carrierFlight??)#("*")#else#(" ")#end#(x.flightNo)  #(x.org)#(x.dst) #(x.estimateOff)#((x.estimateOffChange??)?x.estimateOffChange:"  ") #(x.estimateArr)#((x.estimateArrChange??)?x.estimateArrChange:"  ") #((x.planeModelType??)?fillAfter(x.planeModelType,cn.hutool.core.util.CharUtil::SPACE,3):"   ") #(x.stopPoint) #(x.mealCode??" ") #((x.isRecreation??)?x.isRecreation:" ") #((x.cycle??)?fillAfter(x.cycle,cn.hutool.core.util.CharUtil::SPACE,4):"    ") #(x.startDate)#(x.endDate) #(x.sellCabin)#(wrap())
#end
