*************************TKT DEVICE INFORMATION DISPLAY***********************#(wrap())
OFFICE: #(results[0].officeNo)#(wrap())
IATANBR: #(results[0].agentIata)#(wrap())
AUTHORIZED AIRLINE CODES:#(wrap())
#if(results[0].airCodes != "" && results[0].airCodes != null)
#(results[0].airCodes)#(wrap())
#end
============================== AGENCY INFORMATION ============================#(wrap())
#if(results[0].agentIata != "" && results[0].agentIata != null)
#(results[0].officeNo)/T-#(results[0].agentContactPhone)/#(results[0].agentEname)#(wrap())
#end
ADDRESS: #(results[0].agentContactAddress)#(wrap())
CONTACT: #(results[0].agentContactCname)#(wrap())
PHONE: #(results[0].agentContactPhone)#(wrap())
FAX:#(wrap())
============================== DEVICE INFORMATIONS ===========================#(wrap())
DEV  PID   TYPE    CTL-  CTL-  CURRENCY        TKT  NUMBER RANGE     IATA OFF-#(wrap())
                   PID  AGENT                                         NUMBER#(wrap())
--- ----- ------- ----- ----- --------------- ---------------------- ---------#(wrap())
#for(cabinet:results)
#if(cabinet.printerNo != "" && cabinet.printerNo != null)
#(cabinet.printerNo) #(cabinet.printerPid) 4-BSPD# #(cabinet.siPid) #(cabinet.siNo) #(cabinet.currencyType) #(cabinet.tkt) #(cabinet.ticketRange) #(cabinet.agentIata)#(wrap())
#end
#end