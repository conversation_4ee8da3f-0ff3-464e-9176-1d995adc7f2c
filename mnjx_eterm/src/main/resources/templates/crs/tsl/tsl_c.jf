********************************************************************************
*                  CAAC  MIS  OPTAT  DAILY-SALES-REPORT                        *
*                                                                              *
*   OFFICE : #(resDto.office)    IATA NUMBER : #(resDto.iataNumber)    DEVICE : #(fillAfter(resDto.device,cn.hutool.core.util.CharUtil::SPACE,3))  #(resDto.pid)            *
*   DATE   : #(resDto.dateCom)                               AIRLINE:   #(fillAfter(resDto.airline,cn.hutool.core.util.CharUtil::SPACE,3))                *
--------------------------------------------------------------------------------
#if(paramsDto.param=="O")
  TKT-NUMBER    ORIG-DEST     COLLECTION      TAXS    COMM    PNR   FOP   AGENT#(wrap())
#else
  TKT-NUMBER       ORIG-DEST   COLLECTION    TAXS    COMM     PNR   FOP   AGENT#(wrap())
#end
--------------------------------------------------------------------------------
#if(paramsDto.param!="T")
 #for(t:resDto.ticketList)
  #if(paramsDto.param=="O")
#((t.tktNumber??)?fillAfter(t.tktNumber,cn.hutool.core.util.CharUtil::SPACE,15):"              ") #((t.orgDst??)?fillAfter(t.orgDst,cn.hutool.core.util.CharUtil::SPACE,10):"          ") #((t.totalPrice??)?fillBefore(t.totalPrice,cn.hutool.core.util.CharUtil::SPACE,8):"        ") #((t.collection??)?fillBefore(t.collection,cn.hutool.core.util.CharUtil::SPACE,8):"        ")  #((t.taxs??)?fillAfter(t.taxs,cn.hutool.core.util.CharUtil::SPACE,6):"      ")  #((t.comm??)?fillAfter(t.comm,cn.hutool.core.util.CharUtil::SPACE,4):"    ")  #((t.crsPnr??)?fillAfter(t.crsPnr,cn.hutool.core.util.CharUtil::SPACE,6):"      ")  CAS   #(t.agentNo??)#(wrap())
    #(t.oldTicketOi??)#(wrap())
    CN    #(t.cnPrice??)      YQ    #(t.yqPrice??)#(wrap())
   #else
#((t.tktNumber??)?fillAfter(t.tktNumber,cn.hutool.core.util.CharUtil::SPACE,17):"                 ")  #((t.orgDst??)?fillAfter(t.orgDst,cn.hutool.core.util.CharUtil::SPACE,10):"          ")  #((t.collection??)?fillBefore(t.collection,cn.hutool.core.util.CharUtil::SPACE,8):"        ")     #((t.taxs??)?fillAfter(t.taxs,cn.hutool.core.util.CharUtil::SPACE,6):"      ")   #((t.comm??)?fillAfter(t.comm,cn.hutool.core.util.CharUtil::SPACE,4):"    ")    #((t.crsPnr??)?fillAfter(t.crsPnr,cn.hutool.core.util.CharUtil::SPACE,6):"      ")  CAS  #(t.agentNo??)#(wrap())
  #end
 #end
#end
*==============================================================================*
     TOTAL TICKETS:       #(resDto.totalTickets) (      0 TICKETS VOID /      #(resDto.ticketRefund) TICKETS REFUND )#(wrap())
----------------NORMAL TICKETS--------------------------------------------------
   NORMAL  FARE-- AMOUNT :          #(fillBefore(resDto.normalFare,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
      CARRIERS -- AMOUNT :          #(fillBefore(resDto.carries,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
        COMMIT -- AMOUNT :          #(fillBefore(resDto.commit,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
   NORMAL  TAX -- AMOUNT :          #(fillBefore(resDto.normalTax,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
----------------REFUND TICKETS--------------------------------------------------
    NET REFUND -- AMOUNT :          #(fillBefore(resDto.netRefund,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
     DEDUCTION -- AMOUNT :          #(fillBefore(resDto.deduction,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
 REFUND    TAX -- AMOUNT :          #(fillBefore(resDto.refundTax,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
 REFUND COMMIT -- AMOUNT :          #(fillBefore(resDto.refundCommit,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
*==============================================================================*