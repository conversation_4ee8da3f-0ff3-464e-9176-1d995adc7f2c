package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.IEtrfService;
import javax.annotation.Resource;
import java.util.Map;

/**
 * ETRF指令格式验证
 *
 * <AUTHOR>
 */
@OperateType(action = "ETRF")
public class EtrfHandler implements Handler {

    @Resource
    private IEtrfService iEtrfService;

    /**
     * @param cmd       指令
     */
    @Override
    public String handle(String cmd) throws UnifiedResultException {
        // ETRF:航段号/票号/PRNT/打票机号
        // ETRF:航段号/票号/PRNT/打票机号/OPEN
        cmd = cmd.replace(" ", "");
        String[] tmp1 = cmd.trim().split(":");
        String[] tmp = tmp1[1].trim().split("/");
        if (tmp1.length != Constant.TWO) {
            return Constant.FORMAT;
        }
        if (tmp.length != Constant.FOUR && tmp.length != Constant.FIVE) {
            return Constant.FORMAT;
        }
        if (!tmp[0].matches("[0-9]{1,2}") || !tmp[1].matches("(\\w{1,14}.00)")
                || !"PRNT".equalsIgnoreCase(tmp[2])
                || !tmp[3].matches("[0-9]{1,2}")) {
            return Constant.FORMAT;
        }
        Map<String, Object> map = iEtrfService.dealCmd(cmd, tmp);
        return (String) map.get("key");
    }
}
