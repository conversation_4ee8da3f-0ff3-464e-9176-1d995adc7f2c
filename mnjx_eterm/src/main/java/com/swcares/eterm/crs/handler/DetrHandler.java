package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.obj.dto.DetrDto;
import com.swcares.eterm.crs.service.IDetrService;

import javax.annotation.Resource;

/**
 * DETR指令验证
 * DETR:TN/票号[,OPTION]
 * DETR:CN/PNR CRS编码
 * DETR:NI/身份证号
 * DETR:PP/护照号码
 * DETR:ID/护照号码
 * DETR:NM/旅客姓名
 *
 * <AUTHOR>
 *  2022-07-25 11:18:05
 */
@OperateType(action = "DETR", template = "/crs/detr.jf", fullScreen = true)
public class DetrHandler implements Handler {

    @Resource
    private IDetrService iDetrService;

    /**
     * @param cmd 指令
     */
    @Override
    public UnifiedResult handle(String cmd) throws UnifiedResultException {
        //解析参数
        DetrDto detrDto = iDetrService.parseDetr(cmd);
        //业务处理
        return iDetrService.handle(detrDto);
    }
}
