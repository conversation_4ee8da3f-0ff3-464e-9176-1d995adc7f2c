package com.swcares.eterm.crs.handler;

import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.IFvService;
import com.swcares.obj.dto.AvDto;
import com.swcares.obj.vo.AvVo;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/5/9
 */
@OperateType(action = "FV", shorthand = true, template = "crs/fv.jf")
public class FvHandler implements Handler {

    @Resource
    private IFvService iFvService;

    @Resource
    private AvHandler avHandler;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        AvDto avDto = this.parseCmd(cmd);
        AvVo avVo = iFvService.handle(avDto);
        unifiedResult.setArgs(new Object[]{avDto});
        unifiedResult.setResults(new Object[]{avVo});
        return unifiedResult;
    }

    private AvDto parseCmd(String cmd) throws UnifiedResultException {
        // FV指令格式和AV的城市对通用格式一样，直接调用AV的指令解析
        AvDto avDto = avHandler.parseAv(cmd);
        // AV解析中用到的缓存不会在FV使用，FV这里需要清掉调用AV解析产生的缓存
        iFvService.clearAvDtoCache(MemoryDataUtils.getMemoryData().getMemoryDataPnr());
        return avDto;
    }
}
