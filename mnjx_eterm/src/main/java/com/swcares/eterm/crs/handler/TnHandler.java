package com.swcares.eterm.crs.handler;

import cn.hutool.core.util.ReUtil;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.obj.type.EnumTnType;
import com.swcares.eterm.crs.obj.dto.TnDto;
import com.swcares.eterm.crs.service.ITnService;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Pattern;

/**
 * TN指令验证
 *
 * <AUTHOR>
 */
@OperateType(action = "TN", shorthand = true)
public class TnHandler implements Handler {
    /**
     * 上票 TN:打印机序号X/2217341600-41699
     */
    private static final Pattern TN_X_REG_FORMAT = Pattern.compile("^[Tt][Nn][:\\s](\\d{1,3})([Xx])/(\\d{10})-(\\d{5})$");

    /**
     * 卸票 TN:打印机序号D
     */
    private static final Pattern TN_D_REG_FORMAT = Pattern.compile("^[Tt][Nn][:\\s](\\d{1,3})([Dd])$");

    @Resource
    private ITnService iTnService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        TnDto tnDto = this.parseCmd(cmd);
        return iTnService.handle(tnDto);
    }

    /**
     * 指令解析
     *
     * @param cmd 指令
     * @return 参数对象
     * @throws UnifiedResultException 统一异常
     */
    private TnDto parseCmd(String cmd) throws UnifiedResultException {
        if (!ReUtil.isMatch(TN_X_REG_FORMAT, cmd) && !ReUtil.isMatch(TN_D_REG_FORMAT, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        TnDto tnDto = null;
        if (ReUtil.isMatch(TN_X_REG_FORMAT, cmd)) {
            List<String> args = ReUtil.getAllGroups(TN_X_REG_FORMAT, cmd);
            tnDto = TnDto.builder().build();
            // 打票机号
            tnDto.setPrintNo(args.get(1));
            // 票操作类型
            tnDto.setTnType(EnumTnType.of(args.get(2)));
            // 票开始完整票号
            tnDto.setTktFullStart(args.get(3));
            // 票结束号
            tnDto.setTktShortEnd(args.get(4));
        }

        if (ReUtil.isMatch(TN_D_REG_FORMAT, cmd)) {
            List<String> args = ReUtil.getAllGroups(TN_D_REG_FORMAT, cmd);
            tnDto = TnDto.builder().build();
            // 打票机号
            tnDto.setPrintNo(args.get(1));
            // 票操作类型
            tnDto.setTnType(EnumTnType.of(args.get(2)));
        }
        return tnDto;
    }
}
