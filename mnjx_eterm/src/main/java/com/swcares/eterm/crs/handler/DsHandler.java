package com.swcares.eterm.crs.handler;

import cn.hutool.core.collection.ListUtil;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.obj.dto.SkDto;
import com.swcares.eterm.crs.obj.vo.SkVo;
import com.swcares.eterm.crs.service.IDsService;

import javax.annotation.Resource;
import java.util.List;

/**
 * [用于显示指定日期内所有固定航班情况,其格式与 SK 完全相同。]
 * DS指令
 * DS: 选择项 / 城市对 / 日期 / 起飞时间 / 航空公司代码 /舱位   (除城市对外，其余所有参数都非必填。)
 * DS:P/CTUPEK/./1200/MU/D/C
 * 1.选择项有以下几种：
 * 1.1   P --  显示结果按照起飞时间先后顺序排列
 * 1.2   A --  显示结果按照到达时间先后顺序排列
 * 1.3   E --  显示结果按照飞行时间由短到长排列
 * 不选，默认为 P
 *
 * <AUTHOR>
 */
@OperateType(action = "DS", template = "/crs/ds.jf", shorthand = true)
public class DsHandler implements Handler {
    @Resource
    public IDsService iDsService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        //参数解析
        SkDto skDto = iDsService.parseDs(cmd);
        //业务处理
        List<SkVo> skVoList = iDsService.handle(skDto);
        //设置返回结果
        unifiedResult.setResults(ListUtil.toList(skVoList, skDto).toArray());
        return unifiedResult;
    }
}
