package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.obj.dto.TslParamDto;
import com.swcares.eterm.crs.service.ITslService;

import javax.annotation.Resource;

/**
 * <AUTHOR> by yaodan
 **/
@OperateType(action = "TSL", template = "/crs/tsl.jf")
public class TslHandler implements Handler {
    @Resource
    private ITslService iTslService;

    @Override
    public UnifiedResult handle(String cmd) throws UnifiedResultException {
        //解析参数
        TslParamDto tslParamDto = iTslService.parseTsl(cmd);
        return iTslService.handle(tslParamDto);
    }
}
