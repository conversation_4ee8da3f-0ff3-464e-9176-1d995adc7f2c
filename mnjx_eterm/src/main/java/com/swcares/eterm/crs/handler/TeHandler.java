package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.StrUtils;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.ITeService;

import javax.annotation.Resource;
import java.util.Map;

/**
 * TE指令验证
 *
 * <AUTHOR>
 */
@OperateType(action = "TE")
public class TeHandler implements Handler {

    @Resource
    private ITeService iTeService;

    /**
     * 格式验证通过进入业务处理
     *
     * @param cmd 指令
     * @return 格式验证通过进入业务处理
     * @throws 统一异常
     */
    @Override
    public String handle(String cmd) throws UnifiedResultException {
        // TE:1/U TE:1/X
        cmd = cmd.replace(" ", "");
        if (cmd.endsWith(StrUtils.COLON) || cmd.endsWith(StrUtils.SLASH)) {
            return Constant.FORMAT;
        }
        String[] tmp = cmd.trim().split(":");
        if (tmp.length != Constant.TWO) {
            return Constant.FORMAT;
        }
        String[] tmp1 = tmp[1].trim().split("/");
        if (tmp1.length != Constant.TWO) {
            return Constant.FORMAT;
        }
        String str1 = "[0-9]{1,2}/X";
        String str2 = "[0-9]{1,2}/U";
        if (!tmp[1].trim().matches(str1) && !tmp[1].trim().matches(str2)) {
            return Constant.FORMAT;
        }
        Map<String, Object> map = iTeService.dealCmd(tmp1);
        return (String) map.get("key");
    }
}
