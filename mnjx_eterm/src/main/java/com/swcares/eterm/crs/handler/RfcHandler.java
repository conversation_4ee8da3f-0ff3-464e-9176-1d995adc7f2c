package com.swcares.eterm.crs.handler;

import cn.hutool.core.util.ReUtil;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.IPnrManageService;
import com.swcares.eterm.crs.service.IRfcService;

import javax.annotation.Resource;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2021/11/17
 */
@OperateType(action = "RFC")
public class RfcHandler implements Handler {

	@Resource
	private IRfcService iRfcService;

	@Resource
	private IPnrManageService iPnrManageService;

	/**
	 * RFC
	 */
	private static final Pattern RFC = Pattern.compile("RFC[:|\\s][0-9]+");

	@Override
	public String handle(String cmd) throws UnifiedResultException {
		// RFC:1
		analysisCmd(cmd);
		iRfcService.handle();
		return iPnrManageService.recall(iPnrManageService.getCurrentControlledPnr());
	}

	private void analysisCmd(String cmd) throws UnifiedResultException {
		if (!ReUtil.isMatch(RFC, cmd)) {
			throw new UnifiedResultException(Constant.FORMAT);
		}
	}
}
