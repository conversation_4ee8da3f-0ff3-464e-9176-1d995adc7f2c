package com.swcares.eterm.crs.mapper;

import com.swcares.eterm.crs.obj.dto.DsgSectionDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-7-19
 */
public interface DsgMapper {

    /**
     * retrieveFltSection
     *
     * @param fltNo   fltNo
     * @param fltDate fltDate
     * @return retrieveFltSection
     */
    List<DsgSectionDto> retrieveFltSection(@Param("flightNo") String fltNo, @Param("fltDate") String fltDate);

    /**
     * 查询舱位
     */
    String retrieveSellCabin(@Param("planSectionIds") List<String> planSectionIds, @Param("sellCabin") String sellCabin);
}
