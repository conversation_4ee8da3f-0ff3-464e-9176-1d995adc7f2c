package com.swcares.eterm.crs.handler;

import cn.hutool.core.util.ReUtil;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.ITiService;

import javax.annotation.Resource;
import java.util.regex.Pattern;

/**
 * TI指令验证
 * 指令格式
 * TI:打票机序号
 * <AUTHOR>
 */
@OperateType(action = "TI", shorthand = true, predicate = false, template = "/crs/TI.jf")
public class TiHandler implements Handler {

    private static final String REG_FMT_TI = "TI[:|\\s](\\d{1,3})";

    @Resource
    private ITiService iTiService;

    @Override
    public UnifiedResult handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        // 指令格式验证
        if (!ReUtil.isMatch(REG_FMT_TI, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        // 2、获取指令传输对象
        unifiedResult.setArgs(ReUtil.getAllGroups(Pattern.compile(REG_FMT_TI), cmd).toArray());
        // 3、查询业务数据,构建模板使用对象
        iTiService.handle(unifiedResult);
        return unifiedResult;
    }

}
