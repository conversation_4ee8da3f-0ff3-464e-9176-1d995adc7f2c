package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.ICsService;

import javax.annotation.Resource;

/**
 * 调整航段序号
 * 一般用在pnr中有open航段时才使用此指令
 * >CS:PNR 中航段序号 /PNR 中航段序号
 *
 * <AUTHOR>
 */
@OperateType(action = "CS")
public class CsHandler implements Handler {

	@Resource
	private ICsService iCsService;

	@Override
	public String handle(String cmd) throws UnifiedResultException {
		return iCsService.handle(cmd);
	}
}
