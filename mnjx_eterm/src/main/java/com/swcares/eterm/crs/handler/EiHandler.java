package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.obj.dto.EiParamDto;
import com.swcares.eterm.crs.service.IEiSerivce;

import javax.annotation.Resource;

/**
 * 签注信息组
 *
 * <AUTHOR>
 */
@OperateType(action = "EI", shorthand = true)
public class EiHandler implements Handler {

    @Resource
    private IEiSerivce iEiSerivce;

    @Override
    public String handle(String cmd) throws UnifiedResultException {

        EiParamDto eiParamDto = iEiSerivce.parseEi(cmd);

        iEiSerivce.handle(eiParamDto);

        return iEiSerivce.recall();
    }
}
