package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.IFcService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@OperateType(action = "FC", shorthand = true)
public class FcHandler implements Handler {

    @Resource
    private IFcService iFcService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        return iFcService.handle(cmd);
    }
}
