package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.INmService;
import com.swcares.obj.dto.NmDto;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@OperateType(action = "NM", shorthand = true)
public class NmHandler implements Handler {

    @Resource
    private INmService iNmService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 解析指令
        List<NmDto> nmDtos = iNmService.parseNm(cmd);
        // 处理姓名组
        iNmService.handleNms(nmDtos);
        // 回显PNR的数据
        return iNmService.recall();
    }
}
