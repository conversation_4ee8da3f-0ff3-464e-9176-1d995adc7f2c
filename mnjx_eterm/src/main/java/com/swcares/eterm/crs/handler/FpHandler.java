package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.obj.dto.FpDto;
import com.swcares.eterm.crs.service.IFpService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@OperateType(action = "FP", shorthand = true)
public class FpHandler implements Handler {

    @Resource
    private IFpService iFpService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        FpDto fpDto = iFpService.parseArgs(cmd);
        return iFpService.handle(fpDto);
    }
}
