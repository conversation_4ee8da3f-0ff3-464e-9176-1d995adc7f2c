package com.swcares.eterm.crs.obj.dto;

import lombok.Data;

/**
 * <AUTHOR> by yaodan
 * TSL 票信息的集合
 **/
@Data
public class TslTicket {
    /**
     * 工作号
     */
    private String agentNo;

    private String patType;
    /**
     * 票号
     */
    private String tktNumber;
    /**
     * 操作时间
     */
    private String operateTime;

    /**
     * 起始城市-到达城市
     */
    private String orgDst;
    /**
     * 票价，不含税
     */
    private String collection="0.00";
    /**
     * 基建+燃油
     */
    private String taxs="0.00";
    /**
     * 代理费，默认为0
     */
    private String comm = "0.00";
    /**
     * pnr编码
     */
    private String crsPnr;

    //-----------退票信息字段
    /**
     * 退票单号
     */
    private String refNo;
    /**
     * 退票费
     */
    private String deduction = "0.00";
    //--------------参数A 场景用到的字段
    /**
     * 实收票价
     */
    private String nomalFare="0.00";
    /**
     * 退款金额
     */
    private String refundFare="0.00";
    /**
     * 退票代理费
     */
    private String refComm = "0.00";
    /**
     * 客票张数
     */
    private String tickets;
    /**
     * 退票的客票数
     */
    private String refund;
    //----------------参数 F 场景用到的字段
    /**
     * 航司结算码
     */
    private String settlementCode;

    private String pnrSegNo;
    private String pnrNmId;
    private String pnrXnId;
    private String pnrId;
    private String mtorStatus1;
    private String mtorStatus2;

    //----------------参数O 换开场景
    /**
     * 旧票+航段
     */
    private String oldTicketOi;
    /**
     * 新票的基建
     */
    private String cnPrice="0.00";
    /**
     * 新票的燃油
     */
    private String yqPrice="0.00";
    /**
     * 总票价（票价+税）
     */
    private String totalPrice="0.00";

}
