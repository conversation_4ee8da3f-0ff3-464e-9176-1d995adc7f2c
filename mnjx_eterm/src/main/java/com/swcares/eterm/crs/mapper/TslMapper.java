package com.swcares.eterm.crs.mapper;

import com.swcares.eterm.crs.obj.dto.TslTicket;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TslMapper {

    /**
     * 查询票的集合，以及票的详细信息
     *
     * @param printId  printId
     * @param dataType dataType
     * @return 查询票的集合，以及票的详细信息
     */
    List<TslTicket> retrieveTicketInfo(@Param("printId") String printId, @Param("dataType") String dataType);

    /**
     * retrieveTicketInfoIn
     *
     * @param printId  printId
     * @param dataType dataType
     * @return retrieveTicketInfoIn
     */
    List<TslTicket> retrieveTicketInfoIn(@Param("printId") String printId, @Param("dataType") String dataType);
}
