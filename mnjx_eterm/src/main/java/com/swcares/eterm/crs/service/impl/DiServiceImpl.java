package com.swcares.eterm.crs.service.impl;

import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxOffice;
import com.swcares.entity.MnjxPrinter;
import com.swcares.entity.MnjxSi;
import com.swcares.eterm.crs.mapper.DiMapper;
import com.swcares.eterm.crs.obj.vo.DiVo;
import com.swcares.eterm.crs.service.IDiService;
import com.swcares.service.IMnjxOfficeService;
import com.swcares.service.IMnjxPrinterService;
import com.swcares.service.IMnjxSiService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * DI指令业务处理 打票机的状态信息显示
 *
 * <AUTHOR>
 */
@Service
public class DiServiceImpl implements IDiService {

    @Resource
    private DiMapper diMapper;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    /**
     * DI:1
     */
    private final static String REG_DI = "DI[:|\\s](\\d{1,3})";

    /**
     * 指令内容验证通过执行指令
     *
     * @return 指令内容验证通过执行指令
     */
    @Override
    public Object dealCmd(String cmd) throws UnifiedResultException {
        // 指令格式验证
        boolean match = ReUtil.isMatch(REG_DI, cmd);
        if (!match) {
            return Constant.FORMAT;
        }

        // 打印机序号
        String printNo = ReUtil.get(REG_DI, cmd, 1);

        // 打票机编号不存在提示"DEVICE"
        boolean isExist = this.printerNoIsExist(printNo);
        if (!isExist) {
            return Constant.DEVICE;
        }
        return this.dealCmdBusiness(printNo);
    }

    /**
     * 判断打票机是否存在
     *
     * @param printNo printNo
     * @return 判断打票机是否存在
     */
    private boolean printerNoIsExist(String printNo) {
        MemoryData md = MemoryDataUtils.getMemoryData();
        String officeNum = md.getMnjxOffice().getOfficeNo();
        MnjxOffice office = iMnjxOfficeService.lambdaQuery()
                .eq(MnjxOffice::getOfficeNo, officeNum)
                .one();
        if (ObjectUtil.isEmpty(office)) {
            return false;
        }
        MnjxPrinter printer = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getOfficeId, office.getOfficeId())
                .eq(MnjxPrinter::getPrinterNo, printNo)
                .one();
        return ObjectUtil.isNotEmpty(printer);
    }

    /**
     * 指令执行 显示打票机状态
     *
     * @param printNo printNo
     * @return 指令执行 显示打票机状态
     */
    private Object dealCmdBusiness(String printNo) throws UnifiedResultException {
        MemoryData md = MemoryDataUtils.getMemoryData();
        String officeNo = md.getMnjxOffice().getOfficeNo();
        // 查询打印机是否有票可以打
        DiVo ticket = diMapper.retrieveTicket(printNo, officeNo);
        if (ObjectUtil.isEmpty(ticket)) {
            throw new UnifiedResultException(Constant.NO_RECORD);
        }
        return this.buildResult(ticket);
    }

    /**
     * 封装返回结果
     *
     * @param ticket ticket
     */
    private UnifiedResult buildResult(DiVo ticket) {
        UnifiedResult unifiedResult = new UnifiedResult();
        MemoryData md = MemoryDataUtils.getMemoryData();
        // 组装回显信息（打票机状态）
        final boolean isPre = false;
        // 判断是否设置了建控
        String controlPid = StrUtil.EMPTY;
        String controlAgent = StrUtil.EMPTY;
        if (StrUtil.isNotEmpty(ticket.getSiId())) {
            MnjxSi si = iMnjxSiService.lambdaQuery().eq(MnjxSi::getSiId, ticket.getSiId()).one();
            if (ObjectUtil.isNotEmpty(si)) {
                // 当前建控了该打票机的工作号的PID
                controlPid = si.getSiPid();
                // 当前建控了该打票机的工作号
                controlAgent = si.getSiNo();
            }
        }
        ticket.setControlPid(StrUtil.fill(controlPid, CharUtil.SPACE, 20, isPre));
        ticket.setControlAgent(StrUtil.fill(controlAgent, CharUtil.SPACE, 22, isPre));
        ticket.setOffice(md.getMnjxOffice().getOfficeNo());
        ticket.setPrinterStatus(StrUtil.fill(ticket.getPrinterStatus(), CharUtil.SPACE, 23, isPre));
        ticket.setInputStatus(StrUtil.fill(ticket.getInputStatus(), CharUtil.SPACE, 21, isPre));
        ticket.setOutputStatus(StrUtil.fill(ticket.getOutputStatus(), CharUtil.SPACE, 22, isPre));
        String isNormal = ObjectUtil.isNull(ticket.getIsNormal()) ? StrUtil.EMPTY : ticket.getIsNormal();
        ticket.setIsNormal(StrUtil.fill(isNormal, CharUtil.SPACE, 18, isPre));
        // 如果已上票并且未执行卸票
        if (StrUtil.isNotEmpty(ticket.getTicketStart()) && StrUtil.isNotEmpty(ticket.getTicketEnd())) {
            String lastTicket = "NONE";
            ticket.setStartTicket(StrUtil.toString(ticket.getTicketStart()));
            ticket.setEndTicket(StrUtil.toString(ticket.getTicketEnd()));
            if (ObjectUtil.isNotEmpty(ticket.getLastTicket())) {
                lastTicket = ticket.getLastTicket();
            }
            ticket.setLastTicket(StrUtil.fill(lastTicket, CharUtil.SPACE, 11, isPre));
        }
        unifiedResult.setResults(Collections.singletonList(ticket).toArray());
        return unifiedResult;
    }
}
