package com.swcares.eterm.crs.mapper;

import com.swcares.entity.MnjxOpenCabin;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RfisMapper {

    /**
     * 获取pnrId
     *
     * @param pnrNmId 旅客id
     * @param nmXnId  婴儿id
     * @return 获取pnrId
     */
    String retrievePnrId(@Param("pnrNmId") String pnrNmId, @Param("nmXnId") String nmXnId);

    /**
     * 查询开舱数据
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @param sellCabin  销售舱位
     * @return 开舱数据
     */
    List<MnjxOpenCabin> retrieveOpenCabinList(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate, @Param("sellCabin") String sellCabin);
}
