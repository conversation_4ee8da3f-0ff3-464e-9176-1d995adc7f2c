package com.swcares.eterm.crs.obj.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2022/9/13-10:30
 */
@Data
public class TslDto {
    /**
     * 当前部门号
     */
    private String office;
    /**
     * iata
     */
    private String iataNumber;
    /**
     * 打票机
     */
    private String device;
    /**
     * 打票机Pid
     */
    private String pid;
    /**
     * 日期
     */
    private String dateCom;
    /**
     * 默认ALL 有航司参数设置航司
     */
    private String airline = "ALL";

    /**
     * 票号 pnr 等信息集合
     */
    private List<TslTicket> ticketList = new ArrayList<>();

    /**
     * 总票数
     */
    private String totalTickets;
    /**
     * 退票的票数
     */
    private String ticketRefund;
    /**
     * 总票价，不含税
     */
    private String normalFare="0.00";
    /**
     * 实收票款
     */
    private String carries="0.00";
    /**
     * 默认为0
     */
    private String commit = "0.00";
    /**
     * 总税款
     */
    private String normalTax="0.00";
    /**
     * 实收退款
     */
    private String netRefund="0.00";
    /**
     * 退票手续费，默认为0
     */
    private String deduction = "0.00";
    /**
     * 退票总税款
     */
    private String refundTax="0.00";
    /**
     * 退票代理费总额,默认为0
     */
    private String refundCommit = "0.00";
}
