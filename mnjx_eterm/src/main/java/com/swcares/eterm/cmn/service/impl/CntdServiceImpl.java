package com.swcares.eterm.cmn.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxAirline;
import com.swcares.eterm.cmn.dto.CntdResultDto;
import com.swcares.eterm.cmn.dto.CntdRetrieveDto;
import com.swcares.eterm.cmn.mapper.CntdMapper;
import com.swcares.eterm.cmn.service.ICntdService;
import com.swcares.service.IMnjxAirlineService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * CNTD指令处理 Service 国家/城市/机场信息查询
 *
 * <AUTHOR>
 */
@Service
public class CntdServiceImpl implements ICntdService {

    /**
     * CNDTD:参数/城市航司机场
     * 参数：T A N C M D
     */
    private static final Pattern PATTERN_NORMAL = Pattern.compile("(\\w)" +
            "/((\\w+)(\\s*(\\w*))*)");

    /**
     * CNTD:ALL
     */
    private static final Pattern PATTERN_ALL = Pattern.compile("(ALL|all)");

    /**
     * CNTD:参数/中文
     */
    private static final Pattern PATTERN_ZH = Pattern.compile("(\\w)" +
            "/([\\u4e00-\\u9fa5]*)");

    @Resource
    private CntdMapper cntdMapper;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Override
    public CntdRetrieveDto parseCmd(String cmd) throws UnifiedResultException {
        cmd = cmd.trim();
        String[] tmp = cmd.split(StrUtil.COLON);
        if (tmp.length != Constant.TWO) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        String param = tmp[1];
        CntdRetrieveDto cntdRetrieveDto = new CntdRetrieveDto();
        List<String> allGroups;
        if (ReUtil.isMatch(PATTERN_NORMAL, param)) {
            allGroups = ReUtil.getAllGroups(PATTERN_NORMAL, param);
            cntdRetrieveDto.setByNormal(true);
            // 选项参数
            cntdRetrieveDto.setQueryCode(allGroups.get(1));
            // 其他参数 城市 机场 航司
            cntdRetrieveDto.setQueryParam(allGroups.get(2));
        } else if (ReUtil.isMatch(PATTERN_ALL, param)) {
            // 查询所有
            allGroups = ReUtil.getAllGroups(PATTERN_ALL, param);
            cntdRetrieveDto.setQueryCode(allGroups.get(1));
            cntdRetrieveDto.setByAll(true);
        } else if (ReUtil.isMatch(PATTERN_ZH, param)) {
            throw new UnifiedResultException(Constant.PLS_INPUT_ENGLISH);
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        return cntdRetrieveDto;
    }

    /**
     * dealCmd
     *
     * @param retrieveDto
     * @param unifiedResult
     * @return dealCmd
     */
    @Override
    public List<CntdResultDto> handle(CntdRetrieveDto retrieveDto, UnifiedResult unifiedResult) throws UnifiedResultException {
        List<CntdResultDto> resultDtoList = new ArrayList<>();
        CntdResultDto cntdResultDto;
        switch (retrieveDto.getQueryCode()) {
            // CNTD：T/城市英文名
            case "T":
                retrieveDto.setByTa(true);
                resultDtoList = this.dealCmdTa(retrieveDto);
                break;
            // 查询航空公司两字代码 CNTD：M/航空公司名称（英文）
            case "M":
                retrieveDto.setByM(true);
                cntdResultDto = this.dealCmdM(retrieveDto);
                resultDtoList.add(cntdResultDto);
                break;
            // 根据航空公司两字代码查询航空公司名称 CNTD：D/航空公司两字代码
            case "D":
                retrieveDto.setByD(true);
                cntdResultDto = this.dealCmdD(retrieveDto);
                resultDtoList.add(cntdResultDto);
                break;
            default:
                throw new UnifiedResultException(Constant.SUBFUNCTION);
        }
        unifiedResult.setArgs(new Object[]{retrieveDto});
        return resultDtoList;
    }

    private List<CntdResultDto> dealCmdTa(CntdRetrieveDto retrieveDto) throws UnifiedResultException {
        List<CntdResultDto> resList = cntdMapper.retrieveCityInfo(retrieveDto.getQueryParam(), retrieveDto.getQueryCode());
        if (CollUtil.isEmpty(resList)) {
            throw new UnifiedResultException(Constant.NO_INFO);
        }
        return resList;
    }

    private CntdResultDto dealCmdM(CntdRetrieveDto retrieveDto) throws UnifiedResultException {
        CntdResultDto cntdResultDto = new CntdResultDto();
        List<MnjxAirline> mnjxAirlines = iMnjxAirlineService.lambdaQuery()
                .like(MnjxAirline::getAirlineEname, retrieveDto.getQueryParam())
                .list();
        if (CollUtil.isEmpty(mnjxAirlines)) {
            throw new UnifiedResultException(Constant.NO_INFO);
        }
        cntdResultDto.setAirlineList(mnjxAirlines);
        return cntdResultDto;
    }

    private CntdResultDto dealCmdD(CntdRetrieveDto retrieveDto) throws UnifiedResultException {
        CntdResultDto cntdResultDto = new CntdResultDto();
        String queryCondition = retrieveDto.getQueryParam();
        List<MnjxAirline> mnjxAirlines = iMnjxAirlineService.lambdaQuery()
                .eq(MnjxAirline::getAirlineCode, queryCondition)
                .list();
        if (CollUtil.isEmpty(mnjxAirlines)) {
            throw new UnifiedResultException(Constant.NO_INFO);
        }
        cntdResultDto.setAirlineList(mnjxAirlines);
        return cntdResultDto;
    }
}
