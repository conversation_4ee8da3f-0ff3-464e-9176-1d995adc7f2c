package com.swcares.eterm.cmn.service;

import com.swcares.core.cache.MemoryDataHandlerResult;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IPlService {

    /**
     * 全屏显示
     *
     * @param memoryDataHandlerResult 分页对象
     * @return 全屏显示
     */
    List<Integer> getPlFull(MemoryDataHandlerResult memoryDataHandlerResult);

    /**
     * 半屏显示
     *
     * @param memoryDataHandlerResult 分页对象
     * @return 半屏显示
     */
    List<Integer> getPlHalf(MemoryDataHandlerResult memoryDataHandlerResult);
}
