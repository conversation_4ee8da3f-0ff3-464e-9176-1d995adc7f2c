package com.swcares.eterm.cmn.service;

import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.cmn.dto.CntzResultDto;
import com.swcares.eterm.cmn.dto.CntzRetrieveDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/4 10:29
 */
public interface ICntzService {

    CntzRetrieveDto parseCmd(String cmd) throws UnifiedResultException;

    List<CntzResultDto> handle(CntzRetrieveDto cntzRetrieveDto, UnifiedResult unifiedResult) throws UnifiedResultException;
}
