package com.swcares.eterm.cmn.service.impl;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.eterm.cmn.dto.AoDto;
import com.swcares.eterm.cmn.service.IAoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AoServiceImpl implements IAoService {

    @Override
    public String handle(AoDto aoDto) throws UnifiedResultException {
        throw new UnifiedResultException(Constant.FUNCTION);
    }
}
