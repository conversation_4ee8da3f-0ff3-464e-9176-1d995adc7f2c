package com.swcares.eterm.cmn.handler;

import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.ListUtils;
import com.swcares.eterm.base.handler.Handler;

/**
 * DA指令处理，查看工作区域,PID等
 *
 * <AUTHOR>
 */
@OperateType(action = "DA", predicate = false, template = "/cmn/da.jf",shorthand=true)
public class DaHandler implements Handler {

    @Override
    public UnifiedResult handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        unifiedResult.setResults(ListUtils.toList(
                MemoryDataUtils.getMemoryData().getMemoryDataContainer().getContainer(),
                MemoryDataUtils.getMemoryData().getMemoryDataContainer().getActiveMemoryDataCabinet(),
                MemoryDataUtils.getMemoryData().getMnjxOffice()
        ).toArray());
        return unifiedResult;
    }
}
