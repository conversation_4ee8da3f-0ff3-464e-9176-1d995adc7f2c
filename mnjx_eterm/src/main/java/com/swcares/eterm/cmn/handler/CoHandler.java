package com.swcares.eterm.cmn.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.ListUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.eterm.base.handler.Handler;
import lombok.extern.slf4j.Slf4j;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
@Slf4j
@OperateType(action = "CO", shorthand = true, template = "/cmn/co.jf")
public class CoHandler implements Handler {

    private static final String CO_ADD_OR_SUB = "^[\\-|+.]$";

    private static final String CO_TIME_HX = "^[0-9]{2}[A-Z]{3}[0-9]{2}$";

    private static final String CO_CITY = "[A-Z]{6}";

    private static final String CO_CITY_ONE = "[A-Z]{3}";
    /**
     * 公里换算英里
     */
    private static final BigDecimal K2M = new BigDecimal("0.62137");

    /**
     * 英里换算公里
     */
    private static final BigDecimal M2K = new BigDecimal("1.60934");

    @Override
    public UnifiedResult handle(String cmd) throws UnifiedResultException {
        UnifiedResult unifiedResult = new UnifiedResult();
        /*
         * 1.如果出现m或者k 是公里或者英里换算
         * 2.如果是数字开头。那就是4则运算
         */
        try {
            log.info("Co 4折运算开始 原始指令{}", cmd);
            cmd = cmd.replaceAll(StrUtils.SPACE, StrUtils.EMPTY);
            String[] cos = cmd.split(":");
            cmd = cos[1];
            String[] cmdSplitArr = cmd.split("/");
            // 如果出现大于1 有2种可能，第一种是换算。第二种是除法
            log.debug("如果出现大于1 有2种可能，第一种是换算。第二种是除法");
            if (cmdSplitArr.length > 1) {
                if (cmdSplitArr[0].contains(Constant.COMMON_M)) {
                    BigDecimal dig = new BigDecimal(cmdSplitArr[1]);
                    String result = dig.multiply(K2M).setScale(2, RoundingMode.HALF_UP).toString();
                    log.info("公里换算英里的结果为:{}", result);
                    unifiedResult.setResults(ListUtils.toList(result).toArray());
                    return unifiedResult;
                } else if (cmdSplitArr[0].contains(Constant.COMMON_K)) {
                    BigDecimal dig = new BigDecimal(cmdSplitArr[1]);
                    // 保留2位小数，四舍五入
                    String result = dig.multiply(M2K).setScale(2, RoundingMode.HALF_UP).toString();
                    log.info("英里换算公里:{}", result);
                    unifiedResult.setResults(ListUtils.toList(result).toArray());
                    return unifiedResult;
                } else if (cmdSplitArr[0].contains(Constant.COMMON_T)) {
                    return cityTimeDifference(cmdSplitArr,unifiedResult);
                } else {
                    ScriptEngineManager sem = new ScriptEngineManager();
                    ScriptEngine engine = sem.getEngineByExtension("js");
                    String result = new BigDecimal(engine.eval(cmd).toString()).setScale(2, RoundingMode.HALF_UP).toString();
                    log.info("运算结果是：{}", result);
                    unifiedResult.setResults(ListUtils.toList(result).toArray());
                    return unifiedResult;
                }
            } else {
                ScriptEngineManager sem = new ScriptEngineManager();
                ScriptEngine engine = sem.getEngineByExtension("js");
                String result = new BigDecimal(engine.eval(cmd).toString()).setScale(2, RoundingMode.HALF_UP).toString();
                log.info("运算结果是：{}", result);
                unifiedResult.setResults(ListUtils.toList(result).toArray());
                return unifiedResult;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("报错信息为：{}", e.getMessage());
            throw new UnifiedResultException(Constant.FORMAT);
        }
    }

    /**
     * Title：cityZone <br>
     * description：获取城市所在的时区时间 <br>
     *
     * @return <br>
     * author：zhaoKan <br>
     * date：2021/02/20 <br>
     */
    private String cityZone() {
        return "";
    }

    /**
     * Title：gmtTime <br>
     * description：GMT时间转为航信时间 <br>
     *
     * @return <br>
     * author：zhaoKan <br>
     * date：2021/02/20 <br>
     */
    private String gmtTime(String ymd, String time) {
        // 当前日期格式化为字符串
        String date = DateUtils.formatDateTime(new Date());
        String[] tempYmd = date.split(" ");
        // 如果日期是+-.的处理
        if (StrUtils.strNotEmpty(ymd) && ymd.matches(CO_ADD_OR_SUB)) {
            String[] temp = tempYmd[0].split("-");
            int day;
            if (Constant.CROSSBAR.equals(ymd)) {
                day = Integer.parseInt(temp[2]) - 1;
            } else if (Constant.ADD_SYMBOL.equals(ymd)) {
                day = Integer.parseInt(temp[2]) + 1;
            } else {
                day = Integer.parseInt(temp[2]);
            }
            date = temp[0] + "-" + temp[1] + "-" + String.format("%02d", day) + " " + tempYmd[1];
        } else if (StrUtils.strNotEmpty(ymd) && ymd.matches(CO_TIME_HX)) {
            // 航信日期转为ymd格式字符串
            date = DateUtils.ymd2YmdhmsStr(DateUtils.com2ymd(ymd));

            Date tempDate = DateUtils.ymdhms2Date(date);
            date = DateUtils.formatDateTime(tempDate);
        }
        // 时分的处理
        String[] fDate = date.split(" ");
        if (StrUtils.strNotEmpty(time)) {
            date = fDate[0] + " " + time.substring(0, 2) + ":" + time.substring(2) + ":00";
        } else {
            String strDate = DateUtils.formatDateTime(new Date());
            String[] temp = strDate.split(" ");
            date = fDate[0] + " " + temp[1];
        }
        // 获取东八区时间
        Calendar c = Calendar.getInstance();
        c.setTime(Objects.requireNonNull(DateUtils.ymdhms2Date(date)));
        // 设置时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 设置时区为GMT
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        String str = sdf.format(c.getTime());

        String[] times = str.split(" ");
        String travelTime = DateUtils.ymd2Com(times[0]);
        String hm = times[1].substring(0, times[1].length() - 3).replace(":", "");
        return travelTime + " " + hm;
    }

    /**
     * CO:T/城市对 计算两个城市之间的时差
     * @param cmdSplitArr 参数
     * @param unifiedResult 返回结果
     * @return 返回结果
     * @throws UnifiedResultException 异常
     */
    private UnifiedResult cityTimeDifference(String[] cmdSplitArr,UnifiedResult unifiedResult) throws UnifiedResultException {
        // CO:T/城市对 计算两个城市之间的时差
        if (cmdSplitArr[1].matches(CO_CITY)) {
            StringBuilder result = new StringBuilder();
            String first = cmdSplitArr[1].substring(0, 3);
            String second = cmdSplitArr[1].substring(3);
            String firstZoneStr = cityZone();
            String secondZoneStr = cityZone();
            if (StrUtils.strNotEmpty(firstZoneStr)) {
                result.append(StrUtils.format("{}: {} ", first, firstZoneStr));
            } else {
                throw new UnifiedResultException(Constant.CITY_CODE_ERR_IN);
            }
            if (StrUtils.strNotEmpty(secondZoneStr)) {
                result.append(StrUtils.format("{}: {} ", second, secondZoneStr));
            } else {
                throw new UnifiedResultException(Constant.CITY_CODE_ERR_IN);
            }
            result.append(StrUtils.format("GMT: {} TIM DIF:0", gmtTime(null, null)));
            unifiedResult.setResults(ListUtils.toList(result).toArray());
            return unifiedResult;
        } else if (cmdSplitArr[1].matches(CO_CITY_ONE)) {
            // CO:T/城市对 计算两个城市之间的时差
            StringBuilder result = new StringBuilder();
            String date = null;
            String time = null;
            if (cmdSplitArr.length > Constant.TWO) {
                date = cmdSplitArr[2];
            }
            if (cmdSplitArr.length > Constant.THREE) {
                time = cmdSplitArr[3];
            }
            String cityZoneStr = cityZone();
            if (StrUtils.strNotEmpty(cityZoneStr)) {
                result.append(StrUtils.format("{}: {} ", cmdSplitArr[1], cityZoneStr));
            } else {
                throw new UnifiedResultException(Constant.CITY_CODE_ERR_IN);
            }
            result.append(StrUtils.format("GMT: {}", gmtTime(date, time)));
            unifiedResult.setResults(ListUtils.toList(result).toArray());
            return unifiedResult;
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
    }
}
