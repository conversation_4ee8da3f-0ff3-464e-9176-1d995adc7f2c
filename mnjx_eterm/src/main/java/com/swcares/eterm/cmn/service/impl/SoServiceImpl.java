package com.swcares.eterm.cmn.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataCabinet;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.*;
import com.swcares.eterm.base.service.IRestoreCkiService;
import com.swcares.eterm.cmn.service.ISoService;
import com.swcares.eterm.dcs.cki.service.ICkiCacheService;
import com.swcares.service.IMnjxConfigService;
import com.swcares.service.IMnjxPnrService;
import com.swcares.service.IMnjxPrinterService;
import com.swcares.service.IMnjxSiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SoServiceImpl implements ISoService {

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IRestoreCkiService iRestoreCkiService;

    @Resource
    private IMnjxConfigService iMnjxConfigService;

    @Resource
    private ICkiCacheService iCkiCacheService;

    @Override
    public List<MnjxPnr> retrievePnr(String pnrId) {
        return iMnjxPnrService.lambdaQuery().eq(MnjxPnr::getPnrId, pnrId).list();
    }

    @Override
    public Map<String, Object> handle() throws UnifiedResultException {
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MemoryDataCabinet memoryDataCabinet = memoryData.getMemoryDataContainer()
                .getActiveMemoryDataCabinet();
        if (ObjectUtil.isNull(memoryDataCabinet)) {
            throw new UnifiedResultException("SI");
        }
        MemoryDataPnr memoryDataPnr = memoryData.getMemoryDataPnr();
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        if (ObjectUtil.isNotNull(mnjxPnr)
                && Constant.PNR_OP.equalsIgnoreCase(mnjxPnr.getPnrStatus())) {
            throw new UnifiedResultException(Constant.PENDING);
        }
        MnjxSi mnjxSi = memoryDataCabinet.getMnjxSi();
        MnjxConfig soPrinterConfig = iMnjxConfigService.lambdaQuery()
                .eq(MnjxConfig::getType, "SO_PRINTER")
                .one();
        if (ObjectUtil.isNotEmpty(soPrinterConfig) && Constant.STR_ONE.equals(soPrinterConfig.getAvailable()) && CollUtil.isNotEmpty(iMnjxPrinterService.lambdaQuery().eq(MnjxPrinter::getSiId, mnjxSi.getSiId()).list())) {
            throw new UnifiedResultException(Constant.TICKET_PRINTER_IN_USE);
        }

        // SO退出时还原操作的数据
        MnjxConfig restoreConfig = iMnjxConfigService.lambdaQuery()
                .eq(MnjxConfig::getType, "RESTORE")
                .one();
        if (ObjectUtil.isNotEmpty(restoreConfig) && Constant.STR_ONE.equals(restoreConfig.getAvailable())) {
            log.info("SO执行还原操作");
            iRestoreCkiService.dealWithRestore();
        }

        MnjxOffice mnjxOffice = memoryData.getMnjxOffice();
        Map<String, Object> resultMap = new HashMap<>(1024);
        resultMap.put("officeNo", mnjxOffice.getOfficeNo());
        resultMap.put("siNo", mnjxSi.getSiNo());
        resultMap.put("lable", memoryDataCabinet.getLabel());
        memoryDataCabinet.setActivity(Constant.COMMON_N);
        memoryDataCabinet.setMnjxSi(null);
        memoryDataCabinet.setMnjxLevel(null);
        MnjxSi mnjxSiDb = iMnjxSiService.lambdaQuery()
                .eq(MnjxSi::getOfficeId, mnjxOffice.getOfficeId())
                .eq(MnjxSi::getSiNo, mnjxSi.getSiNo())
                .one();
        mnjxSiDb.setSignInStatus(Constant.STR_ZERO);
        mnjxSiDb.updateById();
        // 清除控制的PNR线程ID
        if (ObjectUtil.isNotEmpty(mnjxPnr) && StrUtil.isNotEmpty(mnjxPnr.getControlThreadId())) {
            mnjxPnr.setControlThreadId(null);
            mnjxPnr.updateById();
        }

        // 清空内存PNR
        memoryDataPnr.clearPnr();
        if (CollUtil.isNotEmpty(memoryData.getMemoryDataHandlerResult().getPnrRecallRecords())) {
            memoryData.getMemoryDataHandlerResult().getPnrRecallRecords().clear();
        }

        memoryData.getMemoryDataFt().clear();
        // 清除值机缓存
        iCkiCacheService.clearPuCache(memoryData);
        iCkiCacheService.clearHbpuCache(memoryData);

        iCkiCacheService.clearBfCache(memoryData);

        iCkiCacheService.clearSbCache(memoryData);

        iCkiCacheService.clearPaCache(memoryData);
        iCkiCacheService.clearHbpaCache(memoryData);

        iCkiCacheService.clearPrCache(memoryData);
        iCkiCacheService.clearHbprCache(memoryData);

        iCkiCacheService.clearPwCache(memoryData);
        iCkiCacheService.clearHbpwCache(memoryData);

        iCkiCacheService.clearPdAbcInfo(memoryData);
        iCkiCacheService.clearPdDefaultInfo(memoryData);
        iCkiCacheService.clearPdSbyInfo(memoryData);

        return resultMap;
    }
}
