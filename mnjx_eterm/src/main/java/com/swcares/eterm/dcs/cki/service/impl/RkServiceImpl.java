package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.MnjxAirport;
import com.swcares.entity.MnjxCnd;
import com.swcares.entity.MnjxPlanFlight;
import com.swcares.entity.MnjxPlanSection;
import com.swcares.eterm.dcs.cki.obj.dto.RkParamDto;
import com.swcares.eterm.dcs.cki.mapper.RkMapper;
import com.swcares.eterm.dcs.cki.service.IRkService;
import com.swcares.service.IMnjxAirportService;
import com.swcares.service.IMnjxCndService;
import com.swcares.service.IMnjxPlanFlightService;
import com.swcares.service.IMnjxPlanSectionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 航班配餐信息显示
 *
 * <AUTHOR>
 */
@Service
public class RkServiceImpl implements IRkService {
    @Resource
    private RkMapper rkMapper;
    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;
    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxCndService iMnjxCndService;
    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Override
    public String handle(RkParamDto rkParamDto) throws UnifiedResultException {
        //查询航班是否存在
        String planFlightId = retrievePlanFlight(rkParamDto.getFltNo(), rkParamDto.getFltDate());
        if (StrUtil.isEmpty(planFlightId)) {
            throw new UnifiedResultException(Constant.FIGHT_NOT_FIND);
        }
        MnjxPlanFlight mnjxPlanFlight = iMnjxPlanFlightService.getById(planFlightId);
        //查询输入的航站时候正确
        MnjxAirport mnjxAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, rkParamDto.getCity())
                .one();
        if (ObjectUtil.isEmpty(mnjxAirport)) {
            throw new UnifiedResultException(Constant.CITY);
        }
        //查询计划航节
        MnjxPlanSection planSection = iMnjxPlanSectionService.lambdaQuery()
                .eq(MnjxPlanSection::getPlanFlightId, planFlightId)
                .eq(MnjxPlanSection::getDepAptId, mnjxAirport.getAirportId()).one();
        List<String> cabinList = new ArrayList<>();
        String gradeEat = planSection.getGradeEat();
        if (StrUtil.isEmpty(gradeEat)) {
            //查询cnd 舱等,拼接舱等的餐食
            MnjxCnd mnjxCnd = iMnjxCndService.lambdaQuery()
                    .eq(MnjxCnd::getCndNo, mnjxPlanFlight.getCndNo())
                    .eq(MnjxCnd::getStatus, Constant.ONE)
                    .one();
            if (ObjectUtil.isEmpty(mnjxCnd)) {
                throw new UnifiedResultException(Constant.CND_NO_NOT_EXIST);
            }
            String firstCabin = mnjxCnd.getFirstCabinClass();
            String secondCabin = mnjxCnd.getSecondCabinClass();
            String thirdCabin = mnjxCnd.getThirdCabinClass();
            String fourthCabin = mnjxCnd.getFourthCabinClass();
            String fifthCabin = mnjxCnd.getFifthCabinClass();
            cabinList.add(firstCabin);
            cabinList.add(secondCabin);
            cabinList.add(thirdCabin);
            cabinList.add(fourthCabin);
            cabinList.add(fifthCabin);
            StringBuilder eat = new StringBuilder();
            for (String value : cabinList) {
                eat.append(value == null ? "" : "/000");
            }
            gradeEat = StrUtil.format("CAT{}", eat.toString());
        }

        return gradeEat;
    }

    @Override
    public String retrievePlanFlight(String fltNo, String fltDate) {
        return rkMapper.retrievePlanFlight(fltNo, DateUtils.com2ymd(fltDate));
    }
}
