package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.ListUtils;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.PaResultDto;
import com.swcares.eterm.dcs.cki.service.IPuService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@OperateType(action = "PU", shorthand = true, template = "/dcs/cki/pu.jf", fullScreen = true)
public class PuHandler implements Handler {

    @Resource
    private IPuService iPuService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
    	UnifiedResult unifiedResult = new UnifiedResult();
        List<PaResultDto> resultDtos = iPuService.dealCmd(cmd, MemoryDataUtils.getMemoryData());
        String replace = cmd.replaceAll(",(AVIH)?\\d+/\\d+(,)?", "");
        if (!replace.equals(cmd) && !cmd.contains("#")) {
            iPuService.printBag(resultDtos);
        }
        iPuService.savePuForPrint(resultDtos, "PU");
		unifiedResult.setResults(ListUtils.toList(resultDtos).toArray());
		return unifiedResult;
    }
}
