package com.swcares.eterm.dcs.cki.obj.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/10
 */
@Data
public class HbpaCmdDto {

    private int hbnbNo;

    private String flightNo;

    private String flightDate;

    private String sellCabin;

    private String orgCityCode;

    private String dstCityCode;

    private String nrec;

    /**
     * 先接收特服，没有进行旅客接收
     */
    private boolean isAdv;

    private String cmd;

    private List<String> optionList = new ArrayList<>();

    private String psgListFrom;
}
