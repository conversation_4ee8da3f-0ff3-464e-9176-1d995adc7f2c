package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.JcsyDto;
import com.swcares.eterm.dcs.cki.obj.vo.JcsyTotalVo;
import com.swcares.eterm.dcs.cki.service.IJcsyService;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2024/8/7 11:05
 */
@OperateType(action = "JCSY", shorthand = true, template = "/dcs/cki/jcsy.jf")
public class JcsyHandler implements Handler {

    @Resource
    private IJcsyService iJcsyService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        JcsyDto jcsyDto = iJcsyService.parseCmd(cmd);
        JcsyTotalVo jcsyTotalVo = iJcsyService.handle(jcsyDto);
        UnifiedResult unifiedResult = new UnifiedResult();
        unifiedResult.setArgs(Arrays.asList(cmd, jcsyDto.getOption()).toArray());
        unifiedResult.setResults(Collections.singletonList(jcsyTotalVo).toArray());
        return unifiedResult;
    }
}
