package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.BabDto;
import com.swcares.eterm.dcs.cki.obj.vo.BabRenderVo;
import com.swcares.eterm.dcs.cki.service.IBabService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@OperateType(action = "BAB", template = "dcs/cki/bab.jf", fullScreen = true)
public class BabHandler implements Handler {

    @Resource
    private IBabService iBabService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        BabDto babDto = iBabService.parseBab(cmd);
        BabRenderVo babRenderVo = iBabService.handle(babDto);
        unifiedResult.setArgs(new Object[]{cmd});
        unifiedResult.setResults(new Object[]{babRenderVo, babDto.getAcceptedNum(), babDto.getFailNum()});
        return unifiedResult;
    }
}
