package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataFt;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.CacheKeyConstant;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.PinyinUtils;
import com.swcares.entity.*;
import com.swcares.eterm.dcs.cki.obj.dto.BabDto;
import com.swcares.eterm.dcs.cki.obj.dto.PdInfoDto;
import com.swcares.eterm.dcs.cki.obj.dto.PdNmDto;
import com.swcares.eterm.dcs.cki.obj.dto.PdParamDto;
import com.swcares.eterm.dcs.cki.obj.vo.BabRenderVo;
import com.swcares.eterm.dcs.cki.obj.vo.BabVo;
import com.swcares.eterm.dcs.cki.service.IBabService;
import com.swcares.eterm.dcs.cki.service.IBdbService;
import com.swcares.eterm.dcs.cki.service.ICkiCacheService;
import com.swcares.eterm.dcs.cki.service.IPdService;
import com.swcares.service.*;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@CacheConfig(cacheNames = CacheKeyConstant.CACHE_ETERM_KEY)
public class BdbServiceImpl implements IBdbService {

    @Resource
    private IBabService iBabService;

    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPsgOperateRecordService iMnjxPsgOperateRecordService;
    @Resource
    private ICkiCacheService iCkiCacheService;
    @Resource
    private IPdService iPdService;

    @Override
    public BabDto parseBab(String cmd) throws UnifiedResultException {
        return iBabService.parseBab(cmd);
    }

    @Override
    public BabRenderVo handle(BabDto babDto) throws UnifiedResultException {
        List<BabVo> acceptBabVoList = new ArrayList<>();
        List<BabVo> failBabVoList = new ArrayList<>();
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MemoryDataFt memoryDataFt = memoryData.getMemoryDataFt();
        if (StrUtil.isEmpty(memoryDataFt.getFlightNo()) && StrUtil.isEmpty(memoryDataFt.getFlightDate())) {
            throw new UnifiedResultException(Constant.NO_DEFAULT_FLIGHT);
        }
        PdInfoDto pdInfoDto = iCkiCacheService.getPdAbcInfo(memoryData);
        if (ObjectUtil.isEmpty(pdInfoDto)) {
            pdInfoDto = iCkiCacheService.getPdDefaultInfo(memoryData);
        }
        if (ObjectUtil.isEmpty(pdInfoDto)) {
            throw new UnifiedResultException(Constant.NO_DISPLAY);
        }
        List<MnjxPsgOperateRecord> operateRecords = new ArrayList<>();
        List<PdNmDto> pdNmDtoList = pdInfoDto.getNms();
        List<String> aboardNoList = babDto.getAboardNoList();
        for (String aboardNo : aboardNoList) {
            BabVo babVo = new BabVo();
            babVo.setAboardNo(StrUtil.fill(StrUtil.toString(Integer.parseInt(aboardNo)), ' ', 3, false));
            List<PdNmDto> babNmDtoList = pdNmDtoList.stream()
                    .filter(p -> StrUtil.isNotEmpty(p.getAboardNo()) && Integer.parseInt(aboardNo.trim()) == Integer.parseInt(p.getAboardNo().trim()))
                    .collect(Collectors.toList());
            // 输入的登机号不在PD结果内
            if (CollUtil.isEmpty(babNmDtoList)) {
                babVo.setReason(Constant.INVALID_BN);
                babDto.setFailNum(babDto.getFailNum() + 1);
                failBabVoList.add(babVo);
            } else {
                PdNmDto pdNmDto = babNmDtoList.get(0);
                String pnrNmId = pdNmDto.getPnrNmId();
                MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
                MnjxPnrSeg pnrSeg = iMnjxPnrSegService.lambdaQuery()
                        .eq(MnjxPnrSeg::getPnrId, pnrNm.getPnrId())
                        .eq(MnjxPnrSeg::getFlightNo, pdInfoDto.getFlightNo())
                        .eq(MnjxPnrSeg::getFlightDate, pdInfoDto.getDate().contains("-") ? pdInfoDto.getDate() : DateUtils.com2ymd(pdInfoDto.getDate()))
                        .eq(MnjxPnrSeg::getOrg, pdNmDto.getOrg())
                        .eq(MnjxPnrSeg::getDst, pdNmDto.getDst())
                        .one();
                MnjxPsgCki psgCki = iMnjxPsgCkiService.lambdaQuery()
                        .eq(MnjxPsgCki::getPnrNmId, pnrNmId)
                        .eq(MnjxPsgCki::getPnrSegNo, pnrSeg.getPnrSegNo())
                        .one();
                String ckiStatus = psgCki.getCkiStatus();
                // 未登机的旅客不能拉下
                if (!Constant.GT.equals(ckiStatus)) {
                    babVo.setReason(Constant.INVALID_BN);
                    babDto.setFailNum(babDto.getFailNum() + 1);
                    failBabVoList.add(babVo);
                } else {
                    // 输入婴儿拉下
                    if (StrUtil.isNotEmpty(babDto.getInf())) {
                        this.bdbInf(babDto, babVo, acceptBabVoList, failBabVoList, psgCki, pnrSeg, operateRecords);
                    }
                    // 一起拉下
                    else {
                        this.bdbAll(babDto, babVo, acceptBabVoList, psgCki, pnrSeg, pdNmDto, operateRecords);
                    }
                    iMnjxPsgCkiService.updateById(psgCki);
                }
            }
        }
        iMnjxPsgOperateRecordService.saveBatch(operateRecords);
        //更新缓存
        String pdCmd = pdInfoDto.getCmdStr();
        PdParamDto pdParamDto = iPdService.dealCmd(pdCmd);
        iPdService.returnDefaultInfo(memoryData, pdParamDto);

        BabRenderVo babRenderVo = new BabRenderVo();
        babRenderVo.setAcceptVoList(acceptBabVoList);
        babRenderVo.setFailVoList(failBabVoList);
        return babRenderVo;
    }

    /**
     * Title: bdbAll
     * Description: 成人婴儿一起拉下
     *
     * <AUTHOR>
     */
    private void bdbAll(BabDto babDto, BabVo babVo, List<BabVo> acceptBabVoList, MnjxPsgCki psgCki, MnjxPnrSeg pnrSeg, PdNmDto pdNmDto, List<MnjxPsgOperateRecord> operateRecords) {
        psgCki.setCkiStatus(Constant.ACC);
        psgCki.setAboardTime(null);
        pdNmDto.setCkiStatus(Constant.ACC);
        // 同时拉下婴儿
        if (Constant.STR_ONE.equals(psgCki.getAbdStatusInfi())) {
            psgCki.setAbdStatusInfi(Constant.STR_ZERO);
            MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, psgCki.getPnrNmId())
                    .one();
            List<MnjxPnrNmTn> xnTnList = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getNmXnId, nmXn.getNmXnId())
                    .list();
            iBabService.updateTicketStatus(xnTnList, pnrSeg, Constant.CHECKED_IN);
            babDto.setAcceptedNum(babDto.getAcceptedNum() + 1);
            babVo.setContainsInf(true);
            // 产生操作记录 BDB PEK4696 AGT55555/29AUG1942
            operateRecords.add(iBabService.constructOperateRecord("BDB", pnrSeg.getPnrSegNo(), psgCki.getPnrNmId()));
            acceptBabVoList.add(babVo);
        }
        List<MnjxPnrNmTn> nmTnList = iMnjxPnrNmTnService.lambdaQuery()
                .eq(MnjxPnrNmTn::getPnrNmId, psgCki.getPnrNmId())
                .list();
        iBabService.updateTicketStatus(nmTnList, pnrSeg, Constant.CHECKED_IN);
        babDto.setAcceptedNum(babDto.getAcceptedNum() + 1);
        String name = pdNmDto.getName();
        if (name.contains(StrUtil.SLASH)) {
            name = name.split(StrUtil.SLASH)[0];
        } else if (ReUtil.isMatch(Constant.CHINESE_REG, name)) {
            name = PinyinUtils.getPinYin(name, false).toUpperCase();
        }
        babVo.setName(name);
        // 产生操作记录 BDB PEK4696 AGT55555/29AUG1942
        operateRecords.add(iBabService.constructOperateRecord("BDB", pnrSeg.getPnrSegNo(), psgCki.getPnrNmId()));
        acceptBabVoList.add(babVo);
    }

    /**
     * Title: bdbInf
     * Description: 婴儿拉下
     *
     * <AUTHOR>
     */
    private void bdbInf(BabDto babDto, BabVo babVo, List<BabVo> acceptBabVoList, List<BabVo> failBabVoList, MnjxPsgCki psgCki, MnjxPnrSeg pnrSeg, List<MnjxPsgOperateRecord> operateRecords) {
        babVo.setContainsInf(true);
        // 如果旅客没有携带婴儿
        MnjxNmSsr inftSsr = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, psgCki.getPnrNmId())
                .eq(MnjxNmSsr::getSsrType, Constant.SSR_TYPE_INFT)
                .one();
        if (ObjectUtil.isEmpty(inftSsr)) {
            babVo.setReason(Constant.NO_INF);
            babDto.setFailNum(babDto.getFailNum() + 1);
            failBabVoList.add(babVo);
        } else {
            MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, psgCki.getPnrNmId())
                    .one();
            List<MnjxPnrNmTn> xnTnList = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getNmXnId, nmXn.getNmXnId())
                    .list();
            iBabService.updateTicketStatus(xnTnList, pnrSeg, Constant.CHECKED_IN);
            psgCki.setAbdStatusInfi(Constant.STR_ZERO);
            babDto.setFailNum(babDto.getFailNum() + 1);
            String xnCname = nmXn.getXnCname();
            if (xnCname.contains(StrUtil.SLASH)) {
                xnCname = xnCname.split(StrUtil.SLASH)[0];
            } else if (ReUtil.isMatch(Constant.CHINESE_REG, xnCname)) {
                xnCname = PinyinUtils.getPinYin(xnCname, false).toUpperCase();
            }
            babVo.setName(xnCname);
            // 产生操作记录 BDB PEK4696 AGT55555/29AUG1942
            operateRecords.add(iBabService.constructOperateRecord("BDB", pnrSeg.getPnrSegNo(), psgCki.getPnrNmId()));
            acceptBabVoList.add(babVo);
        }
    }
}
