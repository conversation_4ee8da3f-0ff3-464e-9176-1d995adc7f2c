package com.swcares.eterm.dcs.cki.obj.dto;

import lombok.Data;


/**
 * <AUTHOR> by yaodan
 * 2022/5/5-15:48
 */
@Data
public class FiTcardSection {

    private String tcardSectionId;

    /**
     * 航班ID
     */
    private String tcardId;
    /**
     * 航节号
     */
    private Integer sectionNo;

    /**
     * 是否尾部航节：0 否 1是
     */
    private String isLastSection;

    /**
     * 机场代码id
     */
    private String airportId;
    /**
     * 机场代码
     */
    private String airportCode;

    /**DEPT离港时间
     *
     */
    private String depTime;

    /**ARR进港时间
     *
     */
    private String arrTime;

    /**进港日期变更：值范围-1到+6（相对于第一航节离港来说），则第一航节没有的此项值
     *
     */
    private String arrDateChange;

    /** "离港日期变更：	值范围-1到+6（相对于第一航节离港来说），则第一航节没有的此项值
     *
     */
    private String offDateChange;
}
