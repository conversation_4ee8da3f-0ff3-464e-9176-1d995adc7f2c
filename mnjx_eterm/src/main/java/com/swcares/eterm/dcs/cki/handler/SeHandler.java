package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.service.ISeService;

import javax.annotation.Resource;

/**
 * 座位的查询指令解析
 * eg:
 * SE:航班号/日期/舱位/航段
 *
 * <AUTHOR>
 */
@OperateType(action = "SE", shorthand = true, needPaging = false, fullScreen = true)
public class SeHandler implements Handler {

    @Resource
    private ISeService iSeService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        return iSeService.handler(cmd);
    }
}
