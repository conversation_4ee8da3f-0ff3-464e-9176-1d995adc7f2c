package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.AlDto;
import com.swcares.eterm.dcs.cki.service.IAlService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@OperateType(action = "AL", shorthand = true, needPaging = false)
public class AlHandler implements Handler {

    @Resource
    private IAlService iAlService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        // 参数解析
        AlDto alDto = iAlService.pares(cmd);
        // 业务处理
        return iAlService.handler(alDto);
    }

}
