package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.service.ISuService;

import javax.annotation.Resource;

/**
 * description：SU：改变座位性质
 * 1、在 SE 操作之后使用； l 该指令对锁定座位
 * 2、该指令对锁定座位（即座位图上字符为“X”的座位）操作无效
 * >SU：+座位符号/座位  改变座位状态
 * >SU：-座位符号/座位  还原座位状态
 *
 * <AUTHOR>
 * date 2022/08/09
 */
@OperateType(action = "SU", shorthand = true, needPaging = false)
public class SuHandler implements Handler {

    @Resource
    private ISuService iSuService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        return iSuService.handler(cmd);
    }
}
