package com.swcares.eterm.dcs.fdc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxFlight;
import com.swcares.eterm.dcs.fdc.service.IBpService;
import com.swcares.service.IMnjxFlightService;
import com.swcares.service.impl.PnrCommandServicePartBp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BpServiceImpl implements IBpService {

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private PnrCommandServicePartBp pnrCommandServicePartBp;

    /**
     * BP:R/ACT
     * BP:R/ACT/CA8888
     */
    private final static Pattern REG_BP = Pattern.compile("BP:R/ACT(/([0-9A-Za-z]{5,7}))?");

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // BP指令格式验证
        if (!ReUtil.isMatch(REG_BP, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        List<String> groups = ReUtil.getAllGroups(REG_BP, cmd);
        // 航班号
        String flightNo = groups.get(2);
        if (StrUtil.isNotEmpty(flightNo)) {
            return singleFlight(flightNo);
        } else {
            return batchTakeEffectFlight();
        }
    }

    /**
     * 生效单个航班
     *
     * @param flightNo flightNo
     * @return 生效单个航班
     * @throws UnifiedResultException 统一异常
     */
    private String singleFlight(String flightNo) throws UnifiedResultException {
        MnjxFlight mnjxFlight = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getFlightNo, flightNo).one();
        if (ObjectUtil.isEmpty(mnjxFlight)) {
            throw new UnifiedResultException(Constant.FLIGHT_NOT_FOUND);
        }
        pnrCommandServicePartBp.bp(mnjxFlight.getFlightId());
        return StrUtil.EMPTY;
    }

    /**
     * 批量生效航班
     *
     * @return 批量生效航班
     * @throws UnifiedResultException 统一异常
     */
    private String batchTakeEffectFlight() throws UnifiedResultException {
        // 查询所有STAGED航班
        List<MnjxFlight> flights = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getFlightStatus, Constant.FLIGHT_STATUS_STAGED).list();
        if (CollUtil.isEmpty(flights)) {
            throw new UnifiedResultException(String.format("%d FLIGHTS SUCCESSFUL,%d FLIGHTS FAILED", 0, 0));
        }
        // 记录执行生效失败的航班
        List<String> nothingFlight = new ArrayList<>();
        // 需要生效的航班
        List<String> flightIds = flights.stream().map(MnjxFlight::getFlightId).collect(Collectors.toList());
        for (String flightId : flightIds) {
            try {
                pnrCommandServicePartBp.bp(flightId);
            } catch (Exception e) {
                nothingFlight.add(flightId);
            }
        }
        return String.format("%d FLIGHTS SUCCESSFUL,%d FLIGHTS FAILED", flights.size() - nothingFlight.size(), nothingFlight.size());
    }
}
