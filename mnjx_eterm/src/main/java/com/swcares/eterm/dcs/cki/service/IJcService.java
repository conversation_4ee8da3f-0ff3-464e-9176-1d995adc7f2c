package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.cache.MemoryData;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.HbpaCmdDto;
import com.swcares.eterm.dcs.cki.obj.dto.PaResultDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IJcService{

    List<HbpaCmdDto> parseJc(String cmd) throws UnifiedResultException;

    List<PaResultDto> handle(MemoryData memoryData, List<HbpaCmdDto> hbpaCmdDtoList) throws UnifiedResultException;

    void print(List<PaResultDto> paResultDtos);
}
