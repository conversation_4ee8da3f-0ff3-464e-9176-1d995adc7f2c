package com.swcares.eterm.dcs.cki.obj.dto;

import com.swcares.entity.*;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * description：PwResultDto <br>
 * <AUTHOR> <br>
 * date 2022/08/12 <br>
 * @version v1.0 <br>
 */
@Data
public class PwResultDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private String fltNo;
	
	private String shareFltNo;

	private String fltDate;

	private String org;

	private String dst;

	private String avLayout;

	private String padLayout;

	private String planeType;

	private String planeVersion;

	private String gate;

	/**
	 * 预计登机时间，形如BDT1500
	 */
	private String estimateBoarding;

	/**
	 * 预计离港时间，形如SD1400（下午2点）
	 */
	private String estimateOff;

	/**
	 * 实际离港时间，形如ED1400
	 */
	private String actualOff;

	/**
	 * SA 实际进港
	 */
	private String actualArr;

	/**
	 * 飞行时间
	 */
	private String ft;

	private String pnrNmId;

	private String queryName;
	
	private String groupName;

	private String seatNo;

	private String ff;

	private String airlineCode;

	private String fqtv;


	/**
	 * 票号
	 */
	private String tktNo;
	
	/**
	 * 婴儿票号
	 */
	private String infTktNo;

	private String foidNo;

	private String isCnin;

	private String name;

	private String officeNo;

	private String phone;

	private String airlineEname;

	private String oInterlink;

	private String sellCabin;

	private String cabinClass;

	private String infName;

	private String bagNumber;

	private Integer bagWeight;

	private Integer avihWeight;

	private List<String> ssrTypeList;

	private String ssrFreeText;

	private String sex;

	private List<String> optionList;

	/**
	 * 值机信息对象
	 */
	private MnjxPsgCki mnjxPsgCki;

	/**
	 * 座位信息对象
	 */
	private MnjxPsgSeat mnjxPsgSeat;

	/**
	 * 航节list值机信息对象
	 */
	private List<MnjxPlanSection> planSectionList;

	private String spml;

	private List<String> otherSsrList;

	private String cndNo;

	private boolean isVip;

	private MnjxPnrNm mnjxPnrNm;

	/**
	 * 是否预处理
	 */
	private boolean isAdvance;

	/**
	 * 免费行李
	 */
	private String fba;

	/**
	 * MSG信息
	 */
	private String msg;

	/**
	 * PIL信息
	 */
	private String pil;
	/**
	 * 担架
	 */
	private String stcr;
	/**
	 * chd
	 */
	private String chld;

	/**
	 * 候补exst,CBBG,COUR,DIPL显示ures
	 */
	private boolean exst;
	
	/**
	 * 是否拉下
	 */
	private boolean isExecPw = false;
	
	/**
	 * 是否显示
	 */
	private boolean isShow = false;
	
	/**
	 * 出票时间
	 */
	private String issuedTime;

	/**
	 * 后续航段
	 */
	private List<PwResultDto> followList;
	/**
	 * 航段序号
	 */
	private Integer pnrSegNo;
	
	/**
	 * 联程集合
	 */
	private List<String> interlineList = new ArrayList<>();
	
	/**
	 * 旅客行李
	 */
	private List<MnjxLuggage> currentSegLuggageList = new ArrayList<>();
	private List<MnjxLuggage> currentSegAvihLuggageList = new ArrayList<>();
	private List<MnjxLuggage> allSegContainsXLuggageList = new ArrayList<>();
	private List<MnjxLuggage> allSegContainsXAvihLuggageList = new ArrayList<>();

	/**
     * OCA3123/23FEB23YSHA
     */
    private HbpwCmdDto lcCmdDto; 
    
    /**
     * WCBD WCBW WCMP WCOB需要显示在左下角
     */
    private String otherWc;

	/**
	 * 删除行李的类型
	 * 1 系统行李
	 * 2 手工行李
	 * 3 AVIH宠物类型行李
	 * 4 AVIH宠物类型手工系统
	 */
	private String delBagType;

	private boolean avih;

	private String meda;

}
