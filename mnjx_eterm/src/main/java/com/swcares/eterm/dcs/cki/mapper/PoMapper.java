package com.swcares.eterm.dcs.cki.mapper;

import com.swcares.entity.MnjxPlanFlight;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2021/7/21-15:31
 */
public interface PoMapper {

    /**
     * retrievePlanSection
     * @param flightNo  flightNo
     * @param flightDate flightDate
     * @return retrievePlanSection
     */
    List<MnjxPlanFlight> retrievePlanSection(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate);
}
