package com.swcares.eterm.dcs.fdc.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.fdc.dto.FsdDto;
import com.swcares.eterm.dcs.fdc.dto.FsdVo;
import com.swcares.eterm.dcs.fdc.service.IFsdService;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2024/7/31 10:00
 */
@OperateType(action = "FSD", shorthand = true, template = "/dcs/fdc/fsd.jf", fullScreen = true)
public class FsdHandler implements Handler {

    @Resource
    private IFsdService iFsdService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        FsdDto fsdDto = iFsdService.parseCmd(cmd);
        FsdVo fsdVo = iFsdService.handle(fsdDto);
        UnifiedResult unifiedResult = new UnifiedResult();
        unifiedResult.setArgs(Collections.singletonList(cmd).toArray());
        unifiedResult.setResults(Collections.singletonList(fsdVo).toArray());
        return unifiedResult;
    }
}
