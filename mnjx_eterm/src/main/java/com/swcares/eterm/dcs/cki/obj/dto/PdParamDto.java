package com.swcares.eterm.dcs.cki.obj.dto;

import java.util.ArrayList;
import java.util.List;

import com.swcares.entity.MnjxAirport;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PdParamDto {

	String fltNo;
	
	String date;
	
	String cabin;
	
	String city;
	
	String queryOrg;
	
	String queryDst;
	
	MnjxAirport org;	
	
	MnjxAirport dst;
	
	String paramsStr;
	
	String queryCabin;
	
	String queryGroupName;
	
	String queryName;
	
	String pnrCode;
	
	String sbyNms;
	
	List<String> params = new ArrayList<>();
	
	String cmdStr;
}
