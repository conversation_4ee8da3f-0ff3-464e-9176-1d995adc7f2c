package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataFt;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.MnjxNmSsr;
import com.swcares.entity.MnjxPnrNm;
import com.swcares.entity.MnjxPnrSeg;
import com.swcares.eterm.dcs.cki.mapper.FtMapper;
import com.swcares.eterm.dcs.cki.obj.dto.FtDto;
import com.swcares.eterm.dcs.cki.obj.dto.SpmlDto;
import com.swcares.eterm.dcs.cki.obj.dto.SpmlResult;
import com.swcares.eterm.dcs.cki.service.ISpmlService;
import com.swcares.service.IMnjxNmSsrService;
import com.swcares.service.IMnjxPnrNmService;
import com.swcares.service.IMnjxPnrSegService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/25 15:42
 */
@Slf4j
@Service
public class SpmlServiceImpl implements ISpmlService {

    /**
     * SPML:CA1234/12JUL
     * SPML:CA1234/12JUL24
     * SPML:CA1234/12JUL24EUOWQQDOQJDWHQW
     * SPML:CA1234/12JUL/PEK
     * SPML:CA1234/12JUL24/PEK
     * SPML:CA1234/12JUL24EUOWQQDOQJDWHQW/PEK
     */
    private static final Pattern SPML_PATTER = Pattern.compile("([A-Z0-9]*\\d*)/(.+)(/.+)?");
    private static final Pattern FLIGHT_DATE_PATTER = Pattern.compile("((\\d{2}[A-Z]{3}(\\d{2})?)|([+.-]))(.)*");

    @Resource
    private FtMapper ftMapper;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Override
    public SpmlDto parseCmd(String cmd) throws UnifiedResultException {
        SpmlDto spmlDto = new SpmlDto();
        String[] split = cmd.split(":");
        if (split.length == 1) {
            // 默认航班
            MemoryDataFt memoryDataFt = MemoryDataUtils.getMemoryData().getMemoryDataFt();
            if (StrUtil.isEmpty(memoryDataFt.getFlightDate()) && StrUtil.isEmpty(memoryDataFt.getFlightNo())) {
                throw new UnifiedResultException(Constant.NO_DEFAULT_FLIGHT);
            }
            String flightNo = memoryDataFt.getFlightNo();
            String flightDate = memoryDataFt.getFlightDate();
            String orgAirportCode = MemoryDataUtils.getMemoryData().getMnjxOffice().getOfficeNo().substring(0, 3);
            spmlDto.setFlightNo(flightNo);
            spmlDto.setFlightDate(DateUtils.ymd2Com(flightDate));
            spmlDto.setOrgAirportCode(orgAirportCode);
        } else  {
            String param = split[1];
            if (ReUtil.isMatch(SPML_PATTER, param)) {
                List<String> allGroups = ReUtil.getAllGroups(SPML_PATTER, param);
                String flightNo = allGroups.get(1);
                String flightDate = allGroups.get(2);
                String orgAirportCode = allGroups.get(3);
                if (StrUtil.isAllEmpty(flightDate, orgAirportCode)) {
                    flightDate = DateUtils.ymd2Com(DateUtils.today());
                }
                if (!ReUtil.isMatch(FLIGHT_DATE_PATTER, flightDate)) {
                    throw new UnifiedResultException(Constant.CITY);
                }
                List<String> dateGroups = ReUtil.getAllGroups(FLIGHT_DATE_PATTER, flightDate);
                flightDate = dateGroups.get(2);
                if (StrUtil.isEmpty(flightDate)) {
                    flightDate = DateUtils.ymd2Com(DateUtils.com2ymd(dateGroups.get(4)));
                }
                if (StrUtil.isNotEmpty(orgAirportCode)) {
                    orgAirportCode = orgAirportCode.substring(1);
                } else {
                    orgAirportCode = MemoryDataUtils.getMemoryData().getMnjxOffice().getOfficeNo().substring(0, 3);
                }
                DateTime dateTime = DateUtil.parseDate(DateUtils.com2ymd(flightDate));
                if (DateUtil.compare(dateTime, DateUtil.parseDate(DateUtil.today())) < 0 || DateUtil.compare(dateTime, DateUtil.tomorrow()) > 0) {
                    throw new UnifiedResultException(Constant.FLT_NBR);
                }
                spmlDto.setFlightNo(flightNo);
                spmlDto.setFlightDate(flightDate);
                spmlDto.setOrgAirportCode(orgAirportCode);
            } else {
                throw new UnifiedResultException(Constant.FORMAT);
            }
        }
        // 查询航班是否初始化
        FtDto ftDto = ftMapper.retrievePlanFlt(spmlDto.getFlightNo(), DateUtils.com2ymd(spmlDto.getFlightDate()));
        if (ObjectUtil.isEmpty(ftDto)) {
            throw new UnifiedResultException(Constant.ERROR);
        }
        if (!Constant.Y.equals(ftDto.getIsFlightInitial())) {
            throw new UnifiedResultException(Constant.NEED_INITIALIZE);
        }
        return spmlDto;
    }

    @Override
    public List<SpmlResult> handle(SpmlDto spmlDto) throws UnifiedResultException {
        List<SpmlResult> spmlResultList = new ArrayList<>();
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getFlightNo, spmlDto.getFlightNo())
                .eq(MnjxPnrSeg::getFlightDate, DateUtils.com2ymd(spmlDto.getFlightDate()))
                .list();
        if (CollUtil.isNotEmpty(pnrSegList)) {
            Map<String, List<MnjxPnrSeg>> segMap = pnrSegList.stream()
                    .filter(p -> spmlDto.getOrgAirportCode().equals(p.getOrg()))
                    .collect(Collectors.groupingBy(MnjxPnrSeg::getCabinClass));
            if (MapUtil.isNotEmpty(segMap)) {
                for (Map.Entry<String, List<MnjxPnrSeg>> entry : segMap.entrySet()) {
                    SpmlResult spmlResult = new SpmlResult();
                    String cabinClass = entry.getKey();
                    pnrSegList = entry.getValue();
                    List<String> pnrIdList = pnrSegList.stream()
                            .map(MnjxPnrSeg::getPnrId)
                            .collect(Collectors.toList());
                    List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                            .in(MnjxPnrNm::getPnrId, pnrIdList)
                            .list();
                    List<String> pnrNmIdList = pnrNmList.stream()
                            .map(MnjxPnrNm::getPnrNmId)
                            .collect(Collectors.toList());
                    List<MnjxNmSsr> spmlSsrList = iMnjxNmSsrService.lambdaQuery()
                            .in(MnjxNmSsr::getPnrNmId, pnrNmIdList)
                            .likeLeft(MnjxNmSsr::getSsrType, "ML")
                            .list();
                    if (CollUtil.isNotEmpty(spmlSsrList)) {
                        Map<String, Long> ssrTypeMap = spmlSsrList.stream()
                                .collect(Collectors.groupingBy(MnjxNmSsr::getSsrType, Collectors.counting()));
                        spmlResult.setMlCountMap(ssrTypeMap);
                    }
                    spmlResult.setCabinClass(cabinClass);
                    spmlResult.setTotal(spmlSsrList.size());
                    spmlResultList.add(spmlResult);
                }
            } else {
                throw new UnifiedResultException(Constant.CITY);
            }
        } else {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        return spmlResultList;
    }
}
