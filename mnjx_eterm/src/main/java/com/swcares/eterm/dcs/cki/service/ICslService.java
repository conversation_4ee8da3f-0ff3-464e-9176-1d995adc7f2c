package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.vo.CslVo;

/**
 * CSL指令接口
 *
 * <AUTHOR>
 */
public interface ICslService {

    /**
     * 指令處理
     *
     * @param cmd 1
     * @return 结果
     * @throws UnifiedResultException 异常
     * @
     * <AUTHOR>
     * @update 2018年6月22日
     */
    CslVo handle(String cmd) throws UnifiedResultException;
}
