package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataCabinet;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.*;
import com.swcares.entity.*;
import com.swcares.eterm.dcs.cki.obj.dto.HbprDto;
import com.swcares.eterm.dcs.cki.obj.dto.PaResultDto;
import com.swcares.eterm.dcs.cki.obj.dto.PdInfoDto;
import com.swcares.eterm.dcs.cki.obj.dto.PdNmDto;
import com.swcares.eterm.dcs.cki.service.*;
import com.swcares.service.*;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created on 2019年1月24日<br>
 * Description: [行李、特服信息操作]<br>
 * Copyright: Copyright (c) 2019<br>
 * Company: 民航西南凯亚<br>
 * Department: 产品部<br>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@CacheConfig(cacheNames = CacheKeyConstant.CACHE_ETERM_KEY)
public class HbpuServiceImpl implements IHbpuService {

    @Resource
    private IAspectCkiService iAspectCkiService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Resource
    private IMnjxPsgSeatService iMnjxPsgSeatService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IPaService iPaService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxSeatService iMnjxSeatService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IPuService iPuService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;

    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IPrService iPrService;

    @Resource
    private IHbprService iHbprService;

    @Resource
    private IMnjxLuggageService iMnjxLuggageService;

    private static final String REG_HBPU = "HBPU[:|\\s]\\s?([0-9A-Z]{5,7})/([+-.]|([0-9]{2}[A-Z]{3}([0-9]{2})?))/?([A-Z*])/?([A-Z]{3})(#)?([0-9]+)((,;[\\w\\W\\u4e00-\\u9fa5]+)+)?";
    private static final String REG_HBPU1 = "HBPU[:|\\s]\\s?([0-9A-Z]{5,7})/([+-.]|([0-9]{2}[A-Z]{3}([0-9]{2})?))/?([A-Z*])/?([A-Z]{3})(#)?(((;)?(\\d+)(,[\\w\\W\\u4e00-\\u9fa5]+)*)+)";

    /**
     * 多旅客同时分配座位 13AB 13-14A 13 13-14
     */
    private static final Pattern MULTI_SEAT_PATTER = Pattern.compile("((\\d+)(-(\\d+))?)(([A-Z])+(-([A-Z]))?)?");

    /**
     * 摇篮 BSCT1/n
     */
    private static final Pattern BSCT_PATTERN = Pattern.compile("BSCT(\\d+)/(\\d+)");

    /**
     * 客舱宠物 PETC2/10
     */
    private static final Pattern PETC_PATTERN = Pattern.compile("^PETC(\\d+)/(\\d+)$");

    @Override
    @CachePut(key = "'hbpuResult' + #memoryData.memoryDataId")
    public List<PaResultDto> dealCmd(String cmd, MemoryData memoryData) throws UnifiedResultException {
        if (!cmd.trim().toUpperCase().startsWith(Constant.HBPU_CODE)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        String replaceCmd = cmd.trim();
        String type;
        Integer indexNum;
        String paramStr;
        List<String> paramStrs = new ArrayList<>();
        List<PaResultDto> resultDtos = new ArrayList<>();
        if (!replaceCmd.matches(REG_HBPU1)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        List<String> groups = ReUtil.getAllGroups(Pattern.compile(REG_HBPU1), replaceCmd);
        type = groups.get(7);
        String multiPsgAndOption = groups.get(8);
        if (multiPsgAndOption.startsWith(StrUtils.SEMICOLON)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        String[] psgSplit = multiPsgAndOption.split(";");
        // 2023.12版本，多个旅客一起操作时，只会修改座位
        if (psgSplit.length > 1) {
            // 必须值机操作
            if (StrUtil.isNotEmpty(type)) {
                throw new UnifiedResultException(Constant.PAX_STATUS_CONFLICT);
            }
            String[] split = multiPsgAndOption.split(",");
            List<PaResultDto> parseDtos = new ArrayList<>();
            // 旅客序号
            String[] psgIndexSplit = split[0].split(";");
            for (String psgIndex : psgIndexSplit) {
                HbprDto hbprDto = new HbprDto();
                hbprDto.setFlightNo(groups.get(1));
                String flightDate = groups.get(2);
                flightDate = DateUtils.com2ymd(flightDate);
                hbprDto.setFlightDate(flightDate);
                List<PdNmDto> pdDtos = new ArrayList<>();
                PdInfoDto pdInfoDto = new PdInfoDto();
                List<String> hbnbNoList = Arrays.asList(StrUtil.fill(StrUtil.toString(psgIndex), '0', 4, true));
                iHbprService.checkoutCacheHit(hbprDto, pdDtos, pdInfoDto, hbnbNoList, memoryData, false, false, false);
                if (StrUtil.isEmpty(pdInfoDto.getFlightNo())) {
                    throw new UnifiedResultException(Constant.NO_RECORD);
                }
                List<String> segInfoList = new ArrayList<>();
                PaResultDto buildPaDto = this.pdDtoCheck(Integer.parseInt(psgIndex), type, pdDtos, pdInfoDto, segInfoList);

                String pnrNmId = buildPaDto.getPnrNmId();
                MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
                MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
                MnjxPnrSeg pnrSeg = iMnjxPnrSegService.lambdaQuery()
                        .eq(MnjxPnrSeg::getPnrId, pnrNm.getPnrId())
                        .eq(MnjxPnrSeg::getFlightNo, StrUtil.isNotEmpty(buildPaDto.getShareFlightNo()) ? buildPaDto.getShareFlightNo() : buildPaDto.getFlightNo())
                        .eq(MnjxPnrSeg::getFlightDate, buildPaDto.getFlightDate())
                        .eq(MnjxPnrSeg::getOrg, buildPaDto.getOrgAirport())
                        .eq(MnjxPnrSeg::getDst, buildPaDto.getDstAirport())
                        .one();
                if (ObjectUtil.isEmpty(pnrSeg)) {
                    throw new UnifiedResultException(Constant.CHECK_SEG);
                }
                MnjxPsgCki psgCki = iMnjxPsgCkiService.lambdaQuery()
                        .eq(MnjxPsgCki::getPnrNmId, pnrNmId)
                        .eq(MnjxPsgCki::getPnrSegNo, pnrSeg.getPnrSegNo())
                        .one();
                MnjxPsgSeat psgSeat = iMnjxPsgSeatService.lambdaQuery()
                        .eq(MnjxPsgSeat::getPsgCkiId, psgCki.getPsgCkiId())
                        .one();
                List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrSegNo, pnrSeg.getPnrSegNo().toString())
                        .eq(MnjxNmSsr::getPnrNmId, buildPaDto.getPnrNmId())
                        .list();
                List<MnjxPnrRecord> pnrRecordList = iMnjxPnrRecordService.lambdaQuery()
                        .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                        .list();
                buildPaDto.setPnrNm(pnrNm);
                buildPaDto.setPnr(pnr);
                buildPaDto.setMnjxPnrSeg(pnrSeg);
                buildPaDto.setPsgCkiId(psgCki.getPsgCkiId());
                buildPaDto.setMnjxPsgCki(psgCki);
                buildPaDto.setMnjxPsgSeat(psgSeat);
                buildPaDto.setCabinClass(psgCki.getCabinClass());
                buildPaDto.setSellCabin(psgCki.getSellCabin());
                buildPaDto.setSsrList(ssrList);
                buildPaDto.setPnrRecordList(pnrRecordList);

                parseDtos.add(buildPaDto);
            }
            // 座位参数
            String seatParam = split[1];
            this.parseMultiSeats(seatParam.substring(1), parseDtos);
            List<PaResultDto> returnStrList = new ArrayList<>();
            for (PaResultDto parseDto : parseDtos) {
                returnStrList.add(iPuService.doOptions(replaceCmd, parseDto));
            }
            for (int i = 0; i < returnStrList.size(); i++) {
                PaResultDto paResultDto = returnStrList.get(i);
                resultDtos.add(changeReturnStr(iPuService.returnStr(paResultDto, replaceCmd, i), replaceCmd));
            }
        } else {
            indexNum = Integer.parseInt(groups.get(11));
            paramStr = groups.get(12);
            if (StrUtil.isBlank(paramStr)) {
                throw new UnifiedResultException(Constant.NO_ACTION_INDICATED);
            }

            HbprDto hbprDto = new HbprDto();
            hbprDto.setFlightNo(groups.get(1));
            String flightDate = groups.get(2);
            flightDate = DateUtils.com2ymd(flightDate);
            hbprDto.setFlightDate(flightDate);
            List<PdNmDto> pdDtos = new ArrayList<>();
            PdInfoDto pdInfoDto = new PdInfoDto();
            List<String> hbnbNoList = Arrays.asList(StrUtil.fill(StrUtil.toString(indexNum), '0', 4, true));
            iHbprService.checkoutCacheHit(hbprDto, pdDtos, pdInfoDto, hbnbNoList, memoryData, false, false, false);
            if (StrUtil.isEmpty(pdInfoDto.getFlightNo())) {
                throw new UnifiedResultException(Constant.NO_RECORD);
            }

            List<String> segInfoList = new ArrayList<>();
            PaResultDto buildPaDto = this.pdDtoCheck(indexNum, type, pdDtos, pdInfoDto, segInfoList);
            if (ObjectUtil.isNull(buildPaDto)) {
                throw new UnifiedResultException(Constant.NO_DISPLAY_SYMBOL);
            }

            if (CollUtil.isNotEmpty(segInfoList)) {
                segInfoList.removeIf(segInfo -> !segInfo.trim().startsWith("O/"));
            }
            List<PaResultDto> generateResultDtos = new ArrayList<>();
            if (CollUtil.isNotEmpty(segInfoList)) {
                generateResultDtos = generateResultDto(buildPaDto, segInfoList, paramStr, paramStrs);
            } else {
                generateResultDtos.add(buildPaDto);
                paramStrs.add(paramStr);
            }
//            boolean throwEx = true;
            if (generateResultDtos.size() > 1) {
                List<PaResultDto> list = generateResultDtos.subList(1, generateResultDtos.size());
                generateResultDtos.get(0).getOList().addAll(Stream.generate(() -> "O").limit(list.size()).collect(Collectors.toList()));
                if (Constant.NACC.equalsIgnoreCase(generateResultDtos.get(0).getCkiStatus()) || Constant.DL.equalsIgnoreCase(generateResultDtos.get(0).getCkiStatus())) {
                    throw new UnifiedResultException(Constant.PAX_STATUS_CONFLICT);
                }
            }
            PaResultDto parseAndDeal;
            String nmId = pdDtos.get(0).getPnrNmId().intern();
            List<PaResultDto> parseDtos = new ArrayList<>();
            synchronized (nmId) {
                for (int i = 0; i < generateResultDtos.size(); i++) {
                    if (StrUtil.isBlank(paramStrs.get(i))) {
                        paramStrs.set(i, ",");
                    }
                    if (i == 0) {
                        parseAndDeal = iPuService.parseAndDeal(replaceCmd, paramStrs.get(i), buildPaDto, type, memoryData);
                    } else {
//                        if (Constant.NACC.equalsIgnoreCase(generateResultDtos.get(i).getCkiStatus()) || Constant.DL.equalsIgnoreCase(generateResultDtos.get(i).getCkiStatus())) {
//                            throwEx = false;
//                        }
                        parseAndDeal = iPuService.parseAndDeal(replaceCmd, paramStrs.get(i), generateResultDtos.get(i), type, memoryData);
                    }
                    parseDtos.add(parseAndDeal);
                }
                List<PaResultDto> returnStrList = new ArrayList<>();
                List<String> paramList = Arrays.asList(paramStr.substring(1).split(","));
                // 参数包含O，表示给当前旅客的联程段修改座位
                if (paramList.contains(Constant.PARAM_O)) {

                }
//                if (!paramList.contains(Constant.PARAM_O)) {
//                    throwEx = false;
//                }
//                if (throwEx && generateResultDtos.size() != 1) {
//                    throw new UnifiedResultException(Constant.NO_ACTION_INDICATED);
//                } else {
                iPaService.validateUwtUaw(parseDtos);
                for (PaResultDto parseDto : parseDtos) {
                    if (Constant.NACC.equals(parseDto.getCkiStatus()) || Constant.DL.equals(parseDto.getCkiStatus())) {
                        this.receivePass(parseDto);
                    }
                    returnStrList.add(iPuService.doOptions(replaceCmd, parseDto));
                }
//                }
                if (CollUtil.isNotEmpty(returnStrList)) {
                    for (int i = 0; i < returnStrList.size(); i++) {
                        PaResultDto paResultDto = returnStrList.get(i);
                        resultDtos.add(changeReturnStr(iPuService.returnStr(paResultDto, replaceCmd, i), replaceCmd));
                    }
                }
            }
        }
        return resultDtos;
    }

    @Override
    public void printBag(List<PaResultDto> resultDtos) {
        iPuService.printBag(resultDtos);
    }

    @Override
    public void savePuForPrint(List<PaResultDto> resultDtos) {
        iPuService.savePuForPrint(resultDtos, "HBPU");
    }

    private void receivePass(PaResultDto paResultDto) {
        List<MnjxPlanSection> thisPsgPlanSections = paResultDto.getPlanSectionList();
        MnjxPsgCki updateOne = iMnjxPsgCkiService.lambdaQuery().eq(MnjxPsgCki::getPsgCkiId, paResultDto.getMnjxPsgCki().getPsgCkiId()).one();
        //设置座位
        MnjxPsgSeat psgSeat = iMnjxPsgSeatService.lambdaQuery().eq(MnjxPsgSeat::getPsgCkiId, updateOne.getPsgCkiId()).one();
        List<String> openCabinIdList = paResultDto.getCabinClassOpenCabinList().stream()
                .map(MnjxOpenCabin::getOpenCabinId).collect(Collectors.toList());
        List<MnjxSeat> allSeats;
        if (StrUtil.isBlank(paResultDto.getSeatNo())) {
            allSeats = iMnjxSeatService.lambdaQuery()
                    .eq(MnjxSeat::getPlanSectionId, thisPsgPlanSections.get(0).getPlanSectionId())
                    .in(MnjxSeat::getOpenCabinId, openCabinIdList)
                    .eq(MnjxSeat::getSeatStatus, "*")
                    .list();
        } else {
            allSeats = iMnjxSeatService.lambdaQuery()
                    .eq(MnjxSeat::getPlanSectionId, thisPsgPlanSections.get(0).getPlanSectionId())
                    .in(MnjxSeat::getOpenCabinId, openCabinIdList)
                    .eq(MnjxSeat::getSeatStatus, "*")
                    .ne(MnjxSeat::getSeatNo, paResultDto.getSeatNo())
                    .list();
        }
        String seatNo = null;
        for (MnjxSeat seat : allSeats) {
            List<MnjxSeat> seatList = iMnjxSeatService.lambdaQuery()
                    .in(MnjxSeat::getOpenCabinId, openCabinIdList)
                    .eq(MnjxSeat::getSeatNo, seat.getSeatNo())
                    .list();
            if (seatList.stream().allMatch(s -> "*".equals(s.getSeatStatus()))) {
                seatNo = seat.getSeatNo();
                break;
            }
        }
        if (StrUtil.isNotBlank(seatNo)) {
            psgSeat.setPsgSeat(seatNo);
            psgSeat.setSeatStatus(".");
            iMnjxPsgSeatService.updateById(psgSeat);
            paResultDto.setMnjxPsgSeat(psgSeat);
            List<String> allPlanSectionIdList = thisPsgPlanSections.stream().map(MnjxPlanSection::getPlanSectionId)
                    .collect(Collectors.toList());
            List<MnjxSeat> seatList = iMnjxSeatService.lambdaQuery()
                    .in(MnjxSeat::getPlanSectionId, allPlanSectionIdList)
                    .eq(MnjxSeat::getSeatNo, seatNo)
                    .list();
            seatList.forEach(o -> {
                String seatStatus = o.getSeatStatus();
                o.setSeatStatus(".");
                o.setSeatStatusOld(seatStatus);
            });
            iAspectCkiService.updateSeatList(seatList, paResultDto.getPnrNmId());
//            iMnjxSeatService.updateBatchById(seatList);
            // 修改值机状态
            List<MnjxPnrSeg> allPnrSegList = iMnjxPnrSegService.lambdaQuery()
                    .eq(MnjxPnrSeg::getFlightDate, paResultDto.getFlightDate())
                    .eq(MnjxPnrSeg::getFlightNo, paResultDto.getFlightNo())
                    .eq(MnjxPnrSeg::getOrg, paResultDto.getOrgAirport())
                    .eq(MnjxPnrSeg::getDst, paResultDto.getDstAirport())
                    .list();
            if (StrUtil.isBlank(updateOne.getAboardNo())) {
                Integer aboardNo = iPaService.getNextAboardNo(allPnrSegList);
                updateOne.setAboardNo(StrUtil.fill(StrUtil.toString(aboardNo), '0', 3, true));
            }

            MnjxPlanSection firstPlanSection = paResultDto.getPlanSectionList().get(0);
            updateOne.setGate(firstPlanSection.getGate());
            updateOne.setCkiStatus(Constant.ACC);
            iMnjxPsgCkiService.updateById(updateOne);
            paResultDto.setMnjxPsgCki(updateOne);
            // 生成操作记录
            MnjxPsgOperateRecord record = this.constructOperateRecord(Constant.ACC, updateOne.getPsgCkiId(), paResultDto, null);
            iAspectCkiService.insertPsgOperateRecordList(Collections.singletonList(record), paResultDto.getPnrNmId());
//            iMnjxPsgOperateRecordService.save(record);
            // 处理票号
            MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, paResultDto.getPnrNmId())
                    .one();
            List<MnjxPnrNmTn> pnrNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getPnrNmId, paResultDto.getPnrNmId())
                    .or(ObjectUtil.isNotEmpty(nmXn), p -> p.eq(MnjxPnrNmTn::getNmXnId, nmXn.getNmXnId()))
                    .list();
            List<String> tnIdList = pnrNmTnList.stream()
                    .map(MnjxPnrNmTn::getTnId)
                    .collect(Collectors.toList());
            List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                    .in(MnjxPnrNmTicket::getPnrNmTnId, tnIdList)
                    .and(m -> m.eq(MnjxPnrNmTicket::getS1Id, paResultDto.getMnjxPnrSeg().getPnrSegId())
                            .or().eq(MnjxPnrNmTicket::getS2Id, paResultDto.getMnjxPnrSeg().getPnrSegId()))
                    .eq(MnjxPnrNmTicket::getIsEt, Constant.STR_ONE)
                    .list();
            if (CollUtil.isNotEmpty(nmTicketList)) {
                // 票面状态改为CHECKED IN
                nmTicketList.forEach(n -> {
                    if (paResultDto.getMnjxPnrSeg().getPnrSegId().equals(n.getS1Id())) {
                        n.setTicketStatus1(Constant.CHECKED_IN);
                    } else if (paResultDto.getMnjxPnrSeg().getPnrSegId().equals(n.getS2Id())) {
                        n.setTicketStatus2(Constant.CHECKED_IN);
                    }
                });
                iAspectCkiService.updatePnrNmTicketList(nmTicketList, paResultDto.getPnrNmId());
//                iMnjxPnrNmTicketService.updateBatchById(nmTicketList);
            }
            // 更新航节各舱等值机人数
            for (MnjxPlanSection planSection : thisPsgPlanSections) {
                String gradeCkNumber = planSection.getGradeCkNumber();
                String[] split = gradeCkNumber.split(StrUtil.SLASH);
                StringBuilder sb = new StringBuilder();
                for (String cabinPsgNum : split) {
                    String cabinClass = cabinPsgNum.substring(0, 1);
                    if (StrUtil.isNotEmpty(sb.toString())) {
                        sb.append(StrUtil.SLASH);
                    }
                    if (paResultDto.getCabinClass().equals(cabinClass)) {
                        int psgNum = Integer.parseInt(cabinPsgNum.substring(1));
                        psgNum++;
                        sb.append(cabinClass).append(StrUtil.fill(StrUtil.toString(psgNum), '0', 3, true));
                    } else {
                        sb.append(cabinPsgNum);
                    }
                }
                planSection.setGradeCkNumber(sb.toString());
            }
            iMnjxPlanSectionService.updateBatchById(thisPsgPlanSections);
        } else {
            updateOne.setCkiStatus(Constant.SB);
            updateOne.setIsHb(Constant.STR_ONE);
            updateOne.setCap(Constant.STR_ONE);
            if (StrUtil.isNotEmpty(paResultDto.getExstType())) {
                updateOne.setUres(Constant.STR_ONE);
            }
            // 接收为候补，产生候补号
            if (StrUtil.isEmpty(updateOne.getHbNo())) {
                int hbNbNo = 1;
                List<MnjxPsgCki> allPsgCkiList = iMnjxPsgCkiService.lambdaQuery()
                        .isNotNull(MnjxPsgCki::getHbNo)
                        .list();
                if (CollUtil.isNotEmpty(allPsgCkiList)) {
                    OptionalInt max = allPsgCkiList.stream()
                            .mapToInt(p -> Integer.parseInt(p.getHbNo()))
                            .max();
                    hbNbNo = max.getAsInt() + 1;
                }
                updateOne.setHbNo(StrUtil.fill(StrUtil.toString(hbNbNo), '0', 4, true));
            }
            iMnjxPsgCkiService.updateById(updateOne);
            paResultDto.setMnjxPsgCki(updateOne);
        }
        // 接收的这一段已删除的行李数据清空
        List<MnjxLuggage> xLuggageList = iMnjxLuggageService.lambdaQuery()
                .eq(MnjxLuggage::getPnrNmId, paResultDto.getPnrNmId())
                .eq(MnjxLuggage::getBagSegNo, paResultDto.getMnjxPnrSeg().getPnrSegNo())
                .eq(MnjxLuggage::getIsDel, "X")
                .list();
        if (CollUtil.isNotEmpty(xLuggageList)) {
            iAspectCkiService.deleteLuggageList(xLuggageList, paResultDto.getPnrNmId());
        }
    }

    private MnjxPsgOperateRecord constructOperateRecord(String operateType, String psgCkiId, PaResultDto paResultDto, List<MnjxLuggage> luggageList) {
        MnjxPsgOperateRecord psgOperateRecord = new MnjxPsgOperateRecord();
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MemoryDataCabinet activeMemoryDataCabinet = memoryData.getMemoryDataContainer().getActiveMemoryDataCabinet();
        MnjxSi mnjxSi = activeMemoryDataCabinet.getMnjxSi();
        MnjxOffice mnjxOffice = memoryData.getMnjxOffice();
        String recordContent = "";
        switch (operateType) {
            case "ACC":
                recordContent = StrUtil.format("{}{} AGT{}/{}{}", mnjxOffice.getOfficeNo().substring(0, 3),
                        mnjxSi.getSiPid(), mnjxSi.getSiNo(), DateUtils.ymd2Com(DateUtil.today()).substring(0, 5),
                        DateUtils.ymdhms2hm(DateUtil.now()));
                break;
            case "MOD":
                recordContent = StrUtil.format("{}{} AGT{}/{}{}/PSM", mnjxOffice.getOfficeNo().substring(0, 3),
                        mnjxSi.getSiPid(), mnjxSi.getSiNo(), DateUtils.ymd2Com(DateUtil.today()).substring(0, 5),
                        DateUtils.ymdhms2hm(DateUtil.now()));
                break;
            case "BAG":
                if (luggageList.size() == 1) {
                    MnjxLuggage luggage = luggageList.get(0);
                    String luggageNo = luggage.getLuggageNo();
                    BigDecimal luggageWeight = new BigDecimal("0");
                    recordContent = StrUtil.format("{}{} AGT{}/{}{}/{}/{}", mnjxOffice.getOfficeNo().substring(0, 3),
                            mnjxSi.getSiPid(), mnjxSi.getSiNo(), DateUtils.ymd2Com(DateUtil.today()).substring(0, 5),
                            DateUtils.ymdhms2hm(DateUtil.now()), luggageNo.substring(luggageNo.length() - 3),
                            luggageWeight);
                } else {
                    OptionalInt max = luggageList.stream().mapToInt(l -> Integer.parseInt(l.getLuggageNo())).max();
                    OptionalInt min = luggageList.stream().mapToInt(l -> Integer.parseInt(l.getLuggageNo())).min();
                    int sum = 0;
                    recordContent = StrUtil.format("{}{} AGT{}/{}{}/{}-{}/{}", mnjxOffice.getOfficeNo().substring(0, 3),
                            mnjxSi.getSiPid(), mnjxSi.getSiNo(), DateUtils.ymd2Com(DateUtil.today()).substring(0, 5),
                            DateUtils.ymdhms2hm(DateUtil.now()),
                            StrUtil.fill(StrUtil.toString(min.getAsInt()), '0', 10, true),
                            StrUtil.fill(StrUtil.toString(max.getAsInt()), '0', 10, true), StrUtil.toString(sum));
                }
                break;
            case "BAGTAG":
                if (luggageList.size() == 1) {
                    MnjxLuggage luggage = luggageList.get(0);
                    String luggageNo = luggage.getLuggageNo();
                    recordContent = StrUtil.format("BAGTAG/{}/{}", luggageNo, paResultDto.getDstAirport());
                } else {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append("BAGTAG");
                    for (MnjxLuggage luggage : luggageList) {
                        stringBuilder
                                .append(StrUtil.format("/{}/{} ", luggage.getLuggageNo(), paResultDto.getDstAirport()));
                    }
                    recordContent = stringBuilder.toString();
                }
                break;
            case "NPA":
                recordContent = StrUtil.format("{}{} AGT{}/{}{} {} {}", mnjxOffice.getOfficeNo().substring(0, 3),
                        mnjxSi.getSiPid(), mnjxSi.getSiNo(), DateUtils.ymd2Com(DateUtil.today()).substring(0, 5),
                        DateUtils.ymdhms2hm(DateUtil.now()), "R" + paResultDto.getInputSeatNo(), paResultDto.getSeatNo());
                break;
            default:
                break;
        }

        psgOperateRecord.setOperateRecordId(IdUtil.getSnowflake(1, 1).nextIdStr());
        psgOperateRecord.setPsgCkiId(psgCkiId);
        psgOperateRecord.setOperator(mnjxOffice.getOfficeNo());
        psgOperateRecord.setOperateType(operateType);
        psgOperateRecord.setContent(recordContent);
        psgOperateRecord.setOperateTime(DateUtil.parseDateTime(DateUtil.now()));
        return psgOperateRecord;
    }

    private List<PaResultDto> generateResultDto(PaResultDto buildPaDto, List<String> segInfoList, String paramStr, List<String> paramStrs) throws UnifiedResultException {
        List<PaResultDto> segInfoDtos = new ArrayList<>();
        List<String> paramList = Arrays.asList(paramStr.substring(1).split(","));
        List<String> dtoParams = new ArrayList<>();
        for (String param : paramList) {
            if ("O".equalsIgnoreCase(param)) {
                dtoParams.add(param);
            }
        }
        int index = 0;
        for (int i = 0; i < dtoParams.size(); i++) {
            if (segInfoList.size() < (index + 1)) {
                throw new UnifiedResultException(Constant.TRANSFER_FLIGHT_NBR);
            } else {
                String trimInfo = segInfoList.get(index).trim();
                String flightNo = trimInfo.substring(2, 8);
                String date = trimInfo.substring(9, 14);
                String dst = trimInfo.substring(trimInfo.length() - 3);
                PaResultDto segInfoDto = buildDto(buildPaDto, flightNo, dst, date);
                index += 1;
                if (Constant.ACC.equals(segInfoDto.getCkiStatus()) || Constant.GT.equals(segInfoDto.getCkiStatus())) {
                    continue;
                }
                segInfoDtos.add(segInfoDto);
            }
        }
        if (paramList.contains(Constant.PARAM_O)) {
            StringBuilder firstBf = new StringBuilder();
            boolean skip = false;
            for (String param : paramList) {
                if ("O".equalsIgnoreCase(param)) {
                    skip = true;
                } else {
                    if (skip) {
                        skip = false;
                    } else {
                        firstBf.append(",").append(param);
                    }
                }
            }
            paramStrs.add(firstBf.toString());
        } else {
            paramStrs.add(paramStr);
        }
        for (int j = 0; j < segInfoDtos.size(); j++) {
            addDtoParam(paramStrs, paramList, j);
        }
        for (int k = 0; k < segInfoList.size(); k++) {
            String segInfo = segInfoList.get(k);
            if (segInfo.trim().startsWith("O/")) {
                String trimInfo = segInfo.trim();
                String flightNo = trimInfo.substring(2, 8);
                String date = trimInfo.substring(9, 14);
                String dst = trimInfo.substring(trimInfo.length() - 3);
                PaResultDto segInfoDto = buildDto(buildPaDto, flightNo, dst, date);
                if (Constant.ACC.equals(segInfoDto.getCkiStatus()) || Constant.GT.equals(segInfoDto.getCkiStatus())) {
                    segInfoDtos.add(k, segInfoDto);
//                    addDtoParam(paramStrs, paramList, -1);
                    addDtoParam(paramStrs, paramList, 0);
                }
            }
        }
        segInfoDtos.add(0, buildPaDto);
        return segInfoDtos;
    }

    private void addDtoParam(List<String> paramStrs, List<String> paramList, int j) {
        StringBuilder paramBf = new StringBuilder();
        for (int i = 0; i < paramList.size(); i++) {
            String param = paramList.get(i);
            if (param.startsWith("UM")) {

            } else if (param.startsWith("CHD")) {

            } else if (param.startsWith("INF")) {
                paramBf.append(",").append(param);
            } else if (StrUtil.startWithAny(param, "R", "SNR", "EXPC", "SPML")) {
                int oIndex = 0;
                for (int k = i; k >= 0; k--) {
                    if ("O".equalsIgnoreCase(paramList.get(k))) {
                        oIndex += 1;
                    }
                }
                if (oIndex != 0 && (oIndex - 1) == j) {
                    paramBf.append(",").append(param);
                }
            } else if ("WCHC".equals(param) || "WCHR".equals(param) || "WCHS".equals(param) || "WCBD".equals(param)
                    || "WCBW".equals(param) || "WCMP".equals(param) || "WCOB".equals(param)) {
                paramBf.append(",").append(param);
            } else if ("STCR".equalsIgnoreCase(param)) {
                paramBf.append(",").append(param);
            } else if ("JMP".equalsIgnoreCase(param)) {
                paramBf.append(",").append(param);
            } else if (param.startsWith("MSG")) {
                paramBf.append(",").append(param);
            } else if ("BLND".equals(param) || "DEAF".equals(param)) {
                paramBf.append(",").append(param);
            } else if ("INAD".equals(param)) {
                paramBf.append(",").append(param);
            } else if ("DEPA".equalsIgnoreCase(param) || "DEPU".equalsIgnoreCase(param)) {
                paramBf.append(",").append(param);
            } else if (param.startsWith("PSM")) {
                paramBf.append(",").append(param);
            } else if (param.startsWith("PIL")) {
                paramBf.append(",").append(param);
            } else if (param.startsWith("CKIN")) {

            } else if (param.startsWith("EXBG")) {
                paramBf.append(",").append(param);
            } else if (ReUtil.isMatch(BSCT_PATTERN, param)) {
                paramBf.append(",").append(param);
            } else if (ReUtil.isMatch(PETC_PATTERN, param)) {
                paramBf.append(",").append(param);
            } else if ("XBT".equals(param)) {
                paramBf.append(",").append(param);
            }
        }
        paramStrs.add(paramBf.toString());
    }

    private PaResultDto buildDto(PaResultDto buildPaDto, String flightNo, String dst, String date) throws UnifiedResultException {
        String dateYmd = DateUtils.com2ymd(date);
        MnjxFlight flight = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getFlightNo, flightNo).one();
        MnjxTcard tcard = iMnjxTcardService.lambdaQuery().eq(MnjxTcard::getFlightId, flight.getFlightId()).one();
        MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery()
                .eq(MnjxPlanFlight::getTcardId, tcard.getTcardId())
                .eq(MnjxPlanFlight::getFlightDate, dateYmd).one();
        List<MnjxPlanSection> planSectionList = iMnjxPlanSectionService.lambdaQuery()
                .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                .orderByAsc(MnjxPlanSection::getIsLastSection).orderByAsc(MnjxPlanSection::getEstimateOffChange)
                .orderByAsc(MnjxPlanSection::getEstimateOff).list();
        // 航班状态验证
        if (Constant.CK_STATUS_OP.equalsIgnoreCase(planFlight.getCkStatus())) {
            if (DateUtil
                    .parse(planFlight.getFlightDate() + " "
                            + planSectionList.get(0).getEstimateOff(), "yyyy-MM-dd HHmm")
                    .before(DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd HHmm"), "yyyy-MM-dd HHmm"))) {
                throw new UnifiedResultException(Constant.LOCATION_TRAVEL_TIME_ZERO);
            }
        } else {
            if (Constant.CK_STATUS_CL.equalsIgnoreCase(planFlight.getCkStatus())) {
                throw new UnifiedResultException(Constant.FLIGHT_CLOSED_FOR_LDP);
            }
            if (Constant.CK_STATUS_CC.equalsIgnoreCase(planFlight.getCkStatus())) {
                throw new UnifiedResultException(Constant.FLT_CLOSED);
            }
        }
        MnjxPnrNm nm = iMnjxPnrNmService.lambdaQuery().eq(MnjxPnrNm::getPnrNmId, buildPaDto.getPnrNmId()).one();
        MnjxPnrSeg pnrSeg = iMnjxPnrSegService.lambdaQuery().eq(MnjxPnrSeg::getPnrId, nm.getPnrId())
                .eq(MnjxPnrSeg::getFlightNo, flightNo).eq(MnjxPnrSeg::getFlightDate, dateYmd)
                .eq(MnjxPnrSeg::getDst, dst).one();
        MnjxPsgCki psgCki = iMnjxPsgCkiService.lambdaQuery().eq(MnjxPsgCki::getPnrNmId, buildPaDto.getPnrNmId())
                .eq(MnjxPsgCki::getPnrSegNo, pnrSeg.getPnrSegNo()).one();
        if (Constant.GT.equalsIgnoreCase(psgCki.getCkiStatus())) {
            throw new UnifiedResultException(Constant.PAX_ALREADY_BOARDED);
        }
        PaResultDto paResultDto = new PaResultDto();
        // 构建航班信息
        paResultDto.setFlightNo(pnrSeg.getFlightNo());
        paResultDto.setFlightDate(pnrSeg.getFlightDate());
        paResultDto.setOrgAirport(pnrSeg.getOrg());
        paResultDto.setAirlineCode(pnrSeg.getFlightNo().substring(0, 2));
        // 构建旅客信息数据
        paResultDto.setQueryName(nm.getQueryName());
        paResultDto.setPnrNmId(nm.getPnrNmId());
        paResultDto.setCnin(StrUtil.isNotEmpty(nm.getIsCnin()));
        paResultDto.setName(nm.getName());
        paResultDto.setDstAirport(pnrSeg.getDst());
        paResultDto.setSellCabin(psgCki.getSellCabin());
        paResultDto.setCabinClass(psgCki.getCabinClass());
        paResultDto.setCkiStatus(psgCki.getCkiStatus());
        return paResultDto;
    }

    private PaResultDto changeReturnStr(PaResultDto parseAndDeal, String replaceCmd) {
        StringBuilder firstBuffer = new StringBuilder(replaceCmd);
        if (replaceCmd.contains(Constant.HBPU_CNIN_TYPE)) {
            String nameStr = replaceCmd.substring(replaceCmd.indexOf(",CNIN/") + 6);
            String name;
            if (nameStr.contains(Constant.COMMA)) {
                name = nameStr.substring(0, nameStr.indexOf(","));
            } else {
                name = nameStr;
            }
            String queryName = PinyinUtils.getPinYin(name, false).toUpperCase();
            firstBuffer.insert(firstBuffer.indexOf(",CNIN/") + 6, queryName);
        }
        firstBuffer.insert(5, " ");
        parseAndDeal.setFirstCode(firstBuffer.toString());
        if (parseAndDeal.getFlightDate().contains(StrUtils.DASHED)) {
            parseAndDeal.setFlightDate(DateUtils.ymd2Com(parseAndDeal.getFlightDate()).substring(0, 5));
        }
        return parseAndDeal;
    }

    private PaResultDto prDtoCheck(Integer indexNum, String type, PdInfoDto paramDto, List<PdNmDto> pdDtos, List<String> segInfoList) throws UnifiedResultException {
        PdInfoDto pdInfoDto = paramDto;
        if (ObjectUtil.isNull(pdInfoDto)) {
            return null;
        }
        if (!pdInfoDto.getDate().contains(Constant.CROSSBAR)) {
            pdInfoDto.setDate(DateUtils.com2ymd(pdInfoDto.getDate()));
        }
        // 旅客序号验证
        if (CollUtil.isEmpty(pdInfoDto.getNms())) {
            return null;
        }
        PdNmDto pdNmDto = null;
        for (PdNmDto tempDto : pdInfoDto.getNms()) {
            if (indexNum.equals(Integer.parseInt(tempDto.getPsgNum()))) {
                pdNmDto = tempDto;
                break;
            }
        }
        if (ObjectUtil.isNull(pdNmDto)) {
            throw new UnifiedResultException(Constant.NUMBER_ERROR);
        }
        MnjxFlight flight;
        if (StrUtil.isNotBlank(pdNmDto.getCarrierFlight())) {
            flight = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getFlightNo, pdNmDto.getCarrierFlight()).one();
        } else {
            flight = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getFlightNo, pdNmDto.getFlightNo()).one();
        }
        MnjxTcard tcard = iMnjxTcardService.lambdaQuery().eq(MnjxTcard::getFlightId, flight.getFlightId()).one();
        MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery()
                .eq(MnjxPlanFlight::getTcardId, tcard.getTcardId())
                .eq(MnjxPlanFlight::getFlightDate, pdInfoDto.getDate()).one();
        List<MnjxPlanSection> planSectionList = iMnjxPlanSectionService.lambdaQuery()
                .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                .orderByAsc(MnjxPlanSection::getIsLastSection).orderByAsc(MnjxPlanSection::getEstimateOffChange)
                .orderByAsc(MnjxPlanSection::getEstimateOff).list();
        // 航班状态验证
        if (Constant.CK_STATUS_OP.equalsIgnoreCase(planFlight.getCkStatus())) {
            if (DateUtil
                    .parse(planFlight.getFlightDate() + " "
                            + planSectionList.get(0).getEstimateOff(), "yyyy-MM-dd HHmm")
                    .before(DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd HHmm"), "yyyy-MM-dd HHmm"))) {
                throw new UnifiedResultException(Constant.LOCATION_TRAVEL_TIME_ZERO);
            }
        } else {
            if (Constant.CK_STATUS_CL.equalsIgnoreCase(planFlight.getCkStatus())) {
                throw new UnifiedResultException(Constant.FLIGHT_CLOSED_FOR_LDP);
            }
            if (Constant.CK_STATUS_CC.equalsIgnoreCase(planFlight.getCkStatus())) {
                throw new UnifiedResultException(Constant.FLT_CLOSED);
            }
        }
        pdDtos.add(pdNmDto);
        // 如果当前旅客没有ET标识
        MnjxPnrNm nm = iMnjxPnrNmService.lambdaQuery().eq(MnjxPnrNm::getPnrNmId, pdNmDto.getPnrNmId()).one();
        MnjxPnrSeg pnrSeg = iMnjxPnrSegService.lambdaQuery().eq(MnjxPnrSeg::getPnrId, nm.getPnrId())
                .eq(MnjxPnrSeg::getFlightNo, pdInfoDto.getFlightNo()).eq(MnjxPnrSeg::getOrg, pdNmDto.getOrg())
                .eq(MnjxPnrSeg::getDst, pdNmDto.getDst()).one();
        MnjxPsgCki psgCki = iMnjxPsgCkiService.lambdaQuery().eq(MnjxPsgCki::getPnrNmId, pdNmDto.getPnrNmId())
                .eq(MnjxPsgCki::getPnrSegNo, pnrSeg.getPnrSegNo()).one();
        if (Constant.GT.equalsIgnoreCase(psgCki.getCkiStatus())) {
            throw new UnifiedResultException(Constant.PAX_ALREADY_BOARDED);
        }
        // 是否有#号验证
        if (StrUtil.isNotBlank(type)) {
            if (Constant.ACC.equalsIgnoreCase(psgCki.getCkiStatus())) {
                throw new UnifiedResultException(Constant.PAX_ALREADY_CHICKED_IN);
            }

        } else {
            if (Constant.NACC.equalsIgnoreCase(psgCki.getCkiStatus())
                    || Constant.DL.equalsIgnoreCase(psgCki.getCkiStatus())) {
                throw new UnifiedResultException(Constant.PAX_STATUS_CONFLICT);
            }
            if (Constant.SB.equalsIgnoreCase(psgCki.getCkiStatus())) {
                throw new UnifiedResultException(Constant.PAX_STATUS_CONFLICT);
            }
        }
        pdNmDto.setCkiStatus(psgCki.getCkiStatus());
        pdNmDto.setSellCabin(psgCki.getSellCabin());
        pdNmDto.setCabinClass(psgCki.getCabinClass());
        PaResultDto returnDto = buildPrDtp(pdInfoDto, pdNmDto);
        if (segInfoList != null) {
            iPrService.getInterline(nm.getPnrId(), pnrSeg.getPnrSegNo().toString(), nm.getPnrNmId(), returnDto.getOrgAirport(), returnDto.getDstAirport(), segInfoList, null);
        }
        return returnDto;
    }

    private PaResultDto pdDtoCheck(Integer indexNum, String type, List<PdNmDto> pdDtos, PdInfoDto paramDto, List<String> segInfoList) throws UnifiedResultException {
        PdInfoDto pdInfoDto = paramDto;
        if (ObjectUtil.isNull(pdInfoDto)) {
            return null;
        }
        if (!pdInfoDto.getDate().contains(Constant.CROSSBAR)) {
            pdInfoDto.setDate(DateUtils.com2ymd(pdInfoDto.getDate()));
        }
        PdNmDto pdNmDto = null;
        for (PdNmDto tempDto : pdInfoDto.getNms()) {
            if (indexNum.equals(Integer.parseInt(tempDto.getPsgNum()))) {
                pdNmDto = tempDto;
                break;
            }
        }
        if (ObjectUtil.isNull(pdNmDto)) {
            return null;
        }
        MnjxFlight flight;
        if (StrUtil.isNotBlank(pdNmDto.getCarrierFlight())) {
            flight = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getFlightNo, pdNmDto.getCarrierFlight()).one();
        } else {
            flight = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getFlightNo, pdNmDto.getFlightNo()).one();
        }
        MnjxTcard tcard = iMnjxTcardService.lambdaQuery().eq(MnjxTcard::getFlightId, flight.getFlightId()).one();
        MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery()
                .eq(MnjxPlanFlight::getTcardId, tcard.getTcardId())
                .eq(MnjxPlanFlight::getFlightDate, pdInfoDto.getDate()).one();
        List<MnjxPlanSection> planSectionList = iMnjxPlanSectionService.lambdaQuery()
                .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                .orderByAsc(MnjxPlanSection::getIsLastSection)
                .orderByAsc(MnjxPlanSection::getEstimateOffChange)
                .orderByAsc(MnjxPlanSection::getEstimateOff)
                .list();
        // 航班状态验证
        if (Constant.CK_STATUS_OP.equalsIgnoreCase(planFlight.getCkStatus())) {
            if (DateUtil
                    .parse(planFlight.getFlightDate() + " " + planSectionList.get(0).getEstimateOff(), "yyyy-MM-dd HHmm")
                    .before(DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd HHmm"), "yyyy-MM-dd HHmm"))) {
                throw new UnifiedResultException(Constant.LOCATION_TRAVEL_TIME_ZERO);
            }
        } else {
            if (Constant.CK_STATUS_CL.equalsIgnoreCase(planFlight.getCkStatus())) {
                throw new UnifiedResultException(Constant.FLIGHT_CLOSED_FOR_LDP);
            }
            if (Constant.CK_STATUS_CC.equalsIgnoreCase(planFlight.getCkStatus())) {
                throw new UnifiedResultException(Constant.FLT_CLOSED);
            }
        }
        pdDtos.add(pdNmDto);
        MnjxPnrNm nm = iMnjxPnrNmService.lambdaQuery().eq(MnjxPnrNm::getPnrNmId, pdNmDto.getPnrNmId()).one();
        MnjxPnrSeg pnrSeg = iMnjxPnrSegService.lambdaQuery().eq(MnjxPnrSeg::getPnrId, nm.getPnrId())
                .eq(MnjxPnrSeg::getFlightNo, pdInfoDto.getFlightNo()).eq(MnjxPnrSeg::getOrg, pdNmDto.getOrg())
                .eq(MnjxPnrSeg::getDst, pdNmDto.getDst()).one();
        MnjxPsgCki psgCki = iMnjxPsgCkiService.lambdaQuery().eq(MnjxPsgCki::getPnrNmId, pdNmDto.getPnrNmId())
                .eq(MnjxPsgCki::getPnrSegNo, pnrSeg.getPnrSegNo()).one();
        if (Constant.GT.equalsIgnoreCase(psgCki.getCkiStatus())) {
            throw new UnifiedResultException(Constant.PAX_ALREADY_BOARDED);
        }
        // 是否有#号验证
        if (StrUtil.isNotBlank(type)) {
            if (Constant.ACC.equalsIgnoreCase(psgCki.getCkiStatus())) {
                throw new UnifiedResultException(Constant.PAX_ALREADY_CHICKED_IN);
            }
        } else {
            if (Constant.NACC.equalsIgnoreCase(psgCki.getCkiStatus())) {
                throw new UnifiedResultException(Constant.PAX_STATUS_CONFLICT);
            }
            if (Constant.SB.equalsIgnoreCase(psgCki.getCkiStatus())) {
                throw new UnifiedResultException(Constant.PAX_STATUS_CONFLICT);
            }
        }
        pdNmDto.setCkiStatus(psgCki.getCkiStatus());
        pdNmDto.setSellCabin(psgCki.getSellCabin());
        pdNmDto.setCabinClass(psgCki.getCabinClass());
        PaResultDto buildPaDto = buildPrDtp(pdInfoDto, pdNmDto);
        if (segInfoList != null) {
            iPrService.getInterline(nm.getPnrId(), pnrSeg.getPnrSegNo().toString(), nm.getPnrNmId(), buildPaDto.getOrgAirport(), buildPaDto.getDstAirport(), segInfoList, null);
        }
        return buildPaDto;
    }

    private PaResultDto buildPrDtp(PdInfoDto pdInfoDto, PdNmDto pdNmDto) {
        PaResultDto paResultDto = new PaResultDto();
        // 构建航班信息
        if (StrUtil.isNotBlank(pdNmDto.getCarrierFlight())) {
            paResultDto.setFlightNo(pdNmDto.getCarrierFlight());
            paResultDto.setShareFlightNo(pdNmDto.getShareFlight());
        } else {
            paResultDto.setFlightNo(pdNmDto.getFlightNo());
        }
        paResultDto.setFlightDate(pdInfoDto.getDate());
        paResultDto.setOrgAirport(pdInfoDto.getCity().substring(0, 3));
        paResultDto.setBdt(pdInfoDto.getBoarding());
        paResultDto.setSd(pdInfoDto.getEstimateOff());
        paResultDto.setEd(pdInfoDto.getActualOff());
        paResultDto.setSa("");
        paResultDto.setFt("");
        paResultDto.setAirlineCode(pdInfoDto.getFlightNo().substring(0, 2));
        // 构建旅客信息数据
        paResultDto.setQueryName(pdNmDto.getQueryName());
        paResultDto.setPnrNmId(pdNmDto.getPnrNmId());
        paResultDto.setCnin(StrUtil.isNotEmpty(pdNmDto.getIsCnin()));
        paResultDto.setName(pdNmDto.getName());
        paResultDto.setOInterlink(pdNmDto.getOInterlink());
        paResultDto.setDstAirport(pdNmDto.getDst());
        paResultDto.setSellCabin(pdNmDto.getSellCabin());
        paResultDto.setCabinClass(pdNmDto.getCabinClass());
        paResultDto.setCkiStatus(pdNmDto.getCkiStatus());
        return paResultDto;
    }

    private void parseMultiSeats(String multiSeatNo, List<PaResultDto> thisFlightNoPaResultDtos) throws UnifiedResultException {
        // 多个旅客分配座位，座位格式为13AB类似的，进行处理为13A和13B
        List<String> seatGroup = ReUtil.getAllGroups(MULTI_SEAT_PATTER, multiSeatNo);
        List<String> multiSeatNoList = new ArrayList<>();
        String seatRow = seatGroup.get(5);
        String startLine = seatGroup.get(2);
        String endLine = seatGroup.get(4);
        String inputSeatNo = multiSeatNo;
        PaResultDto firstPaResultDto = thisFlightNoPaResultDtos.get(0);
        List<MnjxPlanSection> planSections = iPaService.getMatchPlanSections(firstPaResultDto);
        // 获取所有的计划航节ID
        List<String> allPlanSectionIdList = planSections.stream()
                .map(MnjxPlanSection::getPlanSectionId)
                .collect(Collectors.toList());
        // 查询当前旅客舱等对应的所有开舱数据
        List<MnjxOpenCabin> openCabinList = iMnjxOpenCabinService.lambdaQuery()
                .in(MnjxOpenCabin::getPlanSectionId, allPlanSectionIdList)
                .eq(MnjxOpenCabin::getCabinClass, firstPaResultDto.getCabinClass())
                .list();
        List<MnjxSeat> seatList = iMnjxSeatService.lambdaQuery()
                .in(MnjxSeat::getOpenCabinId, openCabinList.stream().map(MnjxOpenCabin::getOpenCabinId).collect(Collectors.toList()))
                .in(MnjxSeat::getSeatStatus, "*", "Q", "B", "N", "U", "H", "A", "L", "/", "I", "S", "+", "$")
                .list();
        // 带有字母行
        if (StrUtil.isNotEmpty(seatRow)) {
            // 有 - 符号的 13A-C 13-14A-C
            if (seatRow.contains(StrUtil.DASHED)) {
                // 开始-结束列号字母
                String startRow = seatGroup.get(6);
                String endRow = seatGroup.get(8);
                int i1 = startRow.charAt(0);
                int i2 = endRow.charAt(0);
                // 重新排序行号
                if (i1 > i2) {
                    String tmp = startRow;
                    startRow = endRow;
                    endRow = tmp;
                } else if (i1 == i2) {
                    throw new UnifiedResultException(Constant.FORMAT);
                }
                List<String> inputRowList = new ArrayList<>();
                // 排序取出可用座位号的列
                List<String> rowList = seatList.stream()
                        .map(m -> m.getSeatNo().substring(m.getSeatNo().length() - 1))
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());
                for (String s : rowList) {
                    // 取出符合输入座位参数的所有座位号的列
                    if (s.equals(startRow) || s.equals(endRow) || (CollUtil.isNotEmpty(inputRowList) && !inputRowList.contains(endRow))) {
                        inputRowList.add(s);
                    }
                }
                // 拼接输入的座位号参数
                if (StrUtil.isNotEmpty(endLine)) {
                    inputSeatNo = StrUtil.format("{}-{}{}-{}", startLine, endLine, startRow, endRow);
                } else {
                    inputSeatNo = StrUtil.format("{}{}-{}", startLine, startRow, endRow);
                }
                // 拼接存储可用座位号列表
                for (String s : inputRowList) {
                    if (StrUtil.isNotEmpty(endLine)) {
                        for (int i = Integer.parseInt(startLine); i <= Integer.parseInt(endLine); i++) {
                            multiSeatNoList.add(StrUtil.format("{}{}", i, s));
                        }
                    } else {
                        multiSeatNoList.add(StrUtil.format("{}{}", startLine, s));
                    }
                }
            }
            // 13ABC 13L
            else {
                // 列号，只允许一个字母
                String[] split = seatRow.split(StrUtil.EMPTY);
                if (split.length > 1) {
                    throw new UnifiedResultException(Constant.FORMAT);
                } else {
                    inputSeatNo = StrUtil.format("{}{}", seatGroup.get(1), split[0]);
                }
                // 筛选所有可用座位号列表
                multiSeatNoList = seatList.stream()
                        .filter(s -> s.getSeatRow().equals(multiSeatNo.substring(0, multiSeatNo.length() - 1)) && s.getSeatColumn().hashCode() <= multiSeatNo.substring(multiSeatNo.length() - 1).hashCode())
                        .map(MnjxSeat::getSeatNo)
                        .sorted()
                        .collect(Collectors.toList());
                // 如果筛选的座位号不包含输入的座位号，即输入的座位号状态不可用，报错
                if (!multiSeatNoList.contains(inputSeatNo)) {
                    throw new UnifiedResultException(Constant.SEATING_CONFLICT);
                }
                // 查询符合输入行号的座位号（包含不可用状态）
                List<MnjxSeat> rowSeatList = iMnjxSeatService.lambdaQuery()
                        .in(MnjxSeat::getOpenCabinId, openCabinList.stream().map(MnjxOpenCabin::getOpenCabinId).collect(Collectors.toList()))
                        .eq(MnjxSeat::getSeatRow, multiSeatNo.substring(0, multiSeatNo.length() - 1))
                        .orderByAsc(MnjxSeat::getSeatNo)
                        .list();
                // 输入行的座位数不足
                if (rowSeatList.size() < thisFlightNoPaResultDtos.size()) {
                    throw new UnifiedResultException(Constant.SEATING_CONFLICT);
                } else {
                    Optional<MnjxSeat> first = rowSeatList.stream()
                            .filter(s -> multiSeatNo.equals(s.getSeatNo()))
                            .findFirst();
                    // 获取输入座位号所在列表的下标
                    int index = first.map(rowSeatList::indexOf).get();
                    // 记录找到可用座位数
                    int findSeatsNum = 0;
                    // 清空记录的可用座位号列表
                    multiSeatNoList.clear();
                    // 向右查找
                    f1:for (int i = index; i < rowSeatList.size(); i++) {
                        // 记录当前输入座位号，数量+1
                        if (i == index) {
                            findSeatsNum++;
                            multiSeatNoList.add(rowSeatList.get(i).getSeatNo());
                            continue;
                        }
                        // 查找右边可用座位并记录数量
                        if (StrUtil.equalsAny(rowSeatList.get(i).getSeatStatus(), "*", "Q", "B", "N", "U", "H", "A", "L", "/", "I", "S")) {
                            findSeatsNum++;
                            multiSeatNoList.add(rowSeatList.get(i).getSeatNo());
                            // 如果数量足够匹配旅客人数，跳出
                            if (findSeatsNum == thisFlightNoPaResultDtos.size()) {
                                break;
                            }
                            // 循环到最右的座位后数量不足，不跳过循环，向左边查找
                            if (i < rowSeatList.size() - 1) {
                                continue;
                            }
                        }
                        // 如果向右查找到不可用状态的座位或查找到最右边的座位时数量不足，开始向左查找
                        if (findSeatsNum < thisFlightNoPaResultDtos.size()) {
                            // 向右查找直到座位状态不可用，向左查找
                            for (int j = index - 1; j >= 0; j--) {
                                if (StrUtil.equalsAny(rowSeatList.get(j).getSeatStatus(), "*", "Q", "B", "N", "U", "H", "A", "L", "/", "I", "S")) {
                                    findSeatsNum++;
                                    multiSeatNoList.add(rowSeatList.get(j).getSeatNo());
                                    if (findSeatsNum == thisFlightNoPaResultDtos.size()) {
                                        break f1;
                                    }
                                } else {
                                    break;
                                }
                            }
                            // 向左查找结束座位数依然不足，报错
                            if (findSeatsNum < thisFlightNoPaResultDtos.size()) {
                                throw new UnifiedResultException(Constant.SEATING_CONFLICT);
                            }
                        }
                    }
                }
            }
        }
        // 无字母行
        // 20 20-21
        else {
            if (multiSeatNo.contains(StrUtil.DASHED)) {
                String[] rowSplit = multiSeatNo.split(StrUtil.DASHED);
                List<MnjxSeat> rowSeatList = seatList.stream()
                        .filter(s -> Arrays.stream(rowSplit).anyMatch(r -> s.getSeatRow().equals(r)))
                        .sorted(Comparator.comparing(MnjxSeat::getSeatNo))
                        .collect(Collectors.toList());
                multiSeatNoList.addAll(rowSeatList.stream().map(MnjxSeat::getSeatNo).collect(Collectors.toList()));
            } else {
                List<MnjxSeat> rowSeatList = seatList.stream()
                        .filter(s -> s.getSeatRow().equals(multiSeatNo))
                        .sorted(Comparator.comparing(MnjxSeat::getSeatNo))
                        .collect(Collectors.toList());
                multiSeatNoList.addAll(rowSeatList.stream().map(MnjxSeat::getSeatNo).collect(Collectors.toList()));
            }
        }
        // 解析输入获取的座位列表分配给该航班的旅客
        for (int i = 0; i < multiSeatNoList.size(); i++) {
            String psgSeatNo = multiSeatNoList.get(i);
            if (i < thisFlightNoPaResultDtos.size()) {
                thisFlightNoPaResultDtos.get(i).setSeatNo(psgSeatNo);
                thisFlightNoPaResultDtos.get(i).setInputSeatNo(inputSeatNo);
                thisFlightNoPaResultDtos.get(i).setNormalSeat(true);
            }
        }
        // 对旅客循环一次，如果分配的座位数不够，报错
        for (int i = 0; i < thisFlightNoPaResultDtos.size(); i++) {
            PaResultDto dto = thisFlightNoPaResultDtos.get(i);
            if (i < multiSeatNoList.size()) {
                String psgSeatNo = multiSeatNoList.get(i);
                dto.setSeatNo(psgSeatNo);
            } else {
                throw new UnifiedResultException(Constant.SEATING_CONFLICT);
            }
            dto.setInputSeatNo(inputSeatNo);
            dto.setNormalSeat(true);
        }
        for (PaResultDto paResultDto : thisFlightNoPaResultDtos) {
            paResultDto.setPlanSectionList(planSections);
            paResultDto.setCabinClassOpenCabinList(openCabinList);
            iPaService.setCorrectSeats(openCabinList, "", paResultDto.getSeatNo(), planSections.size(), paResultDto, new HashMap<>(1024));
        }
    }
}
