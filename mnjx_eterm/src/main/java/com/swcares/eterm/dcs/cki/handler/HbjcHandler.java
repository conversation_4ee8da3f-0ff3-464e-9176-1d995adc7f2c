package com.swcares.eterm.dcs.cki.handler;

import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.DateUtils;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.HbpaCmdDto;
import com.swcares.eterm.dcs.cki.obj.dto.PaResultDto;
import com.swcares.eterm.dcs.cki.service.IHbjcService;

import javax.annotation.Resource;
import java.util.List;

/**
 * HBJC指令处理
 * // HBJC:CA1964/22JUN21YCTU/11,R11L
 * // HBJC:CA6655/28OCT21YCTUPEK/2
 * // HBJC:CA3221/27OCT21YCTUSHA/1
 * // HBJC:CA7660/27OCT21YCTU/2
 *
 * <AUTHOR>
 * @date 2021-6-22
 */
@OperateType(action = "HBJC", template = "/dcs/cki/hbjc.jf")
public class HbjcHandler implements Handler {

    @Resource
    private IHbjcService iHbjcService;


    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        //目前根据白屏开发只支持 HBJC:CA6655/28OCT21YCTUPEK/2  这种格式，等需求完善在补充
        List<HbpaCmdDto> hbpaCmdDtoList = iHbjcService.parseHbjc(cmd);
        List<PaResultDto> paResultDtos = iHbjcService.handle(MemoryDataUtils.getMemoryData(), hbpaCmdDtoList);

        iHbjcService.print(paResultDtos);

        PaResultDto paResultDto = paResultDtos.get(0);
        String flightDate = paResultDto.getFlightDate().contains("-") ? DateUtils.ymd2Com(paResultDto.getFlightDate()) : paResultDto.getFlightDate();
        String returnCmd = cmd.split(":")[1];
        if (paResultDto.isCap()) {
            String[] split = returnCmd.split("/");
            returnCmd = StrUtil.format("{}/{}/{}", paResultDto.getFlightNo(), flightDate.substring(0, 5), split[split.length - 1]);
        }
        unifiedResult.setArgs(new Object[]{StrUtil.format("{}:{}", cmd.split(":")[0], returnCmd)});
        unifiedResult.setResults(paResultDtos.toArray());
        return unifiedResult;
    }
}
