package com.swcares.eterm.dcs.cki.mapper;

import com.swcares.entity.MnjxOpenCabin;
import com.swcares.entity.MnjxPsgSeat;
import com.swcares.entity.MnjxSeat;
import com.swcares.eterm.dcs.cki.obj.dto.RaDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * 2022/9/14
 */
public interface RaMapper {
    /***
     * 查询航班舱等的座位状态
     * @param raDto raDto
     * @return 查询航班舱等的座位状态
     */
    List<MnjxSeat> retrieveSeatStatus(@Param("raDto") RaDto raDto);

    /***
     * 查询航班开舱舱等信息
     * @param raDto raDto
     * @return 查询航班开舱舱等信息
     */
    List<MnjxOpenCabin> retrieveCabin(@Param("raDto") RaDto raDto);

    /***
     * 查询该航班旅客值机占座信息记录
     * @param raDto raDto
     * @param seatNos  座位号
     * @return 查询该航班旅客值机占座信息记录
     */
    List<MnjxPsgSeat> retrievePsgSeat(@Param("raDto") RaDto raDto, @Param("seatNos") List<String> seatNos);
}
