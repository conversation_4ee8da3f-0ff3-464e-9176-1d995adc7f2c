package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataFt;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.CollUtils;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.eterm.dcs.cki.mapper.SyMapper;
import com.swcares.eterm.dcs.cki.obj.dto.SyDto;
import com.swcares.eterm.dcs.cki.obj.dto.SyResultDto;
import com.swcares.eterm.dcs.cki.obj.dto.SySegResultDto;
import com.swcares.eterm.dcs.cki.service.ISyService;
import com.swcares.service.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SyServiceImpl implements ISyService {

    /**
     * SY:航班号/日期/始发城市/选项
     */
    private static final Pattern SY_PATTERN = Pattern.compile("^(\\w{5,6})" +
            "(/((\\d{2}[A-Z]{3}(\\d{2})?)|([+-.])))?" +
            "(/([A-Z]{3}))?" +
            "(/([A-Z]))?$");

    private static final Pattern SY_DEFAULT_PATTERN = Pattern.compile("[BESTZ]");

    @Resource
    private SyMapper syMapper;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IMnjxTcardSectionService iMnjxTcardSectionService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;

    @Resource
    private IMnjxPlaneService iMnjxPlaneService;

    @Resource
    private IMnjxPlaneModelService iMnjxPlaneModelService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxCndService iMnjxCndService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;

    @Resource
    private IMnjxLuggageService iMnjxLuggageService;

    @Resource
    private IMnjxLuggageCarryonService iMnjxLuggageCarryonService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxSeatModelService iMnjxSeatModelService;

    @Resource
    private IMnjxSeatService iMnjxSeatService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Override
    public SyDto parseSy(String cmd) throws UnifiedResultException {
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        SyDto syDto = new SyDto();
        String[] split = cmd.split(StrUtil.COLON);
        // 如果长度为1，输入的是SY 或 SY:，需要有默认航班
        if (split.length == 1) {
            MemoryDataFt memoryDataFt = memoryData.getMemoryDataFt();
            if (StrUtil.isEmpty(memoryDataFt.getFlightDate()) && StrUtil.isEmpty(memoryDataFt.getFlightNo())) {
                throw new UnifiedResultException(Constant.NO_DEFAULT_FLIGHT);
            }
            syDto.setFlightDate(memoryDataFt.getFlightDate());
            syDto.setFlightNo(memoryDataFt.getFlightNo());
            syDto.setOrgAirport(memoryData.getMnjxOffice().getOfficeNo().substring(0, 3));
        } else if (split.length == Constant.TWO) {
            if (ReUtil.isMatch(SY_PATTERN, split[1])) {
                List<String> allGroups = ReUtil.getAllGroups(SY_PATTERN, split[1]);
                String flightNo = allGroups.get(1);
                String flightDate = allGroups.get(3);
                String orgCity = allGroups.get(8);
                String option = allGroups.get(10);
                // 选项只有 Z S E T B
                if (StrUtil.isNotEmpty(option) && !StrUtil.equalsAny(option, Constant.SY_Z, Constant.SY_T, Constant.SY_E, Constant.SY_S, Constant.SY_B, Constant.SY_A)) {
                    throw new UnifiedResultException(Constant.OPTION);
                }
                String today = DateUtil.today();
                // 日期转换
                if (StrUtil.isNotEmpty(flightDate)) {
                    flightDate = DateUtils.com2ymd(flightDate);
                }
                // 为空默认当天
                else {
                    flightDate = today;
                }
                // 如果日期小于当前日期前1天，或日期大于当前日期后2天，则报错：FLT NBR
                int dayDiff = this.getDayDiff(flightDate);
                if (dayDiff < -1 || dayDiff > Constant.TWO) {
                    throw new UnifiedResultException(Constant.FLT_NBR);
                }
                Date now = new Date();
                DateTime front = DateUtil.offsetDay(now, -1);
                DateTime back = DateUtil.offsetDay(now, 2);
                // 把时间处理成yyyy-MM-dd格式
                List<String> dayList = DateUtil.rangeToList(front, back, DateField.DAY_OF_YEAR).stream()
                        .map(DateTime::toDateStr)
                        .collect(Collectors.toList());
                if (!dayList.contains(flightDate)) {
                    throw new UnifiedResultException(Constant.FLT_NBR);
                }
                // 航站如果为空，默认当前office前三位
                if (StrUtil.isEmpty(orgCity)) {
                    orgCity = memoryData.getMnjxOffice().getOfficeNo().substring(0, 3);
                }
                syDto.setFlightDate(flightDate);
                syDto.setFlightNo(flightNo);
                syDto.setOrgAirport(orgCity);
                syDto.setOption(option);
            } else if (ReUtil.isMatch(SY_DEFAULT_PATTERN, split[1])) {
                MemoryDataFt memoryDataFt = memoryData.getMemoryDataFt();
                if (StrUtil.isEmpty(memoryDataFt.getFlightDate()) && StrUtil.isEmpty(memoryDataFt.getFlightNo())) {
                    throw new UnifiedResultException(Constant.NO_DEFAULT_FLIGHT);
                }
                syDto.setFlightDate(memoryDataFt.getFlightDate());
                syDto.setFlightNo(memoryDataFt.getFlightNo());
                syDto.setOrgAirport(memoryData.getMnjxOffice().getOfficeNo().substring(0, 3));
                syDto.setOption(split[1]);
            } else {
                throw new UnifiedResultException(Constant.FORMAT);
            }
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        return syDto;
    }

    @Override
    public SyResultDto handle(SyDto syDto, UnifiedResult unifiedResult) throws UnifiedResultException {
        SyResultDto syResultDto = new SyResultDto();
        List<SySegResultDto> sySegResultDtos = new ArrayList<>();
        SySegResultDto totalResult = new SySegResultDto();

        String flightDate = syDto.getFlightDate();
        String flightNo = syDto.getFlightNo();
        String option = syDto.getOption();
        BeanUtil.copyProperties(syDto, syResultDto);
        syResultDto.setFlightDate(DateUtils.ymd2Com(flightDate));

        // 获取航班
        MnjxFlight mnjxFlight = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightNo, flightNo)
                .one();
        if (ObjectUtil.isEmpty(mnjxFlight)) {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        String flightId = mnjxFlight.getFlightId();
        // 有承运航班号，说明当前输入的是共享航班号
        if (StrUtil.isNotEmpty(mnjxFlight.getCarrierFlight())) {
            if (Constant.STR_ZERO.equals(mnjxFlight.getShareState())) {
                throw new UnifiedResultException(Constant.FLT_NBR);
            } else {
                syResultDto.getShareFlightNoList().add(mnjxFlight.getFlightNo());
                flightId = iMnjxFlightService.lambdaQuery()
                        .eq(MnjxFlight::getFlightNo, mnjxFlight.getCarrierFlight())
                        .one()
                        .getFlightId();
            }
        }
        // 当前输入的是承运航班号，查询是否有共享航班
        else {
            List<MnjxFlight> shareFlightList = iMnjxFlightService.lambdaQuery()
                    .eq(MnjxFlight::getCarrierFlight, flightNo)
                    .eq(MnjxFlight::getShareState, Constant.STR_ONE)
                    .list();
            if (CollUtil.isNotEmpty(shareFlightList)) {
                syResultDto.getShareFlightNoList().addAll(shareFlightList.stream().map(MnjxFlight::getFlightNo).collect(Collectors.toList()));
            }
        }
        // 获取tcard
        MnjxTcard mnjxTcard = iMnjxTcardService.lambdaQuery()
                .eq(MnjxTcard::getFlightId, flightId)
                .one();
        String tcardId = mnjxTcard.getTcardId();
        // 获取tcard section，按航节序号升序排序
        List<MnjxTcardSection> tcardSectionList = iMnjxTcardSectionService.lambdaQuery()
                .eq(MnjxTcardSection::getTcardId, tcardId)
                .orderByAsc(MnjxTcardSection::getSectionNo)
                .list();
        // 验证机场存在
        MnjxAirport orgAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, syDto.getOrgAirport())
                .one();
        if (ObjectUtil.isEmpty(orgAirport)) {
            throw new UnifiedResultException(Constant.CITY);
        }
        // 获取非尾航节机场ID
        List<String> airportIdList = tcardSectionList.stream()
                .map(MnjxTcardSection::getAirportId)
                .collect(Collectors.toList());
        String orgAirportId = orgAirport.getAirportId();
        if (!airportIdList.contains(orgAirportId)) {
            orgAirportId = airportIdList.get(0);
        }
        // 计划航班
        MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery()
                .eq(MnjxPlanFlight::getTcardId, tcardId)
                .eq(MnjxPlanFlight::getFlightDate, flightDate)
                .one();
        // 航班计划是否存在
        if (ObjectUtil.isEmpty(planFlight)) {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        // cnd
        MnjxCnd mnjxCnd = iMnjxCndService.lambdaQuery()
                .eq(MnjxCnd::getCndNo, planFlight.getCndNo())
                .one();
        syResultDto.setFlightStatus(planFlight.getCkStatus());
        // 航班计划是否初始化
        if (StrUtil.isEmpty(planFlight.getIsFlightInitial())) {
            throw new UnifiedResultException(Constant.NEED_INITIALIZE);
        }
        // 计划航节
        List<MnjxPlanSection> planSectionList = iMnjxPlanSectionService.lambdaQuery()
                .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                .orderByAsc(MnjxPlanSection::getPlanFlightId)
                .orderByAsc(MnjxPlanSection::getIsLastSection)
                .orderByAsc(MnjxPlanSection::getEstimateOffChange)
                .orderByAsc(MnjxPlanSection::getEstimateOff)
                .list();
        MnjxPlanSection firstPlanSection = planSectionList.get(0);

        // SY只显示输入航站为起始航站的航段信息，如CTU-PEK-SHA，输入的CTU，只会返回CTU-PEK和CTU-SHA
        // 如果是输入的PEK，只返回PEK-SHA，和一个TRANSIT
        String finalOrgAirportId = orgAirportId;
        List<MnjxPlanSection> orgAirportPlanSectionList = planSectionList.stream()
                .filter(p -> p.getDepAptId().equals(finalOrgAirportId))
                .collect(Collectors.toList());

        // 为空时表示输入的航站是尾航站
        if (CollUtil.isEmpty(orgAirportPlanSectionList)) {
            syResultDto.setLastAirport(true);
            MnjxAirport airport = iMnjxAirportService.getById(tcardSectionList.get(tcardSectionList.size() - 1).getAirportId());
            syResultDto.setOrgAirport(airport.getAirportCode());
            return syResultDto;
        }

        // SY回显的机型版本号飞机号使用输入城市为起始城市的那个航节
        MnjxPlanSection orgPlanSection = orgAirportPlanSectionList.get(0);
        // 设置3种限额
        String officeAirportCode = MemoryDataUtils.getMemoryData().getMnjxOffice().getOfficeNo().substring(0, 3);
        MnjxAirport officeAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, officeAirportCode)
                .one();
        List<String> gs = new ArrayList<>();
        List<String> id = new ArrayList<>();
        List<String> hl = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(officeAirport) && orgAirportId.equals(officeAirport.getAirportId())) {
            boolean addLimit = false;
            for (MnjxPlanSection planSection : planSectionList) {
                if (officeAirport.getAirportId().equals(planSection.getDepAptId())) {
                    addLimit = true;
                }
                if (addLimit) {
                    String currentAirportCode = iMnjxAirportService.getById(planSection.getDepAptId()).getAirportCode();
                    if (StrUtil.isNotEmpty(this.formatLimit(planSection.getGoshowLimit()))) {
                        gs.add(StrUtil.format("{}/{}", currentAirportCode, this.formatLimit(planSection.getGoshowLimit())));
                    }
                    if (StrUtil.isNotEmpty(this.formatLimit(planSection.getHlLimit()))) {
                        id.add(StrUtil.format("{}/{}", currentAirportCode, this.formatLimit(planSection.getHlLimit())));
                    }
                    if (StrUtil.isNotEmpty(this.formatLimit(planSection.getIdLimit()))) {
                        hl.add(StrUtil.format("{}/{}", currentAirportCode, this.formatLimit(planSection.getIdLimit())));
                    }
                }
            }
        } else {
            if (StrUtil.isNotEmpty(this.formatLimit(orgPlanSection.getGoshowLimit()))) {
                gs.add(StrUtil.format("{}/{}", orgAirport.getAirportCode(), this.formatLimit(orgPlanSection.getGoshowLimit())));
            }
            if (StrUtil.isNotEmpty(this.formatLimit(orgPlanSection.getHlLimit()))) {
                id.add(StrUtil.format("{}/{}", orgAirport.getAirportCode(), this.formatLimit(orgPlanSection.getHlLimit())));
            }
            if (StrUtil.isNotEmpty(this.formatLimit(orgPlanSection.getIdLimit()))) {
                hl.add(StrUtil.format("{}/{}", orgAirport.getAirportCode(), this.formatLimit(orgPlanSection.getIdLimit())));
            }
        }
        syResultDto.setGs(gs);
        syResultDto.setId(id);
        syResultDto.setHl(hl);
        AtomicInteger uwt = new AtomicInteger(200000);
        AtomicInteger uaw = new AtomicInteger(200000);
        // 如果输入的航站不是这个航班的出发站，需要添加一份从出发到结束的那一段TRANSIT
        if (!orgPlanSection.equals(firstPlanSection)) {
            sySegResultDtos.add(this.constructSySegResult(planSectionList, true, option, flightNo, syResultDto.getShareFlightNoList(), flightDate, mnjxCnd.getLayout(), uwt, uaw));
        }

        // 获取出发航节的飞机
        MnjxPlane mnjxPlane = iMnjxPlaneService.getById(orgPlanSection.getPlaneId());

        // 获取出发航节的机型版本号
        MnjxPlaneModel planeModel = iMnjxPlaneModelService.getById(mnjxPlane.getPlaneModelId());

        // 构建回显数据
        // 构建SY航段数据
        this.constructSeg(planSectionList, orgAirportId, sySegResultDtos, option, flightNo, syResultDto.getShareFlightNoList(), flightDate, totalResult, mnjxCnd.getLayout(), uwt, uaw);

        // 构建通用数据
        this.constructNormalData(syResultDto, mnjxPlane, planeModel, orgPlanSection, planFlight);

        // 构建布局相关的数据
        // 原始舱位布局
        syResultDto.setCnf(mnjxCnd.getLayout().replace(StrUtil.SLASH, ""));
        List<MnjxSeatModel> seatModelList = iMnjxSeatModelService.lambdaQuery()
                .eq(MnjxSeatModel::getCndId, mnjxCnd.getCndId())
                .list();
        this.constructLayoutData(syResultDto, seatModelList, planSectionList, flightNo, flightDate, orgPlanSection);

        // Z选项时的ZONE信息
        if (Constant.SY_Z.equals(option)) {
            this.constructZone(syResultDto, seatModelList, orgPlanSection, mnjxCnd);
        }
        syResultDto.setSySegResultDtos(sySegResultDtos);
        syResultDto.setTotalResult(totalResult);
        syResultDto.setUwt(StrUtil.fill(uwt.toString(), '0', 6, true));
        syResultDto.setUaw(StrUtil.fill(uaw.toString(), '0', 6, true));
        return syResultDto;
    }

    /**
     * Title: constructNormalData
     * Description: 构建通用数据
     *
     * @param syResultDto    syResultDto
     * @param mnjxPlane      mnjxPlane
     * @param planeModel     planeModel
     * @param orgPlanSection orgPlanSection
     * @param planFlight     planFlight
     * @return 构建通用数据
     * <AUTHOR>
     * @date 2022/12/8 9:23
     */
    private void constructNormalData(SyResultDto syResultDto, MnjxPlane mnjxPlane, MnjxPlaneModel planeModel, MnjxPlanSection orgPlanSection, MnjxPlanFlight planFlight) {
        // 飞机号
        syResultDto.setPlaneNo(mnjxPlane.getPlaneNo());
        // 飞机机型
        syResultDto.setPlaneType(planeModel.getPlaneModelType());
        // 飞机版本号
        syResultDto.setPlaneVersion(planeModel.getPlaneModelVersion());
        // 登机口
        syResultDto.setGate(StrUtil.isEmpty(orgPlanSection.getGate()) ? "????" : StrUtil.fill(orgPlanSection.getGate(), ' ', 4, false));
        // 登机时间
        if (StrUtil.isNotEmpty(orgPlanSection.getActualBoarding())) {
            syResultDto.setBdt(StrUtil.fill(orgPlanSection.getActualBoarding(), '0', 4, true));
        } else if (StrUtil.isNotEmpty(orgPlanSection.getEstimateBoarding())) {
            syResultDto.setBdt(StrUtil.fill(orgPlanSection.getEstimateBoarding(), '0', 4, true));
        }
        // 预计离港时间
        syResultDto.setSd(StrUtil.fill(orgPlanSection.getEstimateOff(), '0', 4, true));
        // 实际离港时间
        if (StrUtil.isNotEmpty(orgPlanSection.getActualOff())) {
            syResultDto.setEd(StrUtil.fill(orgPlanSection.getActualOff(), '0', 4, true));
        } else {
            syResultDto.setEd(syResultDto.getSd());
        }

        // 航班状态修改时间
        Date statusUpdateTime = planFlight.getStatusUpdateTime();
        if (ObjectUtil.isNotEmpty(statusUpdateTime)) {
            String hh = DateUtils.date2hh(statusUpdateTime);
            String mi = DateUtils.date2mi(statusUpdateTime);
            String updateTime = StrUtil.format("{}{}", hh, mi);
            if (Constant.CK_STATUS_CI.equals(planFlight.getCkStatus())) {
                syResultDto.setCi(StrUtil.fill(updateTime, '0', 4, true));
            } else if (Constant.CK_STATUS_CC.equals(planFlight.getCkStatus())) {
                syResultDto.setCc(StrUtil.fill(updateTime, '0', 4, true));
            }
        }
    }

    private String formatLimit(String limit) {
        String res = null;
        if (StrUtil.isNotEmpty(limit)) {
            String[] split = limit.split("/");
            StringBuilder sb = new StringBuilder();
            for (String s : split) {
                if (!s.contains("-") && Integer.parseInt(s.substring(1)) != 0) {
                    sb.append(s);
                }
            }
            res = sb.toString();
        }
        return res;
    }

    /**
     * Title: constructSeg
     * Description: 构建航段数据
     *
     * @param planSectionList planSectionList
     * @param orgAirportId    orgAirportId
     * @param sySegResultDtos sySegResultDtos
     * @param option          option
     * @param flightNo        flightNo
     * @param flightDate      flightDate
     * @param totalResult     totalResult
     * @return 构建航段数据
     * <AUTHOR>
     * @date 2022/7/29 11:20
     */
    private void constructSeg(List<MnjxPlanSection> planSectionList, String orgAirportId,
                              List<SySegResultDto> sySegResultDtos, String option, String flightNo, List<String> shareFlightNoList,
                              String flightDate, SySegResultDto totalResult, String layout, AtomicInteger uwt, AtomicInteger uaw) {
        // 存储符合条件的所有航节
        List<List<MnjxPlanSection>> normalPlanSectionList = new ArrayList<>();
        // 记录每次航节添加的数量
        int tempSize = 0;
        for (int i = 0; i < planSectionList.size(); i++) {
            List<MnjxPlanSection> tempPlanSectionList = new ArrayList<>();
            // 从输入站开始拿后面所有的航节
            boolean isStart = false;
            for (MnjxPlanSection innerMnjxPlanSection : planSectionList) {
                if (innerMnjxPlanSection.getDepAptId().equals(orgAirportId)) {
                    isStart = true;
                    tempSize++;
                }
                if (isStart) {
                    tempPlanSectionList.add(innerMnjxPlanSection);
                }
                if (tempPlanSectionList.size() == tempSize && tempSize > 0) {
                    normalPlanSectionList.add(tempPlanSectionList);
                    break;
                }
            }
        }
        for (List<MnjxPlanSection> list : normalPlanSectionList) {
            sySegResultDtos.add(this.constructSySegResult(list, false, option, flightNo, shareFlightNoList, flightDate, layout, uwt, uaw));
        }
        // 构建total数据
        if (sySegResultDtos.size() > 1) {
            this.constructTotalSySegResult(totalResult, sySegResultDtos);
        }
    }

    /**
     * Title: constructLayoutData
     * Description: 构建布局数据
     *
     * @param syResultDto    syResultDto
     * @param seatModelList  seatModelList
     * @param flightNo       flightNo
     * @param flightDate     flightDate
     * @param orgPlanSection orgPlanSection
     * @return 构建布局数据
     * <AUTHOR>
     * @date 2022/7/29 11:20
     */
    private void constructLayoutData(SyResultDto syResultDto, List<MnjxSeatModel> seatModelList, List<MnjxPlanSection> planSectionList, String flightNo, String flightDate, MnjxPlanSection orgPlanSection) {
        List<String> planSectionIdList = planSectionList.stream()
                .map(MnjxPlanSection::getPlanSectionId)
                .collect(Collectors.toList());
        List<MnjxOpenCabin> openCabinList = iMnjxOpenCabinService.lambdaQuery()
                .in(MnjxOpenCabin::getPlanSectionId, planSectionIdList)
                .list();
        Map<String, List<MnjxOpenCabin>> cabinClassOpenCabinMap = openCabinList.stream()
                .collect(Collectors.groupingBy(MnjxOpenCabin::getCabinClass));
        Map<String, List<MnjxSeatModel>> cabinClassSeatModelMap = seatModelList.stream()
                .collect(Collectors.groupingBy(MnjxSeatModel::getCabinClass));
        Set<Map.Entry<String, List<MnjxSeatModel>>> entries = cabinClassSeatModelMap.entrySet();
        StringBuilder capSb = new StringBuilder();
        StringBuilder avSb = new StringBuilder();
        String org = iMnjxAirportService.getById(orgPlanSection.getDepAptId()).getAirportCode();
        String dst = iMnjxAirportService.getById(orgPlanSection.getArrAptId()).getAirportCode();
        for (Map.Entry<String, List<MnjxSeatModel>> entry : entries) {
            String cabinClass = entry.getKey();
            if (StrUtil.isEmpty(cabinClass)) {
                continue;
            }

            List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                    .eq(MnjxPnrSeg::getFlightNo, flightNo)
                    .eq(MnjxPnrSeg::getFlightDate, flightDate)
                    .eq(MnjxPnrSeg::getCabinClass, cabinClass)
                    .eq(MnjxPnrSeg::getOrg, org)
                    .eq(MnjxPnrSeg::getDst, dst)
                    .list();
            List<String> pnrIdList = pnrSegList.stream()
                    .map(MnjxPnrSeg::getPnrId)
                    .collect(Collectors.toList());
            List<MnjxPsgCki> psgCkiList = new ArrayList<>();
            if (CollUtil.isNotEmpty(pnrIdList)) {
                List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                        .in(MnjxPnrNm::getPnrId, pnrIdList)
                        .list();
                List<String> pnrNmIdList = pnrNmList.stream()
                        .map(MnjxPnrNm::getPnrNmId)
                        .collect(Collectors.toList());
                psgCkiList = iMnjxPsgCkiService.lambdaQuery()
                        .in(MnjxPsgCki::getPnrNmId, pnrNmIdList)
                        .in(MnjxPsgCki::getCkiStatus, Constant.ACC, Constant.DL)
                        .list();
            }
            if (CollUtil.isNotEmpty(psgCkiList)) {
                String bnNo = syResultDto.getBnNo();
                if (StrUtil.isEmpty(bnNo)) {
                    String paTotal = StrUtil.toString(psgCkiList.size());
                    // 接收旅客总数
                    syResultDto.setBnNo(StrUtil.fill(paTotal, '0', 3, true));
                } else {
                    String paTotal = StrUtil.toString(psgCkiList.size() + Integer.parseInt(syResultDto.getBnNo()));
                    // 接收旅客总数
                    syResultDto.setBnNo(StrUtil.fill(paTotal, '0', 3, true));
                }
            } else if (StrUtil.isEmpty(syResultDto.getBnNo())) {
                syResultDto.setBnNo("000");
            }
            List<MnjxOpenCabin> cabinList = cabinClassOpenCabinMap.get(cabinClass);
            List<String> openCabinIdList = cabinList.stream()
                    .map(MnjxOpenCabin::getOpenCabinId)
                    .collect(Collectors.toList());
            List<MnjxSeat> capSeatList = iMnjxSeatService.lambdaQuery()
                    .in(MnjxSeat::getOpenCabinId, openCabinIdList)
                    .isNotNull(MnjxSeat::getSeatNo)
                    .notIn(MnjxSeat::getSeatStatus, "X", "$")
                    .list();
            capSb.append(cabinClass).append(capSeatList.size());
            long avCount = capSeatList.stream()
                    .filter(s -> !StrUtil.equalsAny(s.getSeatStatus(), ".", "P", "V", "T"))
                    .count();
            avSb.append(cabinClass).append(avCount);
        }
        String cnf = syResultDto.getCnf();
        syResultDto.setCap(this.orderLayoutStr(cnf, capSb.toString()));
        // AV剩余座位
        syResultDto.setAv(this.orderLayoutStr(cnf, avSb.toString()));
    }

    /**
     * Title: constructZone
     * Description: 构建ZONE信息
     *
     * @param syResultDto     syResultDto
     * @param seatModelList   seatModelList
     * @param mnjxPlanSection mnjxPlanSection
     * @param mnjxCnd         mnjxCnd
     * @return 构建ZONE信息
     * <AUTHOR>
     * @date 2022/7/29 15:19
     */
    private void constructZone(SyResultDto syResultDto, List<MnjxSeatModel> seatModelList, MnjxPlanSection mnjxPlanSection, MnjxCnd mnjxCnd) {
        // 获取该航段的座位
        List<MnjxSeat> seatList = iMnjxSeatService.lambdaQuery()
                .eq(MnjxSeat::getPlanSectionId, mnjxPlanSection.getPlanSectionId())
                .list();
        // 将座位模型按舱等分组
        Map<String, List<MnjxSeatModel>> listMap = seatModelList.stream()
                .filter(s -> StrUtil.isNotEmpty(s.getSeatNo()))
                .collect(Collectors.groupingBy(MnjxSeatModel::getCabinClass));
        Set<Map.Entry<String, List<MnjxSeatModel>>> entries = listMap.entrySet();
        List<Map<String, String>> rowList = new ArrayList<>();
        entries.forEach(e -> {
            Map<String, String> cabinClassRows = new HashMap<>(1024);
            String cabinClass = e.getKey();
            List<MnjxSeatModel> value = e.getValue();
            // 拿到该舱等最小行数
            OptionalInt min = value.stream()
                    .mapToInt(v -> Integer.parseInt(v.getSeatRow()))
                    .min();
            // 拿到该舱等最大行数
            OptionalInt max = value.stream()
                    .mapToInt(v -> Integer.parseInt(v.getSeatRow()))
                    .max();
            List<String> seatNoList = value.stream()
                    .map(MnjxSeatModel::getSeatNo)
                    .collect(Collectors.toList());
            // 获取该舱等已选座的数量
            long dotStatus = seatList.stream()
                    .filter(s -> seatNoList.contains(s.getSeatNo()))
                    .filter(s -> StrUtil.DOT.equals(s.getSeatStatus()))
                    .count();
            cabinClassRows.put(cabinClass, StrUtil.format("R{}-{}/{}", StrUtil.toString(min.getAsInt()), StrUtil.toString(max.getAsInt()), StrUtil.toString(dotStatus)));
            rowList.add(cabinClassRows);
        });
        StringBuilder zone = new StringBuilder();
        zone.append("ZONES -");
        // 获取cnd布局
        String layout = mnjxCnd.getLayout().replace("/", "");
        // 按舱等顺序拼接ZONE信息
        for (String cabinClass : layout.split("\\d+")) {
            for (Map<String, String> map : rowList) {
                if (map.containsKey(cabinClass)) {
                    zone.append(" ");
                    zone.append(map.get(cabinClass));
                    break;
                }
            }
        }
        syResultDto.setZone(zone.toString());
    }

    private void appendStr(StringBuilder sb, int i, int param, int cabinClassNum, int fillLength) {
        sb.append(StrUtil.fill(StrUtil.toString(param), '0', fillLength, true));
        if (i < cabinClassNum - 1) {
            sb.append(StrUtil.SLASH);
        } else if (sb.toString().split(StrUtil.SLASH).length < 3) {
            sb.append("    ");
        }
    }

    private void appendTotalStr(StringBuilder sb, String param, int fillLength) {
        String[] split = param.split(StrUtil.SLASH);
        int length = split.length;
        for (int i = 0; i < length; i++) {
            String s = split[i].trim();
            if (StrUtil.isEmpty(sb.toString()) || sb.toString().split(StrUtil.SLASH).length < length) {
                this.appendStr(sb, i, Integer.parseInt(s), length, fillLength);
            } else {
                String[] split1 = sb.toString().split(StrUtil.SLASH);
                String s1 = split1[i].trim();
                int sum = Integer.parseInt(s) + Integer.parseInt(s1);
                sb.setLength(0);
                for (int j = 0; j < split1.length; j++) {
                    if (j == i) {
                        this.appendStr(sb, j, sum, split1.length, fillLength);
                    } else {
                        this.appendStr(sb, j, Integer.parseInt(split1[j]), split1.length, fillLength);
                    }
                }
            }
        }
        if (length < 3) {
            sb.append("    ");
        }
    }

    /**
     * Title: constructSySegResult
     * Description: 构建SY航段部分的数据
     *
     * @param mnjxPlanSectionList mnjxPlanSectionList
     * @param isTransit           isTransit
     * @param option              option
     * @param flightNo            flightNo
     * @param flightDate          flightDate
     * @return 构建SY航段部分的数据
     * <AUTHOR>
     * @date 2022/7/28 14:36
     */
    private SySegResultDto constructSySegResult(List<MnjxPlanSection> mnjxPlanSectionList, boolean isTransit, String option, String flightNo, List<String> shareFlightNoList, String flightDate, String layout, AtomicInteger uwt, AtomicInteger uaw) {
        SySegResultDto sySegResultDto = new SySegResultDto();
        MnjxAirport orgMnjxAirport = iMnjxAirportService.getById(mnjxPlanSectionList.get(0).getDepAptId());
        String org = orgMnjxAirport.getAirportCode();
        MnjxAirport dstMnjxAirport = iMnjxAirportService.getById(mnjxPlanSectionList.get(mnjxPlanSectionList.size() - 1).getArrAptId());
        String dst = dstMnjxAirport.getAirportCode();
        String cityPair = StrUtil.format("{}{}", org, dst);
        sySegResultDto.setCityPair(cityPair);
        sySegResultDto.setAirportInfo(orgMnjxAirport.getAirportEname());
        List<String> flightNoList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(shareFlightNoList) && !shareFlightNoList.get(0).equals(flightNo)) {
            flightNoList.addAll(shareFlightNoList);
            flightNoList.add(flightNo);
        }
        List<MnjxPsgCki> allPsgCkiList = syMapper.retrievePsgCki(flightNo, flightNoList, flightDate, org, dst);
        List<String> nmIdList = allPsgCkiList.stream()
                .map(MnjxPsgCki::getPnrNmId)
                .collect(Collectors.toList());
        List<MnjxPsgCki> containsOtherFlightPsgCkiList = new ArrayList<>();
        if (CollUtil.isNotEmpty(nmIdList)) {
            containsOtherFlightPsgCkiList = iMnjxPsgCkiService.lambdaQuery()
                    .in(MnjxPsgCki::getPnrNmId, nmIdList)
                    .list();
        }
        List<MnjxPnrSeg> allIoPnrSegList = new ArrayList<>();

        String[] layoutSplit = layout.split(StrUtil.SLASH);
        // 舱等数
        int cabinClassNum = layoutSplit.length;

        // =============和舱等挂钩的数据==============
        StringBuilder rNo = new StringBuilder();
        StringBuilder cNo = new StringBuilder();
        StringBuilder sbNo = new StringBuilder();
        StringBuilder saNo = new StringBuilder();
        StringBuilder exstNo = new StringBuilder();
        StringBuilder xcrNo = new StringBuilder();
        StringBuilder zcrNo = new StringBuilder();
        StringBuilder retNo = new StringBuilder();
        StringBuilder cetNo = new StringBuilder();
        StringBuilder ediiNo = new StringBuilder();
        StringBuilder edioNo = new StringBuilder();
        StringBuilder tciiNo = new StringBuilder();
        StringBuilder tcioNo = new StringBuilder();
        // 本地值机旅客人数
        StringBuilder lcpxNo = new StringBuilder();
        // 进港旅客人数（待定）
        StringBuilder sfpiNo = new StringBuilder();
        // 出港旅客人数（待定）
        StringBuilder sfpoNo = new StringBuilder();
        // 进港行李件数重量（待定）
        StringBuilder sfbiNo = new StringBuilder();
        // 出港行李件数重量（待定）
        StringBuilder sfboNo = new StringBuilder();

        StringBuilder boardingNo = new StringBuilder();
        StringBuilder vipNo = new StringBuilder();
        StringBuilder cchdNo = new StringBuilder();
        StringBuilder cinfNo = new StringBuilder();
        StringBuilder bchdNo = new StringBuilder();
        StringBuilder binfNo = new StringBuilder();
        StringBuilder infCetNo = new StringBuilder();
        StringBuilder interCetNo = new StringBuilder();
        StringBuilder retrpNo = new StringBuilder();
        StringBuilder cetrpNo = new StringBuilder();
        for (int i = 0; i < cabinClassNum; i++) {
            String cabinClassSeats = layoutSplit[i];
            String cabinClass = cabinClassSeats.substring(0, 1);
            List<MnjxPsgCki> psgCkiList = allPsgCkiList.stream()
                    .filter(p -> cabinClass.equals(p.getCabinClass()))
                    .collect(Collectors.toList());
            this.appendStr(rNo, i, psgCkiList.size(), cabinClassNum, 3);

            // 已值机和登机旅客
            List<MnjxPsgCki> accOrGtPsgCkiList = psgCkiList.stream()
                    .filter(p -> Constant.ACC.equals(p.getCkiStatus()) || Constant.GT.equals(p.getCkiStatus()))
                    .collect(Collectors.toList());
            // 已值机旅客未登机
            List<MnjxPsgCki> accPsgCkiList = psgCkiList.stream()
                    .filter(p -> Constant.ACC.equals(p.getCkiStatus()))
                    .collect(Collectors.toList());
            // 已登机旅客
            List<MnjxPsgCki> gtPsgCkiList = psgCkiList.stream()
                    .filter(p -> Constant.GT.equals(p.getCkiStatus()))
                    .collect(Collectors.toList());

            int accNum = accOrGtPsgCkiList.size();
            this.appendStr(cNo, i, accNum, cabinClassNum, 3);

            // 候补旅客数
            int sbNum = (int) psgCkiList.stream()
                    .filter(p -> Constant.SB.equals(p.getCkiStatus()))
                    .count();
            this.appendStr(sbNo, i, sbNum, cabinClassNum, 3);

            // 公司折扣票旅客数
            this.appendStr(saNo, i, 0, cabinClassNum, 3);

            // 额外占座数（仅值机和登机的部分）
            List<MnjxNmSsr> exstList = new ArrayList<>();
            if (CollUtil.isNotEmpty(accOrGtPsgCkiList)) {
                List<String> accOrGtNmIdList = accOrGtPsgCkiList.stream()
                        .map(MnjxPsgCki::getPnrNmId)
                        .collect(Collectors.toList());
                exstList = iMnjxNmSsrService.lambdaQuery()
                        .in(MnjxNmSsr::getPnrNmId, accOrGtNmIdList)
                        .in(MnjxNmSsr::getSsrType, "EXST", "CBBG", "DIPL", "COUR")
                        .list();
            }
            this.appendStr(exstNo, i, exstList.size(), cabinClassNum, 2);

            // 额外机组占座数
            this.appendStr(xcrNo, i, 0, cabinClassNum, 2);

            //
            this.appendStr(zcrNo, i, 0, cabinClassNum, 2);

            // 电子客票订座数
            List<MnjxPnrNmTicket> nmTicketList = syMapper.retrieveAdtTicket(flightNo, flightNoList, flightDate, org, dst, cabinClass);
            this.appendStr(retNo, i, nmTicketList.size(), cabinClassNum, 3);

            // 电子客票接受数
            long etAccNum = 0;
            if (CollUtil.isNotEmpty(nmTicketList)) {
                List<String> tnIdList = nmTicketList.stream()
                        .map(MnjxPnrNmTicket::getPnrNmTnId)
                        .collect(Collectors.toList());
                List<MnjxPnrNmTn> nmTnList = iMnjxPnrNmTnService.listByIds(tnIdList);
                List<String> etAccNmIdList = nmTnList.stream()
                        .map(MnjxPnrNmTn::getPnrNmId)
                        .collect(Collectors.toList());
                etAccNum = accOrGtPsgCkiList.stream()
                        .filter(p -> etAccNmIdList.contains(p.getPnrNmId()))
                        .count();
            }
            this.appendStr(cetNo, i, (int) etAccNum, cabinClassNum, 3);

            //
            this.appendStr(ediiNo, i, 0, cabinClassNum, 3);

            //
            this.appendStr(edioNo, i, 0, cabinClassNum, 3);

            // 进出港旅客数（已接收）
            int tcii = 0;
            int tcio = 0;
            // 判断有联程接收的旅客
            if (CollUtils.isNotEmpty(containsOtherFlightPsgCkiList) && containsOtherFlightPsgCkiList.stream().anyMatch(p -> cabinClass.equals(p.getCabinClass()) && Constant.STR_ONE.equals(p.getIsThroughCheckIn()))) {
                // 对联程接收的旅客按nmId分组
                Map<String, List<MnjxPsgCki>> nmIdPsgCkiMap = containsOtherFlightPsgCkiList.stream()
                        .filter(p -> cabinClass.equals(p.getCabinClass()) && Constant.STR_ONE.equals(p.getIsThroughCheckIn()) && Constant.ACC.equals(p.getCkiStatus()))
                        .sorted(Comparator.comparing(MnjxPsgCki::getPnrSegNo))
                        .collect(Collectors.groupingBy(MnjxPsgCki::getPnrNmId));
                // 对每个旅客的当前航班所在联程中的位置决定统计为出港还是进港
                for (Map.Entry<String, List<MnjxPsgCki>> entry : nmIdPsgCkiMap.entrySet()) {
                    String currentNmId = entry.getKey();
                    List<MnjxPsgCki> ckiList = entry.getValue();
                    String currentPnrId = iMnjxPnrNmService.getById(currentNmId).getPnrId();
                    // 查询联程接收的航段组数据
                    List<MnjxPnrSeg> currentSegList = iMnjxPnrSegService.lambdaQuery()
                            .eq(MnjxPnrSeg::getPnrId, currentPnrId)
                            .in(MnjxPnrSeg::getPnrSegNo, ckiList.stream().map(c -> Integer.parseInt(c.getPnrSegNo())).collect(Collectors.toList()))
                            .list();
                    // 如果当前航班在联程接收的范围里，进行统计
                    if (currentSegList.stream().anyMatch(s -> flightNo.equals(s.getFlightNo()))) {
                        allIoPnrSegList.addAll(currentSegList);
                        MnjxPnrSeg currentSeg = currentSegList.stream()
                                .filter(s -> flightNo.equals(s.getFlightNo()))
                                .collect(Collectors.toList())
                                .get(0);
                        int currentSegPnrSegNo = currentSeg.getPnrSegNo();
                        // 第一段只统计出港人数
                        if (currentSegPnrSegNo == 1) {
                            tcio++;
                        }
                        // 中间段和最后一段只统计进港人数
                        else {
                            tcii++;
                        }
                    }
                }
            }
            // 当前航班的进港人数
            this.appendStr(tciiNo, i, tcii, cabinClassNum, 3);

            // 当前航班的出港人数
            this.appendStr(tcioNo, i, tcio, cabinClassNum, 3);

            //
//            this.appendStr(lcpxNo, i, accNum, cabinClassNum, 3);
            lcpxNo.append(StrUtil.fill(StrUtil.toString(accNum), '0', 3, true));
            if (i < cabinClassNum - 1) {
                lcpxNo.append(StrUtil.SLASH);
            }

            this.appendStr(sfpiNo, i, 0, cabinClassNum, 3);

            this.appendStr(sfpoNo, i, 0, cabinClassNum, 3);

            this.appendStr(sfbiNo, i, 0, cabinClassNum, 3);

            this.appendStr(sfboNo, i, 0, cabinClassNum, 3);

            // 已登机人数
            int boardingNum = gtPsgCkiList.size();
            this.appendStr(boardingNo, i, boardingNum, cabinClassNum, 3);

            // VIP旅客数
            int vipNum = 0;
            if (CollUtil.isNotEmpty(accPsgCkiList)) {
                List<String> accNmIdList = accPsgCkiList.stream()
                        .map(MnjxPsgCki::getPnrNmId)
                        .collect(Collectors.toList());
                vipNum = iMnjxNmOsiService.lambdaQuery()
                        .in(MnjxNmOsi::getPnrNmId, accNmIdList)
                        .eq(MnjxNmOsi::getPnrOsiType, Constant.VIP_TYPE)
                        .count();
            }
            this.appendStr(vipNo, i, vipNum, cabinClassNum, 3);

            // 已值机儿童数
            int cchdNum = 0;
            if (CollUtil.isNotEmpty(accPsgCkiList)) {
                List<String> accNmIdList = accPsgCkiList.stream()
                        .map(MnjxPsgCki::getPnrNmId)
                        .collect(Collectors.toList());
                cchdNum = iMnjxNmSsrService.lambdaQuery()
                        .in(MnjxNmSsr::getPnrNmId, accNmIdList)
                        .eq(MnjxNmSsr::getSsrType, Constant.SSR_TYPE_CHLD)
                        .count();
            }
            this.appendStr(cchdNo, i, cchdNum, cabinClassNum, 3);

            // 已值机婴儿数
            int cinfNum = 0;
            if (CollUtil.isNotEmpty(accPsgCkiList)) {
                // ACC状态的婴儿数
                List<String> accNmIdList = accPsgCkiList.stream()
                        .map(MnjxPsgCki::getPnrNmId)
                        .collect(Collectors.toList());
                cinfNum = iMnjxNmSsrService.lambdaQuery()
                        .in(MnjxNmSsr::getPnrNmId, accNmIdList)
                        .eq(MnjxNmSsr::getSsrType, Constant.SSR_TYPE_INFT)
                        .count();
                if (CollUtil.isNotEmpty(gtPsgCkiList)) {
                    // 成人登机了，但婴儿还没登机的值机婴儿数
                    List<String> gtNmIdList = gtPsgCkiList.stream()
                            .map(MnjxPsgCki::getPnrNmId)
                            .collect(Collectors.toList());
                    List<MnjxNmSsr> inftSsrList = iMnjxNmSsrService.lambdaQuery()
                            .in(MnjxNmSsr::getPnrNmId, gtNmIdList)
                            .eq(MnjxNmSsr::getSsrType, Constant.SSR_TYPE_INFT)
                            .list();
                    if (CollUtil.isNotEmpty(inftSsrList)) {
                        List<String> inftNmIdList = inftSsrList.stream()
                                .map(MnjxNmSsr::getPnrNmId)
                                .collect(Collectors.toList());
                        long count = gtPsgCkiList.stream()
                                .filter(g -> inftNmIdList.contains(g.getPnrNmId()) && (Constant.STR_ZERO.equals(g.getAbdStatusInfi()) || StrUtil.isEmpty(g.getAbdStatusInfi())))
                                .count();
                        cinfNum = cinfNum + (int) count;
                    }
                }
            }
            this.appendStr(cinfNo, i, cinfNum, cabinClassNum, 3);

            // 已登机儿童数
            int bchdNum = 0;
            if (CollUtil.isNotEmpty(gtPsgCkiList)) {
                List<String> gtNmIdList = gtPsgCkiList.stream()
                        .map(MnjxPsgCki::getPnrNmId)
                        .collect(Collectors.toList());
                bchdNum = iMnjxNmSsrService.lambdaQuery()
                        .in(MnjxNmSsr::getPnrNmId, gtNmIdList)
                        .eq(MnjxNmSsr::getSsrType, Constant.SSR_TYPE_CHLD)
                        .count();
            }
            this.appendStr(bchdNo, i, bchdNum, cabinClassNum, 3);

            // 已登机婴儿数
            int binfNum = 0;
            if (CollUtil.isNotEmpty(gtPsgCkiList)) {
                binfNum = (int) gtPsgCkiList.stream()
                        .filter(g -> Constant.STR_ONE.equals(g.getAbdStatusInfi()))
                        .count();
            }
            this.appendStr(binfNo, i, binfNum, cabinClassNum, 3);

            // 已值机持ET票的婴儿数
            int infCetNum = syMapper.retrieveAccOrGtInfTicket(flightNo, flightDate, org, dst, cabinClass).size();
            this.appendStr(infCetNo, i, infCetNum, cabinClassNum, 3);

            //
            int interCetNum = 0;
            this.appendStr(interCetNo, i, interCetNum, cabinClassNum, 3);

            //
            int retrpNum = 0;
            this.appendStr(retrpNo, i, retrpNum, cabinClassNum, 3);

            //
            int cetrpNum = 0;
            this.appendStr(cetrpNo, i, cetrpNum, cabinClassNum, 3);
        }
        sySegResultDto.setRNo(rNo.toString());
        sySegResultDto.setCNo(cNo.toString());
        sySegResultDto.setSbNo(sbNo.toString());
        sySegResultDto.setSaNo(saNo.toString());
        sySegResultDto.setExstNo(exstNo.toString());
        sySegResultDto.setXcrNo(xcrNo.toString());
        sySegResultDto.setZcrNo(zcrNo.toString());
        sySegResultDto.setTciiNo(tciiNo.toString());
        sySegResultDto.setTcioNo(tcioNo.toString());
        sySegResultDto.setEdiiNo(ediiNo.toString());
        sySegResultDto.setEdioNo(edioNo.toString());
        sySegResultDto.setLcpxNo(lcpxNo.toString());
        sySegResultDto.setSfpiNo(sfpiNo.toString());
        sySegResultDto.setSfpoNo(sfpoNo.toString());
        sySegResultDto.setSfbiNo(sfbiNo.toString());
        sySegResultDto.setSfboNo(sfboNo.toString());
        sySegResultDto.setCetNo(cetNo.toString());
        sySegResultDto.setRetNo(retNo.toString());

        sySegResultDto.setBoardingNo(boardingNo.toString());
        sySegResultDto.setVipNo(vipNo.toString());
        sySegResultDto.setCchdNo(cchdNo.toString());
        sySegResultDto.setCinfNo(cinfNo.toString());
        sySegResultDto.setBchdNo(bchdNo.toString());
        sySegResultDto.setBinfNo(binfNo.toString());
        sySegResultDto.setInfCetNo(infCetNo.toString());
        sySegResultDto.setInterCetNo(interCetNo.toString());
        sySegResultDto.setRetrpNo(retrpNo.toString());
        sySegResultDto.setCetrpNo(cetrpNo.toString());

        // ==============其他和舱等无关的数据===============

        // 行李
        List<MnjxLuggage> luggageList = new ArrayList<>();
        List<MnjxLuggageCarryon> carryonList = new ArrayList<>();
        AtomicInteger bNoPart1 = new AtomicInteger();
        AtomicInteger bNoPart2 = new AtomicInteger();
        AtomicInteger avihNo = new AtomicInteger();
        AtomicInteger bagiNoPart1 = new AtomicInteger();
        AtomicInteger bagiNoPart2 = new AtomicInteger();
        AtomicInteger bagoNoPart1 = new AtomicInteger();
        AtomicInteger bagoNoPart2 = new AtomicInteger();
        if (CollUtil.isNotEmpty(nmIdList)) {
            luggageList = iMnjxLuggageService.lambdaQuery()
                    .in(MnjxLuggage::getPnrNmId, nmIdList)
                    .list();
            luggageList = luggageList.stream()
                    .filter(l -> StrUtil.isEmpty(l.getIsDel()) || !Constant.DELETE_TYPE.equals(l.getIsDel()))
                    .collect(Collectors.toList());
            carryonList = iMnjxLuggageCarryonService.lambdaQuery()
                    .in(MnjxLuggageCarryon::getPnrNmId, nmIdList)
                    .list();
            // 当前段行李
            // 行李重量，只统计已值机或登机的重量
            List<MnjxLuggage> finalLuggageList = luggageList;
            allPsgCkiList.stream()
                    .filter(p -> StrUtil.equalsAny(p.getCkiStatus(), Constant.CKI_STATUS_ACC, Constant.CKI_STATUS_GT))
                    .forEach(cki -> {
                // 取出所有包含当前航段的、行李组分
                List<MnjxLuggage> currentLuggageList = finalLuggageList.stream()
                        .filter(k -> StrUtil.isEmpty(k.getLuggageGroupId()))
                        .filter(k -> k.getBagSegNo().equals(cki.getPnrSegNo()) && k.getPnrNmId().equals(cki.getPnrNmId()))
                        .collect(Collectors.toList());
                // 取出同组行李，去除重复，只计算一次重量，数量要单独计算
                List<MnjxLuggage> luggageGroup = finalLuggageList.stream()
                        .filter(k -> StrUtil.isNotEmpty(k.getLuggageGroupId()))
                        .filter(k -> StrUtil.equalsAny(k.getLuggageType(), Constant.STR_ONE, Constant.STR_TWO))
                        .filter(k -> k.getBagSegNo().equals(cki.getPnrSegNo()) && k.getPnrNmId().equals(cki.getPnrNmId()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(luggageGroup)) {
                    currentLuggageList.addAll(luggageGroup.stream()
                            .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MnjxLuggage::getLuggageGroupId))), ArrayList::new)
                            ));
                }
                List<MnjxLuggage> avihLuggageGroup = finalLuggageList.stream()
                        .filter(k -> StrUtil.isNotEmpty(k.getLuggageGroupId()))
                        .filter(k -> StrUtil.equalsAny(k.getLuggageType(), Constant.STR_THREE, Constant.STR_FOUR))
                        .filter(k -> k.getBagSegNo().equals(cki.getPnrSegNo()) && k.getPnrNmId().equals(cki.getPnrNmId()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(avihLuggageGroup)) {
                    currentLuggageList.addAll(avihLuggageGroup.stream()
                            .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MnjxLuggage::getLuggageGroupId))), ArrayList::new)
                            ));
                }
                if (CollUtil.isNotEmpty(currentLuggageList)) {
                    long luggageSize = currentLuggageList.stream()
                            .filter(k -> k.getBagSegNo().equals(cki.getPnrSegNo()))
                            .filter(k -> StrUtil.equalsAny(k.getLuggageType(), Constant.STR_ONE, Constant.STR_TWO))
                            .count();
                    int groupTypeSize = luggageGroup.stream()
                            .collect(Collectors.groupingBy(MnjxLuggage::getLuggageGroupId))
                            .size();
                    // 计算件数时，luggageSie里面的件数是单件行李件数+组合行李个数（不是所有的组合行李，比如加的3/23，这里个数是1，件数是3，计算总件数应该加件数3），需要减去组合行李个数再加上组合行李件数
                    luggageSize = luggageSize + luggageGroup.size() - groupTypeSize;
                    bNoPart1.addAndGet((int) luggageSize);

                    long avihLuggageSize = currentLuggageList.stream()
                            .filter(k -> k.getBagSegNo().equals(cki.getPnrSegNo()))
                            .filter(k -> StrUtil.equalsAny(k.getLuggageType(), Constant.STR_THREE, Constant.STR_FOUR))
                            .count();
                    int avihGroupTypeSize = avihLuggageGroup.stream()
                            .collect(Collectors.groupingBy(MnjxLuggage::getLuggageGroupId))
                            .size();

                    int avihSize = (int) avihLuggageSize + avihLuggageGroup.size() - avihGroupTypeSize;
                    avihNo.addAndGet(avihSize);
                    int sum = currentLuggageList.stream()
                            .filter(l -> ObjectUtil.isEmpty(l.getWeightPool()))
                            .mapToInt(MnjxLuggage::getLuggageWeight)
                            .sum();
                    sum += currentLuggageList.stream()
                            .filter(l -> ObjectUtil.isNotEmpty(l.getWeightPool()))
                            .mapToInt(MnjxLuggage::getWeightPool)
                            .sum();
                    bNoPart2.addAndGet(sum);
                }
            });
            bNoPart1.addAndGet(carryonList.size());

            // 联程进出港部分行李
            if (CollUtil.isNotEmpty(allIoPnrSegList)) {
                Map<String, List<MnjxPnrSeg>> ioPnrIdSegsMap = allIoPnrSegList.stream().collect(Collectors.groupingBy(MnjxPnrSeg::getPnrId));
                List<MnjxPnrNm> nmList = iMnjxPnrNmService.listByIds(nmIdList);
                Map<String, List<MnjxPnrNm>> nmPnrIdMap = nmList.stream().collect(Collectors.groupingBy(MnjxPnrNm::getPnrId));
                for (Map.Entry<String, List<MnjxPnrSeg>> segEntry : ioPnrIdSegsMap.entrySet()) {
                    List<MnjxPnrSeg> segList = segEntry.getValue();
                    MnjxPnrSeg currentSeg = segList.stream()
                            .filter(p -> flightNo.equals(p.getFlightNo()))
                            .collect(Collectors.toList())
                            .get(0);
                    int currentSegPnrSegNo = currentSeg.getPnrSegNo();
                    List<String> currentNmIdList = nmPnrIdMap.get(segEntry.getKey()).stream()
                            .map(MnjxPnrNm::getPnrNmId)
                            .collect(Collectors.toList());
                    List<MnjxLuggage> currentLuggageList = luggageList.stream()
                            .filter(l -> currentNmIdList.contains(l.getPnrNmId()))
                            .collect(Collectors.toList());
                    List<MnjxLuggage> currentGroupLuggageList = currentLuggageList.stream()
                            .filter(l -> StrUtil.isNotEmpty(l.getLuggageGroupId()))
                            .collect(Collectors.toList());
                    List<MnjxLuggage> currentSingleLuggageList = currentLuggageList.stream()
                            .filter(l -> StrUtil.isEmpty(l.getLuggageGroupId()))
                            .collect(Collectors.toList());
                    for (MnjxLuggage luggage : currentSingleLuggageList) {
                        String bagSegNo = luggage.getBagSegNo();
                        if (bagSegNo.length() > 1 && bagSegNo.contains(StrUtil.toString(currentSegPnrSegNo))) {
                            if (bagSegNo.startsWith(Constant.STR_ONE) && Constant.STR_ONE.equals(StrUtil.toString(currentSegPnrSegNo))) {
                                bagoNoPart1.addAndGet(1);
                                bagoNoPart2.addAndGet(luggage.getLuggageWeight());
                            } else {
                                bagiNoPart1.addAndGet(1);
                                bagiNoPart2.addAndGet(luggage.getLuggageWeight());
                            }
                        } else if (bagSegNo.equals(StrUtil.toString(currentSegPnrSegNo))) {
                            if (Constant.STR_ONE.equals(bagSegNo)) {
                                bagoNoPart1.addAndGet(1);
                                bagoNoPart2.addAndGet(luggage.getLuggageWeight());
                            } else {
                                bagiNoPart1.addAndGet(1);
                                bagiNoPart2.addAndGet(luggage.getLuggageWeight());
                            }
                        }
                    }
                    if (CollUtil.isNotEmpty(currentGroupLuggageList)) {
                        Map<String, List<MnjxLuggage>> collect = currentGroupLuggageList.stream()
                                .collect(Collectors.groupingBy(MnjxLuggage::getLuggageGroupId));
                        for (Map.Entry<String, List<MnjxLuggage>> entry : collect.entrySet()) {
                            List<MnjxLuggage> groupList = entry.getValue();
                            MnjxLuggage groupLuggage = groupList.get(0);
                            String bagSegNo = groupLuggage.getBagSegNo().replace(StrUtil.COMMA, StrUtil.EMPTY).replace(StrUtil.SPACE, StrUtil.EMPTY);
                            if (bagSegNo.length() > 1 && bagSegNo.contains(StrUtil.toString(currentSegPnrSegNo))) {
                                if (bagSegNo.startsWith(StrUtil.toString(currentSegPnrSegNo))) {
                                    bagoNoPart1.addAndGet(groupList.size());
                                    bagoNoPart2.addAndGet(groupLuggage.getLuggageWeight());
                                } else {
                                    bagiNoPart1.addAndGet(groupList.size());
                                    bagiNoPart2.addAndGet(groupLuggage.getLuggageWeight());
                                }
                            } else if (bagSegNo.equals(StrUtil.toString(currentSegPnrSegNo))) {
                                if (Constant.STR_ONE.equals(bagSegNo)) {
                                    bagoNoPart1.addAndGet(groupList.size());
                                    bagoNoPart2.addAndGet(groupLuggage.getLuggageWeight());
                                } else {
                                    bagiNoPart1.addAndGet(groupList.size());
                                    bagiNoPart2.addAndGet(groupLuggage.getLuggageWeight());
                                }
                            }
                        }
                    }
                }
            }
        }
        int avih = avihNo.get();
        if (avih % 64 != 0) {
            if (avih > 64) {
                avih = avih % 64;
            }
            sySegResultDto.setAvihNo(StrUtil.fill(StrUtil.toString(avih), '0', 2, true));
        }
        sySegResultDto.setBNo(StrUtil.format(Constant.TWO_STR_MIX_SPLIT_SLASH, StrUtil.fill(StrUtil.toString(bNoPart1.get()), '0', 4, true), StrUtil.fill(StrUtil.toString(bNoPart2.get()), '0', 6, true)));
        sySegResultDto.setLcbgNo(sySegResultDto.getBNo());
        sySegResultDto.setBagiNo(StrUtil.format(Constant.TWO_STR_MIX_SPLIT_SLASH, StrUtil.fill(StrUtil.toString(bagiNoPart1.get()), '0', 4, true), StrUtil.fill(StrUtil.toString(bagiNoPart2.get()), '0', 6, true)));
        sySegResultDto.setBagoNo(StrUtil.format(Constant.TWO_STR_MIX_SPLIT_SLASH, StrUtil.fill(StrUtil.toString(bagoNoPart1.get()), '0', 4, true), StrUtil.fill(StrUtil.toString(bagoNoPart2.get()), '0', 6, true)));

        List<MnjxNmSsr> ssrList = new ArrayList<>();
        if (CollUtil.isNotEmpty(nmIdList)) {
            ssrList = iMnjxNmSsrService.lambdaQuery()
                    .in(MnjxNmSsr::getPnrNmId, nmIdList)
                    .list();
        }

        // UM类型旅客数量
        long umCount = ssrList.stream()
                .filter(s -> Constant.SSR_TYPE_UMNR.equals(s.getSsrType()))
                .count();
        if (umCount > 0) {
            sySegResultDto.setUmNo(StrUtil.fill(StrUtil.toString(umCount), '0', 3, true));
        }

        // CHLD类型旅客数量
        long chldCount = ssrList.stream()
                .filter(s -> Constant.SSR_TYPE_CHLD.equals(s.getSsrType()))
                .count();
        if (chldCount > 0) {
            sySegResultDto.setChldNo(StrUtil.fill(StrUtil.toString(chldCount), '0', 3, true));
            sySegResultDto.setLcpxChdNo(StrUtil.fill(StrUtil.toString(chldCount), '0', 3, true));
        }

        // INFT类型旅客数量
        long inftCount = ssrList.stream()
                .filter(s -> Constant.SSR_TYPE_INFT.equals(s.getSsrType()))
                .count();
        if (inftCount > 0) {
            sySegResultDto.setInftNo(StrUtil.fill(StrUtil.toString(inftCount), '0', 2, true));
        }

        // 轮椅类型旅客数量
        long wchCount = ssrList.stream()
                .filter(s -> s.getSsrType().startsWith("WC"))
                .count();
        if (wchCount > 0) {
            sySegResultDto.setWchNo(StrUtil.fill(StrUtil.toString(wchCount), '0', 3, true));
        }

        // 候补行李件数
        List<String> sbNmIdList = allPsgCkiList.stream()
                .filter(p -> Constant.SB.equals(p.getCkiStatus()))
                .map(MnjxPsgCki::getPnrNmId)
                .collect(Collectors.toList());
        long sbLuggageCount = luggageList.stream()
                .filter(l -> sbNmIdList.contains(l.getPnrNmId()))
                .count();
        long sbCarryonCount = carryonList.stream()
                .filter(c -> sbNmIdList.contains(c.getPnrNmId()))
                .count();
        int sbBagNo = (int) (sbLuggageCount + sbCarryonCount);
        sySegResultDto.setSbBagNo(StrUtil.fill(StrUtil.toString(sbBagNo), '0', 4, true));

        sySegResultDto.setTransit(isTransit);

        // SY选项S时构建销售舱位及各旅客数量数据
        if (Constant.SY_S.equals(option)) {
            // 使用第一段的开舱和销售舱位
            List<MnjxOpenCabin> firstOpenCabinList = iMnjxOpenCabinService.lambdaQuery()
                    .eq(MnjxOpenCabin::getPlanSectionId, mnjxPlanSectionList.get(0).getPlanSectionId())
                    .list();
            // 查所有销售舱位对应的旅客信息
            firstOpenCabinList.forEach(o -> {
                List<String> sellCabinNmIdList = allPsgCkiList.stream()
                        .filter(p -> p.getSellCabin().equals(o.getSellCabin()))
                        .map(MnjxPsgCki::getPnrNmId)
                        .collect(Collectors.toList());
                List<MnjxPnrNmTn> sellCabinPnrNmTnList = new ArrayList<>();
                List<MnjxPsgCki> sellCabinPsgCkiList = new ArrayList<>();
                if (CollUtil.isNotEmpty(sellCabinNmIdList)) {
                    sellCabinPnrNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                            .in(MnjxPnrNmTn::getPnrNmId, sellCabinNmIdList)
                            .list();
                    sellCabinPsgCkiList = iMnjxPsgCkiService.lambdaQuery()
                            .in(MnjxPsgCki::getPnrNmId, sellCabinNmIdList)
                            .in(MnjxPsgCki::getCkiStatus, Constant.ACC, Constant.GT)
                            .list();
                }
                // 销售舱位
                sySegResultDto.getSellCabinList().add(o.getSellCabin());
                // 各销售舱位所有订座出票的旅客数 + URES（均不包含婴儿）
                sySegResultDto.getResList().add(StrUtil.fill(StrUtil.toString(sellCabinPnrNmTnList.size()), '0', 3, true));
                // 各销售舱位值机的旅客数（均不包含婴儿）
                sySegResultDto.getCkiList().add(StrUtil.fill(StrUtil.toString(sellCabinPsgCkiList.size()), '0', 3, true));
                // 各销售舱位值机的电子客票旅客数（均不包含婴儿）
                sySegResultDto.getCetList().add(StrUtil.fill(StrUtil.toString(sellCabinPnrNmTnList.size()), '0', 3, true));
            });
        }
        // 计算UWT UAW
        int totalPsgNum = Arrays.stream(cNo.toString().trim().split("/")).mapToInt(Integer::parseInt).sum();
        int chdNum = Arrays.stream(cchdNo.toString().trim().split("/")).mapToInt(Integer::parseInt).sum();
        int adultNum = totalPsgNum - chdNum;
        int infNum = Arrays.stream(cinfNo.toString().trim().split("/")).mapToInt(Integer::parseInt).sum();
        uwt.addAndGet(-(adultNum * Constant.ADULT_WEIGHT));
        uwt.addAndGet(-(chdNum * Constant.CHILD_WEIGHT));
        uwt.addAndGet(-(infNum * Constant.INFANT_WEIGHT));
        uwt.addAndGet(-bNoPart2.get());
        uaw.set(uwt.get());
        return sySegResultDto;
    }

    /**
     * Title: getDayDiff
     * Description: 获取输入日期和当前日期的天数差
     *
     * @param ymd
     * @return {@link int}
     * <AUTHOR>
     * @date 2022/7/19 10:02
     */
    private int getDayDiff(String ymd) {
        Date compareDate = DateUtils.ymd2Date(ymd);
        Calendar compareCal = Calendar.getInstance();
        Calendar nowCal = Calendar.getInstance();
        compareCal.setTime(compareDate);
        nowCal.setTime(new Date());
        return compareCal.get(Calendar.DAY_OF_YEAR) - nowCal.get(Calendar.DAY_OF_YEAR);
    }

    /**
     * Title: constructTotalSySegResult
     * Description: 构建total部分的数据
     *
     * @param totalResult     totalResult
     * @param sySegResultDtos sySegResultDtos
     * @return 构建total部分的数据
     * <AUTHOR>
     * @date 2022/7/20 10:49
     */
    private void constructTotalSySegResult(SySegResultDto totalResult, List<SySegResultDto> sySegResultDtos) {
        // =============和舱等挂钩的数据==============
        StringBuilder rNo = new StringBuilder();
        StringBuilder cNo = new StringBuilder();
        StringBuilder sbNo = new StringBuilder();
        StringBuilder saNo = new StringBuilder();
        StringBuilder exstNo = new StringBuilder();
        StringBuilder xcrNo = new StringBuilder();
        StringBuilder zcrNo = new StringBuilder();
        StringBuilder retNo = new StringBuilder();
        StringBuilder cetNoSb = new StringBuilder();
        StringBuilder ediiNo = new StringBuilder();
        StringBuilder edioNo = new StringBuilder();
        StringBuilder tciiNo = new StringBuilder();
        StringBuilder tcioNo = new StringBuilder();
        StringBuilder lcpxNo = new StringBuilder();
        StringBuilder lcbgNo = new StringBuilder();

        StringBuilder boardingNo = new StringBuilder();
        StringBuilder vipNo = new StringBuilder();
        StringBuilder cchdNo = new StringBuilder();
        StringBuilder cinfNo = new StringBuilder();
        StringBuilder bchdNo = new StringBuilder();
        StringBuilder binfNo = new StringBuilder();
        StringBuilder infCetNo = new StringBuilder();
        StringBuilder interCetNo = new StringBuilder();
        StringBuilder retrpNo = new StringBuilder();
        StringBuilder cetrpNo = new StringBuilder();
        int bNoPart1 = 0;
        int bNoPart2 = 0;
        int avihNo = 0;
        int bagiNoPart1 = 0;
        int bagiNoPart2 = 0;
        int bagoNoPart1 = 0;
        int bagoNoPart2 = 0;
        int umNo = 0;
        int wchNo = 0;
        int inftNo = 0;
        int chldNo = 0;
        int sbBagNo = 0;
        int resNo = 0;
        int ckiNo = 0;
        int cetNo = 0;
        List<String> resList = new ArrayList<>();
        List<String> ckiList = new ArrayList<>();
        List<String> cetList = new ArrayList<>();
        for (SySegResultDto sySegResultDto : sySegResultDtos) {

            this.appendTotalStr(rNo, sySegResultDto.getRNo(), 3);
            this.appendTotalStr(cNo, sySegResultDto.getCNo(), 3);
            this.appendTotalStr(sbNo, sySegResultDto.getSbNo(), 3);
            this.appendTotalStr(saNo, sySegResultDto.getSaNo(), 3);
            this.appendTotalStr(exstNo, sySegResultDto.getExstNo(), 2);
            this.appendTotalStr(xcrNo, sySegResultDto.getXcrNo(), 2);
            this.appendTotalStr(zcrNo, sySegResultDto.getZcrNo(), 2);
            this.appendTotalStr(retNo, sySegResultDto.getRetNo(), 3);
            this.appendTotalStr(cetNoSb, sySegResultDto.getCetNo(), 3);
            this.appendTotalStr(ediiNo, sySegResultDto.getEdiiNo(), 3);
            this.appendTotalStr(edioNo, sySegResultDto.getEdioNo(), 3);
            this.appendTotalStr(tciiNo, sySegResultDto.getTciiNo(), 3);
            this.appendTotalStr(tcioNo, sySegResultDto.getTcioNo(), 3);
            this.appendTotalStr(lcpxNo, sySegResultDto.getLcpxNo(), 3);
            this.appendTotalStr(lcbgNo, sySegResultDto.getLcbgNo(), 3);

            this.appendTotalStr(boardingNo, sySegResultDto.getBoardingNo(), 3);
            this.appendTotalStr(vipNo, sySegResultDto.getVipNo(), 3);
            this.appendTotalStr(cchdNo, sySegResultDto.getCchdNo(), 3);
            this.appendTotalStr(cinfNo, sySegResultDto.getCinfNo(), 3);
            this.appendTotalStr(bchdNo, sySegResultDto.getBchdNo(), 3);
            this.appendTotalStr(binfNo, sySegResultDto.getBinfNo(), 3);
            this.appendTotalStr(infCetNo, sySegResultDto.getInfCetNo(), 3);
            this.appendTotalStr(interCetNo, sySegResultDto.getInterCetNo(), 3);
            this.appendTotalStr(retrpNo, sySegResultDto.getRetrpNo(), 3);
            this.appendTotalStr(cetrpNo, sySegResultDto.getCetrpNo(), 3);

            // 行李件数/重量
            bNoPart1 += Integer.parseInt(sySegResultDto.getBNo().split(StrUtil.SLASH)[0]);
            bNoPart2 += Integer.parseInt(sySegResultDto.getBNo().split(StrUtil.SLASH)[1]);
            if (ObjectUtil.isNotEmpty(sySegResultDto.getAvihNo())) {
                avihNo += Integer.parseInt(sySegResultDto.getAvihNo());
            }
            bagiNoPart1 += Integer.parseInt(sySegResultDto.getBagiNo().split(StrUtil.SLASH)[0]);
            bagiNoPart2 += Integer.parseInt(sySegResultDto.getBagiNo().split(StrUtil.SLASH)[1]);
            bagoNoPart1 += Integer.parseInt(sySegResultDto.getBagoNo().split(StrUtil.SLASH)[0]);
            bagoNoPart2 += Integer.parseInt(sySegResultDto.getBagoNo().split(StrUtil.SLASH)[1]);

            // 旅客类型数量
            if (StrUtil.isNotEmpty(sySegResultDto.getUmNo())) {
                umNo += Integer.parseInt(sySegResultDto.getUmNo());
            }
            if (StrUtil.isNotEmpty(sySegResultDto.getWchNo())) {
                wchNo += Integer.parseInt(sySegResultDto.getWchNo());
            }
            if (StrUtil.isNotEmpty(sySegResultDto.getInftNo())) {
                inftNo += Integer.parseInt(sySegResultDto.getInftNo());
            }
            if (StrUtil.isNotEmpty(sySegResultDto.getChldNo())) {
                chldNo += Integer.parseInt(sySegResultDto.getChldNo());
            }

            // 候补行李
            sbBagNo += Integer.parseInt(sySegResultDto.getSbBagNo());

            // 销售舱位只添加一次
            if (CollUtil.isEmpty(totalResult.getSellCabinList())) {
                totalResult.setSellCabinList(sySegResultDto.getSellCabinList());
            }
            for (String res : sySegResultDto.getResList()) {
                resNo += Integer.parseInt(res);
            }
            resList.add(StrUtil.fill(StrUtil.toString(resNo), '0', 3, true));
            for (String cki : sySegResultDto.getCkiList()) {
                ckiNo += Integer.parseInt(cki);
            }
            ckiList.add(StrUtil.fill(StrUtil.toString(ckiNo), '0', 3, true));
            for (String cet : sySegResultDto.getCetList()) {
                cetNo += Integer.parseInt(cet);
            }
            cetList.add(StrUtil.fill(StrUtil.toString(cetNo), '0', 3, true));
        }

        totalResult.setRNo(rNo.toString());
        totalResult.setCNo(cNo.toString());
        totalResult.setSbNo(sbNo.toString());
        totalResult.setSaNo(saNo.toString());
        totalResult.setExstNo(exstNo.toString());
        totalResult.setXcrNo(xcrNo.toString());
        totalResult.setZcrNo(zcrNo.toString());
        totalResult.setTciiNo(tciiNo.toString());
        totalResult.setTcioNo(tcioNo.toString());
        totalResult.setEdiiNo(ediiNo.toString());
        totalResult.setEdioNo(edioNo.toString());
        totalResult.setLcpxNo(lcpxNo.toString());
        totalResult.setLcbgNo(lcbgNo.toString());
        totalResult.setCetNo(cetNoSb.toString());
        totalResult.setRetNo(retNo.toString());
        if (avihNo % 64 != 0) {
            if (avihNo > 64) {
                avihNo = avihNo % 64;
            }
            totalResult.setAvihNo(StrUtil.fill(StrUtil.toString(avihNo), '0', 2, true));
        }
        totalResult.setBNo(StrUtil.format(Constant.TWO_STR_MIX_SPLIT_SLASH, StrUtil.fill(StrUtil.toString(bNoPart1), '0', 4, true), StrUtil.fill(StrUtil.toString(bNoPart2), '0', 6, true)));
        totalResult.setBagiNo(CharSequenceUtil.format(Constant.TWO_STR_MIX_SPLIT_SLASH, StrUtil.fill(StrUtil.toString(bagiNoPart1), '0', 4, true), StrUtil.fill(StrUtil.toString(bagiNoPart2), '0', 6, true)));
        totalResult.setBagoNo(StrUtil.format(Constant.TWO_STR_MIX_SPLIT_SLASH, StrUtil.fill(StrUtil.toString(bagoNoPart1), '0', 4, true), StrUtil.fill(StrUtil.toString(bagoNoPart2), '0', 6, true)));

        totalResult.setBoardingNo(boardingNo.toString());
        totalResult.setVipNo(vipNo.toString());
        totalResult.setCchdNo(cchdNo.toString());
        totalResult.setCinfNo(cinfNo.toString());
        totalResult.setBchdNo(bchdNo.toString());
        totalResult.setBinfNo(binfNo.toString());
        totalResult.setInfCetNo(infCetNo.toString());
        totalResult.setInterCetNo(interCetNo.toString());
        totalResult.setRetrpNo(retrpNo.toString());
        totalResult.setCetrpNo(cetrpNo.toString());

        if (umNo > 0) {
            totalResult.setUmNo(StrUtil.fill(StrUtil.toString(umNo), '0', 3, true));
        }
        if (wchNo > 0) {
            totalResult.setWchNo(StrUtil.fill(StrUtil.toString(wchNo), '0', 3, true));
        }
        if (inftNo > 0) {
            totalResult.setInftNo(StrUtil.fill(StrUtil.toString(inftNo), '0', 2, true));
        }
        if (chldNo > 0) {
            totalResult.setChldNo(StrUtil.fill(StrUtil.toString(chldNo), '0', 3, true));
        }
        totalResult.setSbBagNo(StrUtil.fill(StrUtil.toString(sbBagNo), '0', 4, true));
        totalResult.setResList(resList);
        totalResult.setCkiList(ckiList);
        totalResult.setCetList(cetList);
    }

    /**
     * Title: orderLayoutStr
     * Description: 对布局字符串重新排序，按原始布局的顺序处理
     *
     * @param origin origin
     * @param target target
     * @return 对布局字符串重新排序，按原始布局的顺序处理
     * <AUTHOR>
     * @date 2022/7/28 17:30
     */
    @Override
    public String orderLayoutStr(String origin, String target) {
        String[] originSplit = origin.split("\\d+");
        String[] targetSplit = target.split("");
        List<String> originList = Arrays.asList(originSplit);
        List<String> targetList = Arrays.asList(targetSplit);
        StringBuilder sb = new StringBuilder();
        for (String t : targetList) {
            if (StrUtil.isNotEmpty(sb) && t.matches("[A-Z]")) {
                sb.append(",");
            }
            sb.append(t);
        }
        List<String> tmpList = Arrays.asList(sb.toString().split(","));
        sb.setLength(0);
        for (String k : originList) {
            for (String l : tmpList) {
                if (l.startsWith(k)) {
                    sb.append(l);
                    break;
                }
            }
        }
        return sb.toString();
    }
}
