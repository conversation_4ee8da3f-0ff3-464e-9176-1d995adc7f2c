package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.BcDto;
import com.swcares.eterm.dcs.cki.obj.dto.PsgInfoDto;

import java.util.List;

/**
 * 改变值机状态的rmi接口
 *
 * <AUTHOR>
 */
public interface IBcService {

    /**
     * dealCmd
     *
     * @param bcDtoList
     * @return dealCmd
     * @throws UnifiedResultException 统一异常
     */
    List<PsgInfoDto> dealCmd(List<BcDto> bcDtoList) throws UnifiedResultException;

    /**
     * constructPsgInfoByBnNumber
     *
     * @param bcDto bcDto
     * @return retrievePsgInfoByType1
     * @throws UnifiedResultException 统一异常
     */
    List<PsgInfoDto> constructPsgInfoByBnNumber(BcDto bcDto) throws UnifiedResultException;

    /**
     * parseCmd
     *
     * @param cmd cmd
     * @return parseCmd
     * @throws UnifiedResultException 统一异常
     */
    List<BcDto> parseCmd(String cmd) throws UnifiedResultException;

    /**
     * retrievePrintJsonData
     *
     * @param psgInfoDto psgInfoDto
     * @return retrievePrintJsonData
     */
    String retrievePrintJsonData(PsgInfoDto psgInfoDto);

    /**
     * printJsonData
     *
     * @param printJsonData printJsonData
     */
    void printJsonData(String printJsonData);

    /**
     * 业务处理
     *
     * @param cmd      指令
     * @return 业务处理
     * @throws UnifiedResultException 统一异常
     */
    String dealItBussiness(String cmd) throws UnifiedResultException;
}
