package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.cache.MemoryData;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.PaResultDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IHbpuService {

    /**
     * 指令处理
     *
     * @param cmd 指令
     * @return 指令处理
     * @throws UnifiedResultException 统一异常
     */
    List<PaResultDto> dealCmd(String cmd, MemoryData memoryData) throws UnifiedResultException;

    void printBag(List<PaResultDto> resultDtos);

    void savePuForPrint(List<PaResultDto> resultDtos);

}
