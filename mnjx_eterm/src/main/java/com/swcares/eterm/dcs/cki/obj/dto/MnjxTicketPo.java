/*
 * Copyright (c) 2021. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.swcares.eterm.dcs.cki.obj.dto;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 原来TICKET表的映射，现在将多张表的数据汇聚在这
 *
 * <AUTHOR>
 */
@Data
@ToString
public class MnjxTicketPo {

    /**
     * 起飞城市
     */
    private String offAirport;

    private String fltNo;

    private String fltDate;

    /**
     * 旅客ID
     */
    private String psgId;

    /**
     * 航节飞行计划号
     */
    private String sectionPlanId;

    /**
     * 行程段ID
     */
    private String segmentId;

    private BigDecimal bagNumber = new BigDecimal(0);

    private BigDecimal bagWeight = new BigDecimal(0);

    private String seat;

    private String psgType;

    private String ckiNo;

    private String aboardNo;

    private String bcNumber;

    private String cabinClass;

    /**
     * 子舱位
     */
    private String cabin;

    /**
     * 升降舱位
     */
    private String beforeCabin;

    /**
     * 升降座位
     */
    private String upSeat;

    /**
     * 升舱或者降舱
     */
    private String upgn;

    /**
     * 升舱降舱备注
     */
    private String upgRmk;

    /**
     * 座位状态
     */
    private String seatStatus;
}
