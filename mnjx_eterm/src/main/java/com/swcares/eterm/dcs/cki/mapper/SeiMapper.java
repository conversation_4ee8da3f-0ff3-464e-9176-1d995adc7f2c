package com.swcares.eterm.dcs.cki.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@SuppressWarnings("AlibabaClassMustHaveAuthor")
public interface SeiMapper {

    /**
     * 获取飞机号和版本信息
     *
     * @param airCode airCode
     * @param type    type
     * @param version version
     * @param layout  layout
     * @return 获取飞机号和版本信息
     */
    List<Map<String, Object>> retrievePlaneType(@Param("airCode") String airCode, @Param("type") String type, @Param("version") String version, @Param("layout") String layout);
}
