package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.ObjectUtils;
import com.swcares.core.util.ReUtils;
import com.swcares.entity.*;
import com.swcares.eterm.dcs.cki.obj.vo.CslVo;
import com.swcares.eterm.dcs.cki.service.ICslService;
import com.swcares.service.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * CSL业务处理
 *
 * <AUTHOR>
 */
@Service
public class CslServiceImpl implements ICslService {

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IMnjxTcardSectionService iMnjxTcardSectionService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    private static final Pattern CSL_PATTERN_ONE = Pattern.compile("(CSL|csl)(:|\\s)([A-Za-z-0-9]{2})");

    private static final Pattern CSL_PATTERN_TWO = Pattern.compile("(CSL|csl)(:|\\s)([A-Za-z-0-9]{5,7})(,|/)?(((\\d{2}[A-Z]{3}(\\d{2})?)|([+-.])))?");

    private static final Pattern CSL_PATTERN_THR = Pattern.compile("(CSL|csl)(:|\\s)");

    @Override
    public CslVo handle(String cmd) throws UnifiedResultException {
        // 输入航司报错TOO MANY，航司不对报错FLT NBR
        if (ReUtils.isMatch(CSL_PATTERN_ONE, cmd)) {
            List<String> allGroups = ReUtils.getAllGroups(CSL_PATTERN_ONE, cmd);
            String code = allGroups.get(2);
            MnjxAirline one = iMnjxAirlineService.lambdaQuery()
                    .eq(MnjxAirline::getAirlineCode, code)
                    .one();
            if (ObjectUtils.isNotEmpty(one)) {
                throw new UnifiedResultException(Constant.TOO_MANY_ITEMS_OF_RESULT);
            } else {
                throw new UnifiedResultException(Constant.FLT_NBR);
            }
        }
        // 正常处理
        else if (ReUtils.isMatch(CSL_PATTERN_TWO, cmd)) {
            return handleCsl(cmd);
        }
        // 没有输入参数报错
        else if (ReUtils.isMatch(CSL_PATTERN_THR, cmd)) {
            throw new UnifiedResultException(Constant.OPTION);
        } else {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
    }

    private CslVo handleCsl(String cmd) throws UnifiedResultException {
        CslVo cslVo = new CslVo();
        List<String> allGroups = ReUtils.getAllGroups(CSL_PATTERN_TWO, cmd);
        String flightNo = allGroups.get(3);
        MnjxFlight carrierFlight;
        MnjxFlight tempFlight = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightNo, flightNo)
                .one();
        List<MnjxFlight> shareFlightList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(tempFlight)) {
            // 承运航班值为空，说明查询的就是承运航班，需要去查该航班的共享航班
            if (StrUtil.isEmpty(tempFlight.getCarrierFlight())) {
                shareFlightList.addAll(iMnjxFlightService.lambdaQuery()
                        .eq(MnjxFlight::getCarrierFlight, tempFlight.getFlightNo())
                        .eq(MnjxFlight::getShareState, Constant.STR_ONE)
                        .list());
                if (CollUtil.isEmpty(shareFlightList)) {
                    throw new UnifiedResultException(Constant.FLT_NBR);
                }
                carrierFlight = tempFlight;
            }
            // 查询的是共享航班
            else {
                // 没有启用报错
                if (Constant.STR_ZERO.equals(tempFlight.getShareState())) {
                    throw new UnifiedResultException(Constant.FLT_NBR);
                }
                shareFlightList.add(tempFlight);
                // 获取承运航班
                carrierFlight = iMnjxFlightService.lambdaQuery()
                        .eq(MnjxFlight::getFlightNo, tempFlight.getCarrierFlight())
                        .one();
            }
        } else {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        MnjxTcard carrierTcard = iMnjxTcardService.lambdaQuery()
                .eq(MnjxTcard::getFlightId, carrierFlight.getFlightId())
                .one();
        cslVo.setCarrierFlightNo(carrierFlight.getFlightNo());
        cslVo.getShareFlightNoList().addAll(shareFlightList.stream().map(MnjxFlight::getFlightNo).collect(Collectors.toList()));
        List<MnjxTcardSection> carrierTcardSections = iMnjxTcardSectionService.lambdaQuery()
                .eq(MnjxTcardSection::getTcardId, carrierTcard.getTcardId())
                .orderByAsc(MnjxTcardSection::getSectionNo)
                .list();
        MnjxAirport orgAirport = iMnjxAirportService.getById(carrierTcardSections.get(0).getAirportId());
        MnjxAirport dstAirport = iMnjxAirportService.getById(carrierTcardSections.get(carrierTcardSections.size() - 1).getAirportId());
        cslVo.setDep(orgAirport.getAirportCode());
        cslVo.setArr(dstAirport.getAirportCode());
        Date startDate = carrierTcard.getStartDate();
        Date endDate = carrierTcard.getEndDate();
        String flightDate = ObjectUtils.isNotEmpty(allGroups.get(5)) ? DateUtils.com2ymd(allGroups.get(5)) : "";
        if (StrUtil.isNotEmpty(flightDate)) {
            DateTime inputDate = DateUtil.parseDate(flightDate);
            if (DateUtil.compare(startDate, inputDate) > 0 || DateUtil.compare(endDate, inputDate) < 0) {
                throw new UnifiedResultException(Constant.DATE);
            }
        }
        cslVo.setDateStart(DateUtils.ymd2Com(DateUtil.format(startDate, "yyyy-MM-dd")));
        cslVo.setDateEnd(DateUtils.ymd2Com(DateUtil.format(endDate, "yyyy-MM-dd")));
        cslVo.setCycle(carrierTcard.getCycle());
        return cslVo;
    }
}
