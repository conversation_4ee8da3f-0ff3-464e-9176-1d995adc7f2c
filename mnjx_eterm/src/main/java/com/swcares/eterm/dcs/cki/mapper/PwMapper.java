package com.swcares.eterm.dcs.cki.mapper;

import com.swcares.entity.MnjxPnrNmTicket;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface PwMapper {

    /**
     * queryNmTicket
     *
     * @param pnrNmId pnrNmId
     * @param fltNo   fltNo
     * @param fltDate fltDate
     * @return queryNmTicket
     */
    MnjxPnrNmTicket queryNmTicket(@Param("pnrNmId") String pnrNmId, @Param("fltNo") String fltNo, @Param("fltDate") String fltDate);

    /**
     * queryXnTicket
     *
     * @param pnrNmId pnrNmId
     * @param fltNo   fltNo
     * @param fltDate fltDate
     * @return queryXnTicket
     */
    MnjxPnrNmTicket queryXnTicket(@Param("pnrNmId") String pnrNmId, @Param("fltNo") String fltNo, @Param("fltDate") String fltDate);
}
