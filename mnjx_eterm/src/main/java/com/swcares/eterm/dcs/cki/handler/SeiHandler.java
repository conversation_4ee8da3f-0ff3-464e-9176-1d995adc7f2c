
package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.service.ISeiService;

import javax.annotation.Resource;

/**
 * 新增指令，处理SEI\SEM指令
 *
 * <AUTHOR>
 */
@OperateType(action = "SEI", needPaging = false, fullScreen = true)
public class SeiHandler implements Handler {

    @Resource
    private ISeiService iSeiService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        return iSeiService.dealCmd(cmd);
    }
}
