package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.SpmlDto;
import com.swcares.eterm.dcs.cki.obj.dto.SpmlResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/25 15:42
 */
public interface ISpmlService {

    SpmlDto parseCmd(String cmd) throws UnifiedResultException;

    List<SpmlResult> handle(SpmlDto spmlDto) throws UnifiedResultException;
}
