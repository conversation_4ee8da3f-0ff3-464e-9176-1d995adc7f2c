package com.swcares.eterm.dcs.fdc.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.fdc.service.IIfService;

import javax.annotation.Resource;

/**
 * 指令IF验证
 * （1）IF：航班号/日期/PNL 初始化航班
 * （2）如果不加 PNL 选项，则作三遍 IF：航班号/日期。
 *
 * <AUTHOR>
 *  2022-06-14 15:53:13
 */
@OperateType(action = "IF",shorthand = true)
public class IfHandler implements Handler {
    @Resource
    private IIfService iIfService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        return iIfService.handle(cmd);
    }

}
