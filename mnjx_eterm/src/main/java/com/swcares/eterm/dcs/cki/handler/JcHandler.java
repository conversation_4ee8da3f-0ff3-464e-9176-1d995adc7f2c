package com.swcares.eterm.dcs.cki.handler;

import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.DateUtils;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.HbpaCmdDto;
import com.swcares.eterm.dcs.cki.obj.dto.PaResultDto;
import com.swcares.eterm.dcs.cki.service.IJcService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 接受候补旅客的解析类
 *
 * <AUTHOR>
 */
@OperateType(action = "JC", shorthand = true, template = "/dcs/cki/hbjc.jf")
public class JcHandler implements Handler {

    @Resource
    private IJcService iJcService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        List<HbpaCmdDto> hbpaCmdDtoList = iJcService.parseJc(cmd);
        List<PaResultDto> paResultDtos = iJcService.handle(MemoryDataUtils.getMemoryData(), hbpaCmdDtoList);

        iJcService.print(paResultDtos);

        PaResultDto paResultDto = paResultDtos.get(0);
        String flightDate = paResultDto.getFlightDate().contains("-") ? DateUtils.ymd2Com(paResultDto.getFlightDate()) : paResultDto.getFlightDate();
        // 重新构建返回时第一行指令的显示
        String returnCmd = cmd.split(":")[1];
        if (paResultDto.isCap()) {
            String[] split = returnCmd.split("/");
            returnCmd = StrUtil.format("{}/{}/{}", paResultDto.getFlightNo(), flightDate.substring(0, 5), split[split.length - 1]);
        }
        if (hbpaCmdDtoList.stream().noneMatch(p -> StrUtil.isNotEmpty(p.getFlightNo()))) {
            returnCmd = StrUtil.format("{}/{}{}", paResultDto.getFlightNo(), paResultDto.getFlightDate(), returnCmd);
        }
        unifiedResult.setArgs(new Object[]{StrUtil.format("{}:{}", cmd.split(":")[0], returnCmd)});
        unifiedResult.setResults(paResultDtos.toArray());
        return unifiedResult;
    }
}
