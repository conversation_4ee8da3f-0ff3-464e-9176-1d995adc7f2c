package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.JcrDto;
import com.swcares.eterm.dcs.cki.obj.dto.PaResultDto;
import com.swcares.eterm.dcs.cki.service.IJcrService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * JCS 指令的解析类
 *
 * <AUTHOR>
 */
@OperateType(action = "JCR", template = "dcs/cki/jcr.jf")
@Slf4j
public class JcrHandler implements Handler {

    @Resource
    private IJcrService iJcrService;

    /**
     * @param cmd 指令
     */
    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        JcrDto jcrDto = iJcrService.parseCmd(cmd);
        PaResultDto paResultDto = iJcrService.handle(jcrDto);
        unifiedResult.setArgs(cmd.split(":"));
        unifiedResult.setResults(new Object[]{paResultDto});
        return unifiedResult;
    }
}