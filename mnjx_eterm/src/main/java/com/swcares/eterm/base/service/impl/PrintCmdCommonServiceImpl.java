package com.swcares.eterm.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxOffice;
import com.swcares.entity.MnjxPrinter;
import com.swcares.entity.MnjxSi;
import com.swcares.eterm.base.mapper.PrintCmdCommonMapper;
import com.swcares.eterm.base.service.IPrintCmdCommonService;
import com.swcares.service.IMnjxOfficeService;
import com.swcares.service.IMnjxPrinterService;
import com.swcares.service.IMnjxSiService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 打票机指令通用方法
 *
 * <AUTHOR>
 */
@Service
public class PrintCmdCommonServiceImpl implements IPrintCmdCommonService {

    @Resource
    private PrintCmdCommonMapper printCmdCommonMapper;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    /**
     * 判断打票机编号是否存在
     *
     * @param printNo 打票机编号
     * @return 判断打票机编号是否存在
     */
    @Override
    public boolean retrievePrintNoIsExist(String printNo) {
        MemoryData md = MemoryDataUtils.getMemoryData();
        MnjxOffice mnjxOffice = iMnjxOfficeService.lambdaQuery()
                .eq(MnjxOffice::getOfficeNo, md.getMnjxOffice().getOfficeNo())
                .one();
        List<MnjxPrinter> printerList = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getPrinterNo, printNo)
                .eq(MnjxPrinter::getOfficeId, mnjxOffice.getOfficeId())
                .list();
        return CollUtil.isNotEmpty(printerList);
    }

    /**
     * 是否被控制
     *
     * @param printNo 打票机号
     * @return 是否被控制
     */
    private boolean retrievePrintIsControl(String printNo) {
        return this.retrieveControlSiIsPrintControlSi(printNo);
    }

    /**
     * 判断控制终端是不是这台打票机的控制终端
     *
     * @param printNo 打票机号
     * @return 判断控制终端是不是这台打票机的控制终端
     * <p>
     * 1.判断该工作号是否与其他打票机建立控制了
     * 2.判断打票机是否已经建立控制
     */
    @Override
    public boolean retrieveControlSiIsPrintControlSi(String printNo) {
        MemoryData md = MemoryDataUtils.getMemoryData();
        MnjxOffice mnjxOffice = iMnjxOfficeService.lambdaQuery()
                .eq(MnjxOffice::getOfficeNo, md.getMnjxOffice().getOfficeNo())
                .one();
        MnjxSi mnjxSi = iMnjxSiService.lambdaQuery()
                .eq(MnjxSi::getSiNo, md.getMemoryDataContainer().getActiveMemoryDataCabinet().getMnjxSi().getSiNo())
                .eq(MnjxSi::getOfficeId, mnjxOffice.getOfficeId())
                .one();
        // 设计一个工作号只能控制一台打票机，因此直接返回一个对象
        MnjxPrinter printer = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getSiId, mnjxSi.getSiId())
                .eq(StrUtil.isNotEmpty(printNo), MnjxPrinter::getPrinterNo, printNo)
                .eq(MnjxPrinter::getOfficeId, mnjxOffice.getOfficeId())
                .one();
        return ObjectUtil.isNotEmpty(printer);
    }

    /**
     * 打票机输入状态
     *
     * @param printerNo 打票机号
     * @return 打票机输入状态
     */
    @Override
    public boolean retrieveInputStatusIsActive(String printerNo) {
        MemoryData md = MemoryDataUtils.getMemoryData();
        MnjxOffice mnjxOffice = iMnjxOfficeService.lambdaQuery()
                .eq(MnjxOffice::getOfficeNo, md.getMnjxOffice().getOfficeNo())
                .one();
        MnjxSi mnjxSi = iMnjxSiService.lambdaQuery()
                .eq(MnjxSi::getSiNo, md.getMemoryDataContainer().getActiveMemoryDataCabinet().getMnjxSi().getSiNo())
                .eq(MnjxSi::getOfficeId, mnjxOffice.getOfficeId())
                .one();
        MnjxPrinter printer = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getSiId, mnjxSi.getSiId())
                .eq(MnjxPrinter::getPrinterNo, printerNo)
                .eq(MnjxPrinter::getOfficeId, mnjxOffice.getOfficeId())
                .one();
        return Constant.ACTIVE.equalsIgnoreCase(printer.getInputStatus());
    }

    /**
     * 打票机输出状态
     *
     * @param printNo 打票机号
     * @return 打票机输出状态
     */
    @Override
    public boolean retrieveOutputStatusIsActive(String printNo) {
        MemoryData md = MemoryDataUtils.getMemoryData();
        MnjxOffice mnjxOffice = iMnjxOfficeService.lambdaQuery()
                .eq(MnjxOffice::getOfficeNo, md.getMnjxOffice().getOfficeNo())
                .one();
        MnjxSi mnjxSi = iMnjxSiService.lambdaQuery()
                .eq(MnjxSi::getSiNo, md.getMemoryDataContainer().getActiveMemoryDataCabinet().getMnjxSi().getSiNo())
                .eq(MnjxSi::getOfficeId, mnjxOffice.getOfficeId())
                .one();
        MnjxPrinter printer = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getSiId, mnjxSi.getSiId())
                .eq(MnjxPrinter::getPrinterNo, printNo)
                .eq(MnjxPrinter::getOfficeId, mnjxOffice.getOfficeId())
                .one();
        return Constant.ACTIVE.equalsIgnoreCase(printer.getOutputStatus());
    }

    /**
     * 判断打票机是否上票
     *
     * @param printNo 打票机序号
     * @return 判断打票机是否上票
     */
    @Override
    public boolean retrieveTicketLimitsIsLoaded(String printNo) {
        MemoryData md = MemoryDataUtils.getMemoryData();
        List<Map<String, Object>> list = printCmdCommonMapper.retrieveTicketLimitsIsLoaded(printNo, md.getMnjxOffice().getOfficeNo());
        return !list.isEmpty();
    }

    /**
     * 查看出票状态
     *
     * @param printNo 打票机号
     * @return 查看出票状态
     */
    @Override
    public boolean retrieveOutTicketStatus(String printNo) {
        return false;
    }

    /**
     * 查看工作状态
     *
     * @return 查看工作状态
     * @
     */
    private boolean retrieveWorkStatus() {
        return false;
    }

    /**
     * 当需要控制打票机时 所提供的验证方法
     *
     * @param tmp 打票机序号
     * @return 当需要控制打票机时 所提供的验证方法
     */
    @Override
    public Map<String, Object> retrieveCheckDeviceStatus(String tmp) {
        Map<String, Object> map = new HashMap<>(1024);
        // 打票机序号不正确
        if (!this.retrievePrintNoIsExist(tmp)) {
            map.put("error", Constant.DEVICE);
            return map;
        }
        // 检查打票机是否被控制,未控制提示"AUTHORITY"
        if (this.retrievePrintIsControl(tmp)) {
            map.put("error", Constant.AUTHORITY);
            return map;
        }
        // 检查控制终端是不是这台打票机的控制终端,不是提示"AUTHORITY"
        if (!this.retrieveControlSiIsPrintControlSi(tmp)) {
            map.put("error", Constant.AUTHORITY);
            return map;
        }
        // 检查打票机的工作状态、输入状态和输出状态,为INACTIVE提示"FUNCTION"
        if (!this.retrieveWorkStatus() || !this.retrieveInputStatusIsActive(tmp) || !this.retrieveOutputStatusIsActive(tmp)) {
            map.put("error", "FUNCTION");
            return map;
        }
        return map;
    }

    /**
     * 判断票号输入
     *
     * @param tmp 指令
     * @return 判断票号输入
     * @
     */
    @Override
    public boolean retrieveTicketNoIsRight(String[] tmp) {
        return false;
    }

    /**
     * 根据票号工作号查询数据
     *
     * @param tickets 票数据
     * @param param   参数
     * @return 根据票号工作号查询数据
     * @
     */
    @Override
    public boolean retrieveTicketNoIsRight(List<String> tickets, Map<String, Object> param) {
        return false;
    }

    /**
     * 判断客票状态
     *
     * @param ticketNo 客票号
     * @return 判断客票状态
     */
    @Override
    public String retrieveTicketStatus(String ticketNo) {
        return null;
    }

    /**
     * 批量查询客票状态并将状态处理成字符串返回
     *
     * @param tickets   票号
     * @param officeNum officeNum
     * @return 批量查询客票状态并将状态处理成字符串返回
     */
    @Override
    public String retrieveTicketStatus(List<String> tickets, String officeNum) {
        List<String> ids = new ArrayList<>();
        List<Map<String, Object>> list1 = retrieveByNmIds(ids, officeNum);
        return CollectionUtils.isEmpty(list1) ? null : "".trim();
    }

    /**
     * 根据旅客id集合和office号查询数据
     *
     * @param ids       ids
     * @param officeNum officeNum
     * @return 根据旅客id集合和office号查询数据
     */
    @Override
    public List<Map<String, Object>> retrieveByNmIds(List<String> ids, String officeNum) {
        return null;
    }

    @Override
    public boolean deviceNoIsRight(String[] tmp) {
        return false;
    }

    @Override
    public boolean etdzTime(String[] tmp) {
        return false;
    }

    /**
     * 查询值机状态
     *
     * @param ticketNo 票号
     */
    @Override
    public boolean retrieveCki(String ticketNo) {
        return false;
    }

    /**
     * 查询值机状态
     *
     * @param tickets 票号集合
     */
    @Override
    public boolean retrieveCkiByTickets(List<String> tickets) {
        return false;
    }

    /**
     * 修改值机状态
     *
     * @param ticketNo 票号
     */
    @Override
    public void updateCki(String ticketNo, String status) {
    }


}
