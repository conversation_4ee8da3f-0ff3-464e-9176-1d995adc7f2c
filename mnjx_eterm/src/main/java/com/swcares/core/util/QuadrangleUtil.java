package com.swcares.core.util;

import cn.hutool.core.util.NumberUtil;

import java.awt.*;

/**
 * <AUTHOR>
 */
public class QuadrangleUtil {

    /**
     * 判断p是否在abcd组成的四边形内
     *
     * @param a a
     * @param b b
     * @param c c
     * @param d d
     * @param p p
     * @return 如果p在四边形内返回true, 否则返回false.
     */
    public static boolean pInQuadrangle(Point a, Point b, Point c, Point d, Point p) {
        double dTriangle = triangleArea(a, b, p) + triangleArea(b, c, p) + triangleArea(c, d, p) + triangleArea(d, a, p);
        double dQuadrangle = triangleArea(a, b, c) + triangleArea(c, d, a);
        return NumberUtil.sub(dTriangle, dQuadrangle) == 0;
    }

    /**
     * 返回三个点组成三角形的面积
     *
     * @param a a
     * @param b b
     * @param c c
     * @return 返回三个点组成三角形的面积
     */
    private static double triangleArea(Point a, Point b, Point c) {
        return Math.abs((a.x * b.y + b.x * c.y + c.x * a.y - b.x * a.y - c.x * b.y - a.x * c.y) / 2.0D);
    }
}
