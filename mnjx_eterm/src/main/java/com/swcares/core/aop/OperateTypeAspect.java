package com.swcares.core.aop;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateEngine;
import com.swcares.core.cache.*;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.*;
import com.swcares.entity.MnjxLevel;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.cmn.service.IAuthService;
import com.swcares.service.IPnrCommandService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Spring缺省使用AspectJ切入点语法
 * 判断当前要执行的指令是否在对应级别中
 *
 * <AUTHOR>
 * <p>
 * aop的名称：
 * 切面（Aspect）：一个关注点的模块化。以注解@Aspect的形式放在类上方，声明一个切面。
 * 连接点（Joinpoint）：在程序执行过程中某个特定的点，比如某方法调用的时候或者处理异常的时候都可以是连接点。
 * 通知（Advice）：通知增强，需要完成的工作叫做通知，就是你写的业务逻辑中需要比如事务、日志等先定义好，然后需要的地方再去用。
 * 主要包括5个注解：Before，After，AfterReturning，AfterThrowing，Around。
 * \@Before：在切点方法之前执行。
 * \@After：在切点方法之后执行
 * \@AfterReturning：切点方法返回后执行
 * \@AfterThrowing：切点方法抛异常执行
 * \@Around：属于环绕增强，能控制切点执行前，执行后，用这个注解后，程序抛异常，会影响@AfterThrowing这个注解 切点（Pointcut）：其实就是筛选出的连接点，匹配连接点的断言，一个类中的所有方法都是连接点，但又不全需要，会筛选出某些作为连接点做为切点。如果说通知定义了切面的动作或者执行时机的话，切点则定义了执行的地点。切入点表达式如何和连接点匹配是AOP的核心：Spring缺省使用AspectJ切入点语法。
 * 引入（Introduction）：在不改变一个现有类代码的情况下，为该类添加属性和方法,可以在无需修改现有类的前提下，让它们具有新的行为和状态。其实就是把切面（也就是新方法属性：通知定义的）用到目标类中去。
 * 目标对象（Target Object）：被一个或者多个切面所通知的对象。也被称做被通知（adviced）对象。既然Spring AOP是通过运行时代理实现的，这个对象永远是一个被代理（proxied）对象。
 * AOP代理（AOP Proxy）：AOP框架创建的对象，用来实现切面契约（例如通知方法执行等等）。在Spring中，AOP代理可以是JDK动态代理或者CGLIB代理。
 * 织入（Weaving）：把切面连接到其它的应用程序类型或者对象上，并创建一个被通知的对象。这些可以在编译时（例如使用AspectJ编译器），类加载时和运行时完成。Spring和其他纯Java AOP框架一样，在运行时完成织入。
 * <p>
 * 1、横切关注点
 * 对哪些方法进行拦截，拦截后怎么处理，这些关注点称之为横切关注点
 * 2、切面（aspect）->（通知+切点）
 * 类是对物体特征的抽象，切面就是对横切关注点的抽象。
 * 通知+切点
 * 意思就是所有要被应用到增强（advice）代码的地方。(包括方法的方位信息)
 * 3、连接点（joinpoint）->（被拦截的方法）
 * 被拦截到的点，因为Spring只支持方法类型的连接点，所以在Spring中连接点指的就是被拦截的方法，实际上连接点还可以是字段或者构造器
 * 4、切入点（pointcut）->（描述拦截那些方法的部分）
 * 对连接点进行拦截的定义
 * 5、通知（advice）->（拦截后执行自己业务逻辑的那些部分）
 * 所谓通知指的就是指拦截到连接点之后要执行的代码，通知分为前置、后置、异常、最终、环绕通知五类
 * 这玩意也叫 增强
 * <p>
 * 参考：<a href="https://www.cnblogs.com/niaobulashi/p/springboot-aop.html">...</a>
 */
@Slf4j
@Aspect
@Order(0)
@Component
public class OperateTypeAspect {
    /**
     * 正常情况下指令是用空格和冒号分割的,如果是直接带数字则是简写格式
     * 指令可能是字母,eg:pat av 等等
     * 指令可能是@ \
     * 指令与参数的分隔符可能是：冒号，空格，斜杠（不常用，目前知道的有xn在用）
     */
    private static final Pattern FMT_COMMAND = Pattern.compile("^(\\d*)([A-Za-z@\\\\]+)(?:[:/]?|\\s+|\\d+)");

    /**
     * 返回的数据结构
     */
    private static final Pattern FMT_RESULT = Pattern.compile(".*\\r?\\n?");

    private static final String NUMBER_START_1 = "^\\d{1}\\s{1}[\\d\\D]*";

    private static final String NUMBER_START_2 = "^\\d{2}\\s{1}[\\d\\D]*";

    private static final String NUMBER_START_3 = "^\\d{3}\\s{1}[\\d\\D]*";

    private static final String NUMBER_START_123 = "^((\\d{1}\\s{1})|(\\d{2}\\s{1})|(\\d{3}\\s{1}))[\\d\\D]*";

    @Resource
    private TemplateEngine templateEngine;

    @Resource
    private IAuthService iAuthService;

    @Resource
    private IPnrCommandService iPnrCommandService;

    /**
     * 这个是对类中的方法使用的注解做切面
     * private final static String ANNOTATION_POINTCUT = "@annotation(com.swcares.core.type.OperateType)";
     * 方法的切面
     */
    private static final String METHOD_POINTCUT = "execution(* com.swcares.eterm..*Handler.handle(..))";

    /**
     * 定义连接点（Joinpoint）
     * 一个类中的所有方法都是连接点，但又不全需要，会筛选出某些作为连接点做为切点（Pointcut）。
     * 这里我们使用注解的形式
     * 当然，我们也可以通过切点表达式直接指定需要拦截的package,需要拦截的class 以及 method
     * 切点表达式:   execution(...)
     * <p>
     * 参考：<a href="https://blog.csdn.net/zhengchao1991/article/details/53391244">...</a>
     * <p>
     * \@Pointcut(value = "@annotation(com.swcares.old.core.type.OperateType)")
     */

    @Pointcut(value = METHOD_POINTCUT)
    public void typePointCut() {
    }

    /**
     * 定义通知
     * <p>
     * \@Before：在切点方法之前执行。
     * \@After：在切点方法之后执行
     * \@AfterReturning：切点方法返回后执行
     * \@AfterThrowing：切点方法抛异常执行
     * \@Around：属于环绕增强，能控制切点执行前，执行后，用这个注解后，程序抛异常，会影响@AfterThrowing这个注解
     */
    @Around("typePointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        // 获得代理对象
        Object pointTarget = point.getTarget();
        // 代理对象字节码
        Class<?> pointTargetClass = pointTarget.getClass();
        // 获得自定义注解
        OperateType operateType = pointTargetClass.getAnnotation(OperateType.class);

        // 需要判断
        Object[] pointArgs = point.getArgs();
        if (pointTarget instanceof Handler) {
            this.authority(operateType, pointArgs);
        }
        // 执行目标方法
        Object result = point.proceed();
        this.changePageMarkPosition(operateType.action(), pointArgs);
        //数据类型判定,是否自动渲染
        if (result instanceof UnifiedResult) {
            result = this.render(operateType, (UnifiedResult) result);
        }

        // 当返回的是字符串时，我们要经过特殊处理
        if (result instanceof String) {
            result = this.stagingPaging(operateType, result);
        }
        return result;
    }

    /**
     * Title: changePageMarkPosition
     * Description: 根据指令不同，翻页+-号标识位置修改
     *
     * @param action    action
     * @param pointArgs pointArgs
     * <AUTHOR>
     * @date 2022/10/21 9:11
     */
    private void changePageMarkPosition(String action, Object[] pointArgs) {
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MemoryDataHandlerResult memoryDataHandlerResult = memoryData.getMemoryDataHandlerResult();
        // 对非翻页指令进行存储，对非BS RA AL SU BT指令进行存储，对应座位图的连续修改
        if (!StrUtil.equalsAny(action, Constant.CMD_PN, Constant.CMD_PB, Constant.CMD_PF, Constant.CMD_PL, Constant.CMD_PG, "BS", "RA", "AL", "SU", "BT")) {
            memoryDataHandlerResult.setLastAction(action);
        }
        // 当前指令是PD或翻页前指令是PD
        if (Constant.CMD_PD.equals(action) || Constant.CMD_PD.equals(memoryDataHandlerResult.getLastAction())) {
            // PD指令参数为SBY，存储SBY参数
            if (StrUtil.toString(pointArgs[0]).contains(Constant.PARAM_SBY) || StrUtil.isNotEmpty(memoryDataHandlerResult.getPdParamSby())) {
                memoryDataHandlerResult.setPdParamSby(Constant.PARAM_SBY);
            }
            // PD指令参数为ABC，设置翻页符号标识位置修改
            else if (StrUtil.toString(pointArgs[0]).contains(Constant.PARAM_ABC) || memoryDataHandlerResult.isChangePageMarkPosition()) {
                memoryDataHandlerResult.setChangePageMarkPosition(true);
            }
            // 其他PD参数不修改翻页符号标识位置
            else {
                memoryDataHandlerResult.setPdParamSby(null);
                memoryDataHandlerResult.setChangePageMarkPosition(false);
            }
        }
        // SY指令B参数设置翻页符号标识位置修改
        else if ((StrUtil.equals(Constant.CMD_SY, action) && (pointArgs[0].toString().split(StrUtils.COLON).length > 1 && Constant.PARAM_B.equals(pointArgs[0].toString().split(StrUtils.COLON)[1])))
                || (Constant.CMD_SY.equals(memoryDataHandlerResult.getLastAction()) && memoryDataHandlerResult.isChangePageMarkPosition())) {
            memoryDataHandlerResult.setChangePageMarkPosition(true);
        }
        // BAB BDB ETKD指令设置翻页符号标识位置修改
        else if (StrUtils.equalsAny(action, Constant.CMD_BAB, Constant.CMD_BDB)
                || (StrUtil.equalsAny(memoryDataHandlerResult.getLastAction(), Constant.CMD_BAB, Constant.CMD_BDB) && memoryDataHandlerResult.isChangePageMarkPosition())) {
            memoryDataHandlerResult.setChangePageMarkPosition(true);
        }
        else {
            memoryDataHandlerResult.setPdParamSby(null);
            memoryDataHandlerResult.setChangePageMarkPosition(false);
        }
    }

    /**
     * 讲返回的统一对象根据模板渲染为字符串
     *
     * @param operateType   注解对象
     * @param unifiedResult 统一返回对象
     * @return 讲返回的统一对象根据模板渲染为字符串
     */
    private String render(OperateType operateType, UnifiedResult unifiedResult) {
        // 这个地方自动将指令封装进对象（这个地方有点点不符合程序的规定）
        unifiedResult.setAction(operateType.action().toUpperCase());
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        // 下面都是模板的代码
        String templateName = memoryData.getMemoryDataHandlerResult().getLastAction().equals(Constant.AV) ? "crs/av.jf" : operateType.template();
        Template template = templateEngine.getTemplate(templateName);
        Map<String, Object> unifiedResultMap = BeanUtil.beanToMap(unifiedResult);
        String renderResult = template.render(unifiedResultMap);
        renderResult = renderResult.replace("\r\n\r\n", "\r\n");
        renderResult = renderResult.replace("\n\n", "\n");
        renderResult = StrUtil.trimEnd(renderResult);
        return renderResult;
    }

    /**
     * 数据保存分页
     *
     * @param operateType 操作类型
     * @param result      数据保存分页
     * @return 数据保存分页
     */
    private Object stagingPaging(OperateType operateType, Object result) {
        MemoryDataHandlerResult memoryDataHandlerResult = MemoryDataUtils.getMemoryData().getMemoryDataHandlerResult();
        // 是否缓存，默认情况下都是要缓存的
        boolean staging = operateType.staging();
        String action = operateType.action();
        // 除翻页外的指令
        if (!StrUtils.equalsAny(operateType.action(), Constant.CMD_PN, Constant.CMD_PB, Constant.CMD_PF, Constant.CMD_PL, Constant.CMD_PG)) {
            // 如果设置了默认全屏，初始化页码数修改
            if (!operateType.canChangePageSize() && operateType.fullScreen()) {
                memoryDataHandlerResult.setCurrentPages(ListUtil.toList(0, 1));
                if (Constant.CMD_ETKD.equals(action)) {
                    memoryDataHandlerResult.setPageType(16);
                } else {
                    memoryDataHandlerResult.setPageType(Constant.FULL_SCREEN);
                }
            }
            // 当前页码数,默认半屏显示一页
            else {
                memoryDataHandlerResult.setPageType(Constant.HALF_SCREEN);
                memoryDataHandlerResult.setCurrentPages(ListUtil.toList(BigDecimal.ZERO.intValue()));
            }
            memoryDataHandlerResult.setCanChangePageSize(operateType.canChangePageSize());
            memoryDataHandlerResult.setFullScreen(operateType.fullScreen());
        }
        // 指令结果要暂存，则上一个空间的内容会被当前指令结果重新填充
        if (staging || (Constant.AV.equals(memoryDataHandlerResult.getLastAction()) && memoryDataHandlerResult.isDateChange())) {
            this.setMemoryDataHandlerResult(memoryDataHandlerResult, ObjectUtils.toString(result));
        }
        if (operateType.needPaging()) {
            // 分页
            result = this.paging(memoryDataHandlerResult, action);
        }
        return result;
    }

    /**
     * 权限认证
     */
    private void authority(OperateType operateType, Object[] args) throws UnifiedResultException {
        // 获得是否需要判断
        if (operateType.predicate()) {
            // 获取当前活动的空间用户
            MemoryDataCabinet memoryDataCabinet = MemoryDataUtils.getMemoryData().getMemoryDataContainer().getActiveMemoryDataCabinet();
            if (ObjectUtils.isNotNull(memoryDataCabinet)) {
                boolean limited = this.permission(StrUtil.toString(args[0]), memoryDataCabinet.getMnjxLevel());
                log.info("权限认证结果：{}", limited);
                if (!limited) {
                    throw new UnifiedResultException(Constant.AUTHORITY);
                }
            } else {
                throw new UnifiedResultException(Constant.SI);
            }
        }
    }

    /**
     * 生成分页的缓存对象
     *
     * @param handlerResult 原始字符串
     */
    private void setMemoryDataHandlerResult(MemoryDataHandlerResult memoryDataHandlerResult, String handlerResult) {
        // 返回的原始字符串
        memoryDataHandlerResult.setResult(handlerResult);
        // 查询的结果行数
        List<String> recordList = ReUtils.findAll(FMT_RESULT, handlerResult, 0);
        // 对查询结果行数处理，如果最后的一行或几行是空的或是只有换行符，则需要去掉这些行
        int subEndIndex = 0;
        for (int i = recordList.size() - 1; i > 0; i--) {
            if (StrUtil.isEmpty(recordList.get(i).trim())) {
                subEndIndex = i;
            } else {
                break;
            }
        }
        if (subEndIndex > 0) {
            recordList = recordList.subList(0, subEndIndex);
        }
        memoryDataHandlerResult.setRecords(recordList);
        if (Constant.PNR_COMMAND_LIST.contains(memoryDataHandlerResult.getLastAction())) {
            memoryDataHandlerResult.setPnrRecallRecords(recordList);
        }
        // 总页码数(初始值),根据输入的指令不同，引起要显示的行数不通，则总页面数会动态变化。ETKD特殊处理按16行分页
        memoryDataHandlerResult.setTotalPage(NumberUtils.parseInt(NumberUtils.ceilDiv(memoryDataHandlerResult.getRecords().size(), Constant.CMD_ETKD.equals(memoryDataHandlerResult.getLastAction()) ? 16 : Constant.HALF_SCREEN)));
    }

    /**
     * 查看是否有权限做该指令
     *
     * @param cmd cmd
     * @return 查看是否有权限做该指令
     */
    private boolean permission(String cmd, MnjxLevel mnjxLevel) {
        // 获取指令
        String instruction = ReUtil.get(FMT_COMMAND, cmd, 2);
        // 此级别下是否有指令
        return iAuthService.retrieveAuth(instruction, mnjxLevel.getLevelCode());
    }

    /**
     * 分页功能
     * 通过回车换行符来确定位置，
     * 半屏是12个回程换行
     * 全屏是24个回程换行
     *
     * @param memoryDataHandlerResult 要分页的对象
     * @return 分页后的字符串
     */
    private Object paging(MemoryDataHandlerResult memoryDataHandlerResult, String action) {
        // 获取页码
        List<Integer> currentPages = memoryDataHandlerResult.getCurrentPages();
        // 要回显的数据列表
        List<String> tarData1 = new ArrayList<>(currentPages.size());
        StringBuilder resultBuffer;
        // 获取分页数据（对页码排序,页码去重，并且排序：小的页码在前）
        if (!Constant.NEED_SORT_CODE.contains(memoryDataHandlerResult.getLastAction())) {
            // other
            currentPages.stream()
                    .distinct()
                    .sorted(Comparator.comparingInt(o -> o))
                    .forEach(
                            currentPage -> tarData1.addAll(ListUtil.page(currentPage, Constant.CMD_ETKD.equals(memoryDataHandlerResult.getLastAction()) ? 16 : Constant.HALF_SCREEN, memoryDataHandlerResult.getRecords()))
                    );
            // 将子集重新组装成字符串
            resultBuffer = new StringBuilder(String.join(StrUtils.EMPTY, tarData1));
        } else {
            if (!currentPages.contains(0)) {
                tarData1.add(memoryDataHandlerResult.getRecords().get(0));
            }
            // AV  SK  DS
            currentPages.stream()
                    .distinct()
                    .sorted(Comparator.comparingInt(o -> o))
                    .forEach(currentPage -> this.intoList(tarData1, currentPage, memoryDataHandlerResult.getRecords()));

            List<Integer> avVos = new ArrayList<>();
            // 修改序号
            List<String> tarData2 = this.changeIndex(tarData1, avVos);
            // 将子集重新组装成字符串
            resultBuffer = new StringBuilder(String.join(StrUtils.EMPTY, tarData2));

            //每页数据放入缓存
            MemoryDataPnr memoryDataPnr = MemoryDataUtils.getMemoryData().getMemoryDataPnr();
            iPnrCommandService.avCurrentPageCacheDate(memoryDataPnr, avVos);
        }

        if (resultBuffer.toString().endsWith(StrUtils.CROSS) || resultBuffer.toString().endsWith(StrUtils.DASHED)) {
            return resultBuffer.toString();
        }
        StringBuilder res = new StringBuilder();
        /*
            后面或前面还有未展示完的数据，需要加一行+-号，提示翻页
            如果在只有2页的情况下且指令配置展示的全屏，不添加+-号
         */
        boolean needAddPagingMark = (currentPages.get(currentPages.size() - 1) < memoryDataHandlerResult.getTotalPage() - 1 || currentPages.get(0) > 0) && !(memoryDataHandlerResult.getTotalPage() == 2 && currentPages.size() == 2 && currentPages.get(0) == 0 && currentPages.get(1) == 1);
        if (needAddPagingMark) {
            if (currentPages.get(0) == 0) {
                if (memoryDataHandlerResult.isChangePageMarkPosition()) {
                    String s = StrUtil.replaceLast(resultBuffer.toString(), StrUtils.CRLF, "");
                    String[] split = s.split(StrUtils.CRLF);
                    String lastStr = split[split.length - 1];
                    lastStr = StrUtil.fill(lastStr, ' ', 79, false);
                    lastStr = StrUtil.format("{}+", lastStr);
                    for (int i = 0; i < split.length - 1; i++) {
                        res.append(split[i]);
                        res.append(StrUtils.CRLF);
                    }
                    res.append(lastStr);
                } else {
                    if (!StrUtil.endWith(resultBuffer.toString(), StrUtils.CRLF)) {
                        resultBuffer.append(StrUtils.CRLF);
                    }
                    if (action.endsWith(Constant.CMD_PW)) {
                        resultBuffer.append("                                                                             +");
                    } else {
                        resultBuffer.append("                                                                +");
                    }
                }
            } else if (currentPages.get(currentPages.size() - 1) == (memoryDataHandlerResult.getTotalPage() - 1)) {
                if (memoryDataHandlerResult.isChangePageMarkPosition() || (StrUtil.equalsAny(Constant.CMD_HBPA, action, memoryDataHandlerResult.getLastAction()))) {
                    String s = StrUtil.replaceLast(resultBuffer.toString(), StrUtils.CRLF, "");
                    String[] split = s.split(StrUtils.CRLF);
                    res.append(StrUtil.format("{}-", StrUtil.fill("", ' ', 79, false)));
                    res.append(StrUtils.CRLF);
                    for (String value : split) {
                        res.append(value);
                        res.append(StrUtils.CRLF);
                    }
                    log.info(res.toString());
                    log.info("换行符数量：{}", (res + "1").split(StrUtils.CRLF).length);
                } else {
                    if (!StrUtil.endWith(resultBuffer.toString(), StrUtils.CRLF)) {
                        resultBuffer.append(StrUtils.CRLF);
                    }
                    resultBuffer.append("                                                                -");
                }
            } else {
                if (memoryDataHandlerResult.isChangePageMarkPosition()) {
                    String s = StrUtil.replaceLast(resultBuffer.toString(), StrUtils.CRLF, "");
                    String[] split = s.split(StrUtils.CRLF);
                    String lastStr = split[split.length - 1];
                    lastStr = StrUtil.fill(lastStr, ' ', 78, false);
                    lastStr = StrUtil.format("{}-+", lastStr);
                    for (int i = 0; i < split.length - 1; i++) {
                        res.append(split[i]);
                        res.append(StrUtils.CRLF);
                    }
                    res.append(lastStr);
                } else {
                    if (!StrUtil.endWith(resultBuffer.toString(), StrUtils.CRLF)) {
                        resultBuffer.append(StrUtils.CRLF);
                    }
                    resultBuffer.append("                                                                +");
                }
            }
        }
        return StrUtil.isEmpty(res.toString()) ? resultBuffer.toString() : res.toString();
    }

    /**
     * 修改序号
     *
     * @param tarDatas 数据List
     * @param avVos    保留原序号
     * @return 修改序号后数据List
     */
    List<String> changeIndex(List<String> tarDatas, List<Integer> avVos) {
        List<String> backList = new ArrayList<>(tarDatas.size());
        int a = 1;
        for (String str : tarDatas) {
            if (StrUtil.isEmpty(str)) {
                backList.add(str);
                continue;
            }
            if (str.matches(NUMBER_START_123)) {
                if (str.matches(NUMBER_START_1)) {
                    avVos.add(new Integer(str.substring(0, 1)));
                    if (a > 9) {
                        backList.add(a + "" + str.substring(2));
                    } else {
                        backList.add(a + "" + str.substring(1));
                    }
                }
                if (str.matches(NUMBER_START_2)) {
                    avVos.add(new Integer(str.substring(0, 2)));
                    if (a > 9) {
                        backList.add(a + "" + str.substring(2));
                    } else {
                        backList.add(a + " " + str.substring(2));
                    }
                }
                if (str.matches(NUMBER_START_3)) {
                    avVos.add(new Integer(str.substring(0, 3)));
                    if (a > 9) {
                        if (a > 99) {
                            backList.add(a + "" + str.substring(3));
                        } else {
                            backList.add(a + " " + str.substring(3));
                        }
                    } else {
                        backList.add(a + "  " + str.substring(3));
                    }
                }
                a++;
            } else {
                backList.add(str);
            }
        }
        return backList;
    }

    /**
     * AV SK 按需求分页
     *
     * @param tarData1 结果List
     * @param page     页码
     * @param records  原始数据List
     */
    private void intoList(List<String> tarData1, Integer page, List<String> records) {
        Integer nowPagePoint = page * Constant.HALF_SCREEN;
        this.preAddList(tarData1, nowPagePoint, nowPagePoint + Constant.HALF_SCREEN, records);
        this.aftAddList(tarData1, nowPagePoint + Constant.HALF_SCREEN, records);
    }

    /**
     * AV SK 后置数据
     *
     * @param tarData1 结果List
     * @param point    本页开始位置
     * @param records  原始数据List
     */
    private void aftAddList(List<String> tarData1, Integer point, List<String> records) {
        if (records.size() > point && !records.get(point).matches(NUMBER_START_123)) {
            tarData1.add(records.get(point));
            this.aftAddList(tarData1, point + 1, records);
        }
    }

    /**
     * AV SK 分页数据
     *
     * @param tarData1 结果List
     * @param point    本页开始位置
     * @param aftPoint 本页结束位置
     * @param records  原始数据List
     */
    private void preAddList(List<String> tarData1, Integer point, Integer aftPoint, List<String> records) {
        if (point >= records.size()) {
            return;
        }
        if (point.equals(0) || records.get(point).matches(NUMBER_START_123)) {
            if (records.size() <= aftPoint) {
                tarData1.addAll(records.subList(point, records.size()));
            } else {
                tarData1.addAll(records.subList(point, aftPoint));
            }
        } else {
            if (point.equals(aftPoint)) {
                return;
            }
            this.preAddList(tarData1, point + 1, aftPoint, records);
        }
    }
}
