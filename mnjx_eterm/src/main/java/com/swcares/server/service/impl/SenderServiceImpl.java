package com.swcares.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateException;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataFt;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.*;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.base.service.IDataUpdateService;
import com.swcares.server.service.ISenderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SenderServiceImpl implements ISenderService {

    /**
     * 数据更新
     */
    private static final Pattern REG_UPDATE_DATA = Pattern.compile("^\\d+/\\d.*$");

    /**
     * 记录注入的有@OperateType注解的Handler
     */
    @Resource
    private Map<OperateType, Handler> schedulingMap;

    @Resource
    private IDataUpdateService iDataUpdateService;

    /**
     * 发送指令 通过此方法发送指令，能对指令进行一些处理 包括记录返回结果，调用一次性封口等方式来节约流量
     * 发送指令 可以处理多条并发
     * <p>
     * 单指令： 输入的内容是一次性输入，里面就包含一条指令（数据可能换行）
     * 多指令： 输入的内容是一次性输入，但是数据包含多条指令（每条指令都可能数据会换行）
     * 数字开头的输入： 数字开头的输入不是指令，是对连接通信中的内存数据进行更新，使用序号标注要更新的序号数据
     */
    @Override
    public String send(String inContent) {
        log.debug(StrUtil.format("{}收到用户发送的数据:({})"), LocalDate.now(), inContent);
        // 指令的执行结果(如果是多个指令循环执行，那最后一个指令的执行结果才是最后的返回结果)
        String executeResult = StrUtils.EMPTY;
        // 如果是rr指令，反过来
        String[] split = inContent.split("\r\n");
        String splitMark;
        if (inContent.contains("\r\n")) {
            splitMark = "\r\n";
        } else {
            splitMark = "\r";
        }
        if (split.length == 1) {
            split = inContent.split("\r");
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < split.length; i++) {
            if (ReUtil.isMatch("^\\d{1,3}RR$", split[i])) {
                sb.append(new StringBuffer(split[i]).reverse());
            } else if (ReUtils.isMatch(REG_UPDATE_DATA, split[i])) {
                //如果当前指令是以数字开头的，说明是更新数据,这个要将指令补全
                sb.append(iDataUpdateService.updateNm(split[i]));
            } else {
                sb.append(split[i]);
            }
            if (i < split.length - 1) {
                sb.append(splitMark);
            }
        }
        inContent = sb.toString();
        // 存放解析后的指令列表
        List<StringBuffer> cmds = new ArrayList<>();
        if (StrUtil.startWithIgnoreCase(inContent, Constant.CMD_BF) && StrUtil.endWithAnyIgnoreCase(inContent, StrUtil.BACKSLASH, Constant.BF_IG)) {
            cmds.add(new StringBuffer(inContent));
        } else {
            cmds = this.preparseInstruction(inContent);
        }
        // 循环执行指令
        // 执行前记录memoryData，如果是订座多指令执行中某条指令异常，需要恢复成执行前的memoryData
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MemoryDataPnr lastMemoryDataPnr;
        MemoryDataPnr lastTmpMemoryDataPnr;
        MemoryDataFt lastMemoryDataFt = new MemoryDataFt();
        try {
            lastMemoryDataPnr = memoryData.getMemoryDataPnr().deepCopy();
            lastTmpMemoryDataPnr = memoryData.getTmpMemoryDataPnr().deepCopy();
        } catch (Exception e) {
            log.error("发生异常：{}", e.getMessage());
            return Constant.FORMAT_ERROR;
        }
        BeanUtil.copyProperties(memoryData.getMemoryDataFt(), lastMemoryDataFt);
        for (StringBuffer cmd : cmds) {
            try {
                executeResult = this.executeCmd(cmd.toString());
            } catch (UnifiedResultException unifiedResultException) {
                log.error("发生错误：{}", unifiedResultException.getMessage());
                if (!StrUtil.equalsAny(unifiedResultException.getMessage(), Constant.PNR_MAX_TIME, Constant.NO_PNR, Constant.FORMAT_ERROR)) {
                    try {
                        memoryData.setMemoryDataPnr(lastMemoryDataPnr.deepCopy());
                        memoryData.setTmpMemoryDataPnr(lastTmpMemoryDataPnr.deepCopy());
                    } catch (Exception e) {
                        log.error("发生异常：{}", e.getMessage());
                        return Constant.FORMAT_ERROR;
                    }
                    BeanUtil.copyProperties(lastMemoryDataFt, memoryData.getMemoryDataFt());
                }
                return unifiedResultException.getMessage();
            }
        }
        // 返回最后一次执行的结果
        return executeResult;
    }

    /**
     * 执行指令
     *
     * @param cmd 单条指令
     * @return 每个执行的执行结果
     */
    @Transactional(rollbackFor = Exception.class)
    public String executeCmd(String cmd) throws UnifiedResultException {
        // 要返回的结果
        String executeResult;
        // 获取处理器，我们找到最短的那个，目前的方案
        OperateType operateType = this.getValidOperateType(cmd);
        // 找到了简写的指令处理器
        if (ObjectUtils.isNotNull(operateType)) {
            // 如果支持简写那就做简写的格式化处理
            int actionLen = operateType.action().length();
            // 支持简写
            if (operateType.shorthand()) {
                //
                if (cmd.trim().length() != actionLen) {
                    // 指令的下一个字符，
                    String nextStr = cmd.substring(actionLen, actionLen + 1);
                    // 标识位
                    List<String> flags = CollUtils.toList(StrUtils.SPACE, StrUtils.SLASH, StrUtils.COLON);
                    // 下一个字符是分隔符，这整个一起来替换
                    if (flags.contains(nextStr)) {
                        cmd = StrUtils.replaceFirst(cmd, StrUtils.format("{}{}", operateType.action(), nextStr), StrUtils.format("{}{}", operateType.action(), StrUtils.COLON));
                    }
                    // 如果不是分隔符，则在指令后后面添加冒号
                    else {
                        cmd = StrUtils.insertStrToLocation(cmd, StrUtils.COLON, actionLen);
                        MemoryData memoryData = MemoryDataUtils.getMemoryData();
                        memoryData.setShorthand(true);
                    }
                }
            }
            // 如果不支持简写，就要看后面的是否正常了
            else {
                // 当前指令后面有参数就将所有的隔离符统一为冒号，
                if (cmd.trim().length() != actionLen) {
                    // 指令的下一个字符，
                    String nextStr = cmd.substring(actionLen, actionLen + 1);
                    // 标识位
                    List<String> flags = CollUtils.toList(StrUtils.SPACE, StrUtils.SLASH, StrUtils.COLON);
                    // 说明是一个指令
                    if (flags.contains(nextStr)) {
                        cmd = StrUtils.replaceFirst(cmd, StrUtils.format("{}{}", operateType.action(), nextStr), StrUtils.format("{}{}", operateType.action(), StrUtils.COLON));
                    }
                    // 格式不正常的情况下，直接抛出格式异常
                    else {
                        throw new UnifiedResultException(Constant.FORMAT);
                    }
                }
            }
            //
            Handler handler = this.schedulingMap.get(operateType);
            // 找到了指令的处理类
            if (ObjectUtil.isNotNull(handler)) {
                try {
                    // 执行具体的操作
                    Object rt = handler.handle(cmd);
                    // 操作结果返回
                    executeResult = (rt instanceof String) ? (String) rt : StrUtils.toString(rt);
                } catch (UnifiedResultException | DateException unifiedException) {
                    String message = unifiedException.getMessage();
                    log.info("指令手动返回的业务异常信息：{}", message);
                    throw new UnifiedResultException(message);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("发生程序错误:{}", e.getMessage());
                    throw new UnifiedResultException(Constant.FORMAT_ERROR);
                }
            } else {
                throw new UnifiedResultException(Constant.FORMAT);
            }
        } else {
            log.error("出现这个错误是前端传入了简写的指令，但是后端的对应指令处理器没有设置简写属性");
            throw new UnifiedResultException(Constant.FORMAT);
        }
        // 返回结果
        return executeResult;
    }

    /**
     * 将输入内容转换为指令列表
     * 如果输入的内容没有指令，则将输入的内容原封不动的返回
     *
     * @param inContent 输入项内容
     * @return 指令列表
     */
    private List<StringBuffer> preparseInstruction(String inContent) {
        // 存放解析后的指令列表
        List<StringBuffer> cmds = new Vector<>();
        // 输入的内容项都是字符串内容,按照换行符进行分割成多行数据
        List<String> divideContents = StrUtils.split(inContent, StrUtils.C_CR);
        // 以行为单位来找指令
        for (String divideContent : divideContents) {
            // 判断当前行是否为指令
            OperateType operateType = this.getValidOperateType(divideContent);
            // 如果是指令就新起
            if (ObjectUtils.isNotNull(operateType)) {
                cmds.add(new StringBuffer(divideContent));
            }
            // 如果不是就在列表的最后那个补充字符
            else {
                this.appendLastInstruction(cmds, divideContent);
            }
        }
        return cmds;
    }

    /**
     * 判断当前数据行是否为一个指令
     *
     * @param divideContent 当前数据行
     * @return 是否为指令
     */
    private OperateType getValidOperateType(String divideContent) {
        // 找出所有的
        List<OperateType> operateTypes = this.schedulingMap.keySet().stream()
                // 找到这个输入项可能对应了哪些指令
                .filter(operateType -> StrUtils.startWithIgnoreCase(divideContent, operateType.action()))
                // 根据指令的长度排一下序,长的优先匹配
                .sorted((o1, o2) -> o2.action().length() - o1.action().length())
                .collect(Collectors.toList());
        // 找出当前行内容的可能对应的指令
        OperateType validOperateType = null;
        for (OperateType operateType : operateTypes) {
            int actionLen = operateType.action().trim().length();
            // 要先判断是否支持简写,默认都不支持简写，如果支持简写，后面跟任何内容都可以
            if (operateType.shorthand()) {
                validOperateType = operateType;
                break;
            }
            /*
            如果不支持简写,有2种情况
            如果指令后面还有内容，说明在指令后面的必须存在（空格，冒号，斜线）这3种的一种情况，并且至少出现一次，如果没有出现，说明此行是上一行的数据，不是一个指令
            如果指令后面没有内容了，就什么都不管了
            */
            else {
                // 这个代表后面没有其他内容了
                if (divideContent.trim().length() == actionLen) {
                    validOperateType = operateType;
                    break;
                } else {
                    // 如果长度不相同，说明还存在其他内容,指令的下一个字符，
                    String nextStr = divideContent.substring(actionLen, actionLen + 1);
                    // 标识位
                    List<String> flags = CollUtils.toList(StrUtils.SPACE, StrUtils.SLASH, StrUtils.COLON);
                    // 如果存在这个标识位，说明时一个新的指令
                    if (flags.contains(nextStr)) {
                        validOperateType = operateType;
                        break;
                    }
                }
            }
        }
        return validOperateType;
    }

    /**
     * 为最后一个元素内容追加内容
     *
     * @param cmds          指令列表集合
     * @param divideContent 要添加的元素
     */
    private void appendLastInstruction(List<StringBuffer> cmds, String divideContent) {
        // 当没有指令时，那就直接将当前添加进去
        if (CollUtils.isEmpty(cmds)) {
            cmds.add(new StringBuffer(divideContent));
        }
        // 当存在行数据时，找到最后一行数据，并且为最后一行数据连接最新的行
        else {
            int lastIndex = cmds.size() - 1;
            // 获取列表中的最后一个内容
            StringBuffer cmdTmp = cmds.get(lastIndex);
            // 因为要重新添加，所有最后一行内容要先移除，将需要的内容补充完整后在重新添加
            cmds.remove(lastIndex);
            // 补充内容
            cmdTmp.append(StrUtils.C_CR).append(divideContent);
            cmds.add(cmdTmp);
        }
    }
}
