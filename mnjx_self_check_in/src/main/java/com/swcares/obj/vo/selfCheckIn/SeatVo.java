package com.swcares.obj.vo.selfCheckIn;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/11 13:41
 */
@Data
@ApiModel("座位详细数据")
public class SeatVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "座位号")
    private String seatNo;

    @ApiModelProperty(value = "坐标区域填充字符串")
    private String coordinateString;

    @ApiModelProperty(value = "座位状态")
    private String seatStatus;

    @ApiModelProperty(value = "座位行号")
    private String seatRow;

    @ApiModelProperty(value = "座位列号")
    private String seatColumn;

    @ApiModelProperty(value = "座位X坐标")
    private Integer seatX;

    @ApiModelProperty(value = "座位Y坐标")
    private Integer seatY;
}
