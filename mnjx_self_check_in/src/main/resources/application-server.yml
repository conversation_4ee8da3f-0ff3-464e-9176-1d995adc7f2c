#=============================端口===========================#
server:
  port: 8359
  tomcat:
    max-connections: 200
    max-threads: 300
    uri-encoding: utf-8
    max-swallow-size: 5MB
  servlet:
    context-path: /mnjx_self_check_in
#======================spring配置===============================#
spring:
  # 项目名称
  application:
    name: mnjx_self_check_in
  # 个人理解为禁用了开发属性(默认为true，生产我们设置为false)
  devtools:
    add-properties: false