package com.swcares;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;

/**
 * <AUTHOR> yaa
 */
@Slf4j
@SpringBootApplication
public class PrinterStarter {
    public static void main(String[] args) {
        SpringApplicationBuilder springApplicationBuilder = new SpringApplicationBuilder(PrinterStarter.class);
        springApplicationBuilder.headless(false).run(args);
//        SpringApplication.run(PrinterStarter.class, args);
        log.info(StrUtil.format("swagger api 地址:  http://{}:{}/printer/swagger-ui/index.html", "localhost", "18350"));
    }
}
