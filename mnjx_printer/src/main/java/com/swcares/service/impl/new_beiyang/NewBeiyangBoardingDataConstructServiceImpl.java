package com.swcares.service.impl.new_beiyang;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.snbc.sdk.LabelPrinter;
import com.swcares.core.print.PrinterConfiguration;
import com.swcares.core.utils.DateUtils;
import com.swcares.service.INewBeiyangBoardingDataConstructService;
import com.swcares.vo.BoardingVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 根据打印耗材不同，构建不同的登机牌数据
 *
 * <AUTHOR>
 * @date 2023/5/22
 */
@Slf4j
@Service
public class NewBeiyangBoardingDataConstructServiceImpl implements INewBeiyangBoardingDataConstructService {

    @Resource
    private PrinterConfiguration printerConfiguration;

    @Override
    public void constructBoarding(LabelPrinter labelPrinter, BoardingVo boardingVo) {
        log.info("读取到登机牌配置：{}", printerConfiguration.getNewBeiyangBoarding());
        switch (printerConfiguration.getNewBeiyangBoarding()) {
            case "上海民航职业技术学院":
                this.constructShanghai(labelPrinter, boardingVo);
                break;
            case "中国航信":
                this.constructTravelsky(labelPrinter, boardingVo);
                break;
            case "海南航空":
                this.constructHainan(labelPrinter, boardingVo);
                break;
            case "南方航空":
                this.constructSouthern(labelPrinter, boardingVo);
                break;
            case "重庆海联":
                this.constructHailian(labelPrinter, boardingVo);
                break;
            default:
                break;
        }
    }

    /**
     * 重庆海联模板
     * 新北洋打印机需要调整打印的两个绿色旋钮，左右一个调整2，右边一个调整4，这连个是控制热敏纸的压力
     * 字体调试：数字越大字体越粗，这里部分字体调整为2，主要是避免有些地方显示不完整
     *
     * @param labelPrinter
     * @param boardingVo
     */
    private void constructHailian(LabelPrinter labelPrinter, BoardingVo boardingVo) {
        log.info("打印重庆海联航空登机牌");
        labelPrinter.SetLabelSize(82 * 8, 203 * 8);
        // 姓名标识
        labelPrinter.PrintTrueTypeText(60 * 8, 185 * 8, "0", 18, 24, "姓名", 90, 2);
        // 姓名拼音标识
        labelPrinter.PrintTrueTypeText(55 * 8, 185 * 8, "0", 16, 20, "Name", 90, 2);
        // 姓名
        labelPrinter.PrintTrueTypeText(60 * 8, 158 * 8, "2", 24, 24, boardingVo.getNameZh(), 90, 0);
        labelPrinter.PrintTrueTypeText(55 * 8, 153 * 8, "2", 24, 24, PinyinUtil.getPinyin(boardingVo.getNameZh(), StrUtil.EMPTY).toUpperCase(), 90, 0);

        // 航班号标识
        labelPrinter.PrintTrueTypeText(50 * 8, 180 * 8, "0", 18, 24, "航班号", 90, 2);
        labelPrinter.PrintTrueTypeText(45 * 8, 178 * 8, "0", 12, 20, "Flight no.", 90, 2);
        //航班号
        labelPrinter.PrintText(46 * 8, 175 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);

        // 目的地标识
        labelPrinter.PrintTrueTypeText(38 * 8, 181 * 8, "0", 18, 24, "目的地", 90, 2);
        labelPrinter.PrintTrueTypeText(33 * 8, 183 * 8, "0", 16, 20, "Dest.", 90, 2);
        // 目的地
        labelPrinter.PrintTrueTypeText(38 * 8, 163 * 8, "3", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(33 * 8, 175 * 8, "3", boardingVo.getTo(), 90, 0, 0, 0);

        // 日期标识
        labelPrinter.PrintTrueTypeText(28 * 8, 183 * 8, "0", 18, 24, "日 期", 90, 2);
        labelPrinter.PrintTrueTypeText(25 * 8, 185 * 8, "0", 16, 20, "Date", 90, 2);
        // 日期
        labelPrinter.PrintText(26 * 8, 175 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);

        // 座位号标识
        labelPrinter.PrintTrueTypeText(60 * 8, 125 * 8, "0", 18, 24, "座位号", 90, 2);
        labelPrinter.PrintTrueTypeText(55 * 8, 126 * 8, "0", 12, 20, "Seat no.", 90, 2);
        // 座位号
        labelPrinter.PrintText(57 * 8, 122 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);

        // 票序/舱位标识
        labelPrinter.PrintTrueTypeText(48 * 8, 114 * 8, "0", 18, 24, "票序 / 舱位", 90, 2);
        labelPrinter.PrintTrueTypeText(43 * 8, 116 * 8, "0", 16, 20, "BDNo./Class", 90, 2);
        // 票序（值机号）/舱位
        labelPrinter.PrintText(45 * 8, 112 * 8, "3", StrUtil.format("{}/{}", boardingVo.getBoardingNo(), boardingVo.getCabin()), 90, 0, 0, 0);

        // 登机口标识
        labelPrinter.PrintTrueTypeText(37 * 8, 126 * 8, "0", 18, 24, "登机口", 90, 2);
        labelPrinter.PrintTrueTypeText(34 * 8, 130 * 8, "0", 16, 20, "Gate", 90, 2);
        // 登机口
        labelPrinter.PrintText(35 * 8, 118 * 8, "3", boardingVo.getGate(), 90, 0, 0, 0);

        // 登机时间标识
        labelPrinter.PrintTrueTypeText(29 * 8, 121 * 8, "0", 18, 24, "登机时间", 90, 2);
        labelPrinter.PrintTrueTypeText(25 * 8, 124 * 8, "0", 16, 20, "BD Time", 90, 2);
        // 登机时间
        labelPrinter.PrintText(27 * 8, 118 * 8, "3", boardingVo.getBoardingTime(), 90, 0, 0, 0);

        // 票号
        labelPrinter.PrintText(18 * 8, 195 * 8, "3", boardingVo.getTicketNo(), 90, 0, 0, 0);
        // 条形码
        String barcode = boardingVo.getBarcode();
        String barcodeDate = barcode.substring(barcode.length() - 7);
        String ymd = DateUtils.com2ymd(barcodeDate).replace("-", "");
        barcode = StrUtil.format("{}{}", barcode.substring(0, 9), ymd);
        labelPrinter.PrintBarcode1D(12 * 8, 165 * 8, 1, 90, "B" + barcode, 10 * 8, 0, 3, 6);

        // --------------------小票联1-----------------------------
        // 航班号标识
        labelPrinter.PrintTrueTypeText(60 * 8, 32 * 8, "0", 10, 18, "航班号 Flight No.", 90, 2);
        // 航班号
        labelPrinter.PrintText(53 * 8, 54 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);

        // 座位号/票序标识
        labelPrinter.PrintTrueTypeText(44 * 8, 28 * 8, "0", 8, 16, "座位号Seat No./票序BD No.", 90, 2);
        // 座位号
        labelPrinter.PrintText(39 * 8, 55 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);
        // 票序（值机号）
        labelPrinter.PrintText(39 * 8, 44 * 8, "3", boardingVo.getBoardingNo(), 90, 0, 0, 0);

        // 目的地/日期标识
        labelPrinter.PrintTrueTypeText(31 * 8, 30 * 8, "0", 10, 18, "目的地Dest./日期Date", 90, 2);
        // 目的地
        labelPrinter.PrintTrueTypeText(25 * 8, 45 * 8, "2", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(20 * 8, 54 * 8, "2", boardingVo.getTo(), 90, 0, 0, 0);
        // 航班日期
        labelPrinter.PrintText(25 * 8, 45 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);

        // 姓名标识
        labelPrinter.PrintTrueTypeText(15 * 8, 35 * 8, "0", 16, 20, "姓名 Name", 90, 2);
        // 姓名
        labelPrinter.PrintTrueTypeText(10 * 8, 34 * 8, "2", 24, 24, boardingVo.getNameZh(), 90, 0);
        labelPrinter.PrintTrueTypeText(5 * 8, 34 * 8, "2", 16, 24, PinyinUtil.getPinyin(boardingVo.getNameZh(), StrUtil.EMPTY).toUpperCase(), 90, 0);

        // --------------------小票联2-----------------------------
        // 航班号标识
        labelPrinter.PrintTrueTypeText(60 * 8, 3 * 8, "0", 10, 18, "航班号 Flight No.", 90, 2);
        // 航班号
        labelPrinter.PrintText(53 * 8, 25 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);

        // 座位号/票序标识
        labelPrinter.PrintTrueTypeText(44 * 8, 1 * 8, "0", 8, 16, "座位号Seat No./票序BD No.", 90, 2);
        // 座位号
        labelPrinter.PrintText(39 * 8, 27 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);
        // 票序（值机号）
        labelPrinter.PrintText(39 * 8, 15 * 8, "3", boardingVo.getBoardingNo(), 90, 0, 0, 0);

        // 目的地/日期标识
        labelPrinter.PrintTrueTypeText(31 * 8, 2 * 8, "0", 10, 18, "目的地Dest./日期Date", 90, 2);
        // 目的地
        labelPrinter.PrintTrueTypeText(25 * 8, 18 * 8, "2", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(20 * 8, 25 * 8, "2", boardingVo.getTo(), 90, 0, 0, 0);
        // 航班日期
        labelPrinter.PrintText(25 * 8, 18 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);

        // 姓名标识
        labelPrinter.PrintTrueTypeText(15 * 8, 7 * 8, "0", 16, 20, "姓名 Name", 90, 2);
        // 姓名
        labelPrinter.PrintTrueTypeText(10 * 8, 8 * 8, "2", 24, 24, boardingVo.getNameZh(), 90, 0);
        labelPrinter.PrintTrueTypeText(5 * 8, 8 * 8, "2", 16, 24, PinyinUtil.getPinyin(boardingVo.getNameZh(), StrUtil.EMPTY).toUpperCase(), 90, 0);
    }

    /**
     * 海南航空模板-红色
     * 新北洋打印机需要调整打印的两个绿色旋钮，左右一个调整2，右边一个调整4，这连个是控制热敏纸的压力
     * 字体调试：数字越大字体越粗，这里部分字体调整为2，主要是避免有些地方显示不完整
     *
     * @param labelPrinter
     * @param boardingVo
     */
    private void constructHainan(LabelPrinter labelPrinter, BoardingVo boardingVo) {
        log.info("打印海南航空登机牌");
        labelPrinter.SetLabelSize(82 * 8, 203 * 8);
        // 姓名
        labelPrinter.PrintTrueTypeText(60 * 8, 163 * 8, "2", 24, 24, boardingVo.getNameZh(), 90, 0);
        labelPrinter.PrintTrueTypeText(55 * 8, 158 * 8, "2", 24, 24, PinyinUtil.getPinyin(boardingVo.getNameZh(), StrUtil.EMPTY).toUpperCase(), 90, 0);
        //航班号
        labelPrinter.PrintText(46 * 8, 175 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);
        // 目的地
        labelPrinter.PrintTrueTypeText(38 * 8, 163 * 8, "3", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(33 * 8, 175 * 8, "3", boardingVo.getTo(), 90, 0, 0, 0);
        // 日期
        labelPrinter.PrintText(28 * 8, 175 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);
        // 座位号
        labelPrinter.PrintText(55 * 8, 125 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);
        // 票序（值机号）/舱位
        labelPrinter.PrintText(44 * 8, 115 * 8, "3", StrUtil.format("{}/{}", boardingVo.getBoardingNo(), boardingVo.getCabin()), 90, 0, 0, 0);

        // 登机口
        labelPrinter.PrintText(35 * 8, 120 * 8, "3", boardingVo.getGate(), 90, 0, 0, 0);
        // 登机时间
        labelPrinter.PrintText(25 * 8, 120 * 8, "3", boardingVo.getBoardingTime(), 90, 0, 0, 0);

        // 票号
        labelPrinter.PrintText(13 * 8, 195 * 8, "3", boardingVo.getTicketNo(), 90, 0, 0, 0);
        // 条形码
        String barcode = boardingVo.getBarcode();
        String barcodeDate = barcode.substring(barcode.length() - 7);
        String ymd = DateUtils.com2ymd(barcodeDate).replace("-", "");
        barcode = StrUtil.format("{}{}", barcode.substring(0, 9), ymd);
        labelPrinter.PrintBarcode1D(7 * 8, 165 * 8, 1, 90, "B" + barcode, 10 * 8, 0, 3, 6);

        // --------------------小票联1-----------------------------
        // 航班号
        labelPrinter.PrintText(53 * 8, 58 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);
        // 座位号
        labelPrinter.PrintText(39 * 8, 60 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);
        // 票序（值机号）
        labelPrinter.PrintText(39 * 8, 48 * 8, "3", boardingVo.getBoardingNo(), 90, 0, 0, 0);
        // 目的地
        labelPrinter.PrintTrueTypeText(25 * 8, 53 * 8, "2", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(20 * 8, 62 * 8, "2", boardingVo.getTo(), 90, 0, 0, 0);
        // 航班日期
        labelPrinter.PrintText(25 * 8, 53 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);
        // 姓名
        labelPrinter.PrintTrueTypeText(10 * 8, 45 * 8, "2", 24, 24, boardingVo.getNameZh(), 90, 0);
        labelPrinter.PrintTrueTypeText(5 * 8, 38 * 8, "2", 24, 24, PinyinUtil.getPinyin(boardingVo.getNameZh(), StrUtil.EMPTY).toUpperCase(), 90, 0);

        // --------------------小票联2-----------------------------
        // 航班号
        labelPrinter.PrintText(53 * 8, 28 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);
        // 座位号
        labelPrinter.PrintText(39 * 8, 30 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);
        // 票序（值机号）
        labelPrinter.PrintText(39 * 8, 18 * 8, "3", boardingVo.getBoardingNo(), 90, 0, 0, 0);
        // 目的地
        labelPrinter.PrintTrueTypeText(25 * 8, 23 * 8, "2", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(20 * 8, 30 * 8, "2", boardingVo.getTo(), 90, 0, 0, 0);
        // 航班日期
        labelPrinter.PrintText(25 * 8, 18 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);
        // 姓名
        labelPrinter.PrintTrueTypeText(10 * 8, 10 * 8, "2", 24, 24, boardingVo.getNameZh(), 90, 0);
        labelPrinter.PrintTrueTypeText(5 * 8, 8 * 8, "2", 24, 24, PinyinUtil.getPinyin(boardingVo.getNameZh(), StrUtil.EMPTY).toUpperCase(), 90, 0);
    }

    /**
     * 上海登机牌模板
     *
     * @param labelPrinter
     * @param boardingVo
     */
    private void constructShanghai(LabelPrinter labelPrinter, BoardingVo boardingVo) {
        log.info("打印上海航空登机牌");
        //  x:82   y:203
        labelPrinter.SetLabelSize(82 * 8, 203 * 8);
        // 航班号
        labelPrinter.PrintText(55 * 8, 193 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);
        // 目的地
        labelPrinter.PrintTrueTypeText(45 * 8, 180 * 8, "3", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(41 * 8, 190 * 8, "3", boardingVo.getTo(), 90, 0, 0, 0);
        // 姓名
        labelPrinter.PrintTrueTypeText(33 * 8, 180 * 8, "3", 24, 24, boardingVo.getNameZh(), 90, 0);
        labelPrinter.PrintTrueTypeText(28 * 8, 180 * 8, "3", 24, 24, PinyinUtil.getPinyin(boardingVo.getNameZh(), StrUtil.EMPTY).toUpperCase(), 90, 0);
        // 航班日期
        labelPrinter.PrintText(55 * 8, 174 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);
        // 舱位
        labelPrinter.PrintText(55 * 8, 140 * 8, "3", boardingVo.getCabin(), 90, 0, 0, 0);
        // 始发地中文
        labelPrinter.PrintTrueTypeText(46 * 8, 135 * 8, "3", 24, 24, boardingVo.getFromZh(), 90, 0);
        // 始发地英文
        labelPrinter.PrintText(42 * 8, 145 * 8, "3", boardingVo.getFrom(), 90, 0, 0, 0);
        // 值机号
        labelPrinter.PrintText(55 * 8, 120 * 8, "3", boardingVo.getBoardingNo(), 90, 0, 0, 0);
        // 登机口
        labelPrinter.PrintText(44 * 8, 120 * 8, "3", boardingVo.getGate(), 90, 0, 0, 0);
        // 票号
        labelPrinter.PrintText(28 * 8, 112 * 8, "3", boardingVo.getTicketNo(), 90, 0, 0, 0);
        // 登机时间
        labelPrinter.PrintText(44 * 8, 90 * 8, "3", boardingVo.getBoardingTime(), 90, 0, 0, 0);
        // 座位号
        labelPrinter.PrintText(55 * 8, 88 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);
        // 条形码
        labelPrinter.PrintBarcode1D(15 * 8, 150 * 8, 1, 90, "B" + boardingVo.getBarcode(), 10 * 8, 0, 3, 6);
        //  小票联
        labelPrinter.PrintText(55 * 8, 55 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);
        labelPrinter.PrintText(43 * 8, 55 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);
        labelPrinter.PrintTrueTypeText(34 * 8, 45 * 8, "3", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(30 * 8, 55 * 8, "3", boardingVo.getTo(), 90, 0, 0, 0);
        labelPrinter.PrintText(20 * 8, 55 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);

        labelPrinter.PrintText(55 * 8, 25 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);
        labelPrinter.PrintText(43 * 8, 25 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);
        labelPrinter.PrintTrueTypeText(34 * 8, 15 * 8, "3", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(30 * 8, 25 * 8, "3", boardingVo.getTo(), 90, 0, 0, 0);
        labelPrinter.PrintText(20 * 8, 25 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);
    }

    /**
     * 航信登机牌模板
     *
     * @param labelPrinter
     * @param boardingVo
     */
    private void constructTravelsky(LabelPrinter labelPrinter, BoardingVo boardingVo) {
        log.info("打印中国航信登机牌");
        labelPrinter.SetLabelSize(82 * 8, 203 * 8);
        // 航班号
        labelPrinter.PrintText(55 * 8, 193 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);
        // 目的地
        labelPrinter.PrintTrueTypeText(45 * 8, 180 * 8, "3", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(41 * 8, 190 * 8, "3", boardingVo.getTo(), 90, 0, 0, 0);
        // 姓名
        labelPrinter.PrintTrueTypeText(33 * 8, 180 * 8, "3", 24, 24, boardingVo.getNameZh(), 90, 0);
        labelPrinter.PrintTrueTypeText(28 * 8, 180 * 8, "3", 24, 24, PinyinUtil.getPinyin(boardingVo.getNameZh(), StrUtil.EMPTY).toUpperCase(), 90, 0);
        // 航班日期
        labelPrinter.PrintText(55 * 8, 174 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);
        // 舱位
        labelPrinter.PrintText(55 * 8, 140 * 8, "3", boardingVo.getCabin(), 90, 0, 0, 0);
        // 出发地
        labelPrinter.PrintTrueTypeText(46 * 8, 135 * 8, "3", 24, 24, boardingVo.getFromZh(), 90, 0);
        labelPrinter.PrintText(42 * 8, 145 * 8, "3", boardingVo.getFrom(), 90, 0, 0, 0);
        // 登机序号
        labelPrinter.PrintText(55 * 8, 120 * 8, "3", boardingVo.getBoardingNo(), 90, 0, 0, 0);
        // 登机口
        labelPrinter.PrintText(44 * 8, 120 * 8, "3", boardingVo.getGate(), 90, 0, 0, 0);
        // 票号
        labelPrinter.PrintText(28 * 8, 112 * 8, "3", boardingVo.getTicketNo(), 90, 0, 0, 0);
        // 登机时间
        labelPrinter.PrintText(44 * 8, 90 * 8, "3", boardingVo.getBoardingTime(), 90, 0, 0, 0);
        // 座位号
        labelPrinter.PrintText(55 * 8, 88 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);
        // 条形码
        labelPrinter.PrintBarcode1D(15 * 8, 150 * 8, 1, 90, "B" + boardingVo.getBarcode(), 10 * 8, 0, 3, 6);

        // 小票联1
        labelPrinter.PrintText(55 * 8, 55 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);
        labelPrinter.PrintText(43 * 8, 55 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);
        labelPrinter.PrintTrueTypeText(34 * 8, 45 * 8, "3", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(30 * 8, 55 * 8, "3", boardingVo.getTo(), 90, 0, 0, 0);
        labelPrinter.PrintText(20 * 8, 55 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);
        // 小票联2
        labelPrinter.PrintText(55 * 8, 25 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);
        labelPrinter.PrintText(43 * 8, 25 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);
        labelPrinter.PrintTrueTypeText(34 * 8, 15 * 8, "3", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(30 * 8, 25 * 8, "3", boardingVo.getTo(), 90, 0, 0, 0);
        labelPrinter.PrintText(20 * 8, 25 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);
    }

    /**
     * 南方航空模板
     * 新北洋打印机需要调整打印的两个绿色旋钮，左右一个调整2，右边一个调整4，这连个是控制热敏纸的压力
     * 字体调试：数字越大字体越粗，这里部分字体调整为2，主要是避免有些地方显示不完整
     *
     * @param labelPrinter
     * @param boardingVo
     */
    private void constructSouthern(LabelPrinter labelPrinter, BoardingVo boardingVo) {
        log.info("打印南方航空登机牌");
        labelPrinter.SetLabelSize(82 * 8, 203 * 8);
        // ======================左侧小联==================
        //航班号
        labelPrinter.PrintText(58 * 8, 188 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);
        // 日期
        labelPrinter.PrintText(48 * 8, 188 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);
        // 目的地
        labelPrinter.PrintTrueTypeText(42 * 8, 173 * 8, "3", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(38 * 8, 183 * 8, "3", boardingVo.getTo(), 90, 0, 0, 0);
        // 序号
        labelPrinter.PrintText(31 * 8, 188 * 8, "3", boardingVo.getBoardingNo(), 90, 0, 0, 0);
        // 座位号
        labelPrinter.PrintText(23 * 8, 188 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);
        // 姓名
        labelPrinter.PrintTrueTypeText(16 * 8, 170 * 8, "3", 24, 24, boardingVo.getNameZh(), 90, 0);
        labelPrinter.PrintText(10 * 8, 188 * 8, "3", PinyinUtil.getPinyin(boardingVo.getNameZh(), StrUtil.EMPTY).toUpperCase(), 90, 0, 0, 0);

        // ===================中间==================
        //航班号
        labelPrinter.PrintText(58 * 8, 155 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);
        // 目的地
        labelPrinter.PrintTrueTypeText(50 * 8, 145 * 8, "3", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(46 * 8, 155 * 8, "3", boardingVo.getTo(), 90, 0, 0, 0);
        // 姓名
        labelPrinter.PrintTrueTypeText(40 * 8, 140 * 8, "3", 24, 24, boardingVo.getNameZh(), 90, 0);
        labelPrinter.PrintText(36 * 8, 160 * 8, "3", PinyinUtil.getPinyin(boardingVo.getNameZh(), StrUtil.EMPTY).toUpperCase(), 90, 0, 0, 0);
        // 日期
        labelPrinter.PrintText(58 * 8, 118 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);
        // 座位号
        labelPrinter.PrintText(48 * 8, 118 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);
        // 舱位
        labelPrinter.PrintText(58 * 8, 75 * 8, "3", boardingVo.getCabin(), 90, 0, 0, 0);
        // 登机口
        labelPrinter.PrintText(48 * 8, 75 * 8, "3", boardingVo.getGate(), 90, 0, 0, 0);
        // 登机时间
        labelPrinter.PrintText(40 * 8, 75 * 8, "3", boardingVo.getBoardingTime(), 90, 0, 0, 0);
        // 票号
        labelPrinter.PrintText(23 * 8, 160 * 8, "3", boardingVo.getTicketNo(), 90, 0, 0, 0);
        // 条形码
        String barcode = boardingVo.getBarcode();
        String barcodeDate = barcode.substring(barcode.length() - 7);
        String ymd = DateUtils.com2ymd(barcodeDate).replace("-", "");
        barcode = StrUtil.format("{}{}", barcode.substring(0, 9), ymd);
        labelPrinter.PrintBarcode1D(12 * 8, 160 * 8, 1, 90, "B" + barcode, 10 * 8, 0, 3, 6);

        // =================右侧小联================
        //航班号
        labelPrinter.PrintText(58 * 8, 43 * 8, "3", boardingVo.getFlightNo(), 90, 0, 0, 0);
        // 日期
        labelPrinter.PrintText(48 * 8, 43 * 8, "3", boardingVo.getFlightDate(), 90, 0, 0, 0);
        // 目的地
        labelPrinter.PrintTrueTypeText(40 * 8, 30 * 8, "3", 24, 24, boardingVo.getToZh(), 90, 0);
        labelPrinter.PrintText(36 * 8, 40 * 8, "3", boardingVo.getTo(), 90, 0, 0, 0);
        // 序号
        labelPrinter.PrintText(23 * 8, 20 * 8, "3", boardingVo.getBoardingNo(), 90, 0, 0, 0);
        // 座位号
        labelPrinter.PrintText(23 * 8, 45 * 8, "3", boardingVo.getSeatNo(), 90, 0, 0, 0);
        // 姓名
        labelPrinter.PrintTrueTypeText(32 * 8, 27 * 8, "3", 24, 24, boardingVo.getNameZh(), 90, 0);
        labelPrinter.PrintText(28 * 8, 45 * 8, "3", PinyinUtil.getPinyin(boardingVo.getNameZh(), StrUtil.EMPTY).toUpperCase(), 90, 0, 0, 0);
    }
}
