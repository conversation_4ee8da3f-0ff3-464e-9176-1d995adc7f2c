package com.swcares.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 行李牌
 *
 * <AUTHOR>
 */
@ApiModel(value = "luggageVo", description = "行李牌打印规范")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LuggageVo {

    /**
     * 中文姓名
     */
    @ApiModelProperty("中文姓名")
    private String name;

    /**
     * 行李件数
     */
    @ApiModelProperty("行李件数")
    private String bagCnt;

    /**
     * 行李总重量
     */
    @ApiModelProperty("行李总重量")
    private String bagWeight;

    /**
     * 行李牌号
     */
    @ApiModelProperty("行李牌号[航空公司二字码]")
    private String bagNoAirlineCode;

    /**
     * 接收行李代理
     */
    @ApiModelProperty("接收行李代理")
    private String bagAgent;

    /**
     * 行李牌号
     */
    @ApiModelProperty("行李牌号[航空公司数字编号]")
    private String bagNoAirlineSerial;

    /**
     * 接收序号
     */
    @ApiModelProperty("接收序号")
    private String seqNo;

    /**
     * 行李的航段信息
     */
    @ApiModelProperty("行李的航段信息")
    private List<LuggageSegVo> luggageSegVos;

    /**
     * 条码
     */
    @ApiModelProperty("条码")
    private String barCode;
}
