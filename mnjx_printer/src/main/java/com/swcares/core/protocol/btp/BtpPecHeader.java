package com.swcares.core.protocol.btp;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
public class BtpPecHeader {
    /**
     * 指示打印机以下所发的字符流将是一个PECTAB
     */
    private final static String BTT = "BTT";

    /**
     * PECTAB 编号
     */
    private final static String SERIAL_NO = "01";

    /**
     * PECTAB版本号
     */
    private final static String VERSION = "01";

    /**
     * 继续字符用于在一条数据信息中分隔两个文档单元
     */
    private final static String CON_CHAR = "]";

    /**
     * 自动递增域长度，从A~Z，分别代表1~26
     */
    private final static String INCREMENTAL_DOMAIN = "F";

    /**
     * 文档宽度,本参量设置文档的打印宽度
     */
    private final static String PRINT_WIDTH = "520";

    /**
     * 镜像点位置
     */
    private final static String MIRROR_POINT = "287";

    /**
     * 返回头部数据
     *
     * @return 返回头部数据
     */
    public static String toStr() {
        return StrUtil.format("{}}{}{}{}{} {}{}=#",
                BTT, SERIAL_NO, VERSION, CON_CHAR, INCREMENTAL_DOMAIN,
                PRINT_WIDTH, MIRROR_POINT);
    }
}
