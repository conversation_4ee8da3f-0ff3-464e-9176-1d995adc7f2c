package com.swcares.core.unified;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;


/**
 * 全局统一异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice
@ResponseBody
public class UnifiedExceptionHandler {
    /**
     * 捕捉所有的异常，
     * 在业务逻辑中dao、service、controller都不处理异常
     *
     * @param exp 异常数据
     * @return 返回包装后的异常数据
     */
    @ExceptionHandler(Exception.class)
    public UnifiedResult handleException(Exception exp) {
        // 业务异常
        if (exp instanceof UnifiedResultException) {
            // 日志记录
            log.error(exp.getMessage());
            return UnifiedResult.fail(exp.getMessage());
        } else if (exp instanceof MethodArgumentNotValidException) {
            // 字段校验问题
            MethodArgumentNotValidException methodArgumentNotValidException = (MethodArgumentNotValidException) exp;
            BindingResult bindingResult = methodArgumentNotValidException.getBindingResult();
            List<FieldError> fieldErrors = bindingResult.getFieldErrors();
            List<String> errors = new ArrayList<>();
            fieldErrors.forEach(fieldError -> errors.add(StrUtil.format("({}):{}", fieldError.getField(), fieldError.getDefaultMessage())));
            return UnifiedResult.fail(CollUtil.join(errors, StrUtil.COMMA));
        } else {
            // 控制台打印
            exp.printStackTrace();
            // 日志记录
            log.error(exp.getMessage());
            // 程序异常(前端没有区分400和500错误，她们是直接取的不等于200的时候取message信息)
            return UnifiedResult.fail(HttpStatus.HTTP_INTERNAL_ERROR, exp.getMessage());
        }
    }


}
