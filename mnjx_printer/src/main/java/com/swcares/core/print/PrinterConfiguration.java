package com.swcares.core.print;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 打印机机配置
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "printer")
@Component
public class PrinterConfiguration {

    /**
     * 打印机的类型，目前只支持 Honeywell 和 IER400
     */
    private String category;

    /**
     * 打印机使用的打印方式：串口或者网络
     */
    private String type;

    /**
     * 使用新北洋6800X型号USB连接时，指定登机牌打印数据构建
     */
    private String newBeiyangBoarding;

    /**
     * 使用新北洋6800X型号USB连接时，指定行李牌打印数据构建
     */
    private String newBeiyangLuggage;

    /**
     * 登机牌打印机的打印机信息
     */
    private String boardingPrinterInfo;

    /**
     * 行李牌打印机的打印机信息
     */
    private String luggagePrinterInfo;

    /**
     * 登机牌打印机的序列号信息
     */
    private String boardingPrinterSerial;

    /**
     * 行李牌打印机的序列号信息
     */
    private String luggagePrinterSerial;

    /**
     * 打印机连接模式，0：不需要指定串口号，1：需要指定串口号
     */
    private String printerConnectionModel;

    /**
     * 指定登机牌打印机串口号
     */
    private String cpCom;

    /**
     * 指定行李牌打印机串口号
     */
    private String btpCom;

    /**
     * 使用A4纸打印或者使用241x93mm三等分纸打印：A4;241x93
     */
    private String itineraryType;

    /**
     * 使用三等分纸打印时是否只打印数据。0否，1是
     */
    private String onlyPrintData;

    /**
     * 只打印数据时打印数据的方向。0反向，1正向
     */
    private String direction;

    /**
     * 字体大小。只打印数据时设置为 10，其他设置为 16
     */
    private int fontSize;
}
