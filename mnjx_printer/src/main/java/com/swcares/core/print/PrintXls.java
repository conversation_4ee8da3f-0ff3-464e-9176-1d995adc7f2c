package com.swcares.core.print;

import cn.hutool.core.util.StrUtil;
import com.spire.xls.PageSetup;
import com.spire.xls.Workbook;
import com.spire.xls.Worksheet;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.printing.PDFPrintable;
import org.apache.pdfbox.printing.Scaling;

import java.awt.print.*;
import java.io.FileInputStream;
import java.io.InputStream;


/**
 * ClassName：com.kaiya.mnjx.printSocket.xls.PrintXls <br>
 * description：打印excel <br>
 *
 * <AUTHOR> <br>
 * date 2020/11/16 <br>
 * @version v1.0 <br>
 */
public class PrintXls {

    /**
     * Title：exec <br>
     * description：打印行程单<br>
     *
     * @param path <br>
     *             author：zhaokan <br>
     *             date：2020/11/16 <br>
     */
    public static void exec(String path, PrinterConfiguration printerConfiguration, int serial) {
        if ("A4".equals(printerConfiguration.getItineraryType())) {
            printeA4(path);
        } else {
            printStandard(path, serial);
        }
    }

    /**
     * Title: printeA4
     * Description: 使用A4纸打印
     *
     * @param path
     * @return
     * <AUTHOR>
     * @date 2023/6/18 15:06
     */
    private static void printeA4(String path) {
        // Create a workbook and load an Excel file
        Workbook workbook = new Workbook();
        workbook.loadFromFile(path);
        PrinterJob printerJob = PrinterJob.getPrinterJob();

        PageFormat pageFormat = printerJob.defaultPage();

        Paper paper = pageFormat.getPaper();

        // 设置纸张大小
        paper.setSize(595, 842);
        pageFormat.setPaper(paper);
        //打印方向
        pageFormat.setOrientation(PageFormat.LANDSCAPE);
        pageFormat.setPaper(paper);

        printerJob.setCopies(1);
        printerJob.setPrintable(workbook, pageFormat);
        try {
            // 开启打印窗口
//            boolean b = printerJob.printDialog();
//            if (b) {
//                printerJob.print();
//            }
            printerJob.print();
        } catch (PrinterException e) {
            e.printStackTrace();
        }
    }

    /**
     * Title: printStandard
     * Description: 使用标准发票类型纸打印，当前规格241x93mm（单张）
     *
     * @param path
     * @return
     * <AUTHOR>
     * @date 2023/6/18 15:06
     */
    private static void printStandard(String path, int serial) {
        // Create a workbook and load an Excel file
        Workbook workbook = new Workbook();
        workbook.loadFromFile(path);

        // 直接打印excel
//        excelPrint(workbook);

        // excel转换为pdf再打印
        toPdfPrint(workbook, serial);
    }

    private static void excelPrint(Workbook workbook) {
        // 创建 PrinterJob对象
        PrinterJob printerJob = PrinterJob.getPrinterJob();

        // 指定打印页面为默认大小和方向
        PageFormat pageFormat = printerJob.defaultPage();

        // 设置纸张的大小和打印区域
        Paper paper = new Paper();
        // 纸张大小计算：宽（高）毫米*72*10/254
        paper.setSize(688, 264);
        // 设置打印区域：x y：左上角坐标；width height：右下角坐标。单位同纸张大小计算
        paper.setImageableArea(0, 0, 688, 264);
        pageFormat.setPaper(paper);
        printerJob.setCopies(1);
        printerJob.setPrintable(workbook, pageFormat);

        // 执行打印
        try {
            printerJob.print();
        } catch (PrinterException e) {
            e.printStackTrace();
        }
    }

    private static void toPdfPrint(Workbook workbook, int serial) {
        // 设置转换SHEET匹配页面大小
        workbook.getConverterSetting().setSheetFitToPage(true);

        // 获取第一个工作簿
        Worksheet worksheet = workbook.getWorksheets().get(0);

        PageSetup pageSetup = worksheet.getPageSetup();
        // 设置适应页面大小
        pageSetup.isFitToPage(true);

        // 设置边距
        pageSetup.setTopMargin(0.3);
        pageSetup.setBottomMargin(0.3);
        pageSetup.setLeftMargin(1);
        pageSetup.setRightMargin(1);

        // 设置打印质量
        pageSetup.setPrintQuality(600);

        // 转PDF存储
        String fileName = StrUtil.format("{}{}.pdf", ".\\行程单", serial);
        String reverseFileName = StrUtil.format("{}{}.pdf", ".\\反向行程单", serial);
        worksheet.saveToPdf(fileName);

        // 查默认打印机打印纸张型号
//        PrintService printService = PrintServiceLookup.lookupDefaultPrintService();
//        Media[] objs = (Media[]) printService.getSupportedAttributeValues(Media.class, null, null);
//        for (Media obj : objs) {
//            if (obj instanceof MediaSizeName) {
//                System.out.println("纸张型号：" + obj);
//            } else if (obj instanceof MediaTray) {
//                System.out.println("纸张来源：" + obj);
//            } else {
//                System.out.println("其他：" + obj);
//            }
//        }

        PrinterJob printerJob = PrinterJob.getPrinterJob();
        // 设置纸张的大小和打印区域
        Paper paper = new Paper();
        // 纸张大小计算：宽（高）毫米*72*10/254
        paper.setSize(688, 264);
        // 设置打印区域：x y：左上角坐标；width height：右下角坐标。单位同纸张大小计算
        paper.setImageableArea(0, 0, 688, 264);
//        int marginLeft = 96;
//        int marginRight = 54;
//        int marginTop = 31;
//        int marginBottom = 20;
//        paper.setImageableArea(marginLeft, marginRight, 683 - (marginLeft + marginRight), 263 - (marginTop + marginBottom));
//        paper.setImageableArea(59.5, 36.8, 683 - (59.5 + 36.8), 263 - 16);

        PageFormat pageFormat = new PageFormat();
        // 设置页面打印方向为纵向
        pageFormat.setOrientation(PageFormat.PORTRAIT);
        pageFormat.setPaper(paper);

        try {
            InputStream in = new FileInputStream(fileName);
            PDDocument document = PDDocument.load(in);
            PDPage page = document.getPage(0);
            page.setRotation(180);
            document.save(reverseFileName);
            // 设置PDF适应打印页面大小
            PDFPrintable printable = new PDFPrintable(document, Scaling.SCALE_TO_FIT);
            Book book = new Book();
            book.append(printable, pageFormat);
            printerJob.setPageable(book);
            // 开启打印窗口
//            boolean b = printerJob.printDialog();
//            if (b) {
//                printerJob.print();
//            }
            printerJob.print();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
